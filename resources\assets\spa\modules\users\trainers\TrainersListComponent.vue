<template>
    <UsersListComponent :store="store" :columns="columns" :initFilters="initFilters" type="teacher">
        <template #actions="{ row, position }">
            <GridActionMenuItem
                v-if="position === 'before-delete'"
                @click="openModal(row)"
                :item="{
                    icon: 'edit',
                    label: 'Update Trainer Code',
                    id: 'update-trainer-code',
                }"
            />
        </template>
    </UsersListComponent>
    <TeacherCodeForm :selectedRow="selectedRow" />
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import UsersListComponent from '@spa/modules/users/UsersListComponent.vue';
import { useTeacherStore } from '@spa/stores/modules/teacher/useTeacherStore.js';
import GridActionMenuItem from '@spa/components/AsyncComponents/Grid/Partials/GridActionMenuItem.vue';
import TeacherCodeForm from '@spa/modules/users/trainers/partials/TeacherCodeForm.vue';
const store = useTeacherStore();
//store.updateTab('trainers');
const selectedRow = ref(null);

const columns = [
    {
        field: 'name',
        name: 'name',
        title: 'Name',
        width: '256px',
        sortable: true,
        replace: true,
        locked: true,
    },
    {
        field: 'code',
        name: 'code',
        title: 'Staff Number',
        width: '150px',
        replace: true,
    },
    {
        field: 'email',
        name: 'email',
        title: 'Email',
        width: '232px',
    },
    {
        field: 'role_id',
        name: 'user_roles',
        title: 'Role(s)',
        width: '256px',
        replace: true,
        sortable: true,
        filterable: true,
        customFilter: true,
    },
    {
        field: 'user_status',
        name: 'user_status',
        title: 'User Status',
        width: '150px',
        replace: true,
        sortable: true,
        filterable: true,
        customFilter: true,
        filterableConfig: {
            type: 'select',
            options: [
                { value: 1, label: 'Active' },
                { value: 0, label: 'Inactive' },
                { value: 2, label: 'Pending' },
                { value: 3, label: 'Disabled' },
            ],
        },
    },
    {
        field: 'last_login',
        name: 'last_login',
        title: 'Last Login',
        width: '200px',
        replace: true,
        // filterable: true,
        // filterableConfig: {
        //     type: 'date',
        // },
    },
    {
        field: 'created_by',
        name: 'created_by',
        title: 'Created By',
        width: '200px',
        replace: true,
        // filterable: true,
        // filterableConfig: {
        //     type: 'string',
        // },
    },
];

const openModal = (row) => {
    store.actionDialog = true;
    selectedRow.value = row;
};

const initFilters = () => {
    store.filters = {};
};

onMounted(() => {
    initFilters();
});
</script>
