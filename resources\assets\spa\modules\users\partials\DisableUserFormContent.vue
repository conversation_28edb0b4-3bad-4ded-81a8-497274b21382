<template>
    <h3 class="text-base font-medium text-gray-900">
        {{ formTitle }}
    </h3>

    <ul class="list-disc pl-5 text-gray-600">
        <li>Access the platform.</li>
        <li>Receive login invites or reset emails.</li>
        <li>Submit attendance, applications, or view student data.</li>
    </ul>
    <p>This action does <strong>NOT</strong> delete user data. It only revokes access.</p>
</template>

<script setup>
import { useUsersStore } from '@spa/stores/modules/users/useUsersStore.js';
import { computed } from 'vue';

const props = defineProps({
    store: Object,
});

const userStore = useUsersStore();

const formTitle = computed(() => {
    let content =
        userStore.selected?.length > 1
            ? `${userStore.selected?.length} users`
            : userStore.selected[0]?.name;
    return `You are about to disable login access for ${content}`;
});
</script>
