<template>
    <k-dialog
        :visibleDialog="visible"
        :hideOnOverlayClick="true"
        :fixedActionBar="false"
        :width="'600px'"
        :isDisabled="loading"
        :isSubmitting="loading"
        @modalclose="handleClose"
    >
        <template #title>
            <div class="flex items-center gap-3">
                <span class="font-semibold text-gray-800">Bulk Approve Commission</span>
            </div>
        </template>

        <template #content>
            <div class="space-y-4">
                <p class="text-gray-700">
                    Are you sure you want to approve {{ selectedItems?.length || 0 }} selected
                    commission(s)?
                </p>

                <!-- Warning for expired contracts -->
                <div v-if="hasExpiredContracts" class="w-full space-y-2">
                    <p class="w-full rounded-lg bg-yellow-100 p-2 text-sm font-medium text-red-500">
                        Agent commission valid date range: {{ expiredContractDateRange }}.
                    </p>
                    <div class="flex w-full space-x-2">
                        <span class="rounded-lg bg-yellow-100 p-2 text-sm font-medium text-red-500">
                            The agent contract has expired, would you like to extend the contract?
                        </span>
                        <button
                            type="button"
                            class="btn-primary px-3 py-2"
                            title="Extend Date Range"
                            @click="handleExtendContract"
                        >
                            <span class="text-sm font-medium leading-4 text-white">Yes</span>
                        </button>
                    </div>
                </div>

                <!-- Commission details -->
                <div
                    class="flex w-full flex-col items-start justify-start space-y-4 rounded-lg border border-gray-200 bg-white p-4"
                >
                    <div class="grid w-full grid-cols-[80px_1fr] gap-2">
                        <label class="text-sm font-medium text-gray-700">Agent Inv:</label>
                        <div class="text-sm text-gray-900">
                            <div v-for="item in selectedItems" :key="item.id" class="mb-1">
                                {{ item.formatted_invoice_number || 'N/A' }}
                            </div>
                        </div>
                    </div>

                    <div class="w-full">
                        <label
                            for="bulk-approve-remarks"
                            class="mb-2 block text-sm font-medium text-gray-700"
                        >
                            Remarks (optional):
                        </label>
                        <textarea
                            id="bulk-approve-remarks"
                            v-model="remarks"
                            placeholder="Enter remarks..."
                            rows="4"
                            class="resize-vertical w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-700 focus:outline-none focus:ring-1 focus:ring-blue-700"
                        ></textarea>
                    </div>
                </div>
            </div>
        </template>

        <template #footer>
            <div class="mt-5 flex justify-end gap-3">
                <Button variant="secondary" @click="handleClose" :disabled="loading">
                    Cancel
                </Button>
                <Button variant="primary" @click="handleApprove" :disabled="loading">
                    {{ loading ? 'Approving...' : 'Approve' }}
                </Button>
            </div>
        </template>
    </k-dialog>
</template>

<script>
import KendoDialog from '@spa/components/KendoModals/KendoDialog.vue';
import Button from '@spa/components/Buttons/Button.vue';

export default {
    components: {
        'k-dialog': KendoDialog,
        Button,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        selectedItems: {
            type: Array,
            default: () => [],
        },
        loading: {
            type: Boolean,
            default: false,
        },
        hasExpiredContracts: {
            type: Boolean,
            default: false,
        },
        expiredContractDateRange: {
            type: String,
            default: '',
        },
        editAgentCommissionUrl: {
            type: String,
            default: '#',
        },
    },
    emits: ['close', 'approve', 'extend-contract'],
    data() {
        return {
            remarks: '',
        };
    },
    watch: {
        visible(newVisible) {
            if (newVisible) {
                this.remarks = '';
            }
        },
    },
    methods: {
        handleClose() {
            this.remarks = '';
            this.$emit('close');
        },
        handleApprove() {
            this.$emit('approve', {
                items: this.selectedItems,
                remarks: this.remarks.trim(),
            });
        },
        handleExtendContract() {
            this.$emit('extend-contract', {
                url: this.editAgentCommissionUrl,
            });
        },
    },
};
</script>
