r
<template>
    <FormElement class="space-y-4 pt-4">
        <template
            v-if="actionType === 'bulkMove' || actionType === 'move' || actionType === 'upload'"
        >
            <fieldset class="k-form-fieldset">
                <div class="space-y-1 px-4">
                    <div class="flex items-center justify-between">
                        <Label class="w-16 min-w-fit text-sm font-medium leading-5 text-gray-700">{{
                            fieldInfo.label
                        }}</Label>
                        <IconTooltip
                            :content="'Select an existing folder to upload your document, or create a new folder.'"
                        />
                    </div>
                    <div class="w-full max-w-[438px]">
                        <DropDownTree
                            :id="fieldInfo.id"
                            ref="folderDropdown"
                            :name="fieldInfo.name"
                            :value="folderName"
                            :valueMap="valueMap"
                            @change="handleDropdownChange"
                            :placeholder="placeholder"
                            :data-items="folderData"
                            :text-field="textField"
                            :data-item-key="dataItemKey"
                            :expandField="expandField"
                            @expandchange="onExpandChange"
                            :required="false"
                            :validation-message="'Please select one folder'"
                            :filterable="true"
                            @filterchange="onFilterChange"
                            :popup-settings="popupSettings"
                            :footer="'myFooter'"
                            @click="handleFocus"
                        >
                            <template v-slot:myFooter>
                                <div class="flex items-center justify-center">
                                    <Button
                                        variant="ghost"
                                        class="item-center flex justify-center gap-1"
                                        @click="handleNewFolder"
                                    >
                                        <icon name="add" width="12" height="12" fill="#40a9ff" />
                                        <span>New Folder</span>
                                    </Button>
                                </div>
                            </template>
                        </DropDownTree>
                    </div>
                </div>
            </fieldset>
            <fieldset class="k-form-fieldset" v-if="showNewFolderField">
                <div class="space-y-1 px-4">
                    <Label class="w-16 text-sm font-medium leading-5 text-gray-700"
                        >Folder Name:</Label
                    >
                    <Field
                        :id="'folder_name'"
                        :name="'folder_name'"
                        :component="'folderTemplate'"
                        :validator="requiredtrue"
                    >
                        <template #folderTemplate="{ props }">
                            <FormInput
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                                :pt="{
                                    root: 'w-full',
                                }"
                                ref="firstInput"
                            />
                        </template>
                    </Field>
                </div>
            </fieldset>
            <fieldset class="k-form-fieldset" v-if="actionType === 'upload'">
                <div class="px-0">
                    <FileUploader
                        :variant="'dropzone'"
                        :customList="false"
                        :batch="false"
                        :auto-upload="false"
                        @add="handleAdd"
                        @remove="handleRemove"
                        :custom-list="true"
                        :custom-list-ui="'clientProgress'"
                        :progress="getUploadProgress"
                        :error="getUploadError"
                        :isDocumentUploaderClass="true"
                        :accept="'image/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document'"
                    />
                </div>
            </fieldset>
        </template>
        <template v-else-if="actionType === 'createFolder' || actionType === 'rename'">
            <fieldset class="k-form-fieldset">
                <div class="space-y-1 px-4">
                    <Label class="w-16 text-sm font-medium leading-5 text-gray-700">{{
                        fieldInfo.label
                    }}</Label>
                    <Field
                        :id="fieldInfo.id"
                        :name="fieldInfo.name"
                        :component="'folderTemplate'"
                        :validator="requiredtrue"
                    >
                        <template #folderTemplate="{ props }">
                            <FormInput
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                                :pt="{
                                    root: 'w-full',
                                }"
                            />
                        </template>
                    </Field>
                </div>
            </fieldset>
        </template>
        <template
            v-else-if="
                actionType === 'viewAccess' ||
                actionType === 'bulkStudentAccess' ||
                actionType === 'bulkTeacherAccess'
            "
        >
            <div class="space-y-4 px-4 py-2">
                <field
                    :id="'student_view'"
                    :name="'student_view'"
                    :label="'Student:'"
                    :component="'studentViewTemplate'"
                    :value-label="true"
                    :label-position="'end'"
                    :dynamic-label="['Available', 'Not Available']"
                    :pt="{
                        root: '!flex justify-between items-center',
                        wrapper: 'items-center gap-2',
                        label: '!font-normal !text-gray-600',
                        value: 'min-w-20',
                    }"
                    v-if="actionType === 'bulkStudentAccess' || actionType === 'viewAccess'"
                >
                    <template v-slot:studentViewTemplate="{ props }">
                        <FormSwitch
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
                <field
                    :id="'teacher_view'"
                    :name="'teacher_view'"
                    :label="'Teacher:'"
                    :component="'teacherViewTemplate'"
                    :value-label="true"
                    :label-position="'end'"
                    :dynamic-label="['Available', 'Not Available']"
                    :pt="{
                        root: '!flex justify-between items-center',
                        wrapper: 'items-center gap-2',
                        label: '!font-normal !text-gray-600',
                        value: 'min-w-20',
                    }"
                    v-if="actionType === 'bulkTeacherAccess' || actionType === 'viewAccess'"
                >
                    <template v-slot:teacherViewTemplate="{ props }">
                        <FormSwitch
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
            </div>
        </template>
        <div
            class="sticky bottom-0 flex w-full items-center justify-end gap-4 bg-white px-6 py-4 shadow-inner-top"
        >
            <Button :variant="'secondary'" @click="closePopup"
                ><span>{{ getUploadSuccess ? 'Close' : 'Cancel' }}</span></Button
            >
            <Button
                type="submit"
                variant="primary"
                class="min-w-[100px]"
                :disabled="false"
                v-if="!getUploadSuccess"
                >{{ primaryBtnLabel }}
            </Button>
        </div>
    </FormElement>
</template>
<script>
import { mapState } from 'pinia';
import { Field, FormElement } from '@progress/kendo-vue-form';
import { Label } from '@progress/kendo-vue-labels';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import Button from '@spa/components/Buttons/Button.vue';
import FolderDropdown from '@spa/components/MediaManager/partials/FolderDropdown.vue';
import IconTooltip from '@spa/components/Tooltip/IconTooltip.vue';
import FileUploader from '@spa/components/Uploader/FileUploader.vue';
import { processTreeData, expandedState } from '@spa/helpers/treeDataOperations';
import { DropDownTree } from '@progress/kendo-vue-dropdowns';
import { useDocumentStore } from '@spa/stores/modules/document.store.js';
import FormSwitch from '@spa/components/KendoInputs/FormSwitch.vue';

const dataItemKey = 'id';
const subItemsField = 'items';
const expandField = 'expanded';
const selectField = 'selected';
const textField = 'name';

const fields = {
    dataItemKey,
    expandField,
    selectField,
    subItemsField,
};

export default {
    mounted() {
        this.kendoForm.onChange('folder_id', {
            value: this.decryptItParentId,
        });
    },
    inject: {
        kendoForm: { default: {} },
    },
    props: {
        folders: {
            type: Array,
            default: () => [],
        },
        field: {
            type: Object,
            default: {},
        },
        actionType: {
            type: String,
        },
        decryptItParentId: {
            type: String,
        },
    },
    components: {
        Field,
        FormElement,
        FormInput,
        Label,
        Button,
        FolderDropdown,
        IconTooltip,
        FileUploader,
        DropDownTree,
        FormSwitch,
    },
    data() {
        return {
            folderName: this.folders.find((item) => item.id === this.decryptItParentId),
            popupSettings: {
                animate: false,
                offset: { left: 150, top: 50 },
                className: 'tw-dropdowntree',
            },
            showNewFolderField: false,
            fields,
            textField,
            dataItemKey,
            subItemsField,
            expandField,
            selectField,
            value: this.decryptItParentId,
            expanded: [this.folders[0][dataItemKey], this.decryptItParentId],
            filter: null,
            placeholder: 'Select Folder',
            isOpened: true,
            label: 'Available',
        };
    },
    computed: {
        ...mapState(useDocumentStore, [
            'getUploadSuccess',
            'getUploadError',
            'getUploadProgress',
            'setUploadSuccess',
            'setUploadError',
            'setUploadProgress',
        ]),
        folderName2() {
            const targetId = this.decryptItParentId;
            const result = this.folders.find((item) => item.id === targetId);
            return result;
        },
        folderData() {
            const buildTree = (data, parentId = 0) => {
                return data
                    .filter((item) => item.parent_id === parentId)
                    .map((item) => ({
                        name: item.name,
                        id: item.id,
                        parent_id: item.parent_id,
                        items: buildTree(data, item.id),
                    }));
            };
            const data = buildTree(this.folders);
            return processTreeData(
                data,
                {
                    expanded: this.expanded,
                    value: this.value,
                    filter: this.filter,
                },
                fields
            );
        },
        primaryBtnLabel() {
            let buttonLabelMappings = {
                upload: 'Upload',
                createFolder: 'Create',
                move: 'Move File',
                bulkMove: 'Move Files',
                rename: 'Rename',
                bulkDelete: 'Delete Files',
                delete: 'Delete',
                viewAccess: 'Provide Access',
                bulkStudentAccess: 'Provide Access',
                bulkTeacherAccess: 'Provide Access',
            };
            return buttonLabelMappings[this.actionType] || 'Save';
        },
        fieldInfo() {
            let fieldMappings = {
                rename: {
                    label: 'Name',
                    id: 'name',
                    name: 'name',
                },
                move: {
                    label: 'Select Folder',
                    id: 'folder_id',
                    name: 'folder_id',
                },
                bulkMove: {
                    label: 'Select Folder',
                    id: 'folder_id',
                    name: 'folder_id',
                },
                createFolder: {
                    label: 'Folder Name',
                    id: 'folder_name',
                    name: 'folder_name',
                },
                upload: {
                    label: 'Upload',
                    id: 'upload',
                    name: 'upload',
                },
            };
            return fieldMappings[this.actionType] || {};
        },
    },
    methods: {
        requiredtrue,
        handleNewFolder() {
            this.folderName = {
                name: 'New Folder',
                id: 0,
                parent_id: 0,
                expanded: false,
                selected: null,
            };
            this.kendoForm.onChange('folder_id', {
                value: 0,
            });
            this.showNewFolderField = true;

            document.getElementById('upload').click();
        },
        handleFocus(e) {
            console.log('focus', e);
            this.isOpened = !this.isOpened;
        },
        handleDropdownChange(e) {
            const value = e.value || null;
            this.folderName = value;
            this.isOpened = false;
            this.kendoForm.onChange('folder_id', {
                value: value.id,
            });
            this.showNewFolderField = false;
        },
        onExpandChange(event) {
            this.expanded = expandedState(event.item, dataItemKey, this.expanded);
        },
        closePopup() {
            this.setUploadSuccess(false);
            this.setUploadError(null);
            this.setUploadProgress(0);
            this.$emit('close');
        },
        handleFileUpload(event) {
            const file = event.target.files[0];
            emit('submit', { action: 'upload', file });
        },
        valueMap(value) {
            if (Array.isArray(value)) {
                return value.map((item) => item.id);
            }
            return value ? value.id : null;
        },
        onFilterChange(event) {
            this.filter = event.filter;
        },
        handleAdd(event, files) {
            this.kendoForm.onChange('files', {
                value: files,
            });
        },
        handleRemove(event, files) {
            this.kendoForm.onChange('files', {
                value: files,
            });
        },
    },
};
</script>
<style>
.k-popup-dropdowntree .k-treeview,
.k-dropdowntree-popup .k-treeview,
.k-multiselecttree-popup .k-treeview {
    padding-block: 4px;
    padding-inline: 4px;
    height: 250px;
    overflow-y: scroll;
}
</style>
