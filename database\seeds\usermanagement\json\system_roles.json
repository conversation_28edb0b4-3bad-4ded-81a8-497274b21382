[{"role_name": "Master Super Admin", "role_alias": "master_admin", "role_alias_update_to": "master_admin", "role_description": "This role will have access to all actions. It will have no sub-roles. This system role is also not visible in the manage roles/permissions UI.", "role_scope": "TYPE_SADMIN", "is_super_admin": "1", "can_have_roles": "0", "has_all_access_role": "1", "all_access_role_name": "Super Admin", "is_active": "1", "children": []}, {"role_name": "Team Members (Staffs)", "role_alias": "team", "role_alias_update_to": "team-members", "role_description": "College Team", "role_scope": "TYPE_COLLEGE_TEAM", "is_super_admin": "0", "can_have_roles": "1", "has_all_access_role": "1", "all_access_role_name": "Staff Full Access", "is_active": "1", "children": [{"role_name": "Non Academic", "role_alias": "team-nonacademic", "role_alias_update_to": "team-nonacademic", "role_description": "Non Academic Team Roles", "role_scope": "TYPE_COLLEGE_TEAM", "is_super_admin": "0", "can_have_roles": "1", "has_all_access_role": "1", "all_access_role_name": "Non Academic Full Access", "is_active": "1"}]}, {"role_name": "Trainers (Teachers)", "role_alias": "team-academic", "role_alias_update_to": "teachers", "role_description": "Teacher Roles", "role_scope": "TYPE_TEACHER", "is_super_admin": "0", "can_have_roles": "1", "has_all_access_role": "1", "all_access_role_name": "Teacher Full Access", "is_active": "1"}, {"role_name": "Agent Staffs", "role_alias": "agent-staffs", "role_alias_update_to": "agent-staffs", "role_description": "This is the agent's staff role", "role_scope": "TYPE_AGENT_STAFF", "is_super_admin": "0", "can_have_roles": "1", "has_all_access_role": "0", "all_access_role_name": "Agent Staff Full Access", "is_active": "1"}, {"role_name": "Education Agency (Agents)", "role_alias": "agents", "role_alias_update_to": "agents", "role_description": "This is the agent's role", "role_scope": "TYPE_AGENT", "is_super_admin": "0", "can_have_roles": "1", "has_all_access_role": "1", "all_access_role_name": "Agent Full Access", "is_active": "1"}, {"role_name": "Employers", "role_alias": "employers", "role_alias_update_to": "employers", "role_description": "This is the employer's role", "role_scope": "TYPE_STAFF", "is_super_admin": "0", "can_have_roles": "1", "has_all_access_role": "1", "all_access_role_name": "Employers Full Access", "is_active": "1"}, {"role_name": "Students", "role_alias": "student", "role_alias_update_to": "students", "role_description": "This is the student's role", "role_scope": "TYPE_STUDENT", "is_super_admin": "0", "can_have_roles": "1", "has_all_access_role": "1", "all_access_role_name": "Student Full Access", "is_active": "1"}, {"role_name": "Service Providers", "role_alias": "service-providers", "role_alias_update_to": "service-providers", "role_description": "These are service providers", "role_scope": "TYPE_STUDENTSERVICE", "is_super_admin": "0", "can_have_roles": "1", "has_all_access_role": "1", "all_access_role_name": "Placement Providers Full Access", "is_active": "1"}, {"role_name": "Placement Providers", "role_alias": "placement-providers", "role_alias_update_to": "placement-providers", "role_description": "These are placement providers", "role_scope": "TYPE_STUDENTSERVICE", "is_super_admin": "0", "can_have_roles": "1", "has_all_access_role": "1", "all_access_role_name": "Service Providers Full Access", "is_active": "1"}]