<template>
    <Dialog
        v-if="visible"
        title="Edit Attendance Import"
        :style="{ maxWidth: '700px' }"
        @close="closeModal"
        :class="'k-modal-window'"
        :dialog-class="'tw-dialog custom-modal-wrapper max-w-[700px] w-full mx-4'"
        append-to="body"
    >
        <div v-if="importData" class="space-y-4">
            <form @submit.prevent="saveChanges" class="space-y-4">
                <div class="grid grid-cols-2 gap-4 max-md:grid-cols-1">
                    <!-- Student ID -->
                    <div>
                        <label for="studentId" class="block text-sm font-medium text-gray-700"
                            >Student ID</label
                        >
                        <input
                            id="studentId"
                            v-model="formData.StudentId"
                            type="text"
                            class="tw-input-text"
                            required
                        />
                        <p v-if="errors.StudentId" class="mt-1 text-sm text-red-600">
                            {{ errors.StudentId }}
                        </p>
                    </div>

                    <!-- Unit Code -->
                    <div>
                        <label for="unitCode" class="block text-sm font-medium text-gray-700"
                            >Unit Code</label
                        >
                        <input
                            id="unitCode"
                            v-model="formData.UnitCode"
                            type="text"
                            class="tw-input-text"
                            required
                        />
                        <p v-if="errors.UnitCode" class="mt-1 text-sm text-red-600">
                            {{ errors.UnitCode }}
                        </p>
                    </div>

                    <!-- Attendance Date -->
                    <div>
                        <label
                            for="attendanceDate"
                            class="mb-1 block text-sm font-medium text-gray-700"
                            >Attendance Date</label
                        >
                        <DatePicker
                            id="attendanceDate"
                            v-model="datePickerValue"
                            :format="'dd-MM-yyyy'"
                            :format-placeholder="{
                                day: 'dd',
                                month: 'MM',
                                year: 'yyyy',
                            }"
                            placeholder="dd-MM-yyyy"
                            class="w-full"
                            @change="handleDateChange"
                        />
                        <p v-if="errors.AttendanceDate" class="mt-1 text-sm text-red-600">
                            {{ errors.AttendanceDate }}
                        </p>
                    </div>

                    <!-- Attendance Status -->
                    <div>
                        <label
                            for="attendanceStatus"
                            class="block text-sm font-medium text-gray-700"
                            >Attendance Status</label
                        >
                        <DropDownList
                            id="attendanceStatus"
                            :data-items="statusOptions"
                            text-field="text"
                            value-field="value"
                            :data-item-key="'value'"
                            v-model="formData.AttendanceStatus"
                            :value-primitive="true"
                            :default-item="{ text: 'Select Status', value: null }"
                            class="w-full"
                        />
                        <p v-if="errors.AttendanceStatus" class="mt-1 text-sm text-red-600">
                            {{ errors.AttendanceStatus }}
                        </p>
                    </div>
                </div>
            </form>
        </div>
        <DialogActionsBar>
            <div class="flex w-full justify-end space-x-2 px-3 py-1">
                <Button variant="secondary" @click="closeModal"> Cancel </Button>
                <Button variant="primary" @click="saveChanges" :disabled="saving">
                    {{ saving ? 'Saving...' : 'Save Changes' }}
                </Button>
            </div>
        </DialogActionsBar>
    </Dialog>
</template>

<script>
import { Dialog, DialogActionsBar } from '@progress/kendo-vue-dialogs';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import { DatePicker } from '@progress/kendo-vue-dateinputs';
import Button from '@spa/components/Buttons/Button.vue';
import { reactive, ref, watch } from 'vue';

export default {
    components: { Dialog, DialogActionsBar, Button, DropDownList, DatePicker },
    props: {
        visible: { type: Boolean, default: false },
        importData: { type: Object, default: null },
        saving: { type: Boolean, default: false },
    },
    setup(props, { emit }) {
        const formData = reactive({
            StudentId: '',
            UnitCode: '',
            AttendanceDate: '',
            AttendanceStatus: null,
        });
        const datePickerValue = ref(null);
        const errors = ref({});

        const statusOptions = [
            { text: 'Present', value: 'Present' },
            { text: 'Absent', value: 'Absent' },
            { text: 'Leave', value: 'Leave' },
        ];

        const toYmd = (str) => {
            if (!str || typeof str !== 'string') return '';
            const s = str.trim();
            // Already YYYY-MM-DD
            if (/^\d{4}-\d{2}-\d{2}$/.test(s)) return s;
            // DD/MM/YYYY
            let m = s.match(/^(\d{2})\/(\d{2})\/(\d{4})$/);
            if (m) return `${m[3]}-${m[2]}-${m[1]}`;
            // MM/DD/YYYY
            m = s.match(/^(\d{2})\/(\d{2})\/(\d{4})$/);
            if (m) return `${m[3]}-${m[1]}-${m[2]}`;
            // DD-MM-YYYY
            m = s.match(/^(\d{2})-(\d{2})-(\d{4})$/);
            if (m) return `${m[3]}-${m[2]}-${m[1]}`;
            // MM-DD-YYYY
            m = s.match(/^(\d{2})-(\d{2})-(\d{4})$/);
            if (m) return `${m[3]}-${m[1]}-${m[2]}`;
            return '';
        };

        const parseStringToDate = (str) => {
            if (!str) return null;
            const ymd = toYmd(str);
            if (!ymd) return null;
            const [year, month, day] = ymd.split('-');
            return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
        };

        const formatDateToYmd = (date) => {
            if (!date || !(date instanceof Date)) return '';
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        };

        watch(
            () => props.importData,
            (val) => {
                if (val) {
                    formData.StudentId = val.import_data?.StudentId || '';
                    formData.UnitCode = val.import_data?.UnitCode || '';
                    const dateStr = val.import_data?.AttendanceDate || '';
                    formData.AttendanceDate = toYmd(dateStr) || '';
                    datePickerValue.value = parseStringToDate(dateStr);
                    const st = val.import_data?.AttendanceStatus || null;
                    formData.AttendanceStatus = ['Present', 'Absent', 'Leave'].includes(st)
                        ? st
                        : null;
                }
            },
            { immediate: true, deep: true }
        );

        const handleDateChange = (event) => {
            const selectedDate = event.value;
            if (selectedDate) {
                formData.AttendanceDate = formatDateToYmd(selectedDate);
            } else {
                formData.AttendanceDate = '';
            }
        };

        const validate = () => {
            const e = {};
            if (!formData.StudentId) e.StudentId = 'Student ID is required';
            if (!formData.UnitCode) e.UnitCode = 'Unit Code is required';
            if (!formData.AttendanceDate) e.AttendanceDate = 'Attendance Date is required';
            if (!formData.AttendanceStatus) e.AttendanceStatus = 'Attendance Status is required';
            errors.value = e;
            return Object.keys(e).length === 0;
        };

        const closeModal = () => {
            emit('update:visible', false);
            errors.value = {};
        };

        const saveChanges = () => {
            if (!validate()) return;
            emit('save', {
                id: props.importData.id,
                import_data: {
                    StudentId: formData.StudentId,
                    UnitCode: formData.UnitCode,
                    AttendanceDate: formData.AttendanceDate,
                    AttendanceStatus: formData.AttendanceStatus,
                },
            });
        };

        return {
            formData,
            datePickerValue,
            errors,
            closeModal,
            saveChanges,
            handleDateChange,
            statusOptions,
        };
    },
};
</script>
