<template>
    <fieldwrapper :class="rootClass">
        <klabel
            :class="labelClass"
            :editor-id="id"
            :editor-valid="internalValid"
            :disabled="disabled"
            :optional="optional"
        >
            {{ label }}
            <div v-if="hint && hintOnLabel" class="font-light italic text-gray-400">
                ({{ hint }})
            </div>
            <span v-if="required || indicaterequired" class="ml-1 text-red-500">*</span>
        </klabel>

        <div :class="wrapClass">
            <div class="tw-phone-mask-input flex w-full">
                <!-- Country Dropdown -->
                <DropDownList
                    :data-items="countries"
                    :value="selectedCountry"
                    text-field="name"
                    value-field="name"
                    :popup-settings="{
                        position: 'bottom',
                        style: 'width: 100px !important',
                        className: 'tw-width-auto',
                        animate: false,
                    }"
                    @change="handleCountryChange"
                    :style="{ width: '80px !important', flex: '1 0 auto' }"
                    :item-render="'itemRender'"
                    :value-render="'valueRender'"
                    :filterable="true"
                    @filterchange="handleFilterChange"
                >
                    <template #itemRender="{ props }">
                        <li v-bind="props">
                            <div class="flex items-center gap-2">
                                <CountryImage :shortName="props.dataItem.short_name" />
                                <span
                                    >{{ props.dataItem.name }} ({{
                                        props.dataItem.short_name
                                    }})</span
                                >
                            </div>
                        </li>
                    </template>
                    <template #valueRender="{ props }">
                        <div v-bind="props">
                            <div class="flex items-center gap-2">
                                <CountryImage :shortName="props.value.short_name" />
                                <span>{{ props.value.short_name }}</span>
                            </div>
                        </div>
                    </template>
                </DropDownList>

                <!-- Masked Input -->
                <MaskedTextBox
                    :style="{ width: '100%' }"
                    :mask="mask"
                    :name="name"
                    :id="id"
                    :disabled="disabled"
                    v-model="vModel"
                    :placeholder="mask"
                    :input-class="inputClass"
                    :input-prefix="'prefix'"
                    @change="handleChange"
                    @blur="handleBlur"
                    @focus="handleFocus"
                >
                    <template #prefix>
                        {{ prefix }}
                    </template>
                </MaskedTextBox>
            </div>

            <error v-if="showValidationMessage">
                {{ validationMessage }}
            </error>
            <hint v-else-if="!showValidationMessage && !hintOnLabel">
                {{ hint }}
            </hint>
        </div>
    </fieldwrapper>
</template>

<script>
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { MaskedTextBox } from '@progress/kendo-vue-inputs';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import CountryImage from '@spa/components/Country/CountryImage.vue';
import { twMerge } from 'tailwind-merge';

const countries = window['kendo_countries_codes'];

export default {
    props: {
        modelValue: [String, Number],
        optional: Boolean,
        disabled: Boolean,
        placeholder: String,
        touched: Boolean,
        label: String,
        validationMessage: String,
        hint: String,
        hintOnLabel: { type: Boolean, default: false },
        id: String,
        valid: { type: Boolean, default: true },
        class: String,
        indicaterequired: { type: Boolean, default: false },
        orientation: { type: String, default: 'vertical' },
        value: [String, Number],
        defaultValue: String,
        name: String,
        required: { type: Boolean, default: false },
        country_code: {
            type: String,
            default: '',
        },
        pt: {
            type: Object,
            default: () => ({}),
        },
    },
    components: {
        fieldwrapper: FieldWrapper,
        klabel: Label,
        hint: Hint,
        error: Error,
        DropDownList,
        MaskedTextBox,
        CountryImage,
    },
    data() {
        return {
            countries: countries.slice(),
            selectedCountry: null,
            mask: '',
            prefix: '',
            internalValid: this.valid,
        };
    },
    computed: {
        vModel: {
            get() {
                return this.modelValue;
            },
            set(val) {
                this.$emit('update:modelValue', val);
            },
        },
        showValidationMessage() {
            return this.touched && this.validationMessage;
        },
        rootClass() {
            const base = 'tw-form__fieldwrapper';
            const orientation = this.orientation === 'horizontal' ? 'field-horizontal' : '';
            return twMerge(`${base} ${orientation}`, this.pt.root);
        },
        wrapClass() {
            return twMerge('k-form-field-wrap', this.pt.wrap);
        },
        labelClass() {
            return twMerge(
                'tw-form__label mb-1 font-medium leading-5 text-gray-700',
                this.pt.label
            );
        },
        inputClass() {
            return twMerge('tw-form__input', this.pt.input);
        },
    },
    watch: {
        modelValue(newVal) {
            // reset internal valid on user input
            this.internalValid = true;
        },
        valid(newVal) {
            this.internalValid = newVal;
        },
        country_code: {
            immediate: true,
            handler(newVal) {
                const prefix = '+' + newVal;
                const index = this.countries?.findIndex((c) => c.prefix === prefix);
                this.selectedCountry = this.countries[index !== -1 ? index : 0];
                this.mask = this.selectedCountry?.mask || '';
                this.prefix = this.selectedCountry?.prefix || '';
            },
        },
    },
    methods: {
        handleCountryChange(event) {
            this.selectedCountry = event.target.value;
            this.mask = this.selectedCountry?.mask || '';
            this.prefix = this.selectedCountry?.prefix || '';
            this.vModel = ''; // clear input
        },
        handleFilterChange(event) {
            this.countries = countries.filter((c) =>
                c.name.toLowerCase().includes(event.filter?.value?.toLowerCase() || '')
            );
        },
        handleChange(event) {
            const raw = event.target.value;
            const cleaned = raw.replace(/[^0-9]/g, '');
            const formatted = this.selectedCountry
                ? `${this.selectedCountry.prefix}-${cleaned}`
                : raw;
            this.vModel = raw;
            this.$emit('change', event, formatted);
        },
        handleBlur(e) {
            const cleaned = this.vModel?.replace(/[^0-9]/g, '');
            const formatted = this.selectedCountry
                ? `${this.selectedCountry.prefix}-${cleaned}`
                : this.vModel;
            this.$emit('change', e, formatted);
            this.$emit('blur', e);
        },
        handleFocus(e) {
            this.$emit('focus', e);
        },
    },
};
</script>
