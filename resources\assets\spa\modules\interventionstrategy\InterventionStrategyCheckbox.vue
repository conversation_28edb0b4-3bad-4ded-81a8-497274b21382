<script setup>
import AsyncSelect from '@spa/components/AsyncComponents/Select/AsyncSelect.vue';
import AsyncCheckbox from '@spa/components/AsyncComponents/Checkbox/AsyncCheckbox.vue';
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { computed, onMounted, watch } from 'vue';
import { useInterventionStrategyStore } from '@spa/stores/modules/interventionstrategy/useInterventionStrategyStore.js';
const props = defineProps({
    modelValue: [String, Number, Array, Object],
    label: String,
    className: String,
    optionValue: {
        type: String,
        default: 'id',
    },
    optionLabel: {
        type: String,
        default: 'strategy',
    },
    disabled: Boolean,
    clearable: {
        type: Boolean,
        default: true,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    readonly: Boolean,
    useChips: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: 'Select Subjects',
    },
    filters: {
        type: Object,
        default: () => ({}),
    },
    sortBy: {
        type: String,
        default: 'strategy',
    },
});
const emit = defineEmits(['update:modelValue']);
const vModel = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});
const store = useInterventionStrategyStore();

const initFilters = () => {
    store.filters = props.filters || {};
};

onMounted(() => {
    initFilters();
});

watch(
    () => props.filters,
    (newVal) => {
        console.log('newVal', newVal);
        store.filters = newVal;
        initFilters();
    }
);
</script>
<template>
    <FieldWrapper>
        <Label
            :class="labelClass"
            :editor-id="id"
            :editor-valid="valid"
            :disabled="disabled"
            :optional="optional"
            v-if="label"
        >
            {{ label }}
            <span v-if="indicaterequired" :class="'ml-1 text-red-500'">*</span>
        </Label>
        <div class="k-form-field-wrap">
            <AsyncCheckbox
                :label="label"
                :className="className"
                :optionValue="optionValue"
                :optionLabel="optionLabel"
                :disabled="disabled"
                :store="store"
                v-model="vModel"
                :clearable="clearable"
                :multiple="true"
                :readonly="readonly"
                :useChips="useChips"
                :placeholder="placeholder"
                :filters="filters"
                :sortBy="sortBy"
            />
            <Error v-if="showValidationMessage">
                {{ validationMessage }}
            </Error>
            <Hint v-else>{{ hint }}</Hint>
        </div>
    </FieldWrapper>
</template>
<style scoped></style>
