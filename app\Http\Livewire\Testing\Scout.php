<?php

namespace App\Http\Livewire\Testing;

use App\Model\Task;
use App\Model\v2\CollegeMaterials;
use App\Model\v2\Courses;
use App\Model\v2\CoursesIntakeDate;
use App\Model\v2\Staff;
use App\Model\v2\Student;
use App\Model\v2\Timetable;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;
use Meilisearch\Endpoints\Indexes;

class Scout extends Component
{
    use WithPagination;

    public $form = [
        'keyword' => '',
        'courses' => [],
        'status' => [],
        'origin' => [],
        'batches' => [],
        'teachers' => [],
        'intake_dates' => [],
        'nationality' => [],

    ];

    public $results = [
        'students' => [],
        'tasks' => [],
        'documents' => [],
    ];

    public $filters = [
        'courses' => [],
        'status' => [],
        'origin' => [],
        'batches' => [],
        'teachers' => [],
        'intake_dates' => [],
        'nationality' => [],
    ];

    public function mount()
    {
        $this->initFilters();
    }

    public function getCachedFiltersProperty()
    {
        if ($this->filters['courses'] && count($this->filters['courses'])) {
            return $this->filters;
        }

        $this->initFilters();

        return $this->filters;
    }

    public function initFilters()
    {
        $collegeId = auth()->user()->college_id;
        $this->filters['courses'] = Courses::where(['college_id' => $collegeId])
            ->select(['id as value', DB::raw("CONCAT(course_code,' - ',course_name) as label")])
            ->orderBy('value', 'ASC')
            ->get()
            ->toArray();

        $this->filters['nationality'] = Student::alias('rto_students as rs')
            ->leftjoin('rto_country as country', 'country.id', '=', 'rs.current_country')
            ->where('rs.college_id', $collegeId)
            ->where('country.nationality', '!=', '')
            ->select(['country.id as value', 'country.nationality as label'])
            ->orderBy('country.nationality', 'ASC')
            ->groupBy('country.id')
            ->get()
            ->toArray();

        $this->filters['status'] = collect(config('constants.arrCourseStatus'))->map(function ($item) {
            return ['value' => $item, 'label' => $item];
        })->toArray();

        $this->filters['batches'] = Timetable::where(['college_id' => $collegeId])->groupBy('batch')->select(['batch as label', 'batch as value'])->orderBy('value', 'ASC')->get()->toArray();
        $this->filters['teachers'] = Staff::where(['college_id' => $collegeId, 'position' => Staff::POSITION_TEACHER])->select(['id as value', DB::raw("CONCAT(first_name,' ',last_name) as label")])->orderBy('label', 'ASC')
            ->get()
            ->toArray();

        $this->filters['intake_dates'] = CoursesIntakeDate::where(['college_id' => $collegeId])->select(['intake_year as value', 'intake_year as text'])->groupBy('intake_year')->orderBy('intake_year', 'DESC')->get()->toArray();
    }

    public function updatedForm($val)
    {
        $this->search($val);
    }

    public function search($keyword)
    {
        $scoutBuilder = Student::search($this->form['keyword'], function (Indexes $searchEngine, string $query, array $options) {
            $options['matchingStrategy'] = 'all';

            // dd($options);
            /* "filter" => "courses.course.course_name="Certificate IV in Accounting and Bookkeeping""
  "matchingStrategy" => "all" */
            return $searchEngine->search($query, $options);
        });

        if (count($this->form['courses'])) {
            $scoutBuilder->whereIn('courses.course.id', $this->form['courses']);
        }
        if (count($this->form['status'])) {
            $scoutBuilder->whereIn('courses.status', $this->form['status']);
        }
        if (count($this->form['nationality'])) {
            $scoutBuilder->whereIn('nationality', $this->form['nationality']);
        }
        if (count($this->form['teachers'])) {
            $scoutBuilder->whereIn('teacher_id', $this->form['teachers']);
        }
        if (count($this->form['intake_dates'])) {
            $scoutBuilder->whereIn('courses.intake_year', $this->form['intake_dates']);
        }
        if (count($this->form['batches'])) {
            $scoutBuilder->whereIn('enrollments.batch', $this->form['batches']);
        }
        // ->where('courses.course.course_name', 'Diploma of Accounting')
        // ->where('courses.course.course_name', 'Certificate IV in Accounting and Bookkeeping')

        $this->results['students'] = $scoutBuilder->get();

        $this->results['tasks'] = Task::search($this->form['keyword'], function (Indexes $searchEngine, string $query, array $options) {
            $options['matchingStrategy'] = 'all';

            return $searchEngine->search($query, $options);
        })->query(function ($builder) {
            return $builder->forGlobalSearch();
        })->get();

        $this->results['documents'] = CollegeMaterials::search($this->form['keyword'], function (Indexes $searchEngine, string $query, array $options) {
            $options['matchingStrategy'] = 'all';

            return $searchEngine->search($query, $options);
        })->query(function ($builder) {
            return $builder->forGlobalSearch();
        })->get();
        // dd($this->results['tasks']);
    }

    public function render()
    {
        // dd();
        return view('livewire.testing.scout.index');
    }
}
