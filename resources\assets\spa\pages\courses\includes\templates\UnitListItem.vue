<template>
    <div class="grid grid-cols-12 gap-4">
        <div
            :class="showMoodleSync ? 'col-span-6' : 'col-span-8'"
            class="flex flex-wrap items-start gap-2 md:flex-nowrap"
        >
            <div class="flex items-center">
                <icon :name="'draghandle'" className="mt-1 min-w-5" v-if="display != 'basic'" />
                <UnitTypeBadge :type="type" :item="`${isHigherEd ? 'Subject' : 'Unit'}`" />
            </div>
            <div class="relative flex flex-wrap gap-2 md:flex-nowrap">
                <div
                    v-if="unit.duplicate_unit"
                    class="absolute -left-4 -top-1"
                    title="Duplicate Record"
                >
                    <icon :name="'error'" :fill="'#ffa25e'" :width="16" :height="16" />
                </div>
                <div class="mt-1 text-xs text-gray-500" :title="unit.Code">
                    {{ getUnitCode }}
                </div>
                <div class="line-clamp-2 text-gray-700" :title="unit.Title">
                    {{ getUnitName }}
                </div>
            </div>
        </div>
        <div class="col-span-2 flex justify-start space-x-2">
            <icon :name="'time'" />
            <div v-if="unit.unit_details.nominal_hours">
                {{ unit.unit_details.nominal_hours }} Hrs
            </div>
            <div v-else>N/A</div>
        </div>
        <!-- Moodle Status Column -->
        <div v-if="showMoodleSync" class="col-span-1 flex items-center">
            <div class="flex items-center space-x-2">
                <div
                    class="flex items-center truncate rounded-lg px-2 py-0.5 text-xs font-medium leading-5"
                    :class="[
                        getMoodleStatusClasses,
                        getMoodleStatusText === 'Sync Fail' ? 'cursor-pointer' : '',
                    ]"
                    :title="getMoodleStatusText === 'Sync Fail' ? getMoodleFailureTooltip : ''"
                >
                    {{ getMoodleStatusText }}
                </div>
            </div>
        </div>
        <div
            v-if="showMoodleSync"
            class="col-span-1 flex items-center truncate text-sm text-gray-600"
        >
            {{ getMoodleSyncTime }}
        </div>
        <div
            class="col-span-2 flex flex-wrap justify-center gap-2 md:flex-nowrap"
            v-if="display != 'basic'"
        >
            <div class="flex min-w-[50px] justify-end gap-2">
                <div
                    :class="getSyncIconClasses"
                    @click="syncToMoodle(unit)"
                    :title="getSyncIconTitle"
                    v-if="showMoodleSync"
                >
                    <icon :name="'sync'" :fill="'currentColor'" />
                </div>
                <div class="cursor-pointer text-gray-400" @click="editUnit(unit)" title="Edit Unit">
                    <icon :name="'pencil'" :fill="'currentColor'" />
                </div>
                <div class="cursor-pointer" @click="deleteUnit(unit)" title="Remove Unit">
                    <icon :name="'cross'" />
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import UnitTypeBadge from '@spa/pages/courses/commons/UnitTypeBadge.vue';
import ActionMenu from '@spa/components/KendoGrid/ActionMenu.vue';
import { mapState } from 'pinia';
import { useCoursesStore } from '@spa/stores/modules/courses';

export default {
    emits: ['editunit', 'deleteunit', 'assign', 'syncunit'],
    props: {
        unit: { type: Object, default: {} },
        type: { type: String, default: '' },
        display: { type: String, default: 'full' },
        showMoodleSync: { type: Boolean, default: false },
    },
    data: function () {
        return {};
    },
    components: {
        UnitTypeBadge,
        'dropdown-menu': ActionMenu,
    },
    computed: {
        ...mapState(useCoursesStore, ['course', 'allTemplates']),
        getUnitName() {
            return this.unit.unit_details ? this.unit.unit_details.unit_name : this.unit.Title;
        },
        getUnitCode() {
            return this.unit.unit_details ? this.unit.unit_details.unit_code : this.unit.Code;
        },
        isHigherEd() {
            return this.isCourseHigherEd(this.course.course_type_id || null);
        },
        getMoodleStatusText() {
            const moodleData = this.unit.moodleData;
            if (!moodleData || moodleData === false) {
                return 'Not Sync';
            }

            return moodleData.unit_moodle_sync_status;
        },
        getMoodleStatusClasses() {
            const moodleData = this.unit.moodleData;
            if (!moodleData || moodleData === false) {
                return 'bg-gray-200 text-gray-600';
            }

            const syncStatus = moodleData.unit_moodle_sync_status;
            if (syncStatus === 'Synced') {
                return 'bg-green-100 text-green-600';
            } else if (syncStatus === 'Sync Fail') {
                return 'bg-red-100 text-red-600';
            }

            return 'bg-gray-200 text-gray-600';
        },
        getMoodleSyncTime() {
            const moodleData = this.unit.moodleData;
            if (!moodleData || moodleData === false) {
                return '--';
            }

            return moodleData.unit_moodle_synced_at || '--';
        },
        getMoodleFailureTooltip() {
            const moodleData = this.unit.moodleData;
            if (!moodleData || moodleData === false) {
                return '';
            }

            const syncStatus = moodleData.unit_moodle_sync_status;

            if (syncStatus === 'Sync Fail') {
                const failureReason =
                    moodleData.unit_moodle_failed_message || 'Unknown error occurred during sync';
                return failureReason;
            }

            return '';
        },
        getSyncIconClasses() {
            const moodleData = this.unit.moodleData;
            const isAlreadySynced =
                moodleData &&
                moodleData.unit_moodle_sync_status === 'Synced' &&
                !moodleData.unit_moodle_failed_at;

            if (isAlreadySynced) {
                return 'text-gray-300 cursor-not-allowed opacity-50';
            }

            return 'cursor-pointer text-gray-400 hover:text-blue-500';
        },
        getSyncIconTitle() {
            const moodleData = this.unit.moodleData;
            const isAlreadySynced =
                moodleData &&
                moodleData.unit_moodle_sync_status === 'Synced' &&
                !moodleData.unit_moodle_failed_at;

            if (isAlreadySynced) {
                return 'Unit already synced';
            }

            const isFailedItem = moodleData && moodleData.unit_moodle_sync_status === 'Sync Fail';
            return isFailedItem ? 'Re-sync to Moodle' : 'Sync to Moodle';
        },
    },
    methods: {
        editUnit(unit) {
            this.$emit('editunit', unit);
        },
        deleteUnit(unit) {
            this.$emit('deleteunit', unit);
        },
        syncToMoodle(unit) {
            this.$emit('syncunit', unit);
        },
    },
};
</script>
