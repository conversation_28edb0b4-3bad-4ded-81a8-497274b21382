<?php

namespace Notifications\Types\Users;

use App\Users;
use Illuminate\Notifications\Messages\MailMessage;
use Notifications\BaseNotification;
use Notifications\Contracts\IsInAppNotification;
use Notifications\Types\DTOs\InAppEntity;
use Notifications\Types\DTOs\InAppPayload;

class ImpersonatingNotification extends BaseNotification implements IsInAppNotification
{
    public function __construct(
        public Users $admin
    ) {}

    // public function via($notifiable): array
    // {
    //     return ['database',];
    // }

    public function mailMessage(MailMessage $message, InAppPayload $payload, object $notifiable): ?MailMessage
    {
        return $message
            ->subject(config('app.name').' Impersonation Alert')
            ->greeting("Hello {$notifiable->name},")
            ->line("Your account is being impersonated by: {$this->admin->name}")
            ->line('Contact our support team if you notice any suspicious activity or require assistance.');
    }

    public function inAppPayload(): ?InAppPayload
    {
        return InAppPayload::LazyFromArray([
            'message' => ':admin has impersonated you',
            'entities' => [
                'admin' => InAppEntity::FromUser(
                    $this->admin,
                    null,
                    '#'
                ),

            ],
        ]);
    }
}
