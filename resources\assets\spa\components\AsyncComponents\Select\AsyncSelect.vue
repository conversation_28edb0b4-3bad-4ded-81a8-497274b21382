<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { DropDownList, MultiSelect } from '@progress/kendo-vue-dropdowns';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { FieldWrapper } from '@progress/kendo-vue-form';
import Button from '@spa/components/Buttons/Button.vue';
import { debounce } from 'lodash';

const popupOptions = {
    animate: false,
    popupClass: 'tw-popup',
};

const props = defineProps({
    required: Boolean,
    indicaterequired: Boolean,
    validationMessage: {
        type: String,
        default: '',
    },
    valid: {
        type: Boolean,
        default: true,
    },
    label: String,
    labelClass: String,
    editorId: String,
    hint: String,
    optional: Boolean,
    className: String,
    optionValue: String,
    optionLabel: String,
    disabled: Boolean,
    store: Object,
    modelValue: [String, Number, Array, Object],
    clearable: Boolean,
    multiple: {
        type: Boolean,
        default: true,
    },
    readonly: Boolean,
    placeholder: String,
    hasCreateAction: Boolean,
    filters: {
        type: Object,
        default: () => ({}),
    },
    initFormData: {
        type: Object,
        default: () => ({}),
    },
    forceReload: Boolean,
    style: {
        type: Object,
        default: () => ({}),
    },
    valuePrimitive: {
        type: Boolean,
        default: true,
    },
    fetchByIds: Function,
    sortBy: {
        type: String,
        default: 'id',
    },
    // Virtual scrolling props
    enableVirtual: Boolean,
    virtualPageSize: {
        type: Number,
        default: 20,
    },
    popupHeight: {
        type: String,
        default: '250px',
    },
});

const emit = defineEmits(['update:modelValue']);

// Core state
const allOptions = ref([]);
const loading = ref(false);
const searchValue = ref('');
const dropDownRef = ref(null);
const initialLoadComplete = ref(false);
const isLoadingMore = ref(false);

// Virtual scrolling state
const virtualSkip = ref(0);
const subsetData = ref([]);
const lastLoadedPage = ref(1);
const allDataLoaded = ref(false);

// Computed properties
const filteredOptions = computed(() => {
    if (props.enableVirtual) return subsetData.value;
    if (!searchValue.value) return allOptions.value;

    const needle = searchValue.value.toLowerCase();
    return allOptions.value.filter((item) =>
        item[props.optionLabel]?.toLowerCase().includes(needle)
    );
});

const defaultItem = computed(() => ({
    [props.optionLabel]: props.placeholder,
    [props.optionValue]: null,
}));

const virtualSettings = computed(() => {
    if (!props.enableVirtual) return undefined;

    return {
        total: props.store?.serverPagination?.rowsNumber || allOptions.value.length,
        pageSize: props.virtualPageSize,
        skip: virtualSkip.value,
    };
});

const popupSettings = computed(() => ({
    animate: false,
    popupClass: 'tw-popup',
    height: props.popupHeight,
}));

const computedValue = computed({
    get() {
        if (!props.modelValue) return null;

        if (props.multiple && Array.isArray(props.modelValue)) {
            const selectedItems = allOptions.value.filter((item) =>
                props.modelValue.includes(item[props.optionValue])
            );
            return selectedItems.length ? selectedItems : null;
        }

        if (props.valuePrimitive) {
            return (
                allOptions.value.find((item) => item[props.optionValue] == props.modelValue) || null
            );
        }

        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val ?? null);
    },
});

// Helper functions
const getMissingSelectedIds = () => {
    if (!props.modelValue) return [];

    const currentIds = new Set(allOptions.value.map((item) => item[props.optionValue]));

    if (props.multiple && Array.isArray(props.modelValue)) {
        return props.modelValue.filter((id) => !currentIds.has(id));
    }
    return currentIds.has(props.modelValue) ? [] : [props.modelValue];
};

const fetchAndAppendMissingItems = async (missingIds) => {
    if (!missingIds?.length) return;

    try {
        loading.value = true;
        let fetchedItems = [];

        if (props.fetchByIds) {
            fetchedItems = await props.fetchByIds(missingIds);
        } else if (props.store) {
            props.store.filters = { ...props.store.filters, ...props.filters, ids: missingIds };
            fetchedItems = props.store.all || [];
        }

        if (fetchedItems.length) {
            const existingIds = new Set(allOptions.value.map((item) => item[props.optionValue]));
            const newItems = fetchedItems.filter(
                (item) => !existingIds.has(item[props.optionValue])
            );

            if (newItems.length) {
                allOptions.value = [...allOptions.value, ...newItems];
                if (props.enableVirtual) updateSubsetData();
            }
        }
    } catch (error) {
        console.error('Error fetching selected items:', error);
    } finally {
        loading.value = false;
    }
};

const updateSubsetData = () => {
    if (!props.enableVirtual) return;

    const start = virtualSkip.value;
    const end = start + props.virtualPageSize;
    subsetData.value = allOptions.value.slice(start, end);
};

const mergeWithSelectedItems = (newData) => {
    if (!newData) return;

    const selectedIds =
        props.multiple && Array.isArray(props.modelValue)
            ? new Set(props.modelValue)
            : new Set([props.modelValue]);

    const selectedItems = allOptions.value.filter((item) =>
        selectedIds.has(item[props.optionValue])
    );

    const newResultIds = new Set(newData.map((item) => item[props.optionValue]));
    const uniqueSelectedItems = selectedItems.filter(
        (item) => !newResultIds.has(item[props.optionValue])
    );

    allOptions.value = [...newData, ...uniqueSelectedItems];
};

// Event handlers
const handlePageChange = async (event) => {
    if (!props.enableVirtual || !props.store) return;

    const { skip: newSkip, take } = event.page;
    virtualSkip.value = newSkip;

    const requestedEnd = newSkip + take;
    const currentDataEnd = allOptions.value.length;

    // Load more data from server if needed
    if (requestedEnd > currentDataEnd) {
        isLoadingMore.value = true; // Set flag before loading
        loading.value = true;
        try {
            const nextPage =
                Math.floor(currentDataEnd / props.store.serverPagination.rowsPerPage) + 1;
            props.store.serverPagination.page = nextPage;
            await props.store.fetchPaged();

            // Note: The watcher will handle appending new items when isLoadingMore is true
        } catch (error) {
            console.error('Error loading more data:', error);
            isLoadingMore.value = false; // Reset flag on error
        } finally {
            loading.value = false;
        }
    }

    updateSubsetData();
};

const onFilterChange = async (event) => {
    if (!event?.filter || !props.store) return;

    const value = event.filter.value || '';
    searchValue.value = value;

    loading.value = true;
    try {
        const selectedIds =
            props.multiple && Array.isArray(props.modelValue)
                ? new Set(props.modelValue)
                : new Set([props.modelValue]);

        const selectedItems = allOptions.value.filter((item) =>
            selectedIds.has(item[props.optionValue])
        );

        props.store.filters = {
            ...props.filters,
            query: value,
        };
        props.store.serverPagination.page = 1;

        // Reset pagination tracking
        lastLoadedPage.value = 1;
        allDataLoaded.value = false;

        //props.store.filters = newfilters;

        if (props.store?.all) {
            const searchResultIds = new Set(props.store.all.map((item) => item[props.optionValue]));
            const uniqueSelectedItems = selectedItems.filter(
                (item) => !searchResultIds.has(item[props.optionValue])
            );

            allOptions.value = [...props.store.all, ...uniqueSelectedItems];

            if (props.enableVirtual) {
                virtualSkip.value = 0;
                updateSubsetData();
            }
        }
    } catch (error) {
        console.error('Error fetching options:', error);
    } finally {
        loading.value = false;
    }
};

const handleFilterChange = debounce(onFilterChange, 400);

const handleFooterClick = () => {
    dropDownRef.value?.handleBlur();
    props.store.formData = { ...props.initFormData };
    props.store.formDialog = true;
};

// Lifecycle
onMounted(async () => {
    if (props.sortBy && props.sortBy !== 'id') {
        props.store.serverPagination.sortBy = props.sortBy;
        props.store.serverPagination.descending = false;
    }

    try {
        loading.value = true;

        const missingIds = getMissingSelectedIds();
        if (missingIds.length) {
            await fetchAndAppendMissingItems(missingIds);
        } else if (props.filters.length) {
            props.store.filters = { ...props.store.filters, ...props.filters };
        } else if (props.forceReload || !props.store?.all?.length) {
            props.store.serverPagination.page = 1;
            await props.store?.fetchPaged();
        }

        if (props.store?.all) {
            allOptions.value = [...props.store.all];
        }

        if (props.enableVirtual) {
            lastLoadedPage.value = 1; // We loaded page 1
            updateSubsetData();
        }

        initialLoadComplete.value = true;
    } catch (error) {
        console.error('Error initializing AsyncSelect:', error);
    } finally {
        loading.value = false;
    }
});

const resetDropdown = () => {
    computedValue.value = null;
    dropdownKey.value++;
};

// Watchers
watch(
    () => props.store?.all,
    (newVal) => {
        if (!newVal) return;

        // If loading more for virtual scroll, always append
        if (isLoadingMore.value) {
            const existingIds = new Set(allOptions.value.map((item) => item[props.optionValue]));
            const newItems = newVal.filter((item) => !existingIds.has(item[props.optionValue]));

            if (newItems.length) {
                allOptions.value = [...allOptions.value, ...newItems];
            }
            isLoadingMore.value = false;
        }
        // If searching/filtering, replace with merged results
        else if (searchValue.value) {
            mergeWithSelectedItems(newVal);
        }
        // Normal pagination append
        else {
            const existingIds = new Set(allOptions.value.map((item) => item[props.optionValue]));
            const newItems = newVal.filter((item) => !existingIds.has(item[props.optionValue]));

            if (newItems.length) {
                allOptions.value = [...allOptions.value, ...newItems];
            }
        }

        if (props.enableVirtual) {
            updateSubsetData();
        }
    },
    { deep: true }
);

watch(
    () => props.filters,
    async (newVal) => {
        if (newVal) {
            props.store.filters = { ...props.store.filters, ...newVal };
            props.store.serverPagination.page = 1;
            lastLoadedPage.value = 1;
            allDataLoaded.value = false;

            if (props.enableVirtual) {
                virtualSkip.value = 0;
                updateSubsetData();
            }
        }
    }
);

watch(
    () => props.modelValue,
    async (newVal) => {
        if (!newVal || !initialLoadComplete.value) return;

        const missingIds = getMissingSelectedIds();
        if (missingIds.length) {
            await fetchAndAppendMissingItems(missingIds);
        }
    },
    { deep: true }
);
</script>

<template>
    <FieldWrapper class="min-w-0 flex-1">
        <Label
            v-if="label"
            :class="labelClass"
            :editor-id="editorId"
            :editor-valid="valid"
            :disabled="disabled"
            :optional="optional"
        >
            {{ label }}
            <span v-if="required || indicaterequired" class="ml-1 text-red-500">*</span>
        </Label>
        <div class="k-form-field-wrap flex-1">
            <div :class="`async-select ${className}`">
                <MultiSelect
                    v-if="multiple"
                    :class="'async' + label"
                    :data-items="filteredOptions"
                    :text-field="optionLabel"
                    :value-field="optionValue"
                    :data-item-key="optionValue"
                    :filterable="true"
                    :clear-button="clearable"
                    :disabled="disabled"
                    :readonly="readonly"
                    :loading="loading"
                    :value-primitive="valuePrimitive"
                    :auto-close="false"
                    :popup-settings="popupSettings"
                    :default-item="defaultItem"
                    :placeholder="placeholder"
                    @filterchange="handleFilterChange"
                    v-model="computedValue"
                    :virtual="virtualSettings"
                    :footer="hasCreateAction ? 'myFooter' : ''"
                    :touched="false"
                    @update:multiSelectRef="(val) => (dropDownRef = val)"
                    @pagechange="handlePageChange"
                    :style="{
                        width: '100%',
                        minWidth: '150px',
                        maxWidth: '100%',
                        ...style,
                    }"
                >
                    <template v-if="hasCreateAction" #myFooter>
                        <div class="p-[1rem]">
                            <Button variant="primary" size="xs" @click="handleFooterClick">
                                <span>Add New</span>
                                <icon name="add" width="16" height="16" fill="#fff" />
                            </Button>
                        </div>
                    </template>
                </MultiSelect>
                <DropDownList
                    v-else
                    ref="dropDownRef"
                    :class="'async' + label"
                    :data-items="filteredOptions"
                    :text-field="optionLabel"
                    :value-field="optionValue"
                    :data-item-key="optionValue"
                    :filterable="true"
                    :clear-button="clearable"
                    :disabled="disabled"
                    :readonly="readonly"
                    :loading="loading"
                    :value-primitive="valuePrimitive"
                    :popup-settings="popupSettings"
                    :default-item="defaultItem"
                    :virtual="virtualSettings"
                    @filterchange="handleFilterChange"
                    :footer="hasCreateAction ? 'myFooter' : ''"
                    v-model="computedValue"
                    @pagechange="handlePageChange"
                    :style="{
                        width: '100%',
                        minWidth: '150px',
                        maxWidth: '100%',
                        ...style,
                    }"
                >
                    <template v-if="hasCreateAction" #myFooter>
                        <div class="p-[1rem]">
                            <Button variant="primary" size="xs" @click="handleFooterClick">
                                <span>Add New</span>
                                <icon name="add" width="16" height="16" fill="#fff" />
                            </Button>
                        </div>
                    </template>
                </DropDownList>
            </div>
            <Error v-if="!valid">
                {{ validationMessage }}
            </Error>
            <Hint v-else-if="hint">{{ hint }}</Hint>
        </div>
    </FieldWrapper>
    <slot name="createDialog"></slot>
</template>

<style lang="scss">
.async-select {
    .k-multiselect {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
}
</style>
