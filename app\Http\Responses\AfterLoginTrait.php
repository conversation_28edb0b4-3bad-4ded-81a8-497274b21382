<?php

namespace App\Http\Responses;

use App\Model\GroupPermission;
use App\Model\PermissionGroups;
use App\Model\UserActivityLog;
use App\Model\UserGroupPermission;
use App\Model\UserRoleType;
use App\Model\v2\AgentStaff;
use App\Model\v2\Colleges;
use App\Roles;
use App\Users;
use Domains\Customers\Settings\Models\SettingTracker;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Laravel\Fortify\Fortify;

trait AfterLoginTrait
{
    /**
     * @return mixed
     */
    public function logAndRedirect($request, $track = true)
    {

        if (! $request->user()->isActive()) {
            Auth::logout();
            throw ValidationException::withMessages([Fortify::username() => ['Your account has been suspended. Please contact your galaxy supervisor for further action.']]);
        }

        $arrDataActivity = [];
        $arrDataActivity['ip_address'] = $request->ip();
        $arrDataActivity['category'] = '4';
        $arrDataActivity['event_name'] = 'User Authentication';
        $arrDataActivity['event_detail'] = 'Login Success';

        $activity = 'login';
        $objUsers = new Users;
        $objUserActivity = new UserActivityLog;

        $user = $request->user();
        $arrDataActivity['user_id'] = $user->id;
        $arrDataActivity['role_id'] = $user->role_id;
        // TODO: For Agent Staff Standard Role
        /* $userRole = AgentStaff::where('user_id', $user->id)->value('user_role');
        $request->session()->put('agentStaffRole', $userRole); */
        $request->session()->put('currentRole', $user->role_id);
        if ($newRole = $user->roles()->orderByPivot('is_default', 'DESC')->first()) {
            $request->session()->put(\Support\Auth\UserGroup::SESSION_KEY, $newRole->id);
        }
        // $objUsersRole = new UserRoleType;
        // $userRoleArr = $objUsersRole->getUserRoleTypeList($user->id);
        // $request->session()->put('userRole', $userRoleArr);
        $route = 'user_dashboard';

        if ($user->isTeacher()) {
            $request->session()->put('teacher.first_time_reset_pwd', $user->first_time_reset_pwd);
            $route = (route('teacher_dashboard'));
        } elseif ($user->isStudent()) {
            $request->session()->put('student.first_time_reset_pwd', $user->first_time_reset_pwd);
            if (galaxy_feature('student_portal_beta')) {
                $route = (route('spa.studentportal.dashboard'));
            } else {
                $route = (route('student_dashboard'));
            }
        } elseif ($user->isAgent()) {
            $request->session()->put('agent.first_time_reset_pwd', $user->first_time_reset_pwd);
            if (galaxy_feature('agent_portal')) {
                $route = (route('spa.agent.studentList'));
            } else {
                $route = (route('student_apply'));
            }
        } elseif ($user->isAgentStaff()) {

            if (galaxy_feature('agent_staff_portal')) {
                $route = (route('spa.agentstaff.incompleted-application'));
            } else {
                $route = (route('agentstaff-student-online-application'));
            }
            $request->session()->put('agent.first_time_reset_pwd', $user->first_time_reset_pwd);
        } else {
            $role = $request->session()->get('currentRole');
            $checkCollegeData = Colleges::where('id', Auth::user()->college_id)->first();
            $request->session()->put('onboardSetup', $checkCollegeData->onboard_setup);
            if ($user->isSadmin()) {
                $collection = SettingTracker::all();
                $summary = $collection->summary();

                if (galaxy_feature('sso') && $sessionValue = session()->get(\SSO\DTO\KeycloakUserInfo::SSO_CONNECTED_KEY)) {
                    session()->forget(\SSO\DTO\KeycloakUserInfo::SSO_CONNECTED_KEY);

                    return redirect()->route('galaxy.sso.setup');
                }

                if ($summary && $summary->total >= $summary->completed + 10) {
                    $route = (route('user-onboard'));
                } else {
                    $route = (route('user_dashboard'));
                }
            } else {
                $route = (route('user_dashboard'));
            }
        }
        // $objRtoUsers = $objUsers->editLastActivityDate($user, $activity);

        if ($track) {
            $objUserActivity->logActivity($arrDataActivity);
        }

        // $this->updateToken($request);
        // $this->setPermission($request);

        activity('auth')
            // ->causedBy($user)
            // ->performedOn($user)
            ->log('User Logged In');

        return redirect()->intended($route);
    }

    public function updateToken($request)
    {

        if ($request->user()->api_token) {
            $objUsers = Users::find($request->user()->id);
            $objUsers->api_token = $request->user()->createToken('auth_token')->plainTextToken;
            $objUsers->save();
        }
    }

    public function setPermission($request)
    {
        $objPermission = new PermissionGroups;
        $arrPermissionList = $objPermission->getPermissionNameV2();

        if ($request->session()->get('currentRole') != Roles::TYPE_SADMIN) {
            $objUserGroupPermission = new UserGroupPermission;
            $arrGroupPermission = $objUserGroupPermission->getUserGroupPermissionV2(Auth::user()->college_id, Auth::user()->id);
        } else {
            $objGroupPermission = new GroupPermission;
            $arrGroupPermission = $objGroupPermission->getGroupPermission();
        }

        for ($i = 0; $i < count($arrGroupPermission); $i++) {
            $arrPermissionList[$arrGroupPermission[$i]['permission_id']] = 'yes';
        }

        $request->session()->put('arrPermissionList', $arrPermissionList);
        $request->session()->get('arrPermissionList', 'default');
    }
}
