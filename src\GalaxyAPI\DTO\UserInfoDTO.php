<?php

namespace GalaxyAPI\DTO;

use App\Model\Teacher;
use App\Model\v2\Agent;
use App\Model\v2\AgentStaff;
use App\Model\v2\Employer;
use App\Model\v2\SetupProvider;
use App\Model\v2\Staff;
use App\Model\v2\Student;
use App\Model\v2\VpmsPlacementProvider;
use App\Users;
use GalaxyAPI\Enums\ATSICodeEnum;
use GalaxyAPI\Enums\FunctionCodeEnum;
use GalaxyAPI\Enums\HighestQualificationCodeEnum;
use GalaxyAPI\Enums\HighestQualificationPlaceCodeEnum;
use GalaxyAPI\Enums\OrganisationalUnitCodeEnum;
use GalaxyAPI\Enums\StaffWorkLevelCodeEnum;
use GalaxyAPI\Enums\UserStatusEnum;
use GalaxyAPI\Enums\WorkContractCodeEnum;
use GalaxyAPI\Enums\WorkSectorCodeEnum;
use GalaxyAPI\Interfaces\BaseCrudMapperInterface;
use Illuminate\Support\Carbon;
use InvalidArgumentException;
use Support\Traits\ArrayToProps;

class UserInfoDTO implements BaseCrudMapperInterface
{
    use ArrayToProps;

    public function __construct(
        public ?int $id = null,
        public string $secure_id = '',
        public ?int $user_id = null,
        public ?string $user_type = null,
        public ?string $secure_user_id = null,
        public ?int $prev_id = null,
        public ?int $next_id = null,
        public ?string $secure_prev_id = null,
        public ?string $secure_next_id = null,
        public ?string $name_title = null,
        public ?string $first_name = null,
        public ?string $middle_name = null,
        public ?string $last_name = null,
        public string $name = '',
        public ?string $initials = null,
        public ?string $email = '',
        public ?string $email2 = null,
        public ?string $phone = null,
        public ?string $phone2 = null,
        public ?string $mobile = null,
        public ?string $mobile2 = null,
        public ?string $fax = null,
        public ?string $website = null,
        // Address
        public ?string $address1 = null,
        public ?string $address2 = null,
        public ?array $address_postal = null,
        public ?int $status_id = null,
        public ?string $status = null,

        // Users
        public ?string $username = null,
        public ?string $last_login = null,
        public ?array $roles = null,
        public ?string $user_status = null,
        public ?int $user_status_id = null,
        public ?string $last_password_updated_date = null,
        public bool $two_factor_enabled = false,
        public ?bool $has_portal_access = null,
        public ?bool $completion_status = null,
        public ?string $source_of_entry = null,
        public ?array $user_info = null,
        public ?array $employment_info = null,
        public ?array $qualifications = null,
        public ?array $personal_development = null,
        public ?array $bank_details = null,
        public ?array $tcsi_report = null,
        public ?string $gender = null,
        public ?string $dob = null,
        public ?string $role_id = null,
        public ?int $country = null,
        public ?string $timezone = null,
        public ?string $profile_picture = null,
        public ?string $profile_picture_url = null,
        public ?string $profile_picture_thumbnail_url = null,
        public $created_by = null,
        public ?string $created_by_name = null,
        public ?Carbon $created_at = null,
        public $updated_by = null,
        public ?Carbon $updated_at = null,
        public ?string $source_model_class = null,
        public ?Carbon $deleted_at = null,

        // Agents
        public ?string $agent_code = null,
        public ?string $super_agent_id = null,
        public ?string $super_agent = null,
        public ?string $agent_organization = null,
        public ?string $agent_type = null,
        public ?string $industry_id = null,
        public ?string $industry_name = null,
        public ?string $account_manager_id = null,
        public ?string $account_manager = null,
        public ?int $total_employes = null,
        public ?string $notes = null,
        public ?array $referees = null,
        public ?array $target_recruitment_countries = null,

        public ?int $staff_id = null,
        public ?string $secure_staff_id = null,

        // Service Provider
        public ?string $internal_note = null,
        public ?string $other_details = null,

        // Employer
        public ?string $trading_name = null,
        public ?string $abn = null,

        public ?array $upload_docs = null,

        public ?array $form_progress = null,

        // Placement Provider
        public ?string $category = null,

        public ?string $code = null

    ) {}

    public static function setArray(array $data): self
    {
        $data['secure_id'] = encryptIt($data['id'] ?? null);
        $data['secure_user_id'] = encryptIt($data['user_id'] ?? null);
        $data['initials'] = getNameInitials($data['name']);
        $data['has_portal_access'] = $data['user_status_id'] === UserStatusEnum::ACTIVE->value;

        $data['secure_prev_id'] = isset($data['prev_id']) ? encryptIt($data['prev_id']) : null;
        $data['secure_next_id'] = isset($data['next_id']) ? encryptIt($data['next_id']) : null;

        $dto = self::FromArray($data);

        if (! isset($data['prev_id']) && ! isset($data['next_id']) && isset($data['id'])) {
            $dto->fetchNavigationIds($data['id'], $data['source_model_class']);
        }

        return $dto;
    }

    //    public static function fromUser(Users $user): self
    //    {
    //        $staffData = [];
    //        if ($user->staffAndTeacher) {
    //            $staff = $user->staffAndTeacher;
    //            $staffData = self::getStaffAndTeacherData($staff);
    //        }
    //
    //        return self::setArray(array_merge([
    //            'source_model_class' => Users::class,
    //            'user_type' => 'staff',
    //            'id' => $user->id,
    //            'user_id' => $user->id,
    //            'staff_id' => $user->staffAndTeacher?->id,
    //            'secure_staff_id' => encryptIt($user->staffAndTeacher?->id),
    //            'first_name' => $user->staffAndTeacher?->first_name,
    //            'last_name' => $user->staffAndTeacher?->last_name,
    //            'name' => $user->name,
    //            'username' => $user->username,
    //            'email' => $user?->email,
    //            'email2' => $user->staffAndTeacher?->work_email,
    //            'phone' => $user->staffAndTeacher?->phone,
    //            'mobile' => $user->staffAndTeacher?->mobile,
    //            'gender' => $user->staffAndTeacher?->gender,
    //            'dob' => $user->staffAndTeacher?->birth_date,
    //            'country' => $user->staffAndTeacher?->country,
    //            'address1' => $user->staffAndTeacher?->address,
    //            'role_id' => $user->role_id,
    //            'roles' => $user?->userRoles()->with('parentRole')->get()?->map(function ($role) {
    //                return [
    //                    'id' => $role?->id,
    //                    'secure_id' => encryptIt($role?->id),
    //                    'parent_role_id' => $role?->parentRole?->id ?? null,
    //                    'name' => $role?->parentRole?->name ?? $role?->name,
    //                ];
    //            })->toArray() ?? [],
    //            'last_login' => $user->last_login,
    //            'user_status' => $user->status?->getFormatedName(),
    //            'user_status_id' => $user->status->value,
    //            'last_password_updated_date' => $user->last_password_updated_date,
    //            'two_factor_enabled' => (bool) $user->two_factor_secret,
    //            'source_of_entry' => $user->staffAndTeacher?->source_of_entry ?? 'Internal',
    //            'created_by' => $user->creator,
    //            'created_at' => $user->created_at,
    //            'updated_by' => $user->updater,
    //            'updated_at' => $user->updated_at,
    //            'created_by_name' => $user->creator?->name,
    //        ], $staffData));
    //    }
    public static function getUserRoleData($role): array
    {
        $parentRole = $role?->parentRole ?? null;
        $roleType = $role->roleType ?? null;
        $permanentRoleTypes = ['TYPE_STUDENT', 'TYPE_AGENT'];

        return [
            'id' => $role?->id,
            'secure_id' => encryptIt($role?->id),
            'parent_role_id' => $parentRole?->id ?? null,
            'parent_role_secure_id' => $parentRole?->id ? encryptIt($parentRole?->id) : null,
            'name' => $parentRole ? $parentRole->name : $role?->name,
            'is_master_admin' => $roleType?->is_super_admin === 1,
            'is_permanent' => $roleType?->is_super_admin === 1 || in_array($roleType?->role_scope, $permanentRoleTypes),
            'is_default' => $role?->pivot?->is_default === 1,
        ];
    }

    public static function fromStaff(Staff $staff): self
    {
        return self::setArray([
            'source_model_class' => Staff::class,
            'id' => $staff->id,
            'secure_id' => encryptIt($staff->id),
            'user_id' => $staff->user_id,
            'user_type' => 'staff',
            'name_title' => $staff->name_title,
            'first_name' => $staff->first_name,
            'last_name' => $staff->last_name,
            'name' => $staff->first_name.' '.$staff->last_name,
            'email' => $staff->user?->email,
            'email2' => $staff->personal_email,
            'phone' => $staff->phone,
            'phone2' => $staff->phone2,
            'mobile' => $staff->mobile,
            'gender' => $staff->gender,
            'dob' => $staff->birth_date,
            'role_id' => optional($staff->user?->roles)->first()?->id,
            'roles' => $staff->user?->userRoles?->map(function ($role) {
                return self::getUserRoleData($role);
            })->toArray() ?? [],
            'username' => $staff->user?->username,
            'last_login' => $staff->user?->last_login,
            'user_status' => $staff->user?->status?->getFormatedName(),
            'user_status_id' => $staff->user?->status->value,
            'last_password_updated_date' => $staff->user?->last_password_updated_date,
            'has_portal_access' => $staff->user?->status->value === UserStatusEnum::ACTIVE->value,
            'two_factor_enabled' => (bool) $staff->user?->two_factor_secret,
            'source_of_entry' => $staff->source_of_entry ?? 'Internal',
            'country' => $staff->country ? (int) $staff->country : null,
            'created_by' => $staff->creator,
            'created_at' => $staff->created_at,
            'updated_by' => $staff->updater,
            'updated_at' => $staff->updated_at,
            'deleted_at' => $staff->deleted_at,
            'created_by_name' => $staff->creator?->name,
            'code' => $staff->staff_number,
            ...self::getStaffAndTeacherData($staff),
        ]);
    }

    public static function fromAgent(Agent $agent): self
    {
        return self::setArray([
            'source_model_class' => Agent::class,
            'id' => $agent->id,
            'user_id' => $agent->user_id,
            'user_type' => 'agent',
            'agent_code' => $agent->agent_code,
            'code' => $agent->agent_code,
            'name' => $agent->agency_name,
            'email' => $agent->user?->email ?? $agent->primary_email,
            'phone' => $agent->telephone,
            'mobile' => $agent->mobile1,
            'mobile2' => $agent->mobile2,
            'address1' => $agent->office_address,
            'last_login' => $agent->user?->last_login,
            'status' => $agent->agentStatus?->status_type,
            'status_id' => $agent->status,
            'user_status' => $agent->user?->status?->getFormatedName(),
            'last_password_updated_date' => $agent->user?->last_password_updated_date,
            'roles' => $agent->user?->userRoles?->map(function ($role) {
                return self::getUserRoleData($role);
            })->toArray() ?? [],
            'two_factor_enabled' => $agent->user?->two_factor_secret ? true : false,
            'created_by' => $agent->creator,
            'created_at' => $agent->created_at,
            'updated_by' => $agent->updater,
            'updated_at' => $agent->updated_at,
            'deleted_at' => $agent->deleted_at,
            'created_by_name' => $agent->creator?->name,
            'username' => $agent->user?->username,
            'completion_status' => $agent->completion_status,
            'source_of_entry' => $agent->source_of_entry ?? 'Internal',
            'user_status_id' => $agent->user?->status->value,
            'role_id' => optional($agent->user?->roles)->first()?->id,
            'user_info' => [
                'agent_code' => $agent->agent_code,
                'name' => $agent->agency_name,
                'is_agency' => $agent->super_agent_id ? 1 : 0,
                'super_agent_id' => $agent->super_agent_id,
                'super_agent' => $agent->parentAgent?->agency_name,
                'total_employes' => $agent->total_employess,
                'status' => $agent->agentStatus?->status_type,
                'status_id' => $agent->status,
                'industry_id' => $agent->industry_id,
                'industry_name' => $agent->industry?->name,
                'account_manager_id' => $agent->account_manager_id,
                'account_manager' => $agent->accountManager?->first_name.' '.$agent->accountManager?->last_name,
                'notes' => $agent->notes,
                'email2' => $agent->alertnet_email,
                'email' => $agent->user?->email ?? $agent->primary_email,
                'phone' => $agent->telephone,
                'mobile' => $agent->mobile1,
                'role_id' => optional($agent->user?->roles)->first()?->id,
                'website' => $agent->website,
                'fax' => $agent->fax,
                'mobile2' => $agent->mobile2,
                'country' => $agent->office_country ? (int) $agent->office_country : null,
                'country_name' => $agent->officeCountry?->name,

                'emergency_contact_name' => $agent->contact_person,
                'emergency_phone_1' => $agent->contact_person_phone_1,
                'emergency_phone_2' => $agent->contact_person_phone_2,
                'emergency_email' => $agent->contact_person_email,
                'emergency_address' => $agent->contact_person_address,
            ],

            // Address
            'address_postal' => [
                'residential_address' => $agent->office_address,
                'residential_country' => $agent->office_country,
                'residential_country_name' => $agent->getCountryName($agent->office_country),
                'residential_state' => $agent->office_state,
                'residential_city' => $agent->office_city,
                'residential_postcode' => $agent->office_postcode,
                'residential_abn' => $agent->office_ABN,
                'is_post_residential_same' => $agent->office_address === $agent->postal_address ? 1 : 0,
                'postal_address' => $agent->postal_address,
                'postal_country' => $agent->postal_country,
                'postal_country_name' => $agent->getCountryName($agent->postal_country),
                'postal_state' => $agent->postal_state,
                'postal_city' => $agent->postal_city,
                'postal_postcode' => $agent->postal_postcode,
                'postal_abn' => $agent->postal_ABN,
            ],

            'target_recruitment_countries' => [
                'target_recruitment_countries' => $agent->getTargetRecruitmentCountries(),
            ],
            'referees' => [
                'referee_details' => $agent->agentReferees?->map(function ($referee) {
                    return [
                        'name' => $referee->client_name,
                        'email' => $referee->email,
                    ];
                })->toArray(),
            ],
            'bank_details' => [
                'bank_name' => $agent->bank_name,
                'account_name' => $agent->bank_account_name,
                'bsb' => $agent->BSB,
                'account_number' => $agent->bank_account_number,
                'swift_code' => $agent->bank_swift_code,
                'bank_country' => $agent->bank_country,
                //                'tax_file_number' => $agent->tax_file_number,
                //                'super_fund_name' => $agent->super_fund_name,
                //                'super_member_number' => $agent->super_member_number,
            ],
            'upload_docs' => $agent->agentImages?->map(function ($document) {
                return [
                    'id' => $document->id,
                    'name' => $document->document_name,
                    'file' => $document->file_path,
                ];
            })->toArray(),
            'form_progress' => $agent->formProgress?->step_progress ?? [],

        ]);
    }

    public static function fromAgentStaff(AgentStaff $agentstaff): self
    {
        return self::setArray([
            'source_model_class' => AgentStaff::class,
            'id' => $agentstaff->id,
            'user_id' => $agentstaff->user_id,
            'user_type' => 'agent',
            'super_agent_id' => $agentstaff->agent?->id,
            'super_agent' => $agentstaff->agent?->agency_name,
            'agent_type' => $agentstaff->user_role,
            'agent_organization' => $agentstaff->subAgentOf?->organization_name,
            'first_name' => $agentstaff->first_name,
            'last_name' => $agentstaff->last_name,
            'name' => $agentstaff->first_name.' '.$agentstaff->last_name,
            'email' => $agentstaff->email,
            'mobile' => $agentstaff->mobile,
            'address1' => $agentstaff->full_address,
            'address2' => $agentstaff->location,
            'address_postal' => [
                'residential_address' => $agentstaff->full_address,
                'residential_city' => $agentstaff->city,
                'residential_state' => $agentstaff->state,
                'residential_postcode' => $agentstaff->postal_postcode,
                'residential_country' => $agentstaff->country,
                'residential_country_name' => $agentstaff->getCountryName($agentstaff->country),
            ],
            'last_login' => $agentstaff->user?->last_login,
            'user_status' => $agentstaff->user?->status?->getFormatedName(),
            'last_password_updated_date' => $agentstaff->user?->last_password_updated_date,
            'roles' => $agentstaff->user?->userRoles?->map(function ($role) {
                return self::getUserRoleData($role);
            })->toArray() ?? [],
            'status' => $agentstaff?->user_status,
            'two_factor_enabled' => $agentstaff->user?->two_factor_secret ? true : false,
            'created_by' => $agentstaff->creator,
            'created_at' => $agentstaff->created_at,
            'updated_by' => $agentstaff->updater,
            'updated_at' => $agentstaff->updated_at,
            'deleted_at' => $agentstaff->deleted_at,
            'created_by_name' => $agentstaff->creator?->name,
            'username' => $agentstaff->user?->username,
            'has_portal_access' => $agentstaff->has_portal_access,
            'completion_status' => $agentstaff->completion_status,
            'source_of_entry' => $agentstaff->source_of_entry ?? 'Internal',
            'user_status_id' => $agentstaff->user?->status->value,
            'role_id' => optional($agentstaff->user?->roles)->first()?->id,
            'user_info' => [
                'first_name' => $agentstaff->first_name,
                'last_name' => $agentstaff->last_name,
                'name' => $agentstaff->first_name.' '.$agentstaff->last_name,
                'email' => $agentstaff->email,
                'mobile' => $agentstaff->mobile,
                'is_agency' => $agentstaff->is_super_agent ? 1 : 0,
                'is_sub_agent' => $agentstaff->is_sub_agent ? 1 : 0,
            ],
            'address_postal' => [
                'residential_address' => $agentstaff->location,
                'residential_country' => $agentstaff->country,
                'residential_country_name' => $agentstaff->getCountryName($agentstaff->country),
                'residential_city' => $agentstaff->city,
                'residential_state' => $agentstaff->state,
                'residential_postcode' => $agentstaff->postal_postcode,
                'residential_abn' => $agentstaff->postal_ABN,
            ],
            'form_progress' => $agentstaff->formProgress?->step_progress ?? [],
        ]);
    }

    public static function fromTeacher(Teacher $teacher): self
    {
        return self::setArray([
            'source_model_class' => Teacher::class,
            'id' => $teacher->id,
            'user_id' => $teacher->user_id,
            'user_type' => 'teacher',
            'name_title' => $teacher->name_title,
            'first_name' => $teacher->first_name,
            'last_name' => $teacher->last_name,
            'name' => $teacher->first_name.' '.$teacher->last_name,
            'email' => $teacher->user?->email ?? $teacher->email,
            'email2' => $teacher->work_email,
            'phone' => $teacher->phone,
            'phone2' => $teacher->phone2,
            'mobile' => $teacher->mobile,
            'gender' => $teacher->gender,
            'dob' => $teacher->birth_date,
            'role_id' => optional($teacher->user?->roles)->first()?->id,
            'roles' => $teacher->user?->userRoles?->map(function ($role) {
                return self::getUserRoleData($role);
            })->toArray() ?? [],
            'username' => $teacher->user?->username,
            'last_login' => $teacher->user?->last_login,
            'user_status' => $teacher->user?->status?->getFormatedName(),
            'user_status_id' => $teacher->user?->status->value,
            'last_password_updated_date' => $teacher->user?->last_password_updated_date,
            'two_factor_enabled' => (bool) $teacher->user?->two_factor_secret,
            'has_portal_access' => $teacher->user?->status->value === UserStatusEnum::ACTIVE->value,
            'source_of_entry' => $teacher->source_of_entry ?? 'Internal',
            'country' => $teacher->country ? (int) $teacher->country : null,
            'created_by' => $teacher->creator,
            'created_at' => $teacher->created_at,
            'updated_by' => $teacher->updater,
            'updated_at' => $teacher->updated_at,
            'deleted_at' => $teacher->deleted_at,
            'created_by_name' => $teacher->creator?->name,
            'code' => $teacher->staff_number,
            ...self::getStaffAndTeacherData($teacher),
        ]);
    }

    public static function fromEmployer(Employer $employer): self
    {
        return self::setArray([
            'source_model_class' => Employer::class,
            'id' => $employer->id,
            'user_id' => $employer->user_id,
            'user_type' => 'employer',
            'trading_name' => $employer->trading_name,
            'name' => $employer->employer_name,
            'email' => $employer->email,
            'phone' => $employer->phone,
            'mobile' => $employer->mobile,
            'address1' => $employer->address,
            'last_login' => $employer->user?->last_login,
            'user_status' => $employer->user?->status?->getFormatedName(),
            'user_status_id' => $employer->user?->status->value,
            'last_password_updated_date' => $employer->user?->last_password_updated_date,
            'roles' => $employer->user?->userRoles?->map(function ($role) {
                return self::getUserRoleData($role);
            })->toArray() ?? [],
            'two_factor_enabled' => $employer->user?->two_factor_secret ? true : false,
            'created_by' => $employer->creator,
            'created_at' => $employer->created_at,
            'updated_by' => $employer->updater,
            'updated_at' => $employer->updated_at,
            'deleted_at' => $employer->deleted_at,
            'created_by_name' => $employer->creator?->name,
            'username' => $employer->user?->username,
            'has_portal_access' => $employer->has_portal_access,
            'completion_status' => $employer->completion_status,
            'source_of_entry' => $employer->source_of_entry ?? 'Internal',
            'role_id' => optional($employer->user?->roles)->first()?->id,
            'user_info' => [
                'name' => $employer->employer_name,
                'trading_name' => $employer->trading_name,
                'email' => $employer->email,
                'phone' => $employer->phone,
                'mobile' => $employer->mobile,
                'address' => $employer->address,
                'role_id' => optional($employer->user?->roles)->first()?->id,
                'industry_id' => $employer->industry_id,
                'industry_name' => $employer->industry?->name,
                'fax' => $employer->fax,

                'emergency_contact_name' => $employer->contact_person,
                'emergency_phone_1' => $employer->contact_person_phone_1,
                'emergency_phone_2' => $employer->contact_person_phone_2,
                'emergency_email' => $employer->contact_person_email,
                'emergency_address' => $employer->contact_person_address,
            ],
            'address_postal' => [
                'residential_address' => $employer->address,
                'residential_country' => $employer->country_id,
                'residential_country_name' => $employer->getCountryName($employer->country_id),
                'residential_city' => $employer->suburb,
                'residential_state' => $employer->state,
                'residential_postcode' => $employer->postcode,
                'residential_abn' => $employer->ABN,
            ],
        ]);
    }

    public static function fromServiceProvider(SetupProvider $serviceProvider): self
    {
        return self::setArray([
            'source_model_class' => SetupProvider::class,
            'id' => $serviceProvider->id,
            'user_id' => $serviceProvider->user_id,
            'first_name' => $serviceProvider->company_name,
            'name' => $serviceProvider->company_name,
            'user_type' => 'serviceprovider',
            'email' => $serviceProvider->email,
            'phone' => $serviceProvider->company_phone,
            'mobile' => $serviceProvider->mobile,
            'last_login' => $serviceProvider->user?->last_login,
            'user_status' => $serviceProvider->user?->status?->getFormatedName(),
            'user_status_id' => $serviceProvider->user?->status->value,
            'last_password_updated_date' => $serviceProvider->user?->last_password_updated_date,
            'roles' => $serviceProvider->user?->userRoles?->map(function ($role) {
                return self::getUserRoleData($role);
            })->toArray() ?? [],
            'two_factor_enabled' => $serviceProvider->user?->two_factor_secret ? true : false,
            'created_by' => $serviceProvider->creator,
            'created_at' => $serviceProvider->created_at,
            'updated_by' => $serviceProvider->updater,
            'updated_at' => $serviceProvider->updated_at,
            'deleted_at' => $serviceProvider->deleted_at,
            'created_by_name' => $serviceProvider->creator?->name,
            'username' => $serviceProvider->user?->username,
            'role_id' => optional($serviceProvider->user?->roles)->first()?->id,
            'completion_status' => $serviceProvider->completion_status,
            'source_of_entry' => $serviceProvider->source_of_entry ?? 'Internal',
            'user_info' => [
                'name' => $serviceProvider->company_name,
                'email' => $serviceProvider->user?->email,
                'phone' => $serviceProvider->company_phone,
                'mobile' => $serviceProvider->mobile,
                'fax' => $serviceProvider->company_fax,
                'website' => $serviceProvider->website,
                'role_id' => optional($serviceProvider->user?->roles)->first()?->id,
                'notes' => $serviceProvider->comment,
                'internal_note' => $serviceProvider->internal_comment,
                'other_details' => $serviceProvider->other_detail,
                'status' => $serviceProvider->status === 1 ? 'Active' : 'Inactive',
                'emergency_contact_name' => $serviceProvider->contact_name,
            ],
            'address_postal' => [
                'residential_address' => $serviceProvider->company_address,
                'residential_country' => $serviceProvider->country,
                'residential_country_name' => $serviceProvider->getCountryName($serviceProvider->country),
                'residential_city' => $serviceProvider->company_suburb,
                'residential_state' => $serviceProvider->company_state,
                'residential_postcode' => $serviceProvider->postcode,
                'residential_abn' => $serviceProvider->ABN,
            ],
        ]);
    }

    public static function fromPlacementProvider(VpmsPlacementProvider $placementProvider): self
    {
        return self::setArray([
            'source_model_class' => VpmsPlacementProvider::class,
            'id' => $placementProvider->id,
            'user_id' => $placementProvider->user_id,
            'first_name' => $placementProvider->first_name,
            'last_name' => $placementProvider->last_name,
            'user_type' => 'placementprovider',
            'name' => $placementProvider->provider_name,
            'email' => $placementProvider->email,
            'phone' => $placementProvider->contact_number,
            'mobile' => $placementProvider->contact_number,
            'address1' => $placementProvider->address_line_1,
            'address2' => $placementProvider->address_line_2,
            'last_login' => $placementProvider->user?->last_login,
            'user_status' => $placementProvider->user?->status?->getFormatedName(),
            'user_status_id' => $placementProvider->user?->status->value,
            'last_password_updated_date' => $placementProvider->user?->last_password_updated_date,
            'roles' => $placementProvider->user?->userRoles?->map(function ($role) {
                return self::getUserRoleData($role);
            })->toArray() ?? [],
            'two_factor_enabled' => $placementProvider->user?->two_factor_secret ? true : false,
            'created_by' => $placementProvider->creator,
            'created_at' => $placementProvider->created_at,
            'updated_by' => $placementProvider->updater,
            'updated_at' => $placementProvider->updated_at,
            'deleted_at' => $placementProvider->deleted_at,
            'created_by_name' => $placementProvider->creator?->name,
            'username' => $placementProvider->user?->username,
            'role_id' => optional($placementProvider->user?->roles)->first()?->id,
            'completion_status' => $placementProvider->completion_status,
            'source_of_entry' => $placementProvider->source_of_entry ?? 'Internal',
            'code' => $placementProvider->provider_code,
            'user_info' => [
                'name' => $placementProvider->provider_name,
                'agent_code' => $placementProvider->provider_code,
                'code' => $placementProvider->provider_code,
                'email' => $placementProvider->email,
                'phone' => $placementProvider->contact_number,
                'mobile' => $placementProvider->contact_number,
                'website' => $placementProvider->web_url,
                'category' => $placementProvider->categories,
            ],
            'address_postal' => [
                'residential_address' => $placementProvider->address_line_1,
                'residential_city' => $placementProvider->city,
                'residential_state' => $placementProvider->state,
                'residential_postcode' => $placementProvider->postcode,
            ],
        ]);
    }

    public static function getFromModel($model)
    {
        $modelClass = get_class($model);

        return match ($modelClass) {
            //            Users::class => self::fromUser($model),
            Staff::class => self::fromStaff($model),
            Agent::class => self::fromAgent($model),
            Teacher::class => self::fromTeacher($model),
            Employer::class => self::fromEmployer($model),
            SetupProvider::class => self::fromServiceProvider($model),
            VpmsPlacementProvider::class => self::fromPlacementProvider($model),
            AgentStaff::class => self::fromAgentStaff($model),
            Student::class => self::fromStudent($model),
            default => throw new \Exception('Invalid model class'),
        };
    }

    //    private static function toAgentStaffMappedData()
    //    {
    //        return [];
    //    }
    private static function fromStudent($model)
    {
        return self::setArray([
            'source_model_class' => Student::class,
            'id' => $model->id,
            'user_id' => $model->user?->id,
            'first_name' => $model->first_name,
            'middle_name' => $model->middel_name,
            'last_name' => $model->family_name,
            'user_type' => 'student',
            'name' => $model->first_name.' '.$model->middel_name.' '.$model->family_name,
            'email' => $model->email,
            'phone' => $model->current_mobile_phone,
            'mobile' => $model->current_mobile_phone,
            'address1' => $model->current_street_no.' '.$model->current_street_name,
            'last_login' => $model->user?->last_login,
            'user_status' => $model->user?->status?->getFormatedName(),
            'user_status_id' => $model->user?->status->value,
            'last_password_updated_date' => $model->user?->last_password_updated_date,
            'code' => $model->generated_stud_id,
            'roles' => $model->user?->userRoles?->map(function ($role) {
                return self::getUserRoleData($role);
            },
            )->toArray() ?? [],
            'username' => $model->user?->username,
            'role_id' => optional($model->user?->roles)->first()?->id,
            'completion_status' => $model->completion_status,
            'source_of_entry' => $model->source_of_entry ?? 'Internal',
            'created_by' => $model->creator,
            'created_at' => $model->created_at,
            'updated_by' => $model->updater,
            'updated_at' => $model->updated_at,
            'deleted_at' => $model->deleted_at,
            'created_by_name' => $model->creator?->name,
        ]);
    }

    public function toModelMappedData(?string $step = null): array
    {
        $modelClass = $this->source_model_class;
        if (! class_exists($modelClass)) {
            throw new \Exception('Invalid model class');
        }

        return match ($this->source_model_class) {
            //            Users::class => $this->toUserMappedData($step),
            //            Staff::class => $this->toStaffMappedData($step),
            //            Agent::class => $this->toAgentMappedData($step),
            //            Teacher::class => $this->toTeacherMappedData($step),
            //            Employer::class => $this->toEmployerMappedData($step),
            //            SetupProvider::class => $this->toServiceProviderMappedData($step),
            //            VpmsPlacementProvider::class => $this->toPlacementProviderMappedData($step),
            //            AgentStaff::class => $this->toAgentStaffMappedData($step),
            default => throw new InvalidArgumentException("Unsupported model class: {$this->source_model_class}"),
        };
    }

    private static function getStaffAndTeacherData($staff)
    {
        $staffData = [];

        if ($staff) {
            $staffData = [
                // User Info
                'user_info' => [
                    'name_title' => $staff->name_title,
                    'first_name' => $staff->first_name,
                    'last_name' => $staff->last_name,
                    'name' => $staff->first_name.' '.$staff->middle_name.' '.$staff->last_name,
                    'email2' => $staff->personal_email,
                    'email' => $staff->user?->email ?? $staff->email,
                    'phone' => $staff->phone,
                    'mobile' => $staff->mobile,
                    'gender' => $staff->gender,
                    'birth_date' => $staff->birth_date,
                    'country' => $staff->country ? (int) $staff->country : null,
                    'country_name' => $staff->birthCountry?->name,
                    'role_id' => optional($staff->user?->roles)->first()?->id,
                    'code' => $staff->staff_number,
                    'user_image' => $staff->user?->profile_picture ? self::getFileUrl($staff->user->profile_picture) : null,
                    'emergency_contact_name' => $staff->emergency_contact_name,
                    'emergency_contact_relationship' => $staff->emergency_contact_relationship,
                    'emergency_phone_1' => $staff->emergency_phone_1,
                    'emergency_phone_2' => $staff->emergency_phone_2,
                    'emergency_email' => $staff->emergency_email,
                    'emergency_address' => $staff->emergency_address,
                ],

                // Address
                'address_postal' => [
                    'residential_address' => $staff->residential_address,
                    'residential_country' => $staff->residential_country,
                    'residential_country_name' => $staff->getCountryName($staff->residential_country),
                    'residential_city' => $staff->residential_city,
                    'residential_state' => $staff->residential_state,
                    'residential_postcode' => $staff->residential_postcode,
                    'residential_abn' => $staff->residential_abn,
                    'postal_address' => $staff->postal_address,
                    'postal_country' => $staff->postal_country,
                    'postal_country_name' => $staff->getCountryName($staff->postal_country),
                    'postal_city' => $staff->postal_city,
                    'postal_state' => $staff->postal_state,
                    'postal_postcode' => $staff->postal_postcode,
                    'postal_abn' => $staff->postal_abn,
                    'is_post_residential_same' => $staff->is_post_residential_same,
                ],

                // Employment
                'employment_info' => [
                    'staff_number' => $staff->staff_number,
                    'employment_type' => $staff->employment_type,
                    'joined_on' => $staff->joined_on,
                    'campus_location' => $staff->campus_location,
                    'job_position' => $staff->position,
                    'position_name' => $staff->staffPosition?->position,
                    'line_manager' => $staff->line_manager,
                    'employment_histories' => $staff->employmentHistories?->map(function ($history) {
                        return [
                            'id' => $history->id,
                            'staff_id' => $history->staff_id,
                            'title' => $history->title,
                            'organization' => $history->organization,
                            'start_date' => $history->start_date,
                            'end_date' => $history->end_date,
                            'document' => $history->document_path ? self::getFileUrl($history->document_path) : null,
                        ];
                    })->toArray() ?? [],
                ],

                // Qualifications
                'qualifications' => [
                    'education_qualifications' => $staff->educationQualifications?->map(function ($qualification) {
                        return [
                            'id' => $qualification->id,
                            'staff_id' => $qualification->staff_id,
                            'type' => $qualification->type,
                            'name' => $qualification->name,
                            'code' => $qualification->code,
                            'provider' => $qualification->provider,
                            'start_date' => $qualification->start_date,
                            'end_date' => $qualification->end_date,
                            'document' => $qualification->document_path ? self::getFileUrl($qualification->document_path) : null,
                        ];
                    })->toArray() ?? [],
                    'training_qualifications' => $staff->trainingQualifications?->map(function ($qualification) {
                        return [
                            'id' => $qualification->id,
                            'staff_id' => $qualification->staff_id,
                            'type' => $qualification->type,
                            'name' => $qualification->name,
                            'code' => $qualification->code,
                            'provider' => $qualification->provider,
                            'start_date' => $qualification->start_date,
                            'end_date' => $qualification->end_date,
                            'document' => $qualification->document_path ? self::getFileUrl($qualification->document_path) : null,
                        ];
                    })->toArray() ?? [],
                ],

                // Personal developments
                'personal_development' => [
                    'professional_developments' => $staff->professionalDevelopmentHistories?->map(function ($development) {
                        return [
                            'id' => $development->id,
                            'staff_id' => $development->staff_id,
                            'category' => $development->category,
                            'event_name' => $development->event_name,
                            'organized_by' => $development->organized_by,
                            'activity_name' => $development->activity_name,
                            'event_from' => $development->event_from,
                            'event_to' => $development->event_to,
                            'cpd_points' => $development->cpd_points,
                            'comments' => $development->comments,
                            'document' => $development->document ? self::getFileUrl($development->document) : null,
                        ];
                    })->toArray() ?? [],
                ],

                // Banking Details
                'bank_details' => [
                    'bank_name' => $staff->bank_name,
                    'account_name' => $staff->account_name,
                    'bsb' => $staff->bsb,
                    'account_number' => $staff->account_number,
                    'swift_code' => $staff->swift_code,
                    'tax_file_number' => $staff->tax_file_number,
                    'super_fund_name' => $staff->super_fund_name,
                    'super_member_number' => $staff->super_member_number,
                ],

                // TSC Info
                'tcsi_report' => [
                    'birth_date' => self::getDate($staff->birth_date),
                    'joining_date' => self::getDate($staff->joining_date),
                    'atsi_code' => [
                        'code' => $staff->atsi_code,
                        'name' => ATSICodeEnum::tryFrom($staff->atsi_code)?->getFormatedName(),
                    ],
                    'highest_qualification_code' => [
                        'code' => $staff->highest_qualification_code,
                        'name' => HighestQualificationCodeEnum::tryFrom($staff->highest_qualification_code)?->getFormatedName(),
                    ],
                    'highest_qualification_place_code' => [
                        'code' => $staff->highest_qualification_place_code,
                        'name' => HighestQualificationPlaceCodeEnum::tryFrom($staff->highest_qualification_place_code)?->getFormatedName(),
                    ],
                    'work_contract_code' => [
                        'code' => $staff->work_contract_code,
                        'name' => WorkContractCodeEnum::tryFrom($staff->work_contract_code)?->getFormatedName(),
                    ],
                    'staff_work_level_code' => [
                        'code' => $staff->staff_work_level_code,
                        'name' => StaffWorkLevelCodeEnum::tryFrom($staff->staff_work_level_code)?->getFormatedName(),
                    ],
                    'organisational_unit_code' => [
                        'code' => $staff->organisational_unit_code,
                        'name' => OrganisationalUnitCodeEnum::tryFrom($staff->organisational_unit_code)?->getFormatedName(),
                    ],
                    'work_sector_code' => [
                        'code' => $staff->work_sector_code,
                        'name' => WorkSectorCodeEnum::tryFrom($staff->work_sector_code)?->getFormatedName(),
                    ],
                    'function_code' => [
                        'code' => $staff->function_code,
                        'name' => FunctionCodeEnum::tryFrom($staff->function_code)?->getFormatedName(),
                    ],
                    'position' => $staff->position,
                ],
                'upload_docs' => [
                    'proof_of_identification' => $staff->proof_of_identification ? self::getFileUrl($staff->proof_of_identification) : null,
                    'resume' => $staff->resume ? self::getFileUrl($staff->resume) : null,
                    'active_contract' => $staff->active_contract ? self::getFileUrl($staff->active_contract) : null,
                    'other_documents' => $staff->other_documents ? self::parseOtherDocuments($staff->other_documents) : null,
                ],
                'form_progress' => $staff->formProgress?->step_progress ?? [],
            ];
        }

        return $staffData;
    }

    public static function getDate(
        $date = null
    ) {
        if (! $date) {
            return null;
        }

        return \Carbon\Carbon::parse($date)->format('Y-m-d');
    }

    private function fetchNavigationIds($currentId, $modelClass)
    {
        $prevId = $modelClass::where('id', '<', $currentId)
            ->max('id');

        $nextId = $modelClass::where('id', '>', $currentId)
            ->min('id');

        $this->prev_id = $prevId;
        $this->next_id = $nextId;
        $this->secure_prev_id = $prevId ? encryptIt($prevId) : null;
        $this->secure_next_id = $nextId ? encryptIt($nextId) : null;
    }

    private static function parseOtherDocuments($otherDocuments)
    {
        if (! $otherDocuments) {
            return null;
        }

        // If it's already an array, process it
        if (is_array($otherDocuments)) {
            return array_map(function ($doc) {
                return is_string($doc) ? self::getFileUrl($doc) : $doc;
            }, $otherDocuments);
        }

        // If it's a JSON string, decode and process
        $decoded = json_decode($otherDocuments, true);
        if (is_array($decoded)) {
            return array_map(function ($doc) {
                return is_string($doc) ? self::getFileUrl($doc) : $doc;
            }, $decoded);
        }

        // If it's a single string path
        if (is_string($otherDocuments)) {
            return [self::getFileUrl($otherDocuments)];
        }

        return null;
    }

    private static function getFileUrl($path)
    {
        if (! $path) {
            return null;
        }

        // If it's already a full URL, return as is
        if (str_starts_with($path, 'http://') || str_starts_with($path, 'https://')) {
            return $path;
        }

        // Remove leading slash if present
        $path = ltrim($path, '/');

        // Return full URL
        return url($path);
    }
}
