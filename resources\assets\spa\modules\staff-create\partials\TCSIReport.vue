<script setup>
import { computed, onMounted, ref, watch } from 'vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import EnumSelect from '@spa/components/AsyncComponents/Select/EnumSelect.vue';
import FormValidationWrapper from '@spa/components/KendoInputs/FormValidationWrapper.vue';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
import { getValidationMessage } from '@spa/composables/formComposables.js';
import HighlightBox from '@spa/components/HighlightBox/HighlightBox.vue';
const props = defineProps({
    modelValue: {},
});
const emit = defineEmits(['update:modelValue', 'update-not-applicable']);
const formData = computed({
    get() {
        return props.modelValue || {};
    },
    set(value) {
        emit('update:modelValue', value);
    },
});
const notApplicable = ref(false);
const handleNotApplicableChange = (e) => {
    console.log('change', e.target);
    emit('update-not-applicable', notApplicable.value);
};
onMounted(() => {
    if (!formData.value.not_applicable) {
        formData.value.not_applicable = false;
    }
});
</script>
<template>
    <div class="bg-gray-100 p-[2rem]">
        <HighlightBox :variant="'warning'" :class="'mb-4 justify-center'">
            <template #icon>
                <icon name="info-help-outline" />
            </template>
            <template #content>
                <p class="text-yellow-700">
                    This fields are required for higher Ed only. If VET select not applicable at the
                    top of form.
                </p>
            </template>
        </HighlightBox>

        <h3 class="mb-6 text-base font-semibold text-gray-800">TCSI Data Report</h3>

        <div class="mb-6">
            <label class="flex items-center">
                <input
                    type="checkbox"
                    v-model="formData.not_applicable"
                    class="h-4 w-4 rounded border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm font-medium text-gray-700">Not Applicable</span>
            </label>
            <div class="mt-1 text-xs text-gray-500">Enable this if not applicable</div>
        </div>

        <div
            class="grid grid-cols-1 gap-6 xl:grid-cols-2"
            :class="{ 'pointer-events-none opacity-50': formData.not_applicable }"
        >
            <div>
                <FormDatePicker
                    v-model="formData.birth_date"
                    name="birth_date"
                    label="Date of Birth* [ E314 Date Of Birth ]"
                    type="date"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'birth_date')"
                    emit-format="yyyy-MM-dd"
                />
            </div>
            <div>
                <FormDropDown
                    v-model="formData.gender"
                    name="tcsi_gender"
                    label="Gender [ E315 Gender Code ]"
                    :data-items="[
                        { value: 'M', text: 'Male' },
                        { value: 'F', text: 'Female' },
                        { value: 'X', text: 'Other' },
                    ]"
                    :default-item="{ value: null, text: 'Select Gender' }"
                    :value-primitive="true"
                    text-field="text"
                    value-field="value"
                    placeholder="Select"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'gender')"
                />
            </div>
            <div>
                <FormDatePicker
                    v-model="formData.joining_date"
                    name="tcsi_joining_date"
                    label="Joining Date [ E505 Appointment Term ]"
                    type="date"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'tcsi_joining_date')"
                    emit-format="yyyy-MM-dd"
                />
            </div>
            <div>
                <FormValidationWrapper
                    label="ATSI Code [ E316 ATSI Code ]"
                    v-bind="getValidationMessage(formData, 'atsi_code')"
                >
                    <template #field>
                        <EnumSelect
                            :option-label="'name'"
                            :option-value="'code'"
                            :value-primitive="false"
                            className="tw-w-full"
                            enum-class="GalaxyAPI\Enums\ATSICodeEnum"
                            v-model="formData.atsi_code"
                            :placeholder="'Select'"
                        />
                    </template>
                </FormValidationWrapper>
            </div>
            <div>
                <FormValidationWrapper
                    label="Highest Qualification Code [ E501 Highest Qualification Code ]"
                    v-bind="getValidationMessage(formData, 'highest_qualification_code')"
                >
                    <template #field>
                        <EnumSelect
                            :option-label="'name'"
                            :option-value="'code'"
                            :value-primitive="false"
                            className="tw-w-full"
                            enum-class="GalaxyAPI\Enums\HighestQualificationCodeEnum"
                            v-model="formData.highest_qualification_code"
                            :placeholder="'Select'"
                        />
                    </template>
                </FormValidationWrapper>
            </div>
            <div>
                <FormValidationWrapper
                    label="Highest Qualification Place Code [ E502 Highest Qualification Place Code ]"
                    v-bind="getValidationMessage(formData, 'highest_qualification_place_code')"
                >
                    <template #field>
                        <EnumSelect
                            :option-label="'name'"
                            :option-value="'code'"
                            :value-primitive="false"
                            className="tw-w-full"
                            enum-class="GalaxyAPI\Enums\HighestQualificationPlaceCodeEnum"
                            v-model="formData.highest_qualification_place_code"
                            :placeholder="'Select'"
                        />
                    </template>
                </FormValidationWrapper>
            </div>
            <div>
                <FormValidationWrapper
                    label="Work Contract Code [ E506 Work Contract Code ]"
                    v-bind="getValidationMessage(formData, 'work_contract_code')"
                >
                    <template #field>
                        <EnumSelect
                            :option-label="'name'"
                            :option-value="'code'"
                            :value-primitive="false"
                            className="tw-w-full"
                            enum-class="GalaxyAPI\Enums\WorkContractCodeEnum"
                            v-model="formData.work_contract_code"
                            :placeholder="'Select'"
                        />
                    </template>
                </FormValidationWrapper>
            </div>
            <div>
                <FormValidationWrapper
                    label="Staff Work Level Code [ E408 Staff Work Level Code ]"
                    v-bind="getValidationMessage(formData, 'staff_work_level_code')"
                >
                    <template #field>
                        <EnumSelect
                            :option-label="'name'"
                            :option-value="'code'"
                            :value-primitive="false"
                            className="tw-w-full"
                            enum-class="GalaxyAPI\Enums\StaffWorkLevelCodeEnum"
                            v-model="formData.staff_work_level_code"
                            :placeholder="'Select'"
                        />
                    </template>
                </FormValidationWrapper>
            </div>
            <div>
                <FormValidationWrapper
                    label="Organisational Unit Code [ E510 Organisational Unit Code ]"
                    v-bind="getValidationMessage(formData, 'organisational_unit_code')"
                >
                    <template #field>
                        <EnumSelect
                            :option-label="'name'"
                            :option-value="'code'"
                            :value-primitive="false"
                            className="tw-w-full"
                            enum-class="GalaxyAPI\Enums\OrganisationalUnitCodeEnum"
                            v-model="formData.organisational_unit_code"
                            :placeholder="'Select'"
                        />
                    </template>
                </FormValidationWrapper>
            </div>
            <div>
                <FormValidationWrapper
                    label="Work Sector Code [ E511 Work Sector Code ]"
                    v-bind="getValidationMessage(formData, 'work_sector_code')"
                >
                    <template #field>
                        <EnumSelect
                            :option-label="'name'"
                            :option-value="'code'"
                            :value-primitive="false"
                            className="tw-w-full"
                            enum-class="GalaxyAPI\Enums\WorkSectorCodeEnum"
                            v-model="formData.work_sector_code"
                            :placeholder="'Select'"
                        />
                    </template>
                </FormValidationWrapper>
            </div>
            <div>
                <FormValidationWrapper
                    label="Function Code [ E412 Function Code ]"
                    v-bind="getValidationMessage(formData, 'function_code')"
                >
                    <template #field>
                        <EnumSelect
                            :option-label="'name'"
                            :option-value="'code'"
                            :value-primitive="false"
                            className="tw-w-full"
                            enum-class="GalaxyAPI\Enums\FunctionCodeEnum"
                            v-model="formData.function_code"
                            :placeholder="'Select'"
                        />
                    </template>
                </FormValidationWrapper>
            </div>
        </div>
    </div>
</template>

<style scoped></style>
