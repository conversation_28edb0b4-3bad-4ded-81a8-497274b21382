<script setup>
import { useUserGroupStore } from '@spa/stores/modules/user-group/useUserGroupStore.js';
import { computed, onMounted, watch } from 'vue';
import DropdownButton from '@spa/components/Buttons/DropdownButton.vue';
import Button from '@spa/components/Buttons/Button.vue';
import { debounce } from 'lodash';

const props = defineProps({
    modelValue: [String, Number, Array, Object],
    assigned: [Array],
    label: String,
    className: String,
    disabled: Boolean,
    userid: String,
    clearable: {
        type: Boolean,
        default: true,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    readonly: Boolean,
    placeholder: {
        type: String,
        default: 'Select Roles',
    },
    style: {
        type: Object,
        default: () => ({}),
    },
});

const emit = defineEmits(['update:modelValue']);

const vModel = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});

const store = useUserGroupStore();

const initFilters = () => {
    store.filters = { user: props.userid };
};

// Load roles data when component mounts
onMounted(async () => {
    // store.filters = {user:props.userid};
    // // Fetch roles if not already loaded
    // if (!store.all || store.all.length === 0) {
    //     await store.fetchPaged?.(); // Add optional chaining in case method doesn't exist
    // }
});

watch(
    () => props.userid,
    async (newVal) => {
        if (newVal) {
            store.filters = { user: newVal };
            if (!store.all || store.all.length === 0) {
                await store.fetchPaged?.(); // Add optional chaining in case method doesn't exist
            }
        }
    },
    { immediate: true }
);

// Handle role selection with proper multiple/single selection logic
const handleRoleSelect = (roleId) => {
    let assigned = false;
    if (props.assigned) {
        assigned = props.assigned.filter((item) => item.id === roleId)?.length;
    }
    if (props.disabled || props.readonly || assigned > 0) return;

    if (props.multiple) {
        const currentValue = Array.isArray(vModel.value) ? [...vModel.value] : [];
        const index = currentValue.indexOf(roleId);

        if (index > -1) {
            // Remove if already selected
            currentValue.splice(index, 1);
        } else {
            // Add if not selected
            currentValue.push(roleId);
        }

        vModel.value = currentValue;
    } else {
        vModel.value = roleId;
    }
};

// Check if a role is selected
const isRoleSelected = (roleId) => {
    if (props.assigned) {
        const assigned = props.assigned.filter((item) => item.id === roleId)?.length;
        if (assigned > 0) {
            return true;
        }
    }
    if (props.multiple) {
        return Array.isArray(vModel.value) && vModel.value.includes(roleId);
    }
    return vModel.value === roleId;
};

// Get display label for the button
const buttonLabel = computed(() => {
    if (!vModel.value) return 'Assign New Role';

    if (props.multiple) {
        const selectedCount = Array.isArray(vModel.value) ? vModel.value.length : 0;
        if (selectedCount === 0) return 'Assign New Role';
        if (selectedCount === 1) return '1 Role Selected';
        return `${selectedCount} Roles Selected`;
    } else {
        const selectedRole = store.all?.find((role) => role.id === vModel.value);
        return selectedRole ? selectedRole.name : 'Assign New Role';
    }
});

const handleSearch = (event) => {
    store.filters.query = event.target?.value;
};

const debouncedSearch = debounce(handleSearch, 450);
</script>

<template>
    <DropdownButton
        variant="secondary"
        :outline="true"
        :autoHide="!props.multiple"
        :buttonLabel="buttonLabel"
        :icon-name="'plus'"
        :class="['border border-dashed !border-gray-400', props.className]"
        :disabled="props.disabled"
        :style="props.style"
    >
        <template #dropdown-content>
            <div class="relative mx-3 mt-3">
                <span class="absolute left-3 top-1/2 -translate-y-1/2">
                    <icon :name="'lens'" width="16" height="16"
                /></span>
                <input
                    type="text"
                    id="offscreen"
                    :value="store.filters.query"
                    @input="debouncedSearch"
                    class="tw-input-text h-10 pl-8"
                    :placeholder="'Search'"
                />
            </div>
            <div
                v-if="!store.all || store.all.length === 0"
                class="px-4 py-2 text-sm text-gray-500"
            >
                No roles available
            </div>

            <button
                v-for="role in store.all"
                :key="role.id"
                class="flex w-full items-center justify-between px-4 py-2 text-sm font-medium hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50"
                :class="{
                    'bg-blue-50 text-blue-700': isRoleSelected(role.id),
                    'text-gray-900': !isRoleSelected(role.id),
                }"
                :disabled="props.disabled || props.readonly"
                @click="handleRoleSelect(role.id)"
            >
                <span>{{ role.name }}</span>
                <span v-if="isRoleSelected(role.id)" class="text-blue-600">✓</span>
            </button>

            <!-- Clear button for multiple selection -->
            <div
                v-if="
                    props.multiple &&
                    props.clearable &&
                    vModel &&
                    Array.isArray(vModel) &&
                    vModel.length > 0
                "
                class="border-t border-gray-200 pt-1"
            >
                <button
                    class="flex w-full items-center px-4 py-2 text-sm font-medium text-red-600 hover:bg-red-50"
                    :disabled="props.disabled || props.readonly"
                    @click="vModel = []"
                >
                    Clear All
                </button>
            </div>

            <!-- Clear button for single selection -->
            <!--            <div v-else-if="!props.multiple && props.clearable && vModel"-->
            <!--                 class="border-t border-gray-200 pt-1">-->
            <!--                <button-->
            <!--                    class="flex w-full items-center px-4 py-2 text-sm font-medium text-red-600 hover:bg-red-50"-->
            <!--                    :disabled="props.disabled || props.readonly"-->
            <!--                    @click="vModel = null"-->
            <!--                >-->
            <!--                    Clear Selection-->
            <!--                </button>-->
            <!--            </div>-->
        </template>
    </DropdownButton>
</template>

<style scoped></style>
