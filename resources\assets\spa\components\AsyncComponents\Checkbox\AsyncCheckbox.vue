<script setup>
import { ref, computed, onMounted, watch, toRefs } from 'vue';
import { Checkbox, Input } from '@progress/kendo-vue-inputs';
import Button from '@spa/components/Buttons/Button.vue';

const props = defineProps({
    label: String,
    className: String,
    optionValue: String,
    optionLabel: String,
    disabled: Boolean,
    store: Object,
    modelValue: [String, Number, Array, Object],
    clearable: {
        type: Boolean,
        default: false,
    },
    filters: {
        type: Object,
        default: () => ({}),
    },
    forceReload: {
        type: Boolean,
        default: false,
    },
    multiple: {
        type: Boolean,
        default: true,
    },
    readonly: Boolean,
    useChips: {
        type: Boolean,
        default: true,
    },
    bordered: {
        type: Boolean,
        default: true,
    },
    hasSelectAll: {
        type: Boolean,
        default: true,
    },
    hasApplyAction: {
        type: Boolean,
        default: false,
    },
    sortBy: {
        type: String,
        default: 'id',
    },
});

const { modelValue, optionValue } = toRefs(props);

const emit = defineEmits(['update:modelValue']);

const allOptions = ref([]);
const loading = ref(false);
const searchValue = ref('');

// Local state for pending changes when hasApplyAction is true
const pendingValue = ref(null);

const allSelected = computed(() => {
    return (
        filteredOptions.value.length > 0 &&
        filteredOptions.value.every((opt) =>
            currentWorkingValue.value.includes(opt[optionValue.value])
        )
    );
});

const filteredOptions = computed(() => {
    if (!searchValue.value) return allOptions.value;
    const needle = searchValue.value.toLowerCase();
    return allOptions.value.filter((item) =>
        item[props.optionLabel]?.toLowerCase().includes(needle)
    );
});

// Working value that represents the current selection state
const currentWorkingValue = computed(() => {
    if (props.hasApplyAction && pendingValue.value !== null) {
        return props.multiple
            ? Array.isArray(pendingValue.value)
                ? pendingValue.value
                : []
            : pendingValue.value;
    }

    return props.multiple
        ? Array.isArray(props.modelValue)
            ? props.modelValue
            : []
        : props.modelValue;
});

const computedValue = computed({
    get() {
        return currentWorkingValue.value;
    },
    set(val) {
        if (props.hasApplyAction) {
            // Store in pending state instead of emitting immediately
            pendingValue.value = val;
        } else {
            // Emit immediately when hasApplyAction is false
            emit('update:modelValue', val);
        }
    },
});

// Selected values for button state (when hasApplyAction is true)
const selectedValues = computed(() => {
    if (!props.hasApplyAction) return [];
    return props.multiple
        ? Array.isArray(currentWorkingValue.value)
            ? currentWorkingValue.value
            : []
        : currentWorkingValue.value
          ? [currentWorkingValue.value]
          : [];
});

const toggleSelectAll = (checked) => {
    if (checked) {
        const allValues = filteredOptions.value.map((opt) => opt[optionValue.value]);
        computedValue.value = allValues;
    } else {
        computedValue.value = [];
    }
};

// Initialize pending value when component mounts and hasApplyAction is true
onMounted(async () => {
    if (props.sortBy !== 'id') {
        props.store.serverPagination.sortBy = props.sortBy;
        props.store.serverPagination.descending = false;
    }
    if (props.hasApplyAction) {
        pendingValue.value = props.multiple
            ? Array.isArray(props.modelValue)
                ? [...props.modelValue]
                : []
            : props.modelValue;
    }

    if (props.store && typeof props.store.fetchPaged === 'function') {
        loading.value = true;
        try {
            await props.store.fetchPaged();
            allOptions.value = props.store.all || [];
        } catch (error) {
            console.error('Error fetching options:', error);
        } finally {
            loading.value = false;
        }
    }
});

watch(
    () => props.store?.all,
    (newVal) => {
        if (newVal) {
            allOptions.value = newVal;
        }
    },
    { deep: true }
);

// Watch for external changes to modelValue when hasApplyAction is true
watch(
    () => props.modelValue,
    (newVal) => {
        if (props.hasApplyAction) {
            pendingValue.value = props.multiple
                ? Array.isArray(newVal)
                    ? [...newVal]
                    : []
                : newVal;
        }
    },
    { deep: true }
);

function onFilterChange(event) {
    searchValue.value = event.target.value || '';

    if (!props.store || !props.store.filters) return;

    props.store.filters.query = searchValue.value;

    if (searchValue.value === '') {
        loading.value = true;
        try {
            props.store.fetchPaged().then(() => {
                allOptions.value = props.store.all || [];
            });
        } catch (e) {
            console.error('Error fetching options:', e);
        } finally {
            loading.value = false;
        }
    }
}

function handleCheckboxChange(itemValue, checked) {
    if (!props.multiple) {
        // Single selection
        computedValue.value = checked ? itemValue : null;
        return;
    }

    // Multiple selection
    const currentValue = Array.isArray(computedValue.value) ? [...computedValue.value] : [];
    if (checked) {
        if (!currentValue.includes(itemValue)) {
            computedValue.value = [...currentValue, itemValue];
        }
    } else {
        computedValue.value = currentValue.filter((val) => val !== itemValue);
    }
}

function clearSelections() {
    computedValue.value = props.multiple ? [] : null;
}

// Apply the pending changes
function apply() {
    if (props.hasApplyAction && pendingValue.value !== null) {
        emit('update:modelValue', pendingValue.value);
    }
}

// Reset to original modelValue
function reset() {
    if (props.hasApplyAction) {
        pendingValue.value = props.multiple
            ? Array.isArray(props.modelValue)
                ? [...props.modelValue]
                : []
            : props.modelValue;
    }
}
</script>

<template>
    <div :class="`async-checkbox-group ${className}`">
        <Input
            :value="searchValue"
            :placeholder="'Search...'"
            :disabled="disabled || readonly"
            @input="onFilterChange"
            class="k-textbox"
            style="margin-bottom: 10px; width: 100%"
        />
        <div
            class="inline-flex items-center gap-1"
            :class="{ 'pb-1 ps-1': bordered }"
            v-if="hasSelectAll"
        >
            <Checkbox
                :label="allSelected ? `${filteredOptions.length} Selected` : 'Select All'"
                :value="allSelected"
                @change="toggleSelectAll($event.value)"
            />
            <template v-if="clearable && computedValue.length > 0">
                <div class="h-4 w-px bg-gray-200"></div>
                <Button
                    variant="icon"
                    class="h-5 w-5 border-none p-px"
                    title="Clear Selection"
                    :disabled="disabled || readonly"
                    @click="clearSelections"
                >
                    <icon name="cross" />
                </Button>
            </template>
        </div>

        <div v-if="loading" class="k-loading">Loading...</div>
        <div v-else-if="filteredOptions.length === 0" class="k-no-data">No options available</div>
        <div
            v-else
            class="checkbox-container"
            :class="{ 'rounded-md border border-gray-200 p-2': bordered }"
        >
            <Checkbox
                v-for="item in filteredOptions"
                :key="item[optionValue]"
                :label="item[optionLabel]"
                :value="
                    props.multiple
                        ? computedValue.includes(item[optionValue])
                        : computedValue === item[optionValue]
                "
                :disabled="disabled || readonly"
                @change="handleCheckboxChange(item[optionValue], $event.value)"
                class="k-checkbox-item"
            />
        </div>
        <div class="mt-4 grid grid-cols-2 gap-2 border-gray-200" v-if="hasApplyAction">
            <Button
                @click="reset"
                variant="link"
                size="sm"
                class="w-full text-xs"
                :disabled="selectedValues.length === 0"
            >
                Reset
            </Button>
            <Button
                @click="apply"
                variant="primary"
                size="sm"
                class="w-full text-xs"
                :outline="true"
                :disabled="selectedValues.length === 0"
                v-close-popper
            >
                Apply
            </Button>
        </div>
    </div>
</template>

<style lang="scss">
.async-checkbox-group {
    width: 100%;
    min-width: 200px;

    .k-label {
        display: block;
        margin-bottom: 4px;
        font-weight: bold;
    }

    .checkbox-container {
        max-height: 200px;
        overflow-y: auto;
    }

    .k-checkbox-item {
        display: block;
        margin-bottom: 4px;
    }

    .k-button-clear {
        margin-top: 8px;
    }

    .k-loading {
        padding: 8px;
        color: #666;
    }

    .k-no-data {
        padding: 8px;
        color: #999;
    }
}
</style>
