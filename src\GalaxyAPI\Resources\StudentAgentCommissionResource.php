<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class StudentAgentCommissionResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            // Return transformed StudentAgentCommission data
            'agent' => $this->whenLoaded('agent', function () {
                return [
                    'id' => $this->agent->id,
                    'name' => $this->agent->agency_name,
                ];
            }),
            'student' => $this->whenLoaded('student', function () {
                return [
                    'id' => $this->student->id,
                    'secure_id' => encryptIt($this->student->id),
                    'generated_stud_id' => $this->student->generated_stud_id,
                    'name' => $this->student->first_name.' '.$this->student?->family_name,
                ];
            }),
            'course' => $this->whenLoaded('course', function () {
                return [
                    'id' => $this->course->id,
                    'code' => $this->course->course_code,
                    'name' => $this->course->course_name,
                ];
            }),
            'transaction' => $this->whenLoaded('transaction', function () {
                return [
                    'id' => $this->transaction->invoice?->id,
                    'upfront_fee_to_pay' => $this->transaction->invoice?->upfront_fee_to_pay,
                    'paid_amount' => $this->transaction->invoice?->paid_amount,
                    'comm_paid_date' => $this->transaction->invoice?->comm_paid_date,
                ];
            }),
            'initialPaymentDetails' => $this->whenLoaded('initialPaymentDetails', function () {
                return [
                    'id' => $this->initialPaymentDetails->id,
                    'upfront_fee_to_pay' => $this->initialPaymentDetails->upfront_fee_to_pay,
                    'upfront_fee_pay' => $this->initialPaymentDetails->upfront_fee_pay,
                    'commission_value' => $this->initialPaymentDetails->commission_value,
                ];
            }),
            // Direct field mappings for component compatibility
            'invoice_no' => $this->invoice_no,
            'formatted_invoice_number' => $this->formatted_invoice_number,
            'generated_stud_id' => $this->student?->generated_stud_id,
            'student_name' => $this->student ? trim($this->student->first_name.' '.$this->student?->middel_name.' '.$this->student?->family_name) : null,
            'course_code' => $this->course?->course_code,
            'course_name' => $this->course?->course_name,
            'course' => $this->course ? $this->course->course_code.' - '.$this->course->course_name : null,
            'paid_date' => $this->paid_date,
            'due_date' => $this->due_date,
            'transaction_amount' => $this->initialPaymentDetails?->upfront_fee_to_pay, // $this->transaction?->invoice?->paid_amount,
            'commission_percentage' => $this->initialPaymentDetails?->commission_value,
            'refund_amount' => $this->refund_amount,
            'commission' => $this->commission_payable,
            'gst' => $this->gst_amount,
            'commission_payable' => $this->commission_payable + $this->gst_amount,
            'commission_paid' => $this->commission_paid,
            'remarks' => $this->remarks,
            'is_approved' => $this->is_approved,
            'is_process' => $this->is_process,
            'is_paid' => ($this->commission_paid >= $this->commission_payable + $this->gst_amount) ? 1 : 0,
        ];
    }
}
