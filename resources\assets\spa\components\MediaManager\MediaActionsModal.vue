<template>
    <div class="kendo-modal-wrapper">
        <Dialog
            v-if="visible"
            :title="modalTitle"
            :width="modalWidth"
            @close="handleClose"
            dialog-class="tw-content-p-0 upload-custom-modal-wrapper"
        >
            <div class="modal-content">
                <div class="space-y-3">
                    <MediaViewDetail v-if="actionType === 'viewDetail'" :dataItem="dataItem" />
                    <Form
                        ref="createFolderForm"
                        @submit="handleSubmit"
                        :initial-values="formInitialValues"
                        id="createFolderForm"
                        v-else
                    >
                        <MediaActionForm
                            :actionType="actionType"
                            :decryptItParentId="props.decryptItParentId"
                            :folders="folders"
                            @close="handleClose"
                        />
                    </Form>
                </div>
            </div>
        </Dialog>
    </div>
</template>
<script setup>
import { ref, watch, computed } from 'vue';
import { Dialog, DialogActionsBar } from '@progress/kendo-vue-dialogs';
import Button from '@spa/components/Buttons/Button.vue';
import { Form, FormElement } from '@progress/kendo-vue-form';
import MediaViewDetail from '@spa/components/MediaManager/partials/MediaViewDetail.vue';
import MediaActionForm from '@spa/components/MediaManager/partials/MediaActionForm.vue';

const props = defineProps({
    visible: {
        type: Boolean,
        required: true,
    },
    actionType: {
        type: String, // 'upload', 'rename', 'move', 'createFolder'
        required: true,
    },
    decryptItParentId: {
        type: String,
        required: true,
    },
    formData: {
        type: Object,
        required: true,
    },
    folders: {
        type: Array,
        default: () => [], // List of folders for 'move' action
    },
    dataItem: {
        type: Object,
        default: () => {},
    },
});

const emit = defineEmits(['close', 'submit']);

const handleSubmit = (formData) => {
    console.log('Sumbit', formData);
    emit('submit', { action: props.actionType, data: formData });
    if (props.actionType !== 'upload') {
        handleClose();
    }
};

const handleClose = () => {
    emit('closeModal');
};

const modalTitle = computed(() => {
    let titleMappings = {
        upload: 'Upload Documents',
        rename: 'Rename File',
        move: 'Move File',
        createFolder: 'Create New Folder',
        bulkMove: 'Move Files To',
        viewDetail: 'File Detail',
        viewAccess: 'View Access',
        delete: '',
        bulkDelte: '',
        bulkStudentAccess: 'Bulk Access',
        bulkTeacherAccess: 'Bulk Access',
    };

    return titleMappings[props.actionType] || 'Action';
});

const modalWidth = computed(() => {
    let widthMappings = {
        upload: 470,
        viewDetail: 500,
    };
    return widthMappings[props.actionType] || 500;
});

const formInitialValues = computed(() => {
    let valueMappings = {
        rename: {
            name: stripFileExtension(props.dataItem?.name),
        },
    };
    return valueMappings[props.actionType] || {};
});

const stripFileExtension = (fileName) => {
    if (fileName) {
        const lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex === -1) {
            return fileName;
        }
        return fileName.substring(0, lastDotIndex);
    }
};
</script>
<style lang=""></style>
