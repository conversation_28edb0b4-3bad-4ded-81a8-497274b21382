<?php

namespace GalaxyAPI\Controllers;

use App\Users;
use GalaxyAPI\Requests\PermissionRequest;
use GalaxyAPI\Requests\RoleUnassignToUserRequest;
use GalaxyAPI\Resources\PermissionResource;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Support\Auth\Permission;

class PermissionController extends CrudBaseController
{
    public function init()
    {
        $request = request();
        if ($request->filled('filters')) {
            $filters = json_decode($request->query('filters'), true);
        }
        $userId = $filters['user'] ?? null;
        $roleId = $filters['role'] ?? null;
        try {
            $userId = decryptIt($userId);
            $roleId = decryptIt($roleId);
        } catch (\Exception $e) {
            $userId = $roleId = 0;
        }

        $commonLoads = [
            'group_permissions',
            'permissionOf',
            // 'children',
            // 'parent',
        ];
        $this->withAll = [
            ...$commonLoads,
        ];
        $this->loadAll = [
            ...$commonLoads,
        ];
        // get the role and user type

        $this->scopeWithValue = [
            'userRole' => ['user' => $userId, 'role' => $roleId],
        ];
    }

    public function __construct()
    {
        parent::__construct(
            model: Permission::class,
            storeRequest: PermissionRequest::class,
            updateRequest: PermissionRequest::class,
            resource: PermissionResource::class,
        );
    }

    public function index()
    {
        $request = request();
        if ($request->filled('filters')) {
            $filters = json_decode($request->query('filters'), true);
        }
        $userId = $filters['user'] ?? null;
        $roleId = $filters['role'] ?? null;
        $newrole = (int) @$filters['newrole'] ?? null;
        try {
            $userId = decryptIt($userId);
            if ($newrole) {
                $roleId = (int) $roleId;
            } else {
                $roleId = decryptIt($roleId);
            }
        } catch (\Exception $e) {
            $userId = $roleId = 0;
        }
        $rowsPerPage = $request->input('rowsPerPage', 10);
        if ($rowsPerPage > $this->maxRowsPerPage) {
            $rowsPerPage = $this->maxRowsPerPage;
        }
        $permissions = $this->model::getPermissionsListForUserRole($userId, $roleId, $rowsPerPage, $newrole);

        // dd($permissions);
        return $this->resource::collection($permissions)
            ->additional([
                'code' => Response::HTTP_OK,
                'success' => 1,
            ]);
    }

    public function getModules(Request $request)
    {
        return ajaxSuccess([
            'data' => Permission::pluck('module', 'module')->unique()->values()->toArray(),
            '',
        ]);
    }

    public function updateAccess(PermissionRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->DTO();
            $user = Users::find($data->user_id);
            if (! $user) {
                throw new \Exception('User not found');
            }
            $newRole = $user->updatePermissions($data);
            $userRoles = $user->roles()->with('parentRole')->get()?->map(function ($role) use ($data) {
                $parentRole = $role?->parentRole ?? null;

                return [
                    'id' => $role?->id,
                    'secure_id' => encryptIt($role?->id),
                    'parent_role_id' => $parentRole?->id ?? null,
                    'parent_role_secure_id' => $parentRole?->id ? encryptIt($parentRole?->id) : null,
                    'name' => $parentRole?->name ?? $role?->name,
                    'is_current' => $role->id == $data->role_id,
                ];
            });

            return ajaxSuccess(['data' => ['roles' => $userRoles], 'message' => 'Permissions updated successfully.']);
        } catch (\Exception $e) {
            DB::rollback();

            return ajaxError($e->getMessage());
        } finally {
            DB::commit();
        }
    }

    public function unassignRole(RoleUnassignToUserRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->validated();
            $user = Users::find($data['user_id']);
            $role = $user->roles()->where('id', $data['role_id'])->first();
            if ($role) {
                $user->removeRole($role);
                if ($role->duplicated_from > 0) {
                    $role->delete();
                }
            }

            return ajaxSuccess(['data' => [], 'message' => 'Role successfully unassigned from user.']);
        } catch (\Exception $e) {
            DB::rollback();

            return ajaxError($e->getMessage());
        } finally {
            // DB::rollback();
            DB::commit();
        }
    }

    public function setAsDefault(Request $request)
    {
        DB::beginTransaction();
        try {
            $userId = $request->input('user_id');
            $roleId = $request->input('role_id');

            // Decrypt the IDs
            try {
                $userId = decryptIt($userId);
                $roleId = decryptIt($roleId);
            } catch (\Exception $e) {
                throw new \Exception('Invalid user or role ID.');
            }

            // Find the user
            $user = Users::find($userId);
            if (! $user) {
                throw new \Exception('User not found.');
            }

            // Check if the user has this role
            $hasRole = $user->roles()->where('id', $roleId)->exists();
            if (! $hasRole) {
                throw new \Exception('User does not have this role assigned.');
            }

            $modelType = morphAlias(Users::class);

            DB::table(config('permission.table_names.model_has_roles'))
                ->where('model_type', $modelType)
                ->where('model_id', $userId)
                ->update(['is_default' => 0]);

            DB::table(config('permission.table_names.model_has_roles'))
                ->where('model_type', $modelType)
                ->where('model_id', $userId)
                ->where('role_id', $roleId)
                ->update(['is_default' => 1]);

            DB::commit();

            // Refresh user roles with updated is_default values
            $userRoles = $user->userRoles()->with('parentRole')->get()?->map(function ($role) {
                $parentRole = $role?->parentRole ?? null;
                $roleType = $role->roleType ?? null;
                $permanentRoleTypes = ['TYPE_STUDENT', 'TYPE_AGENT'];

                return [
                    'id' => $role?->id,
                    'secure_id' => encryptIt($role?->id),
                    'parent_role_id' => $parentRole?->id ?? null,
                    'parent_role_secure_id' => $parentRole?->id ? encryptIt($parentRole?->id) : null,
                    'name' => $parentRole ? $parentRole->name : $role?->name,
                    'is_master_admin' => $roleType?->is_super_admin === 1,
                    'is_permanent' => $roleType?->is_super_admin === 1 || in_array($roleType?->role_scope, $permanentRoleTypes),
                    'is_default' => $role?->pivot?->is_default === 1,
                ];
            });

            return ajaxSuccess(['data' => ['roles' => $userRoles], 'message' => 'Default role set successfully.']);
        } catch (\Exception $e) {
            DB::rollback();

            return ajaxError($e->getMessage());
        }
    }
}
