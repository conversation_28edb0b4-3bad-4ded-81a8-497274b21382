<?php

namespace App\Http\Requests\FormValidation\StudentProfile;

use App\DTO\studentProfile\AddStudentUnitAvetimissValue;
use App\Traits\ResponseTrait;
use Illuminate\Foundation\Http\FormRequest;
use Support\Traits\CommonTrait;

class StudentUnitAvetimissValue extends FormRequest
{
    use CommonTrait;
    use ResponseTrait;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'funding_source_state' => 'required',
            'funding_source_nat' => 'required',
            'funding_identifier' => 'required_if:funding_source_nat,13',
            'study_reason' => 'required',
            'course_commencing_id' => 'required|numeric',
            'training_contract_id' => 'required|numeric',
            'apprenticeship_client_id' => 'required|numeric',
            'vet_in_school' => 'required',
            'fee_exemption_id' => 'required',
            'con_schedule_id' => 'required|numeric',
            'purchase_contract_id' => 'required|numeric',
            'booking_id' => 'required|numeric',
            'course_site_id' => 'required|numeric',
            'delivery_mode' => 'required',
            'predominant_delivery_mode' => 'required',
        ];

        // If funding_identifier has a value, funding_source_nat must be 13
        if ($this->filled('funding_identifier')) {
            $rules['funding_source_nat'] .= '|in:13';
        }

        return $rules;
    }

    public function DTO()
    {
        return AddStudentUnitAvetimissValue::LazyFromArray($this->input());
    }
}
