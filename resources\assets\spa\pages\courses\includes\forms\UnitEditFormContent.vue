<template>
    <form-element>
        <fieldset class="k-form-fieldset">
            <div class="flex space-x-4" :class="{ 'mb-3': !isVetUnit }">
                <div class="w-1/2">
                    <field
                        :id="'unit_code'"
                        :name="'unit_code'"
                        :label="`${isHigherEdUnit ? 'Subject' : 'Unit'} Code`"
                        :component="'myTemplate'"
                        :validator="requiredCode"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <forminput
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                                :class="'uppercase'"
                            >
                            </forminput>
                        </template>
                    </field>
                </div>
                <div class="w-1/2" v-if="!isHigherEdUnit">
                    <field
                        :id="'vet_unit_code'"
                        :name="'vet_unit_code'"
                        :component="'myTemplate'"
                        :label="'Vet Unit Code'"
                        :validator="codeFormat"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <forminput
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                                :class="'uppercase'"
                            />
                        </template>
                    </field>
                </div>
            </div>
            <div
                class="mb-3 mt-2 flex cursor-pointer items-center space-x-2"
                v-if="isVetUnit || isShortCourse"
                @click="syncUnitInfo"
            >
                <icon
                    :name="'loading'"
                    :width="24"
                    :height="24"
                    :stroke="'#E2E8F0'"
                    :fill="'#10B981'"
                    v-if="syncing"
                />
                <icon
                    :name="'sync'"
                    :width="24"
                    :height="24"
                    :viewbox="'0 0 24 24'"
                    :fill="'#10B981'"
                    v-else
                />
                <div>{{ syncing ? 'Requesting Data' : 'Sync' }}</div>
            </div>
            <div class="mb-3">
                <field
                    :id="'unit_name'"
                    :name="'unit_name'"
                    :component="'myTemplate'"
                    :label="`${isHigherEdUnit ? 'Subject' : 'Unit'} Name`"
                    :validator="requiredtrue"
                >
                    <template v-slot:myTemplate="{ props }">
                        <forminput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
            </div>
            <div class="mb-3">
                <field
                    :id="'description'"
                    :name="'description'"
                    :label="'Description'"
                    :component="'myTemplate'"
                >
                    <template v-slot:myTemplate="{ props }">
                        <formtextarea
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        >
                        </formtextarea>
                    </template>
                </field>
            </div>
            <div class="mb-3">
                <field
                    :id="'unit_type'"
                    :name="'unit_type'"
                    :label="`${isHigherEdUnit ? 'Subject' : 'Unit'} Type`"
                    :data-items="unitTypesData"
                    :layout="'horizontal'"
                    :component="'myTemplate'"
                    :disabled="canChangeUnitType && !forceChangeUnitType"
                >
                    <template v-slot:myTemplate="{ props }">
                        <formradiogroup
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                        <div class="k-hint" v-if="canChangeUnitTypeHint && !forceChangeUnitType">
                            <div class="k-hint-text">
                                This unit is defined by training.gov.au. So, the unit type can not
                                be changed.
                                <Button
                                    :variant="'primary'"
                                    :size="'xxs'"
                                    @click="forceChangeUnitType = true"
                                    >Force Change</Button
                                >
                            </div>
                        </div>
                    </template>
                </field>
            </div>
            <div class="mb-3">
                <field
                    :id="'field_education'"
                    :name="'field_education'"
                    :label="'Field of Education'"
                    :component="'courseGeneral'"
                    :text-field="'text'"
                    :data-item-key="'code'"
                    :valueField="'code'"
                    :valuePrimitive="true"
                    :default-item="defaultEducationField"
                    :data-items="educationFieldItems"
                    :validator="requiredtrue"
                >
                    <template v-slot:courseGeneral="{ props }">
                        <formdropdown
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
            </div>
            <div class="mb-3" v-if="delivery_mode_type == 'dropdown'">
                <field
                    :id="'delivery_mode'"
                    :name="'delivery_mode'"
                    :label="'Delivery Mode'"
                    :component="'courseGeneral'"
                    :text-field="'label'"
                    :data-item-key="'value'"
                    :valueField="'value'"
                    :valuePrimitive="true"
                    :default-item="defaultDeliveryMode"
                    :data-items="deliveryModesData"
                    :validator="requiredtrue"
                >
                    <template v-slot:courseGeneral="{ props }">
                        <formdropdown
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
            </div>
            <div v-else>
                <div class="k-label mb-1 font-medium leading-5 text-gray-700">Delivery Mode</div>
                <div class="mb-3 flex space-x-4">
                    <div class="course-checkbox w-auto">
                        <field
                            :id="'delivery_mode_internal'"
                            :name="'delivery_mode_internal'"
                            :checked="delivery_mode_internal_checked"
                            :label="'Internal'"
                            :label-placement="'after'"
                            :component="'campusCheck'"
                        >
                            <template v-slot:campusCheck="{ props }">
                                <formcheckbox
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                ></formcheckbox>
                            </template>
                        </field>
                    </div>
                    <div class="course-checkbox w-auto">
                        <field
                            :id="'delivery_mode_external'"
                            :name="'delivery_mode_external'"
                            :checked="delivery_mode_external_checked"
                            :label="'External'"
                            :label-placement="'after'"
                            :component="'campusCheck'"
                        >
                            <template v-slot:campusCheck="{ props }">
                                <formcheckbox
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                ></formcheckbox>
                            </template>
                        </field>
                    </div>
                    <div class="course-checkbox w-auto">
                        <field
                            :id="'delivery_mode_workplace'"
                            :name="'delivery_mode_workplace'"
                            :checked="delivery_mode_workplace_checked"
                            :label="'Workplace-Based Delivery'"
                            :label-placement="'after'"
                            :component="'campusCheck'"
                        >
                            <template v-slot:campusCheck="{ props }">
                                <formcheckbox
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                ></formcheckbox>
                            </template>
                        </field>
                    </div>
                </div>
            </div>
            <div class="mb-3 flex justify-start space-x-4">
                <div class="w-1/3">
                    <field
                        :id="'nominal_hours'"
                        :name="'nominal_hours'"
                        :min="0"
                        :label="'Scheduled Nominal Hours'"
                        :component="'nominalHours'"
                        :labelinfo="getLabelInfo"
                        :validator="checkNoninalHoursValidity"
                        :max="400"
                    >
                        <template v-slot:nominalHours="{ props }">
                            <numericinput
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div class="w-1/3">
                    <field
                        :id="'tution_fees'"
                        :name="'tution_fees'"
                        :min="0"
                        :label="'Tuition Fee'"
                        :component="'tuitionFee'"
                        v-if="showInternationalFee"
                    >
                        <template v-slot:tuitionFee="{ props }">
                            <numericinput
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div class="w-1/3" v-if="showDomesticFee">
                    <field
                        :id="'domestic_tution_fees'"
                        :name="'domestic_tution_fees'"
                        :min="0"
                        :label="'Domestic Tuition Fee'"
                        :component="'domtuitionFee'"
                    >
                        <template v-slot:domtuitionFee="{ props }">
                            <numericinput
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
            </div>
            <div class="mb-3">
                <field
                    :id="'module_unit_flag'"
                    :name="'module_unit_flag'"
                    :label="'Module / Unit Flag'"
                    :data-items="flagsData"
                    :layout="'horizontal'"
                    :component="'myTemplate'"
                >
                    <template v-slot:myTemplate="{ props }">
                        <formradiogroup
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
            </div>
            <div class="mb-3 flex w-full space-x-4">
                <div class="w-1/2">
                    <field
                        :id="'grading_type'"
                        :name="'grading_type'"
                        :label="'Grading Type'"
                        :component="'courseGeneral'"
                        :text-field="'text'"
                        :data-item-key="'id'"
                        :valueField="'id'"
                        :valuePrimitive="true"
                        :default-item="defaultGradingType"
                        :data-items="gradingTypes"
                        :validator="requiredtrue"
                    >
                        <template v-slot:courseGeneral="{ props }">
                            <formdropdown
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
            </div>
            <transition>
                <div v-if="isHigherEdUnit">
                    <div class="mb-3 flex justify-between space-x-4">
                        <div class="w-1/2">
                            <field
                                :id="'level'"
                                :name="'level'"
                                :label="'Higher Ed Level'"
                                :component="'courseGeneral'"
                                :text-field="'text'"
                                :data-item-key="'id'"
                                :valueField="'id'"
                                :valuePrimitive="true"
                                :default-item="defaultLevelData"
                                :data-items="levelData"
                            >
                                <template v-slot:courseGeneral="{ props }">
                                    <formdropdown
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                        </div>
                        <div class="w-1/2">
                            <field
                                :id="'credit_point'"
                                :name="'credit_point'"
                                :label="'Credit Points'"
                                :component="'tuitionFee'"
                            >
                                <template v-slot:tuitionFee="{ props }">
                                    <numericinput
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                        </div>
                    </div>
                    <div class="mb-3 flex justify-start space-x-4">
                        <div class="w-1/2">
                            <field
                                :id="'EFTSL_study_load'"
                                :name="'EFTSL_study_load'"
                                :label="'EFTSL Study Load'"
                                :component="'tuitionFee'"
                                :format="''"
                            >
                                <template v-slot:tuitionFee="{ props }">
                                    <numericinput
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                        </div>
                        <div class="w-1/2">
                            <field
                                :id="'max_marks_allow'"
                                :name="'max_marks_allow'"
                                :label="'Maximum Marks Allowed'"
                                :component="'max_marks_allow'"
                                :format="''"
                            >
                                <template v-slot:max_marks_allow="{ props }">
                                    <numericinput
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                        </div>
                    </div>
                    <div class="mb-3">
                        <field
                            :id="'is_long_indicator'"
                            :name="'is_long_indicator'"
                            :checked="is_long_indicator_checked"
                            :label="'Unit of study year long indicator (E622)'"
                            :label-placement="'after'"
                            :component="'campusCheck'"
                        >
                            <template v-slot:campusCheck="{ props }">
                                <formcheckbox
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                ></formcheckbox>
                            </template>
                        </field>
                    </div>
                    <div class="mb-3">
                        <field
                            :id="'is_assessment'"
                            :name="'is_assessment'"
                            :checked="is_assessment_checked"
                            :label="'Aggregate Assessment'"
                            :label-placement="'after'"
                            :component="'campusCheck'"
                        >
                            <template v-slot:campusCheck="{ props }">
                                <formcheckbox
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                ></formcheckbox>
                            </template>
                        </field>
                    </div>
                </div>
            </transition>
            <div class="mb-3 flex items-center justify-start space-x-2" v-if="!isHigherEdUnit">
                <div class="w-auto">
                    <field
                        :id="'vet_flag'"
                        :name="'vet_flag'"
                        :checked="vet_flag_checked"
                        :component="'switchTemplate'"
                        :label="'Vet Flag'"
                    >
                        <template v-slot:switchTemplate="{ props }">
                            <formcheckbox
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
            </div>
            <div class="mb-3 flex items-center justify-start space-x-2">
                <div class="w-auto">
                    <field
                        :id="'work_placement'"
                        :name="'work_placement'"
                        :checked="work_placement_checked"
                        :component="'switchTemplate'"
                        :label="'Work Placement'"
                    >
                        <template v-slot:switchTemplate="{ props }">
                            <formcheckbox
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
            </div>
            <div class="mb-3">
                <field
                    :id="'inc_in_certificate'"
                    :name="'inc_in_certificate'"
                    :checked="inc_in_certificate_checked"
                    :label="'Include In Certificate'"
                    :label-placement="'after'"
                    :component="'campusCheck'"
                >
                    <template v-slot:campusCheck="{ props }">
                        <formcheckbox
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        ></formcheckbox>
                    </template>
                </field>
            </div>
            <div class="mb-3 flex items-center justify-start space-x-2" v-if="!isHigherEdUnit">
                <div class="w-auto">
                    <field
                        :id="'AVETMISS_Report'"
                        :name="'AVETMISS_Report'"
                        :checked="AVETMISS_Report_checked"
                        :component="'switchTemplate'"
                        :label="'AVETMISS Report'"
                    >
                        <template v-slot:switchTemplate="{ props }">
                            <formcheckbox
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
            </div>
            <div class="mb-3 flex items-center justify-start space-x-2">
                <div class="w-auto">
                    <field
                        :id="'status'"
                        :name="'status'"
                        :checked="Status_checked"
                        :component="'switchTemplate'"
                        :label="'Status (Active/Inactive)'"
                    >
                        <template v-slot:switchTemplate="{ props }">
                            <formcheckbox
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
            </div>
        </fieldset>
        <dialog-actions-bar>
            <div class="flex w-full justify-end gap-2">
                <div class="mr-2">
                    <SecondaryButton @click="cancelProcess" :class="'hover:cursor-pointer'">
                        <div class="ml-2">Cancel</div>
                    </SecondaryButton>
                </div>
                <div class="mr-2">
                    <Button
                        :variant="'primary'"
                        :type="'submit'"
                        @click="handleSaveOnly"
                        :size="'base'"
                        :disabled="!kendoForm.allowSubmit"
                    >
                        <ContextLoader
                            :context="'buttonLoading'"
                            template="spin"
                            :loadingText="'Saving Unit'"
                            :pt="{
                                root: 'flex items-center justify-center gap-1',
                            }"
                            v-if="saving && !willclose"
                        />
                        <span v-else>Save Unit</span>
                    </Button>
                </div>
                <div class="mr-2" v-if="kendoForm.allowSubmit && multiplebuttons">
                    <Button
                        :variant="'primary'"
                        :type="'submit'"
                        :size="'base'"
                        :disabled="!kendoForm.allowSubmit"
                    >
                        <ContextLoader
                            :context="'buttonLoading'"
                            template="spin"
                            :loadingText="'Saving Unit'"
                            :pt="{
                                root: 'flex items-center justify-center gap-1',
                            }"
                            v-if="saving && willclose"
                        />
                        <span v-else>Save Unit & Close</span>
                    </Button>
                </div>
            </div>
        </dialog-actions-bar>
    </form-element>
</template>

<script>
import { DialogActionsBar } from '@progress/kendo-vue-dialogs';
import { Field, FormElement } from '@progress/kendo-vue-form';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormNumericInput from '@spa/components/KendoInputs/FormNumericInput.vue';
import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
import FormRadioGroup from '@spa/components/KendoInputs/FormRadioGroup.vue';
import FormSwitch from '@spa/components/KendoInputs/FormSwitch.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormCheckbox from '@spa/components/KendoInputs/FormCheckboxInline.vue';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import { mapState } from 'pinia';
import { useCoursesStore } from '@spa/stores/modules/courses';
import PrimaryButton from '@spa/components/Buttons/PrimaryButton.vue';
import SecondaryButton from '@spa/components/Buttons/SecondaryButton.vue';
import Button from '@spa/components/Buttons/Button';
import ContextLoader from '@spa/components/Loader/ContextLoader.vue';

import {
    requiredtrue,
    requiredpositivenumber,
    requiredpositiveinteger,
    requiredmonetoryvalue,
    requiredNominalHours,
    requiredCode,
    codeFormat,
} from '@spa/services/validators/kendoCommonValidator.js';
import { max } from 'lodash';

export default {
    props: {
        saving: {
            type: Boolean,
            default: false,
        },
        syncing: {
            type: Boolean,
            default: false,
        },
        modelTitle: {
            type: String,
            default: '',
        },
        subjects: {
            type: Object,
            default: [],
        },
        course: {
            type: Object,
            default: [],
        },
        custom: {
            type: Boolean,
            default: false,
        },
        nominalhours: {
            type: Object,
            default: {},
        },
        willclose: {
            type: Boolean,
            default: true,
        },
        multiplebuttons: {
            type: Boolean,
            default: true,
        },
        differentSubjectSetup: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        field: Field,
        'form-element': FormElement,
        forminput: FormInput,
        formtextarea: FormTextArea,
        numericinput: FormNumericInput,
        formradiogroup: FormRadioGroup,
        formswitch: FormSwitch,
        formdropdown: FormDropDown,
        formcheckbox: FormCheckbox,
        'kendo-dropdownlist': DropDownList,
        PrimaryButton,
        SecondaryButton,
        'dialog-actions-bar': DialogActionsBar,
        ContextLoader,
        Button,
    },
    inject: {
        kendoForm: { default: {} },
    },
    computed: {
        ...mapState(useCoursesStore, ['formInits', 'currentposition']),
        educationFieldItems: function () {
            return this.formInits?.education_field;
        },
        deliveryModesData: function () {
            return this.formInits?.unit_delivery_modes || [];
        },
        getSubjectsList: function () {
            return this.subjects;
        },
        getCourse() {
            return this.course || [];
        },
        isCustomUnit() {
            return this.custom || false;
        },
        isVetUnit() {
            return this.isCourseVet(this.course?.course_type_id);
        },
        isShortCourse() {
            return this.isCourseShortCourse(this.course?.course_type_id);
        },
        isHigherEdUnit() {
            return this.isCourseHigherEd(this.course?.course_type_id);
        },
        isSubjectVisible() {
            const solo_subject = this.kendoForm.valueGetter('solo_subject');
            return solo_subject;
        },
        allowSoloSubject() {
            const subjectId = this.kendoForm.valueGetter('course_subject_id');
            if (!subjectId) {
                return true;
            }
            this.subjectSolo = false;
            return false;
        },
        vet_flag_checked() {
            return this.kendoForm.valueGetter('vet_flag') || false;
        },
        work_placement_checked() {
            return this.kendoForm.valueGetter('work_placement') || false;
        },
        AVETMISS_Report_checked() {
            return this.kendoForm.valueGetter('AVETMISS_Report') || false;
        },
        Status_checked() {
            return this.kendoForm.valueGetter('status') || false;
        },
        delivery_mode_internal_checked() {
            return this.kendoForm.valueGetter('delivery_mode_internal') || false;
        },
        inc_in_certificate_checked() {
            return this.kendoForm.valueGetter('inc_in_certificate') || false;
        },
        is_long_indicator_checked() {
            return this.kendoForm.valueGetter('is_long_indicator') || false;
        },
        is_assessment_checked() {
            return this.kendoForm.valueGetter('is_assessment') || false;
        },
        delivery_mode_external_checked() {
            return this.kendoForm.valueGetter('delivery_mode_external') || false;
        },
        delivery_mode_workplace_checked() {
            return this.kendoForm.valueGetter('delivery_mode_workplace') || false;
        },
        canChangeUnitType() {
            const source = this.kendoForm.valueGetter('unit_source') || 'custom';
            return source == 'api';
        },
        canChangeUnitTypeHint() {
            if (this.canChangeUnitType) {
                return 'This unit is defined by training.gov.au. So, the unit type can not be changed.';
            }
            return null;
        },
        isNominalHoursChanged() {
            return (this.nominalhours && this.nominalhours.is_different === true) || false;
        },
        getLabelInfo() {
            if (this.isNominalHoursChanged) {
                const agreed = this.nominalhours.agreed || 0;
                return {
                    icon: 'warning',
                    fill: '#f90',
                    title: 'Different than agreed',
                    tooltip: `Nominal hours differes from argreed hours (${agreed})`,
                };
            }
            return {};
        },
        gradingTypes: function () {
            return this.formInits?.grading_types;
        },
        showDomesticFee: function () {
            const deliveryTarget = this.course?.delivery_target;
            return deliveryTarget == 'Both' || deliveryTarget == 'Domestic';
        },
        showInternationalFee: function () {
            const deliveryTarget = this.course?.delivery_target;
            return deliveryTarget != 'Domestic';
        },
        delivery_mode_type() {
            return this.formInits?.unit_delivery_modeinput_type || 'checkbox';
        },
    },
    mounted() {},
    data: function () {
        return {
            syncreqest: false,
            subjectSolo: false,
            dataitem: [],
            defaultTarget: {
                name: 'Select Delivery Target ...',
                id: '',
            },
            defaultEducationField: {
                text: 'Select Education Field',
                code: null,
            },
            defaultDeliveryMode: {
                text: 'Select Delivery Mode',
                code: null,
            },
            defaultSubjectField: {
                name: 'Select Subject',
                id: '',
            },
            yesNoData: [
                {
                    label: 'Yes',
                    value: 'yes',
                },
                {
                    label: 'No',
                    value: 'no',
                },
            ],
            flagsData: [
                {
                    label: 'C - Unit of Competency',
                    value: 'c',
                },
                {
                    label: 'M - Module',
                    value: 'M',
                },
            ],
            unitTypesData: [
                {
                    label: 'Core',
                    value: 'Core',
                },
                {
                    label: 'Elective',
                    value: 'Elective',
                },
            ],
            defaultGradingType: {
                text: 'Select Grading Type ...',
                id: '',
            },
            defaultLevelData: {
                text: 'Select Higer Ed Level...',
                id: '',
            },
            levelData: [
                {
                    text: 'Level 0',
                    id: 0,
                },
                {
                    text: 'Level 1',
                    id: 1,
                },
            ],
            forceChangeUnitType: false,
        };
    },
    methods: {
        requiredtrue,
        requiredpositivenumber,
        requiredpositiveinteger,
        requiredNominalHours,
        requiredCode,
        codeFormat,
        cancelProcess() {
            this.$emit('cancel');
        },
        syncUnitInfo() {
            const code = this.kendoForm.valueGetter('unit_code');
            if (code) {
                this.$emit('sync', code);
                return;
            }
            this.showPopupWarning('Provide unit code to sync', 'Unit Code is Empty');
        },
        handleSaveOnly(e) {
            this.$emit('preventclose');
            return true;
        },
        checkNoninalHoursValidity(value) {
            if (this.isVetUnit || this.isShortCourse) {
                return this.requiredNominalHours(value, 'unit');
            }
            return '';
        },
    },
};
</script>
