<?php

namespace App\Constants\Menu;

use Support\Auth\Access;

class TeacherSpaMenu
{
    public const TEACHER_MENU_ITEMS = [
        [
            'label' => 'Dashboard',
            'url' => '/teacher/dashboard',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['teacher_dashboard'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 12L5 10M5 10L12 3L19 10M5 10V20C5 20.5523 5.44772 21 6 21H9M19 10L21 12M19 10V20C19 20.5523 18.5523 21 18 21H15M9 21C9.55228 21 10 20.5523 10 20V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V20C14 20.5523 14.4477 21 15 21M9 21H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>',
            'permissions' => [
                // Access::TP_DASHBOARD_ACCESS->value,
            ],
        ],
        [
            'label' => 'Tasks Managment',
            'url' => '/teacher/view-task',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['view-task'],
            'svgicon' => '<svg width="20" height="20" viewBox="0 0 16 12" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M1 7L5 11L15 1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </svg>',
            'permissions' => [
                Access::TP_TASKS_MANAGEMENT_ACCESS->value,
            ],
        ],
        [
            'label' => 'Documents',
            'url' => '#',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['view-college-document-teacher', 'subject-specific', 'course-specific'],
            'gap_after' => true,
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 21H17C18.1046 21 19 20.1046 19 19V9.41421C19 9.149 18.8946 8.89464 18.7071 8.70711L13.2929 3.29289C13.1054 3.10536 12.851 3 12.5858 3H7C5.89543 3 5 3.89543 5 5V19C5 20.1046 5.89543 21 7 21Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>',
            'sub_menu' => [

                [
                    'label' => 'College Documents',
                    'url' => '/teacher/view-college-document-teacher/0',
                    'permissions' => [
                        Access::TP_COLLEGE_DOCUMENTS_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Subject Specific',
                    'url' => '/teacher/subject-specific/0',
                    'permissions' => [
                        Access::TP_SUBJECT_SPECIFIC_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Course Specific',
                    'url' => '/teacher/course-specific/0',
                    'permissions' => [
                        Access::TP_COURSE_SPECIFIC_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'View Timetable',
            'url' => '/teacher/timetable',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['timetable'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 6.25C21 4.45507 19.5449 3 17.75 3H6.25C4.45507 3 3 4.45507 3 6.25V17.75C3 19.5449 4.45507 21 6.25 21H12.0218C11.7253 20.5368 11.4858 20.0335 11.3135 19.5H6.25C5.2835 19.5 4.5 18.7165 4.5 17.75V8.5H19.5V11.3135C20.0335 11.4858 20.5368 11.7253 21 12.0218V6.25ZM6.25 4.5H17.75C18.7165 4.5 19.5 5.2835 19.5 6.25V7H4.5V6.25C4.5 5.2835 5.2835 4.5 6.25 4.5ZM23 17.5C23 14.4624 20.5376 12 17.5 12C14.4624 12 12 14.4624 12 17.5C12 20.5376 14.4624 23 17.5 23C20.5376 23 23 20.5376 23 17.5ZM17.5 17.5H19.5C19.7761 17.5 20 17.7239 20 18C20 18.2762 19.7761 18.5 19.5 18.5H17C16.7268 18.5 16.5048 18.2809 16.5001 18.0089L16.5 17.9999V14.9999C16.5 14.7238 16.7239 14.4999 17 14.4999C17.2761 14.4999 17.5 14.7238 17.5 14.9999L17.5 17.5Z" fill="currentColor"/>
            </svg>',
            'permissions' => [
                Access::TP_VIEW_TIMETABLE_ACCESS->value,
            ],
        ],
        [
            'label' => 'Timesheet',
            'url' => '#',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['teacher-timesheet-submission', 'extra-timesheet-submission', 'view-timesheet', 'upload-invoice'],
            'svgicon' => '<svg width="24" height="24"  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>',
            'sub_menu' => [
                [
                    'label' => 'Timesheet Submission',
                    'url' => '/teacher/teacher-timesheet-submission',
                    'permissions' => [
                        Access::TP_TIMESHEET_SUBMISSION_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Extra Timesheet Submission',
                    'url' => '/teacher/extra-timesheet-submission',
                    'permissions' => [
                        Access::TP_EXTRA_TIMESHEET_SUBMISSION_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'View Timesheet',
                    'url' => '/teacher/view-timesheet',
                    'permissinos' => [
                        Access::TP_VIEW_TIMESHEET_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Upload Invoice',
                    'url' => '/teacher/upload-invoice/0/0',
                    'permissions' => [
                        Access::TP_UPLOAD_INVOICE_ACCESS->value,
                    ],
                ],
            ],
        ],

        [
            'label' => 'Attendance',
            'url' => '#',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['daily-attendance', 'group-attendance'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path class="stroke-none newicon"  d="M11.3135 15.5002C11.4859 14.9667 11.7253 14.4634 12.0219 14.0002H4.25278C3.01076 14.0002 2.00391 15.007 2.00391 16.2491V16.8267C2.00391 17.7195 2.32242 18.583 2.90219 19.2619C4.46849 21.0962 6.8545 22.0013 10.0004 22.0013C10.9314 22.0013 11.7961 21.922 12.5927 21.7629C12.2335 21.3496 11.9256 20.8906 11.6789 20.3957C11.1555 20.466 10.5962 20.5013 10.0004 20.5013C7.26206 20.5013 5.29618 19.7555 4.04287 18.2878C3.69502 17.8805 3.50391 17.3624 3.50391 16.8267V16.2491C3.50391 15.8355 3.83919 15.5002 4.25278 15.5002H11.3135ZM10.0004 2.00488C12.7618 2.00488 15.0004 4.24346 15.0004 7.00488C15.0004 9.76631 12.7618 12.0049 10.0004 12.0049C7.23894 12.0049 5.00036 9.76631 5.00036 7.00488C5.00036 4.24346 7.23894 2.00488 10.0004 2.00488ZM10.0004 3.50488C8.06737 3.50488 6.50036 5.07189 6.50036 7.00488C6.50036 8.93788 8.06737 10.5049 10.0004 10.5049C11.9334 10.5049 13.5004 8.93788 13.5004 7.00488C13.5004 5.07189 11.9334 3.50488 10.0004 3.50488ZM17.5 12.0002C20.5376 12.0002 23 14.4627 23 17.5002C23 20.5378 20.5376 23.0002 17.5 23.0002C14.4624 23.0002 12 20.5378 12 17.5002C12 14.4627 14.4624 12.0002 17.5 12.0002ZM19.5 17.5003H17.5L17.5 15.0002C17.5 14.724 17.2761 14.5002 17 14.5002C16.7239 14.5002 16.5 14.724 16.5 15.0002L16.5 17.9988L16.5 18.0003C16.5 18.2764 16.7239 18.5003 17 18.5003H19.5C19.7761 18.5003 20 18.2764 20 18.0003C20 17.7242 19.7761 17.5003 19.5 17.5003Z" fill="currentColor"/>
                            </svg>',
            'permissions' => [
                Access::TP_ATTENDANCE_ACCESS->value,
            ],
            'sub_menu' => [
                [
                    'label' => 'Daily Attendance',
                    'url' => '/teacher/daily-attendance',
                    'permissions' => [
                        Access::TP_DAILY_ATTENDANCE_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'Competency',
            'url' => '#',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['setup-assessment-task', 'task-entry', 'task-results-entry', 'result-management', 'teacher-transfer-results', 'teacher-vocational-placement', 'teacher-transfer-results-by-unit'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.15186 3.01151C7.9835 2.3641 9.07148 2.00586 10.1527 2.00586C10.7992 2.00586 11.3227 2.22796 11.7193 2.58073C11.8266 2.6762 11.9219 2.77874 12.0066 2.88524C12.0913 2.77874 12.1866 2.6762 12.2939 2.58073C12.6904 2.22796 13.214 2.00586 13.8605 2.00586C14.9417 2.00586 16.0297 2.3641 16.8613 3.01151C17.5083 3.5152 18.0155 4.20952 18.2142 5.04938C18.6344 5.11884 19.0078 5.33301 19.3097 5.61631C19.7904 6.06746 20.1323 6.71923 20.3477 7.39005C20.5662 8.07099 20.6756 8.83647 20.6426 9.57125C20.6257 9.94742 20.5706 10.3308 20.4651 10.6979C20.4871 10.7074 20.509 10.7173 20.5308 10.7275C20.901 10.9021 21.2002 11.1754 21.4253 11.5381C21.8504 12.2228 22 13.2094 22 14.4663C22 15.9108 21.4479 16.8917 20.7377 17.5009C20.3256 17.8544 19.8708 18.0735 19.4681 18.1905C19.3286 18.8831 18.9797 19.6636 18.449 20.3276C17.7262 21.2319 16.6027 21.9961 15.092 21.9961C13.8817 21.9961 12.929 21.3268 12.317 20.6851C12.2051 20.5677 12.1014 20.4482 12.0066 20.3293C11.9117 20.4482 11.8081 20.5677 11.6961 20.6851C11.0842 21.3268 10.1314 21.9961 8.92113 21.9961C7.41046 21.9961 6.287 21.2319 5.5642 20.3276C5.03345 19.6636 4.68458 18.8831 4.54508 18.1905C4.14239 18.0735 3.68758 17.8544 3.27551 17.5009C2.56532 16.8917 2.01318 15.9108 2.01318 14.4663C2.01318 13.2094 2.16274 12.2228 2.58785 11.5381C2.81299 11.1754 3.11222 10.9021 3.48238 10.7275C3.50418 10.7173 3.52608 10.7074 3.54808 10.6979C3.44257 10.3308 3.38752 9.94742 3.37061 9.57125C3.33759 8.83647 3.44696 8.07099 3.66553 7.39005C3.88086 6.71923 4.22275 6.06746 4.70353 5.61631C5.00543 5.33301 5.37882 5.11884 5.79898 5.04938C5.9977 4.20952 6.50484 3.5152 7.15186 3.01151ZM8.07328 4.19514C7.52752 4.62 7.20795 5.18596 7.20795 5.83756C7.20795 6.07868 7.09203 6.3051 6.89641 6.44605C6.70078 6.58701 6.44932 6.62532 6.22059 6.54901C6.08319 6.50317 5.93859 6.51435 5.72995 6.71013C5.49872 6.92712 5.2633 7.32029 5.09376 7.84849C4.92746 8.36658 4.84439 8.95421 4.8691 9.5039C4.89412 10.0605 5.02647 10.5215 5.23478 10.8342C5.26963 10.8866 5.2973 10.9423 5.31771 11H6.39981C7.90396 11 9.13596 12.1653 9.24235 13.6423C9.97936 13.9371 10.5 14.6577 10.5 15.5C10.5 16.6046 9.60457 17.5 8.5 17.5C7.39543 17.5 6.5 16.6046 6.5 15.5C6.5 14.6663 7.01014 13.9517 7.7353 13.6514C7.63924 13 7.07791 12.5 6.39981 12.5H3.7998C3.7914 12.5 3.78302 12.4999 3.77468 12.4996C3.62541 12.8476 3.51318 13.449 3.51318 14.4663C3.51318 15.4872 3.88474 16.0472 4.25219 16.3625C4.65542 16.7084 5.09811 16.798 5.22636 16.798C5.64058 16.798 5.97636 17.1338 5.97636 17.548C5.97636 17.9671 6.20816 18.7308 6.73591 19.3911C7.2447 20.0276 7.96863 20.4961 8.92113 20.4961C9.55821 20.4961 10.145 20.1381 10.6106 19.6499C10.8374 19.4121 11.0135 19.1651 11.129 18.9647C11.1869 18.8643 11.2243 18.7847 11.2449 18.7319L11.2501 18.7183V16.2629C11.25 16.2586 11.25 16.2543 11.25 16.25C11.25 16.2457 11.25 16.2414 11.2501 16.2371L11.2501 9.24999H10.3546C10.0579 9.98295 9.33935 10.5 8.5 10.5C7.39543 10.5 6.5 9.60457 6.5 8.5C6.5 7.39543 7.39543 6.5 8.5 6.5C9.33934 6.5 10.0579 7.01704 10.3546 7.74999H11.2501V5.2236L11.2499 5.21226C11.2496 5.20084 11.249 5.18149 11.2476 5.15538C11.2448 5.10293 11.239 5.02446 11.227 4.92895C11.2027 4.73404 11.1552 4.48751 11.0669 4.25168C10.9773 4.01271 10.8608 3.82469 10.7222 3.70141C10.5992 3.59193 10.43 3.50586 10.1527 3.50586C9.38659 3.50586 8.62717 3.76396 8.07328 4.19514ZM12.7631 17V18.7183L12.7682 18.7319C12.7889 18.7847 12.8263 18.8643 12.8842 18.9647C12.9996 19.1651 13.1757 19.4121 13.4025 19.6499C13.8682 20.1381 14.455 20.4961 15.092 20.4961C16.0446 20.4961 16.7685 20.0276 17.2773 19.3911C17.805 18.7308 18.0368 17.9671 18.0368 17.548C18.0368 17.1338 18.3726 16.798 18.7868 16.798C18.9151 16.798 19.3578 16.7084 19.761 16.3625C20.1284 16.0472 20.5 15.4872 20.5 14.4663C20.5 13.2579 20.3417 12.6364 20.151 12.3293C20.0682 12.196 19.9826 12.1274 19.8909 12.0842C19.7894 12.0363 19.6378 12 19.4026 12C19.1261 12 18.872 11.8478 18.7414 11.604C18.6109 11.3602 18.6251 11.0644 18.7784 10.8342C18.9867 10.5215 19.1191 10.0605 19.1441 9.5039C19.1688 8.95421 19.0857 8.36658 18.9194 7.84849C18.7499 7.32029 18.5145 6.92712 18.2832 6.71013C18.0746 6.51435 17.93 6.50317 17.7926 6.54901C17.5639 6.62532 17.3124 6.58701 17.1168 6.44605C16.9212 6.3051 16.8052 6.07868 16.8052 5.83756C16.8052 5.18596 16.4857 4.62 15.9399 4.19514C15.386 3.76396 14.6266 3.50586 13.8605 3.50586C13.5832 3.50586 13.414 3.59193 13.291 3.70141C13.1524 3.82469 13.0359 4.01271 12.9463 4.25168C12.858 4.48751 12.8105 4.73404 12.7862 4.92895C12.7742 5.02446 12.7684 5.10293 12.7656 5.15538C12.7642 5.18149 12.7636 5.20084 12.7633 5.21226L12.7631 5.2236L12.7631 15.5H13.4C14.1456 15.5 14.75 14.8956 14.75 14.15V12.3546C14.017 12.0579 13.5 11.3393 13.5 10.5C13.5 9.39543 14.3954 8.5 15.5 8.5C16.6046 8.5 17.5 9.39543 17.5 10.5C17.5 11.3393 16.983 12.0579 16.25 12.3546V14.15C16.25 15.724 14.974 17 13.4 17H12.7631ZM8.5 8C8.22386 8 8 8.22386 8 8.5C8 8.77614 8.22386 9 8.5 9C8.77614 9 9 8.77614 9 8.5C9 8.22386 8.77614 8 8.5 8ZM8 15.5C8 15.7761 8.22386 16 8.5 16C8.77614 16 9 15.7761 9 15.5C9 15.2239 8.77614 15 8.5 15C8.22386 15 8 15.2239 8 15.5ZM15 10.5C15 10.7761 15.2239 11 15.5 11C15.7761 11 16 10.7761 16 10.5C16 10.2239 15.7761 10 15.5 10C15.2239 10 15 10.2239 15 10.5Z" fill="currentColor"/>
            </svg>',
            'permissions' => [
                Access::TP_COMPETENCY_ACCESS->value,
            ],
            'sub_menu' => [
                [
                    'label' => 'Result Management',
                    'url' => '/teacher/result-management',
                    'permissions' => [
                        Access::TP_RESULT_MANAGEMENT_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Transfer results',
                    'url' => '/teacher/transfer-results',
                    'permissions' => [
                        Access::TP_TRANSFER_RESULTS_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Vocational Placement Result',
                    'url' => '/teacher/vocational-placement',
                    'permissions' => [
                        Access::TP_VOCATIONAL_PLACEMENT_RESULT_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Transfer results by unit',
                    'url' => '/teacher/transfer-results-by-unit',
                    'permissions' => [
                        Access::TP_TRANSFER_RESULTS_BY_UNIT_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'Traineeship',
            'url' => '#',
            'gap_after' => true,
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['traineeship-activity', 'activity-between-date', 'activity-between-dates'],
            'svgicon' => '<svg width="20" height="20"  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>',
            'permissions' => [
                Access::TP_TRAINEESHIP_ACCESS->value,
            ],
            'sub_menu' => [
                [
                    'label' => 'Traineeship Activity',
                    'url' => '/teacher/traineeship-activity',
                    'permissions' => [
                        Access::TP_TRAINEESHIP_ACTIVITY_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Activity Between Dates',
                    'url' => '/teacher/activity-between-dates',
                    'permissions' => [
                        Access::TP_ACTIVITY_BETWEEN_DATES_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'Communication',
            'url' => '#',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['mailing-list', 'view-communication-log', 'email-request-to-college'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M17.383 15.5a2.2 2.2 0 0 1-.231-1.5h-10.9a2.25 2.25 0 0 0-2.248 2.249v.578c0 .892.318 1.756.898 2.435c1.566 1.834 3.952 2.74 7.098 2.74q.585 0 1.133-.043a2.26 2.26 0 0 1-.008-1.503q-.54.045-1.125.045c-2.738 0-4.704-.746-5.957-2.213a2.25 2.25 0 0 1-.54-1.462v-.577a.75.75 0 0 1 .75-.75zM12 2.005a5 5 0 1 1 0 10a5 5 0 0 1 0-10m0 1.5a3.5 3.5 0 1 0 0 7a3.5 3.5 0 0 0 0-7m6.192 10.49l.476-1.205c.242-.614.92-.933 1.548-.728l.431.141c.724.237 1.326.806 1.35 1.569c.1 3.11-2.476 7.583-5.213 9.055c-.673.362-1.468.123-2.035-.391l-.337-.305a1.253 1.253 0 0 1-.142-1.706l.8-1.01c.29-.367.767-.53 1.22-.42l1.292.313q1.654-1.095 1.774-3.079l-.917-.964a1.2 1.2 0 0 1-.247-1.27" />
                        </svg>',
            'permissions' => [
                Access::TP_COMMUNICATION_ACCESS->value,
            ],
            'sub_menu' => [
                [
                    'label' => 'Mailing List',
                    'url' => '/teacher/mailing-list',
                    'permissions' => [
                        Access::TP_MAILING_LIST_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Communication Log',
                    'url' => '/teacher/view-communication-log',
                    'permissions' => [
                        Access::TP_COMMUNICATION_LOG_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Request to Admin',
                    'url' => '/teacher/email-request-to-college',
                    'permissions' => [
                        Access::TP_REQUEST_TO_ADMIN_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'Others',
            'url' => '#',
            'mainmenu' => ['profile', 'dashboard', 'document', 'material', 'timesheet', 'compliance', 'traineeship', 'communication', 'reports', 'elearning', 'administration', 'clients'],
            'activeurls' => ['generate-report', 'teacher-evaluation', 'teacher_profile', 'view-teacher-elearning-link-list', 'view-teacher-register-improvement', 'edit-teacher-register-improvement', 'add-teacher-register-improvement', 'leave-info'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7 20L11 4M13 20L17 4M6 9H20M4 15H18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>',
            'sub_menu' => [
                [
                    'label' => 'Reports',
                    'url' => '/teacher/generate-report',
                    'permissions' => [
                        Access::TP_REPORTS_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Elearning Link',
                    'url' => '/teacher/view-teacher-elearning-link-list',
                    'permissions' => [
                        Access::TP_ELEARNING_LINK_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Continuous Improvement',
                    'url' => '/teacher/view-teacher-register-improvement',
                    'subactiveurls' => ['add-teacher-register-improvement', 'edit-teacher-register-improvement'],
                    'permissions' => [
                        Access::TP_CONTINUOUS_IMPROVEMENT_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Leave Info',
                    'url' => '/teacher/leave-info',
                    'permissions' => [
                        Access::TP_LEAVE_INFO_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Evaluation',
                    'url' => '/teacher/teacher-evaluation',
                    'permissions' => [
                        Access::TP_EVALUATION_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Edit Profile',
                    'url' => '/teacher/profile',
                    'permissions' => [
                        Access::TP_EDIT_PROFILE_ACCESS->value,
                    ],
                ],
            ],
        ],
    ];
}
