<script setup>
import { computed } from 'vue';
import FormWrapper from '@spa/components/KendoModals/SidebarDrawer.vue';
import LabelValuePair from '@spa/components/LabelValuePair/LabelValuePair.vue';
import { useRegisterImprovementStore } from '@spa/stores/modules/continuous-improvement/registerImprovementStore.js';
import Badge from '@spa/components/badges/Badge.vue';
import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
import { TextArea } from '@progress/kendo-vue-inputs';
import { convertJsDateFormat } from '@spa/composables/dateTimeComposables.js';

const props = defineProps({
    userType: Number,
});

const isSAdmin = computed(() => {
    return props.userType === 6;
});
const store = useRegisterImprovementStore();
</script>

<template>
    <FormWrapper
        :visibleDialog="store.showDialog"
        :hideOnOverlayClick="false"
        :fixedActionBar="false"
        :width="'50%'"
        :maxWidth="'800px'"
        :style="{
            maxWidth: '800px',
        }"
        @drawerclose="store.showDialog = false"
        :position="'center'"
        :pt="{ content: 'px-5 pt-4 pb-8 space-y-4' }"
    >
        <template #title>
            <div class="text-xl font-medium">
                Improvement Request Details - {{ store?.formData?.requester?.full_name }}
            </div>
        </template>
        <template #content>
            <div class="grid grid-cols-3 gap-6">
                <LabelValuePair
                    v-if="isSAdmin"
                    size="base"
                    :label="'User Type:'"
                    :orientation="'vertical'"
                >
                    <Badge :variant="'default'">{{ store?.formData?.user_type_name }}</Badge>
                </LabelValuePair>
                <LabelValuePair
                    size="base"
                    v-if="isSAdmin && store?.formData?.user_type === 1"
                    :label="'Staff Role:'"
                    :value="store?.formData?.staff_role"
                    :orientation="'vertical'"
                />
                <LabelValuePair
                    v-if="isSAdmin"
                    size="base"
                    :label="'Requested By:'"
                    :value="store?.formData?.requester?.full_name"
                    :orientation="'vertical'"
                />
                <LabelValuePair size="base" :label="'Status:'" :orientation="'vertical'">
                    <Badge :variant="store.formData?.status === '1' ? 'success' : 'error'">{{
                        store.formData?.status === '1' ? 'Open' : 'Closed'
                    }}</Badge>
                </LabelValuePair>
                <LabelValuePair
                    size="base"
                    :label="'Logged Date:'"
                    :value="convertJsDateFormat(store?.formData?.logged_date)"
                    :orientation="'vertical'"
                />
                <LabelValuePair
                    size="base"
                    v-if="isSAdmin"
                    :label="'Logged By:'"
                    :value="store?.formData?.logged_by_name"
                    :orientation="'vertical'"
                />
                <LabelValuePair
                    size="base"
                    :label="'Category:'"
                    :value="store?.formData?.category?.name"
                    :orientation="'vertical'"
                />
                <div class="col-span-3 border-t border-gray-200" />
                <div class="col-span-3 space-y-6">
                    <LabelValuePair size="base" :label="'Case Detail:'" :orientation="'vertical'">
                        <div
                            class="w-full break-all rounded-md border border-gray-200 p-2"
                            v-html="store?.formData?.case_detail || 'N/A'"
                        />
                    </LabelValuePair>
                    <LabelValuePair
                        v-if="isSAdmin"
                        size="base"
                        :label="'Escalated To:'"
                        :value="store?.formData?.escalated_to_name"
                        :orientation="'vertical'"
                    />
                    <LabelValuePair
                        v-if="isSAdmin"
                        size="base"
                        :label="'Action Taken:'"
                        :orientation="'vertical'"
                    >
                        <div
                            class="w-full break-all rounded-md border border-gray-200 p-2"
                            v-html="store?.formData?.action_taken || 'N/A'"
                        />
                    </LabelValuePair>
                    <LabelValuePair
                        v-if="isSAdmin"
                        size="base"
                        :label="'Action Taken To Prevent Reoccurence:'"
                        :orientation="'vertical'"
                    >
                        <div
                            class="w-full break-all rounded-md border border-gray-200 p-2"
                            v-html="store?.formData?.action_taken_prevent || 'N/A'"
                        />
                    </LabelValuePair>
                </div>
            </div>
        </template>
    </FormWrapper>
</template>

<style scoped></style>
