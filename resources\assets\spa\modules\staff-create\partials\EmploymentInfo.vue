<script setup>
import { computed, inject } from 'vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormUploader from '@spa/components/KendoInputs/FormUploader.vue';
import Button from '@spa/components/Buttons/Button.vue';
import { getValidationMessage } from '@spa/composables/formComposables.js';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
import StaffPositionSelect from '@spa/modules/staffposition/StaffPositionSelect.vue';

const props = defineProps({
    modelValue: {},
});
const isEdit = inject('isEdit');
const emit = defineEmits(['update:modelValue']);
const formData = computed({
    get() {
        return props.modelValue || {};
    },
    set(value) {
        emit('update:modelValue', value);
    },
});
</script>
<template>
    <div class="space-y-4">
        <h3 class="mb-6 text-lg font-semibold" v-if="!isEdit">Employment Info</h3>
        <div class="mb-8 grid grid-cols-3 gap-4">
            <div class="col-span-1">
                <FormInput
                    v-model="formData.staff_number"
                    name="staff_number"
                    label="Staff Number"
                    placeholder="Add staff number"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'staff_number')"
                />
            </div>
        </div>
        <div class="mb-8 grid grid-cols-1 gap-4 xl:grid-cols-5">
            <div class="col-span-1">
                <FormDropDown
                    v-model="formData.employment_type"
                    name="employment_type"
                    label="Employment Type"
                    :data-items="[
                        { value: 'full_time', text: 'Full Time' },
                        { value: 'part_time', text: 'Part Time' },
                        { value: 'casual', text: 'Casual' },
                        { value: 'contract', text: 'Contract' },
                    ]"
                    :default-item="{ value: null, text: 'Select Type' }"
                    :value-primitive="true"
                    text-field="text"
                    value-field="value"
                    placeholder="Select"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'employment_type')"
                />
            </div>
            <div class="col-span-1">
                <FormDatePicker
                    v-model="formData.joined_on"
                    name="joined_on"
                    label="Joined On"
                    type="date"
                    emit-format="yyyy-MM-dd"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'joined_on')"
                />
            </div>
            <div class="col-span-1">
                <FormInput
                    v-model="formData.campus_location"
                    name="campus_location"
                    label="Location"
                    placeholder="Add address"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'campus_location')"
                />
            </div>
            <div class="col-span-1">
                <StaffPositionSelect
                    v-model="formData.job_position"
                    name="job_position"
                    label="Job Position"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'job_position')"
                />
                <!--                <FormInput-->
                <!--                    v-model="formData.job_position"-->
                <!--                    name="job_position"-->
                <!--                    label="Job Position"-->
                <!--                    placeholder="Add position"-->
                <!--                    :required="true"-->
                <!--                    v-bind="getValidationMessage(formData, 'job_position')"-->
                <!--                />-->
            </div>
            <div class="col-span-1">
                <FormDropDown
                    v-model="formData.line_manager"
                    name="line_manager"
                    label="Line Manager"
                    :data-items="[
                        { value: 'physics_teacher', text: 'Physics Teacher' },
                        { value: 'ansil_darshaka', text: 'A Ansil Darshaka' },
                    ]"
                    :default-item="{ value: null, text: 'Select Manager' }"
                    :value-primitive="true"
                    text-field="text"
                    value-field="value"
                    placeholder="Select"
                    :required="true"
                    v-bind="getValidationMessage('line_manager')"
                />
            </div>
        </div>
    </div>
</template>

<style scoped></style>
