<?php

namespace App\Repositories;

use App\Exports\TCSI\CasualStaffEstimateTcsiPir;
use App\Exports\TCSI\CasualStaffTcsiPir;
use App\Exports\TCSI\FullTimeStaffTcsiPir;
use App\Exports\TCSI\TCSICampus;
use App\Exports\TCSI\TCSICourse;
use App\Exports\TCSI\TCSICourseAdmission;
use App\Exports\TCSI\TCSICourseAdmissionHDR;
use App\Exports\TCSI\TCSICourseOfStudy;
use App\Exports\TCSI\TCSICourseOnCampus;
use App\Exports\TCSI\TCSIStudent;
use App\Exports\TCSI\TCSIUnitEnrollment;
use App\Model\CollegeCourse;
use App\Model\v2\CampusVenue;
use App\Model\v2\Courses;
use App\Model\v2\Student;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentSubjectEnrolment;
use App\Traits\CommonTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class TcsiRepository
{
    use CommonTrait;

    protected $model;

    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    public function getTcsiReportData($request)
    {
        $data = ($request->input()) ? $request->input() : [];
        $reportingType = $data['submission_type'];
        $isPIR = ($data['tcsi_report_type'] == 1) ? true : false;
    }

    private function convertDateData($postArr)
    {
        return [
            ((! empty($postArr['reporting_year'])) ? $postArr['reporting_year'] : ''),   // year
            ((! empty($postArr['report_start_date'])) ? date('Y-m-d', strtotime($postArr['report_start_date'])) : ''),   // fromDate
            ((! empty($postArr['report_end_date'])) ? date('Y-m-d', strtotime($postArr['report_end_date'])) : ''),   // toDate
        ];
    }

    private function convertAdvanceData($postArr)
    {
        return [
            ((! empty($postArr['status'])) ? $postArr['status'] : ''),   // status
            ((! empty($postArr['course_id'])) ? $postArr['course_id'] : ''),   // course_code
            ((! empty($postArr['student_id'])) ? $postArr['student_id'] : ''),   // generated_stud_id
        ];
    }

    public function getPIRCourseStudyData($postArr)
    {

        [$year, $start, $end] = $this->convertDateData($postArr);
        $columnArr = [
            DB::raw('(CASE WHEN (rto_courses.id > 0) THEN "Course of Study" END) as information_type'),
            'rto_courses.course_code as course_study_code',
            'rto_courses.course_name as course_study_name',
            'rhcd.study_type as course_study_type',
            'rhcd.study_load as course_study_load',
            DB::raw('(CASE WHEN rhcd.combined_course_of_study > 0 THEN "TRUE" ELSE "FALSE" END) as course_study_indicator'),
        ];
        $query = Courses::from('rto_courses')
            ->leftjoin('rto_highered_course_detail as rhcd', 'rhcd.course_id', '=', 'rto_courses.id')
            ->where('rto_courses.college_id', $postArr['college_id']);

        /* Apply Custom Filter Start */
        if (! empty($start) && ! empty($end)) {
            $query->whereBetween('rto_courses.effective_start', [$start, $end])
                ->orWhereBetween('rto_courses.effective_end', [$start, $end])
                ->orWhere(function ($query) use ($start, $end) {
                    $query->where('rto_courses.effective_start', '<', $start)->where('rto_courses.effective_end', '>', $end);
                });
        }
        /* Custom Filter End */

        $resArr = $query->select($columnArr)->get()->toArray();

        return $resArr;
    }

    public function getCourseStudyData($request, $reportingType)
    {

        if ($reportingType == 'hep') {
            return Excel::download(new TCSICourseOfStudy($request), 'HEP-Course.xlsx');
        } elseif ($reportingType == 'vsl') {
            return Excel::download(new TCSICourseOfStudy($request), 'VSL-Course.xlsx');
        } elseif ($reportingType == 'pir') {
            return Excel::download(new TCSICourseOfStudy($request), 'PIR-Course.xlsx');
        }
    }

    public function getPIRCourseData($request, $reportingType)
    {

        if ($reportingType == 'hep') {
            return Excel::download(new TCSICourse($request), 'HEP-Course-of-study.xlsx');
        } elseif ($reportingType == 'vsl') {
            return Excel::download(new TCSICourse($request), 'VSL-Course-of-study.xlsx');
        } elseif ($reportingType == 'pir') {
            return Excel::download(new TCSICourse($request), 'PIR-Course-of-study.xlsx');
        }
    }

    public function getCourseData($postArr)
    {

        [$year, $start, $end] = $this->convertDateData($postArr);
        [$status, $courseCode, $studentCode] = $this->convertAdvanceData($postArr);

        $columnArr = [
            'rto_courses.course_code',
            'rto_courses.course_name',
            'rto_courses.course_duration',
            'rto_courses.couse_duration_type',
            'rto_courses.effective_start',
            'rto_courses.effective_end',
            'rsnt1.sub_narrow_key as education_code',
            'rsnt2.sub_narrow_key as ed_supplementary_code',
            'rhcd.special_course_type',
        ];
        $query = Courses::from('rto_courses')
            ->join('rto_student_courses as rsc', 'rsc.course_id', '=', 'rto_courses.id')
            ->leftjoin('rto_highered_course_detail as rhcd', 'rhcd.course_id', '=', 'rto_courses.id')
            ->leftjoin('rto_sub_narrow_type as rsnt1', 'rsnt1.id', '=', 'rhcd.field_of_education_narrow_sub_type')
            ->leftjoin('rto_sub_narrow_type as rsnt2', 'rsnt2.id', '=', 'rhcd.field_of_education_narrow_sub_type')
            ->where('rto_courses.college_id', $postArr['college_id']);

        /* Apply Custom Filter Start */
        if (! empty($start) && ! empty($end)) {
            $query->whereBetween('rto_courses.effective_start', [$start, $end])
                ->orWhereBetween('rto_courses.effective_end', [$start, $end])
                ->orWhere(function ($query) use ($start, $end) {
                    $query->where('rto_courses.effective_start', '<', $start)->where('rto_courses.effective_end', '>', $end);
                });
        }
        if (! empty($courseCode)) {
            $query->where('rto_courses.course_code', $courseCode);
        }
        /* Custom Filter End */
        $resArr = $query->select($columnArr)->groupBy('rto_courses.id')->get()->toArray();

        $finalRes = [];
        foreach ($resArr as $res) {
            $convertedYear = $this->convertIntoYear($res['course_duration'], $res['couse_duration_type']);
            $finalRes[] = [
                'course_code' => $res['course_code'],
                'course_name' => $res['course_name'],
                'course_duration' => $convertedYear,
                'effective_start' => (! empty($res['effective_start']) ? $res['effective_start'] : ''),
                'effective_end' => (! empty($res['effective_end']) ? $res['effective_end'] : ''),
                'education_code' => $res['education_code'],
                'ed_supplementary_code' => $res['ed_supplementary_code'],
                'spe_course_type' => $res['special_course_type'],
            ];
        }

        return $finalRes;
    }

    private function convertIntoYear($duration, $durationType)
    {
        $convertedYear = '';
        if (! empty($duration) && ! empty($durationType)) {
            if ($durationType == 1) {
                $convertedYear = round($duration / 365, 2);
            } elseif ($durationType == 2) {
                $convertedYear = round($duration / 52, 2);
            } elseif ($durationType == 3) {
                $convertedYear = round($duration / 12, 2);
            } else {
                $convertedYear = round($duration, 2);
            }
        }

        return $convertedYear;
    }

    public function getPIRCampusData($request, $reportingType)
    {

        if ($reportingType == 'hep') {
            return Excel::download(new TCSICampus($request), 'HEP-Campus.xlsx');
        } elseif ($reportingType == 'vsl') {
            return Excel::download(new TCSICampus($request), 'VSL-Campus.xlsx');
        } elseif ($reportingType == 'pir') {
            return Excel::download(new TCSICampus($request), 'PIR-Campus.xlsx');
        }
    }

    public function getCampusData($postArr)
    {

        [$year, $start, $end] = $this->convertDateData($postArr);
        $columnArr = [
            'rv.sub_urb as campus_suburb',
            'country.absvalue as campus_country_code',
            'rv.postcode as campus_postcode',
            'rc.contract_start_date as campus_start_date',
            'rc.contract_end_date as campus_end_date',
        ];
        $query = CampusVenue::from('rto_venue as rv')
            ->join('rto_colleges as rc', 'rc.id', '=', 'rv.college_id')
            ->leftjoin('rto_country as country', 'country.id', '=', 'rv.country')
            ->where('rv.college_id', $postArr['college_id']);

        /* Apply Custom Filter Start */
        if (! empty($start) && ! empty($end)) {
            $query->whereBetween('rto_courses.effective_start', [$start, $end])
                ->orWhereBetween('rto_courses.effective_end', [$start, $end])
                ->orWhere(function ($query) use ($start, $end) {
                    $query->where('rto_courses.effective_start', '<', $start)->where('rto_courses.effective_end', '>', $end);
                });
        }
        /* Custom Filter End */

        $resArr = $query->select($columnArr)->groupBy('rv.id')->get()->toArray();
        foreach ($resArr as $k => $res) {
            $resArr[$k]['campus_start_date'] = (empty($res['campus_start_date']) || $res['campus_start_date'] == '0000-00-00') ? 'N/A' : $res['campus_start_date'];
            $resArr[$k]['campus_end_date'] = (empty($res['campus_end_date']) || $res['campus_end_date'] == '0000-00-00') ? 'N/A' : $res['campus_end_date'];
        }

        return $resArr;
    }

    public function getPIRCourseOnCampusData($request, $reportingType)
    {

        if ($reportingType == 'hep') {
            return Excel::download(new TCSICourseOnCampus($request), 'HEP-Course-on-campus.xlsx');
        } elseif ($reportingType == 'vsl') {
            return Excel::download(new TCSICourseOnCampus($request), 'VSL-Course-on-campus.xlsx');
        } elseif ($reportingType == 'pir') {
            return Excel::download(new TCSICourseOnCampus($request), 'PIR-Course-on-campus.xlsx');
        }
    }

    public function getCourseOnCampusData($postArr)
    {

        [$year, $start, $end] = $this->convertDateData($postArr);
        $columnArr = [
            'rc.id',
            'rc.course_code',
            'rc.effective_end',
            'rc.cricos_code',
            'rc.effective_start',
            'rhcd.type_of_operation as operation_type',
            'rhcd.principal_mode_of_delivery_of_offshore_course as principal_delivery_code',
            'rhcd.offshore_delivery_indicator as delivery_code',
            'rhcd.course_fee_type',
            'rhcd.student_contribution_fee',
            'rc.domestic_fee',
            'rhcd.tac_offer',
        ];
        $query = CollegeCourse::from('rto_course_campus as rcc')
            ->join('rto_courses as rc', 'rc.id', '=', 'rcc.course_id')
            ->leftjoin('rto_highered_course_detail as rhcd', 'rhcd.course_id', '=', 'rc.id')
            ->where('rc.college_id', $postArr['college_id']);

        /* Apply Custom Filter Start */
        if (! empty($start) && ! empty($end)) {
            $query->whereBetween('rc.effective_start', [$start, $end])
                ->orWhereBetween('rc.effective_end', [$start, $end])
                ->orWhere(function ($query) use ($start, $end) {
                    $query->where('rc.effective_start', '<', $start)->where('rc.effective_end', '>', $end);
                });
        }
        /* Custom Filter End */

        $resArr = $query->select($columnArr)->groupBy('rcc.id')->get()->toArray();

        return $resArr;
    }

    public function getPIRStudentData($request, $reportingType)
    {

        if ($reportingType == 'hep') {
            return Excel::download(new TCSIStudent($request), 'HEP-Student.xlsx');
        } elseif ($reportingType == 'vsl') {
            return Excel::download(new TCSIStudent($request), 'VSL-Student.xlsx');
        } elseif ($reportingType == 'pir') {
            return Excel::download(new TCSIStudent($request), 'PIR-Student.xlsx');
        }
    }

    public function getStudentData($postArr)
    {

        [$year, $start, $end] = $this->convertDateData($postArr);
        [$status, $courseCode, $studentCode] = $this->convertAdvanceData($postArr);
        $columnArr = [
            'rs.id',
            'rs.generated_stud_id',
            'rs.DOB as dob',
            DB::raw('(CASE WHEN rs.gender = "male" THEN "M" WHEN rs.gender = "female" THEN "F" ELSE "O" END) AS gender_code'),
            'rsd.is_aus_aboriginal_or_islander as aboriginal_code',
            'rstd.citizenship_code',
            DB::raw('(CASE WHEN rsd.is_english_main_lang = "1" THEN "0001" ELSE rsd.main_lang END) AS language_code'),
            'rc1.absvalue as country_code',
            'rc1.nationality',
            'rs.current_postcode',
            'rs.postal_postcode',
            'rs.family_name',
            'rs.first_name',
            'rs.nickname',
            DB::raw('CONCAT(rs.current_street_no," ", rs.current_street_name) AS current_street'),
            'rs.current_city',
            'rs.current_state',
            'rs.USI as usi',
            'rs.student_type',
            'rstd.year_of_arrival_in_australia as year_of_arrival',
            'rstd.level_left_school',
            'rstd.education_of_parent1',
            'rstd.education_of_parent2',
            'rstd.tax_file_number',
            'rstd.chessn_number',
            DB::raw('GROUP_CONCAT(rtsdi.disability_code) as disability_code'),
            DB::raw('(CASE WHEN (rs.id > 0) THEN "NA" END) as not_clear_fields'),
        ];
        $query = Student::alias('rto_students as rs')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rs.id')
            ->leftjoin('rto_student_tcsi_details as rstd', 'rstd.student_id', '=', 'rs.id')
            ->leftjoin('rto_country as rc1', 'rc1.id', '=', 'rs.current_country')
            ->leftjoin('rto_tcsi_student_disability_info as rtsdi', 'rtsdi.student_id', '=', 'rs.id')
            ->where('rs.college_id', $postArr['college_id']);

        /* Apply Custom Filter Start */
        if (! empty($year)) {
            $query->where(function ($yearQuery) use ($year) {
                $yearQuery->whereYear('rstd.citizenship_effective_from_date', $year);
            });
        }
        if (! empty($start) && ! empty($end)) {
            $query->where(function ($dateRangeQuery) use ($start, $end) {
                $dateRangeQuery->whereBetween('rstd.citizenship_effective_from_date', [$start, $end]);
            });
        }
        if (! empty($studentCode)) {
            $query->where('rs.generated_stud_id', $studentCode);
        }
        /* Custom Filter End */

        $resArr = $query->select($columnArr)->groupBy('rs.id')->get()->toArray();

        return $resArr;
    }

    public function getPIRCourseAdmissionData($request, $reportingType)
    {

        if ($reportingType == 'hep') {
            return Excel::download(new TCSICourseAdmission($request), 'HEP-Course-Admission.xlsx');
        } elseif ($reportingType == 'vsl') {
            return Excel::download(new TCSICourseAdmission($request), 'VSL-Course-Admission.xlsx');
        } elseif ($reportingType == 'pir') {
            return Excel::download(new TCSICourseAdmission($request), 'PIR-Course-Admission.xlsx');
        }
    }

    public function getCourseAdmissionData($postArr)
    {

        [$year, $start, $end] = $this->convertDateData($postArr);
        [$status, $courseCode, $studentCode] = $this->convertAdvanceData($postArr);
        $columnArr = [
            'rsc.id',
            'rs.generated_stud_id',
            'rc.course_code',
            'rtsci.commencement_date',
            'rtsci.course_outcome_code',
            'rtsci.completion_date as course_outcome_date',
            'rtsci.highest_attainment_code',
            'rtsci.atar',
            'rtsci.selection_rank',
            'rtsci.attendance_type',
            'rtsci.thesis_submission_date',
            'rtsci.student_separation_code as hdr_primary_code',
            'rtsci.end_user_engagement_code as hdr_secondary_code',
            'rtsci.basis_admission_code',
            'rtsci.previous_rts_eftsl as credit_used_value',
            'rtsci.credit_provider_code',
            'rsnt.sub_narrow_key as specialisation_code',
        ];
        $query = StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->join('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_tcsi_student_course_info as rtsci', function ($join) {
                $join->on('rtsci.course_id', '=', 'rc.id');
                $join->on('rtsci.student_id', '=', 'rs.id');
            })
            ->leftjoin('rto_sub_narrow_type as rsnt', 'rsnt.id', '=', 'rtsci.narrow_sub_type')
            ->where('rs.college_id', $postArr['college_id']);

        /* Apply Custom Filter Start */
        if (! empty($year)) {
            $query->where(function ($yearQuery) use ($year) {
                $yearQuery->whereYear('rsc.start_date', $year);
                $yearQuery->orWhereYear('rsc.finish_date', $year);
            });
        }
        if (! empty($start) && ! empty($end)) {
            $query->where(function ($dateRangeQuery) use ($start, $end) {
                $dateRangeQuery->whereBetween('rsc.start_date', [$start, $end]);
                $dateRangeQuery->orWhereBetween('rsc.finish_date', [$start, $end]);
            });
        }
        if (! empty($courseCode)) {
            $query->where('rc.course_code', $courseCode);
        }
        if (! empty($studentCode)) {
            $query->where('rs.generated_stud_id', $studentCode);
        }
        /* Custom Filter End */

        $resArr = $query->select($columnArr)->groupBy(['rc.id', 'rs.id'])->get()->toArray();

        return $resArr;
    }

    public function getPIRCourseAdmissionHDRData($request, $reportingType)
    {

        if ($reportingType == 'hep') {
            return Excel::download(new TCSICourseAdmissionHDR($request), 'HEP-Course-Admission-HDR.xlsx');
        } elseif ($reportingType == 'vsl') {
            return Excel::download(new TCSICourseAdmissionHDR($request), 'VSL-Course-Admission-HDR.xlsx');
        } elseif ($reportingType == 'pir') {
            return Excel::download(new TCSICourseAdmissionHDR($request), 'PIR-Course-Admission-HDR.xlsx');
        }
    }

    public function getPIRUnitEnrolmentData($request, $reportingType)
    {

        if ($reportingType == 'hep') {
            return Excel::download(new TCSIUnitEnrollment($request), 'HEP-Unit-Enrollment.xlsx');
        } elseif ($reportingType == 'vsl') {
            return Excel::download(new TCSIUnitEnrollment($request), 'VSL-Unit-Enrollment.xlsx');
        } elseif ($reportingType == 'pir') {
            return Excel::download(new TCSIUnitEnrollment($request), 'PIR-Unit-Enrollment.xlsx');
        }
    }

    public function getUnitEnrolmentData($postArr)
    {

        [$year, $start, $end] = $this->convertDateData($postArr);
        $columnArr = [
            'rsse.id',
            'sub.subject_code',
            'rsnt.sub_narrow_key as discipline_code',
            'sub.is_long_indicator',
            'overseasCountry.name as overseas_country',
            'rs.postal_postcode',
            'rtsue.census_date',
            'rsse.activity_start_date',
            'rsse.final_outcome',
            'rtsue.subject_completion_date',
            'rtsue.work_experience_industry',
            'rtsue.summer_winter_school',
            'rtsue.mode_attendance',
            'rtsue.student_status_code',
            'rtsue.max_stud_contribution',
            'rtsue.remission_reason',
            'rtsue.eftsl_value',
            'rtsue.subject_tuition_fee',
            'rtsue.paid_amount_or_upfront',
            'rtsue.loan_fee',
            'rtsue.help_loan_amount',
            'rtsue.academic_organisational_unit',
        ];
        $query = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
            ->join('rto_students as rs', 'rs.id', '=', 'rsse.student_id')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsse.course_id')
            ->join('rto_subject as sub', 'sub.id', '=', 'rsse.subject_id')
            ->leftjoin('rto_tcsi_student_unit_enrollment as rtsue', 'rtsue.stud_subject_enroll_id', '=', 'rsse.id')
            ->leftjoin('rto_country as overseasCountry', 'overseasCountry.id', '=', 'rs.permanent_country')
            ->leftjoin('rto_sub_narrow_type as rsnt', 'rsnt.id', '=', 'sub.discipline_narrow_sub_type')
            ->where('rs.college_id', $postArr['college_id']);

        /* Apply Custom Filter Start */
        if (! empty($year)) {
            $query->where(function ($yearQuery) use ($year) {
                $yearQuery->whereYear('rsse.activity_start_date', $year);
            });
        }
        if (! empty($start) && ! empty($end)) {
            $query->where(function ($dateRangeQuery) use ($start, $end) {
                $dateRangeQuery->whereBetween('rsse.activity_start_date', [$start, $end]);
            });
        }
        /* Custom Filter End */

        $resArr = $query->select($columnArr)->groupBy('rsse.id')->get()->toArray();

        return $resArr;
    }

    public function getSAHelpLoanData($postArr)
    {

        [$year, $start, $end] = $this->convertDateData($postArr);
        $columnArr = [
            'rs.id',
            'rs.generated_stud_id',
            'rsa.debt_incurral_date',
            'rsa.student_status_code',
            'rsa.amount_charged',
            'rsa.upfront_fee',
            'rsa.help_loan_amount',
        ];
        $query = Student::alias('rto_students as rs')
            ->leftjoin('rto_tcsi_student_sa_help as rsa', 'rsa.student_id', '=', 'rs.id')
            ->where('rs.college_id', $postArr['college_id']);

        /* Apply Custom Filter Start */
        if (! empty($year)) {
            $query->where(function ($yearQuery) use ($year) {
                $yearQuery->whereYear('rsa.debt_incurral_date', $year);
            });
        }
        if (! empty($start) && ! empty($end)) {
            $query->where(function ($dateRangeQuery) use ($start, $end) {
                $dateRangeQuery->whereBetween('rsa.debt_incurral_date', [$start, $end]);
            });
        }
        /* Custom Filter End */

        $resArr = $query->select($columnArr)->groupBy('rs.id')->get()->toArray();

        return $resArr;
    }

    public function getOSHelpLoanData($postArr)
    {

        [$year, $start, $end] = $this->convertDateData($postArr);
        $columnArr = [
            'rs.id',
            'rs.generated_stud_id',
            'ros.debt_incurral_date',
            'ros.student_status_code',
            'ros.payment_amount',
            'ros.study_period_commencement_date',
            'ros.loan_fee',
            'ros.primary_study_country',
            'ros.secondary_study_country',
            'ros.language_study_commencement_date',
            'ros.language',
        ];
        $query = Student::alias('rto_students as rs')
            ->leftjoin('rto_tcsi_student_os_help as ros', 'ros.student_id', '=', 'rs.id')
            ->where('rs.college_id', $postArr['college_id']);

        /* Apply Custom Filter Start */
        if (! empty($year)) {
            $query->where(function ($yearQuery) use ($year) {
                $yearQuery->whereYear('ros.debt_incurral_date', $year);
            });
        }
        if (! empty($start) && ! empty($end)) {
            $query->where(function ($dateRangeQuery) use ($start, $end) {
                $dateRangeQuery->whereBetween('ros.debt_incurral_date', [$start, $end]);
            });
        }
        /* Custom Filter End */

        $resArr = $query->select($columnArr)->groupBy('rs.id')->get()->toArray();

        return $resArr;
    }

    public function getFullTimeStaffData($request, $reportingType)
    {
        if ($reportingType == 'hep') {
            return Excel::download(new FullTimeStaffTcsiPir($request), 'Full-Time-Staff(TCSI-HEP).xlsx');
        } elseif ($reportingType == 'vsl') {
            return Excel::download(new FullTimeStaffTcsiPir($request), 'Full-Time-Staff(TCSI-VSL).xlsx');
        } elseif ($reportingType == 'pir') {
            return Excel::download(new FullTimeStaffTcsiPir($request), 'Full-Time-Staff(TCSI-PIR).xlsx');
        }

    }

    public function getCasualStaffData($request, $reportingType)
    {

        if ($reportingType == 'hep') {
            return Excel::download(new CasualStaffTcsiPir($request), 'Full-Time-Staff(TCSI-HEP).xlsx');
        } elseif ($reportingType == 'vsl') {
            return Excel::download(new CasualStaffTcsiPir($request), 'Full-Time-Staff(TCSI-VSL).xlsx');
        } elseif ($reportingType == 'pir') {
            return Excel::download(new CasualStaffTcsiPir($request), 'Full-Time-Staff(TCSI-PIR).xlsx');
        }
    }

    public function getCasualStaffEstimateData($request, $reportingType)
    {

        if ($reportingType == 'hep') {
            return Excel::download(new CasualStaffEstimateTcsiPir($request), 'Full-Time-Staff(TCSI-HEP).xlsx');
        } elseif ($reportingType == 'vsl') {
            return Excel::download(new CasualStaffEstimateTcsiPir($request), 'Full-Time-Staff(TCSI-VSL).xlsx');
        } elseif ($reportingType == 'pir') {
            return Excel::download(new CasualStaffEstimateTcsiPir($request), 'Full-Time-Staff(TCSI-PIR).xlsx');
        }
    }
}
