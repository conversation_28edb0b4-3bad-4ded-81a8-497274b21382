<?php

// $arrIntakeYearData[''] = '- - Select Intake year - -';
for ($i = date('Y'); $i < (date('Y') + 10); $i++) {
    $arrIntakeYearData[$i] = $i;
}
for ($i = date('Y') - 5; $i < (date('Y') + 10); $i++) {
    $arrEditIntakeYearData[$i] = $i;
}

for ($i = date('Y'); $i > (date('Y') - 5); $i--) {
    $arrYearData[$i] = $i;
}

$arrHour[''] = '- - Select Hour - -';
for ($i = 1; $i <= 24; $i++) {
    $arrHour[$i] = $i;
}

for ($i = 1; $i <= 12; $i++) {
    $arrMonth[$i] = $i;
}

$arrMinute[''] = '- - Select Minute - -';
for ($i = 0; $i <= 60; $i++) {
    $i += 4;
    $arrMinute[$i] = $i;
}

$arrDuration[''] = '- - Select Duration - -';
for ($i = 1; $i <= 60; $i++) {
    $arrDuration[$i] = $i;
}

$arrArrivialYear[''] = '- - Select Ariivial Year - -';
$arrArrivialYear['0000'] = '(1)Student/Applicant never arrived in Australia';
$arrArrivialYear['0001'] = '(2)Student/Applicant was born in Australia';
$arrArrivialYear['A998'] = '(4)No information on year of arrival';
$arrArrivialYear['A999'] = '(5)No information on whether student/applicant was born in Australia or not​​';

for ($k = 1940; $k < date('Y'); $k++) {
    $arrArrivialYear[$k] = $k;
}

$paymentHistoryYear[''] = '- - Select Year - -';
for ($k = 2017; $k <= date('Y'); $k++) {
    $paymentHistoryYear[$k] = $k;
}

$arrReportingYear = [];
for ($k = date('Y'); $k >= 2017; $k--) {
    $arrReportingYear["$k"] = "$k";
}

$defaultPath = public_path().'/uploads/{college_id}/';
$viewPath = '/uploads/{college_id}/';

return [

    'LOW_PRIORITY' => 1,
    'MEDIUM_PRIORITY' => 2,
    'HIGH_PRIORITY' => 3,
    'HIGHER_PRIORITY' => 4,
    'QUEUE_DELETE_WEEK' => 2,
    // Current Date Format
    'dateFormat' => 'm/d/Y',

    'default_college_timezone' => 'Australia/Sydney',

    // 'default_payment_mode' => '1', //For EFT

    // Census date configuration
    'census_date_days_after_start' => 20, // Default days to add to activity_start_date for census_date

    'arrTimeZoneList' => [
        'Australia/Adelaide' => 'Australia/Adelaide',
        'Australia/Brisbane' => 'Australia/Brisbane',
        'Australia/Broken_Hill' => 'Australia/Broken_Hill',
        'Australia/Darwin' => 'Australia/Darwin',
        'Australia/Eucla' => 'Australia/Eucla',
        'Australia/Hobart' => 'Australia/Hobart',
        'Australia/Lindeman' => 'Australia/Lindeman',
        'Australia/Lord_Howe' => 'Australia/Lord_Howe',
        'Australia/Melbourne' => 'Australia/Melbourne',
        'Australia/Perth' => 'Australia/Perth',
        'Australia/Sydney' => 'Australia/Sydney',
    ],

    // String encrypt and decrypt via below key
    'encrypt_decrypt_key' => 'galaxy360',
    'google_place_key' => env('VITE_GOOGLE_API_KEY'),
    // PerPage Listing for Listing
    'pagination' => ['perPage' => '15'],

    'defaultThumbText' => 'thumb-',        // Use for uploaded image's thumb generate with this prefix word
    'smallThumbText' => 'smallThumb-',   // Use for uploaded image's small thumb generate with this prefix word
    'defaultThumbSize' => '75',            // Use for uploaded image's medium thumb generate (75X75)
    'smallThumbSize' => '25',            // Use for uploaded image's small thumb generate (25X25)
    'defaultProfileSize' => '170',

    'uploadFilePath' => [
        'CollegeMarterials' => ['default' => $defaultPath.'college_marterials/',      'view' => $viewPath.'college_marterials/'],
        'StudentMailAttach' => ['default' => $defaultPath.'StudentMailAttach/',       'view' => $viewPath.'StudentMailAttach/'],
        'StaffMailAttach' => ['default' => $defaultPath.'StaffMailAttach/',         'view' => $viewPath.'StaffMailAttach/'],
        'AgentMailAttach' => ['default' => $defaultPath.'AgentMailAttach/',         'view' => $viewPath.'AgentMailAttach/'],
        'StudentFiles' => ['default' => $defaultPath.'StudentFiles/',            'view' => $viewPath.'StudentFiles/'],
        'AgentFiles' => ['default' => $defaultPath.'AgentFiles/',              'view' => $viewPath.'AgentFiles/'],
        'OfferFiles' => ['default' => $defaultPath.'OfferFiles/',              'view' => $viewPath.'OfferFiles/'],
        'ApplicationForm' => ['default' => $defaultPath.'ApplicationForm/',         'view' => $viewPath.'ApplicationForm/'],
        'LetterFile' => ['default' => $defaultPath.'LetterFile/',              'view' => $viewPath.'LetterFile/'],
        'StudentCOE' => ['default' => $defaultPath.'StudentCOE/',              'view' => $viewPath.'StudentCOE/'],
        'CollegeLogo' => ['default' => $defaultPath.'CollegeLogo/',             'view' => $viewPath.'CollegeLogo/'],
        'Users' => ['default' => $defaultPath.'Users/',                   'view' => $viewPath.'Users/'],
        'SignedOfferLetter' => ['default' => $defaultPath.'SignedOfferLetter/',       'view' => $viewPath.'SignedOfferLetter/'],
        'GteDocument' => ['default' => $defaultPath.'GteDocument/',             'view' => $viewPath.'GteDocument/'],
        'GtePayment' => ['default' => $defaultPath.'GtePayment/',              'view' => $viewPath.'GtePayment/'],
        'ConfirmationOfEnrollment' => ['default' => $defaultPath.'ConfirmationOfEnrollment/', 'view' => $viewPath.'ConfirmationOfEnrollment/'],
        'GteVisa' => ['default' => $defaultPath.'GteVisa/',                 'view' => $viewPath.'GteVisa/'],
        'LetterSetting' => ['default' => $defaultPath.'LetterSetting/',           'view' => $viewPath.'LetterSetting/'],
        'StudentPics' => ['default' => $defaultPath.'StudentPics/',             'view' => $viewPath.'StudentPics/'],
        'StudentNotes' => ['default' => $defaultPath.'StudentNotes/',            'view' => $viewPath.'StudentNotes/'],
        'PrismSetup' => ['default' => $defaultPath.'PrismSetup/',              'view' => $viewPath.'PrismSetup/'],
        'Templates' => ['default' => $defaultPath.'Templates/',               'view' => $viewPath.'Templates/'],
        'USIFile' => ['default' => $defaultPath.'USIFile/',                 'view' => $viewPath.'USIFile/'],
        'TempMailAttachment' => ['default' => $defaultPath.'TempMailAttachment/',      'view' => $viewPath.'TempMailAttachment/'],
        'SubjectMarterials' => ['default' => $defaultPath.'SubjectMarterials/',       'view' => $viewPath.'SubjectMarterials/'],
        'CourseMarterials' => ['default' => $defaultPath.'CourseMarterials/',        'view' => $viewPath.'CourseMarterials/'],
        'CourseCoverImages' => ['default' => $defaultPath.'CourseCoverImages/',       'view' => $viewPath.'CourseCoverImages/'],
        'AVETMISS' => ['default' => $defaultPath.'AVETMISS/',                'view' => $viewPath.'AVETMISS/'],
        'NVR-Report' => ['default' => $defaultPath.'NVR-Report/',              'view' => $viewPath.'NVR-Report/'],
        'VetFeeHelp' => ['default' => $defaultPath.'VetFeeHelp/',              'view' => $viewPath.'VetFeeHelp/'],
        'TeacherInvoice' => ['default' => $defaultPath.'TeacherInvoice/',          'view' => $viewPath.'TeacherInvoice/'],
        'TraineeshipActivity' => ['default' => $defaultPath.'TraineeshipActivity/',     'view' => $viewPath.'TraineeshipActivity/'],
        'TaskFile' => ['default' => $defaultPath.'Taskfile/',                'view' => $viewPath.'Taskfile/'],
        'StudentCertificate' => ['default' => $defaultPath.'student_certificate/',     'view' => $viewPath.'student_certificate/'],
        'StudentLetterZip' => ['default' => $defaultPath.'LetterZip/',               'view' => $viewPath.'LetterZip/'],
        'StudentLetterSetting' => ['default' => $defaultPath.'StudentLetterSetting/',    'view' => $viewPath.'StudentLetterSetting/'],
        'StudentLetterTextarea' => ['default' => $defaultPath.'LetterTextarea/',          'view' => $viewPath.'LetterTextarea/'],
        'StudentEmailTextarea' => ['default' => $defaultPath.'EmailTextarea/',           'view' => $viewPath.'EmailTextarea/'],
        'AvetmissImport' => ['default' => $defaultPath.'AvetmissImport/',          'view' => $viewPath.'AvetmissImport/'],
        'SendEmailTemp' => ['default' => $defaultPath.'SendEmailTemp/',           'view' => $viewPath.'SendEmailTemp/'],
        'TempFiles' => ['default' => $defaultPath.'TempFiles/',               'view' => $viewPath.'TempFiles/'],
        'Intervention' => ['default' => $defaultPath.'Intervention/',         'view' => $viewPath.'Intervention/'],
        'TimetableImport' => ['default' => $defaultPath.'TimetableImport/',         'view' => $viewPath.'TimetableImport/'],
        'DeferStudentDoc' => ['default' => $defaultPath.'DeferDocuments/',         'view' => $viewPath.'DeferDocuments/'],
        'ExtraDocuments' => ['default' => $defaultPath.'ExtraDocuments/',         'view' => $viewPath.'ExtraDocuments/'],
        'StudentCard' => ['default' => $defaultPath.'student_card/',     'view' => $viewPath.'student_card/'],
    ],

    'foldersAllowedForPublicAccess' => ['CollegeLogo'],

    'SMSApiKey' => '68MhDU_eTwGcodIKZRzLAg==',
    'sendSmsUrl' => 'https://platform.clickatell.com/messages/http/send',
    // SetUp Controller - OSHCInfo
    'arrOSHCType' => [
        '' => '- - Select Type - -',
        'Family' => 'Family',
        'Single' => 'Single',
        'Couple' => 'Couple',
    ],
    // SetUp Controller - Offer Document checklist
    'arrDocumentType' => [
        '' => '- - Select Document Type - -',
        '1' => 'In_Application',
        '2' => 'Post_Application',
    ],
    'arrStudentOrigin' => [
        'Offshore' => 'Overseas Student (Offshore)',
        'Onshore' => 'Overseas Student in Australia (Onshore)',
        'Domestic' => 'Resident Student (Domestic)',
    ],
    // StudentsController - Apply-online
    'arrNameTitle' => [
        'Mr.' => 'Mr.',
        'Ms.' => 'Ms.',
        'Miss.' => 'Miss.',
        'Mrs.' => 'Mrs.',
        'Dr.' => 'Dr.',
    ],
    // StudentsController - Apply-online-step2
    'arrMainLang' => [
        '1' => 'French',
        '2' => 'German',
        '3' => 'Chinese',
        '4' => 'Other',
    ],
    'arrEngSpeakLevel' => [
        '1' => '1 -Very well',
        '2' => '2 -Well',
        '3' => '3 -Not well',
        '4' => '4 -Not at all',
        '@' => '@ -Not stated',
    ],
    'arrEngSpeakLevelFront' => [
        '1' => 'Very well',
        '2' => 'Well',
        '3' => 'Not well',
        '4' => 'Not at all',
        '@' => 'Not stated',
    ],
    'arrSchoolLevel' => [
        '02' => '02 -Did not go to school',
        '08' => '08 -Year 8 or below',
        '09' => '09 -Year 9 or equivalent',
        '10' => '10 -Completed year 10',
        '11' => '11 -Completed Year 11',
        '10' => '10 -Completed Year 10',
        '12' => '12 -Completed Year 12',
        '@@' => '@@ -Not Specified',
    ],
    // StudentsController - Apply-online-step4
    'arrHereAbout' => [
        '' => '- - Select - -',
        '1' => 'Press & Print media',
        '2' => 'Internet',
        '3' => 'Agent',
        '4' => 'Friend',
        '5' => 'Other',
    ],
    'arrAccountManager' => [
        '' => '- - Select Manager- -',
        '1' => 'Manager 1',
        '2' => 'Manager 2',
    ],
    // CourseController - coursesIntakeDateAdd
    'arrIntakeReceiver' => [
        'International' => 'International',
        'Local' => 'Local',
        'Both' => 'Both',
    ],
    // CourseController - CoursesAdd - CoursesEdit
    'arrCourseDurationType' => [
        '' => '--Select Duration type--',
        '1' => 'Day',
        '2' => 'Week',
        '3' => 'Month',
        '4' => 'Year',
    ],
    'arrCourseDeliveryType' => [
        // '' => '--Select Delivery Type--',
        'Full Time' => 'Full Time',
        'Part Time' => 'Part Time',
        'Both' => 'Both',
    ],
    'arrCourseDeliveryTypeNew' => [
        // '' => '--Select Delivery Type--',
        '1' => 'Full Time',
        '2' => 'Part Time',
        '3' => 'Both',
    ],
    'arrCourseCompletionType' => [
        // '' => '--Select Course Completion Type--',
        '1' => 'Subject Count',
        '2' => 'Credit Points',
    ],
    'arrSpecialCourseTypeName' => [
        'HigherEd',
        'highered',
    ],
    'arrDeliveryTarget' => [
        '' => '- - Choose Delivery Target - -',
        'InternationalOffShore' => 'International (Offshore)',
        'InternationalOnshore' => 'International (Onshore)',
        'International' => 'International (Both Onshore and Offshore)',
        'Domestic' => 'Domestic Only',
        'Both' => 'All Origins',
    ],
    'arrResultsCalculationMethod' => [
        '' => '- - Choose Result Calculation Method - -',
        '1' => 'Subject Based',
        '2' => 'Unit Based',
        '3' => 'Unit Based &amp; Across Subject',
        '4' => 'Element or Performance Criteria &amp; Across Subject Based',
    ],
    'arrRecipient' => [
        '' => '- - Choose Recipient - -',
        '3' => 'Agent',
        '6' => 'Lead',
        '5' => 'Staff',
        '1' => 'Student',
        '2' => 'Student_Course',
        '4' => 'Student for Trainer',
    ],
    'arrTemplateRecipient' => [
        '3' => 'Agent',
        '6' => 'Lead',
        '5' => 'Staff',
        '1' => 'Student',
        '2' => 'Student_Course',
        '4' => 'Student for Trainer',
    ],
    'arrServiceName' => [
        // '0' => '- - Choose Services Name - -',
        '1' => 'Airport Pickup',
        '2' => 'Guardianship',
        '3' => 'Homestay Placement',
        '4' => 'Homestay Accommodation',
        '5' => 'Homestay Placement (Guardian)',
        '6' => 'Homestay Accommodation (Guardian)',
    ],
    'arrGradingType' => [
        '' => '- - Choose Gradding Type - -',
        '1' => 'Competency Grading',
        '2' => 'Marks Grading',
    ],
    // 'arrDeliveryMode' => [
    //     // '' => '- - Choose Delivery Mode - -',
    //     '10' => '10 -Classroom based',
    //     '20' => '20 -Electronic based',
    //     '30' => '30 -Employment based',
    //     '40' => '40 -Other delivery (e.g. correspondence)',
    //     '90' => '90 -Not applicable - recognition of prior learning/ recognition of current competency/ credit transfer',
    // ],
    // course subject
    'arrStage' => [
        '' => '- - Select Stage - -',
        '1' => '0',
        '2' => '1',
    ],
    'arrSubjectType' => [
        // '' => "- - Select Subject Type - -",
        '1' => 'Core',
        '2' => 'Elective',
    ],
    'arrStaffPosition' => [
        '' => '- - Select Position - -',
        '1' => 'Admin',
        '2' => 'Head Of Operations',
        '3' => 'Marketing Manager',
        '4' => 'Marketing Officer',
        '5' => 'Principal',
        '8' => 'Supervisor',
        '6' => 'Support Officer',
        '7' => 'Teacher',
    ],
    'arrRoleType' => [
        '1' => 'Account',
        '2' => 'Admin',
        '3' => 'DOS',
        '4' => 'DOS-ELICOS',
        '5' => 'DOS-HS',
        '6' => 'DOS-VET',
        '7' => 'IT',
        '8' => 'Marketing',
        '9' => 'Sadmin',
        '10' => 'Staff',
        '11' => 'StudentServices',
        '12' => 'Teacher',
        '13' => 'Student',
        '14' => 'Agent',
    ],
    'countryLevel' => [
        '0' => '0',
        '1' => '1',
        '2' => '2',
        '3' => '3',
        '4' => '4',
        '5' => '5',
        '6' => '6',
        '7' => '7',
        '8' => '8',
        '9' => '9',
    ],
    'region' => [
        '' => '- - Select Region - -',
        '0' => 'Americans',
        '1' => 'North Africa and the Middle East',
        '2' => 'North-East Asia',
        '3' => 'North-West Europe',
        '4' => 'North Specified',
        '5' => 'Oceania and Antartica',
        '6' => 'South-East Asia',
        '7' => 'Southern and Central Asia',
        '8' => 'Southern and Eastern Europe',
        '9' => 'Sub-Saharan Africa',
    ],
    'certificateType' => [
        '' => '- - Select Certificate Type - -',
        '1' => 'Certificate',
        '2' => 'Duplicate Certificate',
        '3' => 'Statement of Attainment',
        '4' => 'Intrim Transcript',
        '5' => 'Record of Result',
        '6' => 'Certificate of Participation',
    ],
    'defaultCertificateType' => '3',
    'generateCertificateList' => [
        'C' => 'Certificate',
        'SOA' => 'Statement of Attainment (SOA) ',
        'TOCA' => 'Record of Result',
        'CL' => 'Completion Letter',
        'TOGA' => 'Interim Transcript',
        'UTOGA' => 'Unofficial Interim Transcript',
    ],
    'generateCertificateListType' => [
        'C' => '1',
        'CL' => '2',
        'SOA' => '3',
        'TOGA' => '4',
        'TOCA' => '5',
        'UTOGA' => '4',
    ],
    'arrTeacherRoleType' => [
        '12' => 'Teacher',
    ],
    'arrStaffRoleType' => [
        '10' => 'Staff',
    ],
    'arrCalendarType' => [
        '' => '- - Select Calendar Type - -',
        '1' => 'Calendar Set1',
        '2' => 'Calendar Set2',
        '3' => 'Calendar Set3',
        '4' => 'Calendar Set4',
        '5' => 'Calendar Set5',
        '6' => 'Calendar Set6',
        '7' => 'Calendar Set7',
        '8' => 'Calendar Set8',
        '9' => 'Calendar Set9',
        '10' => 'Calendar Set10',
        '11' => 'Calendar Set11',
        '12' => 'Calendar Set12',
        '13' => 'Calendar Set13',
        '14' => 'Calendar Set14',
        '15' => 'Calendar Set15',
        '16' => 'Calendar Set16',
        '17' => 'Calendar Set17',
        '18' => 'Calendar Set18',
        '19' => 'Calendar Set19',
        '20' => 'Calendar Set20',
        '21' => 'Calendar Set21',
        '22' => 'Calendar Set22',
        '23' => 'Calendar Set23',
        '24' => 'Calendar Set24',
        '25' => 'Calendar Set25',
        '26' => 'Calendar Set26',
        '27' => 'Calendar Set27',
        '28' => 'Calendar Set28',
        '29' => 'Calendar Set29',
        '30' => 'Calendar Set30',
    ],
    'arrCalendarTypeV2' => [
        '1' => 'Calendar Set1',
        '2' => 'Calendar Set2',
        '3' => 'Calendar Set3',
        '4' => 'Calendar Set4',
        '5' => 'Calendar Set5',
        '6' => 'Calendar Set6',
        '7' => 'Calendar Set7',
        '8' => 'Calendar Set8',
        '9' => 'Calendar Set9',
        '10' => 'Calendar Set10',
        '11' => 'Calendar Set11',
        '12' => 'Calendar Set12',
        '13' => 'Calendar Set13',
        '14' => 'Calendar Set14',
        '15' => 'Calendar Set15',
        '16' => 'Calendar Set16',
        '17' => 'Calendar Set17',
        '18' => 'Calendar Set18',
        '19' => 'Calendar Set19',
        '20' => 'Calendar Set20',
        '21' => 'Calendar Set21',
        '22' => 'Calendar Set22',
        '23' => 'Calendar Set23',
        '24' => 'Calendar Set24',
        '25' => 'Calendar Set25',
        '26' => 'Calendar Set26',
        '27' => 'Calendar Set27',
        '28' => 'Calendar Set28',
        '29' => 'Calendar Set29',
        '30' => 'Calendar Set30',
    ],
    'arrResultGradeFinalOutComeCode' => [
        '' => '- - Select Result Grade Final Out Come - -',
        '20' => '20 - (Competency achived / Pass)',
        '30' => '30 - (Competency not achived / Fail)',
    ],
    /* Claim traking Year array */
    'arrClaimStageYear' => [
        '' => '- - Select Claim Stage Year - -',
        '1' => '1',
        '2' => '2',
        '3' => '3',
    ],
    /* Claim traking Status array */
    'arrClaimStageStatus' => [
        '' => '- - Select Claim Stage Status - -',
        '1' => 'Lodged',
        '2' => 'Rejected',
        '3' => 'Paid',
        '4' => 'Do not claim',
        '5' => 'Return',
    ],
    'arrState' => [
        //        '' => '- - Select State - -',
        '01' => 'NSW',
        '02' => 'VIC',
        '03' => 'QLD',
        '04' => 'SA',
        '05' => 'WA',
        '06' => 'TAS',
        '07' => 'NT',
        '08' => 'ACT',
        '09' => 'Other-Overseas',
        '00' => 'All',
    ],

    'arrContractState' => [
        '01' => 'NSW',
        '02' => 'VIC',
        '03' => 'QLD',
        '04' => 'SA',
        '05' => 'WA',
        '06' => 'TAS',
        '07' => 'NT',
        '08' => 'ACT',
    ],
    'onboardSetupStateList' => [
        'NSW_TOID' => 'NSW',
        'VIC_TOID' => 'VIC',
        'QLD_TOID' => 'QLD',
        'SA_TOID' => 'SA',
        'WA_TOID' => 'WA',
        'TAS_TOID' => 'TAS',
        'NT_TOID' => 'NT',
        'ACT_TOID' => 'ACT',
        // '' => 'Other-Overseas'
    ],
    'arrStateList' => [
        //        '' => '- - Select State - -',
        '01' => 'NSW',
        '02' => 'VIC',
        '03' => 'QLD',
        '04' => 'SA',
        '05' => 'WA',
        '06' => 'TAS',
        '07' => 'NT',
        '08' => 'ACT',
        '09' => 'Other-Overseas',
    ],
    'arrFundingSource' => [
        '' => '- - Select Funding Source - -',
        '01C' => '01C |  Literacy',
        '01E' => '01E |  Equity Program',
        '01I' => '01I |  Industry Priority Program',
        '01J' => '01J |  Recurrent Funding (Training Priorities)',
        '01K' => '01K |  New Apprenticeships',
        '01L' => '01L |  Pre-Employment and Accelerated Trade Training',
        '01M' => '01M |  Community Response Program',
        '01N' => '01N |  VET in Schools',
        '01Q' => '01Q |  Re-Skilling and Up-Skilling',
        '01R' => '01R |  Oil and Gas Skilling',
        '01U' => '01U |  Australians Working Together',
        '01V' => '01V |  VET in Schools for Remote Students',
        '01W' => '01W |  Pre-Apprenticeships',
        '01Y' => '01Y |  Build Skills',
        '01Z' => '01Z |  Other recurrent funding',
        '02E' => '02E |  Indigenous Education Strategic Initiatives Program (IESIP)',
        '02S' => '02S |  Training for Remote Youth',
        '02Z' => '02Z |  Other specific funding',
        '03A' => '03A |  Fee for service enrolments',
        '03K' => '03K |  Fee for Service New Apprenticeships',
        '04A' => '04A |  Overseas full fee paying enrolments',
        '1' => '1 |  T01 TAFE Apprentice',
        '10' => '10 |  SAW - Profile Funded',
        '11' => '11 |  SAW - Competitively Allocated Training',
        '11B' => '11B |  Indigenous Response Program (IRP) - formerly Flexible Response Funding (FRF)',
        '11C' => '11C |  Productivity Places Program - Existing Workers',
        '11D' => '11D |  Government Initiatives',
        '11E' => '11E |  Equity Program',
        '11J' => '11J |  Recurrent',
        '11K' => '11K |  User Choice',
        '11L' => '11L |  Building Better Schools (Initiatives)',
        '11M' => '11M |  Community Response Program',
        '11N' => '11N |  VET in schools for Urban Students',
        '11P' => '11P |  Buildign Better Schools (VETiS)',
        '11Q' => '11Q |  Workready Program',
        '11S' => '11S |  Training for Remote Youth',
        '11U' => '11U |  Australians Working Together',
        '11V' => '11V |  VET in Schools for Remote Students',
        '11W' => '11W |  Pre-Employment',
        '11Y' => '11Y |  Projects',
        '11Z' => '11Z |  Other Recurrent Funding',
        '12' => '12 |  SAW - Special Projects',
        '13' => '13 |  Commonwealth Specific Programs',
        '13A' => '13A |  Joint Indigenous Funding Pool',
        '13C' => '13C |  Productivity Places Program Job Seekers',
        '13E' => '13E |  Indigenous Education Strategic Initiatives Program',
        '13Z' => '13Z |  Other specific funding - National',
        '14' => '14 |  SAW - User Choice',
        '15Z' => '15Z |  Other State Specific Purpose Programs',
        '1GT' => '1GT |  Gap Training',
        '1SA' => '1SA |  Skills Assessment',
        '2' => '2 |  T02 TAFE Trainee',
        '20' => '20 |  Domestic Fee For Service',
        '200' => '200 |  Signature Projects - Full Qualification',
        '201' => '201 |  Signature Projects - Skills Sets',
        '202' => '202 |  Skills Development & PCID',
        '204' => '204 |  Pathways for Youth at Risk',
        '205' => '205 |  Work Ready Training',
        '206' => '206 |  Skills fund special release - Full qualification',
        '207' => '207 |  Skills Fund Special Release - Skills sets for Tasmania',
        '208' => '208 |  Signature Projects Grants',
        '20A' => '20A |  Australian Full Fee-Paying (Fee for Service) Client',
        '20K' => '20K |  Australian Full Fee-Paying (Fee for Service) Apprenticeship or Traineeship',
        '21' => '21 |  T21 TAFE SBA',
        '23' => '23 |  Non-SAW - Commonwealth Funded Specific Purpose',
        '25' => '25 |  Non-SAW - State Funded Specific Purpose',
        '2GT' => '2GT |  Gap Training',
        '2SA' => '2SA |  Skills Assessment',
        // '3' => '3 |  Fee For Service',
        '30' => '30 |  The Skills Fund – Existing Workers – Skill Sets',
        '30A' => '30A |  International Full Fee-Paying (Fee for Service) Client',
        '31' => '31 |  The Skills Fund – Existing Workers – Full Qualification',
        '32' => '32 |  The Skills Fund – Job Seekers – Full Qualification',
        '33' => '33 |  The Skills fund – Mind the Gap',
        '34' => '34 |  The Skills Fund – Mind the Gap – Full Qualification',
        '35' => '35 |  Domestic Fee for Service',
        '36' => '36 |  Bridging and Foundation Skills – Skill Sets',
        '37' => '37 |  Career Start',
        '38' => '38 |  Skills Fund Projects – Full Qualification',
        '39' => '39 |  Skills Fund Projects – Skills Sets',
        '40' => '40 |  International Fee for Service',
        '50' => '50 |  Cadetship program',
        '51' => '51 |  Seasonal Industry - Skill Sets',
        '52' => '52 |  PPP - Existing worker Productivity Places Program - Exisitng Workers',
        '55' => '55 |  Productivity Placement Program – Job Seekers',
        '56' => '56 |  Productivity Placement Program – Existing Workers',
        '57' => '57 |  Productivity Placement Program – Job Seekers – Skill Sets',
        '58' => '58 |  Productivity Placement Program – Existing Workers – Skill Sets',
        '59' => '59 |  Career Start –LLN and Employability Skills',
        '60' => '60 |  Non-TAFE User choice funding',
        '65' => '65 |  VET in Schools',
        '70' => '70 |  Non-TAFE Tas Skills and Skills Equip funding',
        '71' => '71 |  Non-TAFE Equity Grants Program',
        '72' => '72 |  Other State Specific',
        '73' => '73 |  Single and Teenage Parent Program',
        '74' => '74 |  Skills Equip Programs',
        '75' => '75 |  Non-TAFE Environmental Tourism Competitive bids funding',
        '76' => '76 |  Other State Projects – full qualification',
        '77' => '77 |  E-Learning Grants',
        '78' => '78 |  VET in Schools – Non-Government Schools',
        '79' => '79 |  Skills Equip - Full Qualification',
        '7CG' => '7CG |  Construction - General Gap Training rolled over from 2007-2008 financial year',
        '7CO' => '7CO |  Construction - Offsite Gap Training rolled over from 2007-2008 financial year',
        '7CS' => '7CS |  Construction - Services Gap Training rolled over from 2007-2008 financial year',
        '7GT' => '7GT |  Gap Training, delivered against Skills Assessments undertaken under prior Construction Skills Queensland contracts for Skills Assessment and Gap Training',
        '7OG' => '7OG |  Other Provider General',
        '80' => '80 |  Subcontracted from another RTO',
        '80A' => '80A |  Revenue earned from another Registered Training Organisation',
        '85' => '85 |  DEST Directly Funded Programs eg. New Apprenticeship Access Program',
        '8CG' => '8CG |  Construction - General',
        '8CO' => '8CO |  Construction - Offsite',
        '8CS' => '8CS |  Construction - Services',
        '8GT' => '8GT |  Gap Training, delivered against Skills Assessments undertaken under this 2008/09 contract for Skills Assessment and Gap Training',
        '8SA' => '8SA |  Skills Assessments',
        '90' => '90 |  Private RTO - Overseas Full fee paying student',
        '95' => '95 |  Revenue earned through sub contracting arrangements with another registered training organisation',
        '9GT' => '9GT |  Gap Training',
        '9SA' => '9SA |  Skills Assessment',
        'A' => 'A |  ANTA Adult Literacy Program',
        'AA' => 'AA |  User Choice Pilot National Projects',
        'AAA' => 'AAA |  Australian Apprenticeships Access',
        'AAP' => 'AAP |  Australian Apprenticeships (funded and unfunded)',
        'ABA' => 'ABA |  Abilities For All',
        'ACE' => 'ACE |  Adult and Community Education (ACE)',
        'ACW' => 'ACW |  Aged Care Workforce Vocational Education Training',
        'ACWVET' => 'ACWVET |  Skills Set 2012/13',
        'AD1' => 'AD1 |  ACEVET - Disability Program',
        'AHL' => 'AHL |  Additional Health Places - Apprentice/Trainee',
        'AHP' => 'AHP |  Additional Health Places - General (not Apprentice/Trainee)',
        'AI' => 'AI |  Australian Independent Schools of Queensland (School Apprent)',
        'AIC' => 'AIC |  Aboriginal Industry Clusters',
        'ALL' => 'ALL |  Special Allocations',
        'AME' => 'AME |  Adult Migrant Program',
        'ASL' => 'ASL |  Asylum Seeker or Victim of Human Trafficking Apprentice/Trainee VTG enrolment',
        'ASP' => 'ASP |  Asylum Seeker or Victim of Human Trafficking General (not Apprentice/Trainee) VTG enrolment',
        'AtB' => 'AtB |  Apprentice to Business Owner Program',
        'ATR' => 'ATR |  Automotive Transformation Taskforce RPL',
        'AVS' => 'AVS |  Aviation Strategy',
        'AWD' => 'AWD |  ACE Workforce Development',
        'AWT' => 'AWT |  Australians Working Together',
        'BCD' => 'BCD |  BACE funded seminar/conference/PD conducted by ACE organisations',
        'BFA' => 'BFA |  Accredited Building Family Opportunities',
        'BFN' => 'BFN |  Non-Accredited Building Family Opportunities',
        'BID' => 'BID |  ACE VET Standard professional Development conducted by ACE Organisatio',
        'BIE' => 'BIE |  IESIP',
        'BLC' => 'BLC |  Prevocational',
        'BLD' => 'BLD |  Language and Literacy Professional Development',
        'BLN' => 'BLN |  Community Access',
        'BMI' => 'BMI |  Queensland Mining Industry Training Advisory Body Inc Broker',
        'BOC' => 'BOC |  Building Our Community',
        'BRD' => 'BRD |  Professional Development - conducted by or for Clusters of ACE (non EL',
        'BRU' => 'BRU |  Queensland Rural Industry Training Council',
        'BSP' => 'BSP |  Equity Initiatives',
        'BSV' => 'BSV |  Business Skills Vouchers',
        'BVD' => 'BVD |  ACE VET Professional Development conducted by ACE Organisations',
        'BVE' => 'BVE |  Accredited ESL',
        'BVG' => 'BVG |  ACE VET Progam',
        'BVL' => 'BVL |  Accredited Literacy',
        'BW3' => 'BW3 |  Certificate 3 Guarantee Boost',
        'BWL' => 'BWL |  Back to Work Scheme (Apprentice/Trainee)',
        'BWP' => 'BWP |  Back to Work Scheme (non Apprentice/Trainee)',
        'C' => 'C |  DPA Profile Funded Training',
        'CAD' => 'CAD |  Cadetships',
        'CAV' => 'CAV |  Commonwealth Funded - VAT AMC',
        'CBF' => 'CBF |  Capability Building Foundation Skills',
        'CCP' => 'CCP |  Civil Construction Brokerage Pilot',
        'CDL' => 'CDL |  International Computer Driving Licence',
        'CE' => 'CE |  CompetitiveTendering - ACE VET Program',
        'CHL' => 'CHL |  Civil Higher Level',
        'CLG' => 'CLG |  Community Learning Guarantee',
        'CLI' => 'CLI |  Community Learning Intervention',
        'CLP' => 'CLP |  DEIR GRANT (Fee for Service)',
        'CP' => 'CP |  Competitive Tendering - Client Purchase Arrangement',
        'CPS' => 'CPS |  Contracted Programm of Study',
        'CRB' => 'CRB |  Constructing Roads to a Bright Future',
        'CRT' => 'CRT |  Community Responsive Training Program',
        'CSD' => 'CSD |  CSO Disadvantage',
        'CSG' => 'CSG |  CSQ Skills Assessment and Gap Training Civil',
        'CSI' => 'CSI |  Critical Skills Investment Fund',
        'CSIF' => 'CSIF |  Critical Skills Investment Fund',
        'CSQ' => 'CSQ |  Construction Skills Queensland',
        'CSR' => 'CSR |  CSO Regional and Remote',
        'CTP' => 'CTP |  Contracted Training Provision Program',
        'CY1' => 'CY1 |  Cape York Strategy - Apprentices',
        'CY2' => 'CY2 |  Cape York Strategy - Trainees',
        'CY3' => 'CY3 |  Cape York Strategy - School Based Apprentices/Trainees',
        'CY6' => 'CY6 |  Cape York Strategic - General Training',
        'D' => 'D |  Training activity funded directly from the Commonwealth (e.g. DEEWR, DOHA)',
        'DAH' => 'DAH |  Dual Award - HE AMC',
        'DAV' => 'DAV |  Dual Award - VET AMC',
        'DC1' => 'DC1 |  Doorways 2 Construction',
        'DEF' => 'DEF |  DECS Enterprise Funded',
        'DEO' => 'DEO |  DEST Funded Programs (Other)',
        'DEV' => 'DEV |  DEST Funded Programs (Vouchers)',
        'DHE' => 'DHE |  Existing worker Productivity Places Program',
        'DHJ' => 'DHJ |  Job seeker',
        'DHP' => 'DHP |  Existing worker Productivity Places Program',
        'DLQ' => 'DLQ |  Priority Health - Apprentice/Trainee',
        'DPE' => 'DPE |  PRODUCTIVITY GENERAL EXISTING WORKERS',
        'DPJ' => 'DPJ |  Productivity General Job Seeker',
        'DQ' => 'DQ |  Priority Health - General (not Apprentice/Trainee)',
        'DSD' => 'DSD |  DSD Staff Development',
        'E' => 'E |  Existing worker participant who is employer referred',
        'EAA' => 'EAA |  North East Development Agency Inc',
        'EAP' => 'EAP |  Employment Assistance Program',
        'EB' => 'EB |  Competitive Tendering - Employment Based Diplomas',
        'EBP' => 'EBP |  Enterprise Based Industry Skills PPP',
        'EF' => 'EF |  Program Initiatives - Women Re-entering the Workforce',
        'EG' => 'EG |  Competitive Tendering - Community Literacy & Numeracy',
        'ELD' => 'ELD |  Training and Mentoring Skills',
        'EM' => 'EM |  Competitive Tendering - Access Program',
        'ENH' => 'ENH |  Mainstream student',
        'ENJ' => 'ENJ |  When the Student Contribution Fee is paid by a Commonwealth Government agency such as a Jobs Services Australia Provider',
        'ENT' => 'ENT |  Standard Fund Source Code',
        'EPA' => 'EPA |  Employment Projects Accredited',
        'EPN' => 'EPN |  Employment Projects Non-accredited',
        'EQ' => 'EQ |  Education Queensland (School Apprenticeships)',
        'ESP' => 'ESP |  Job Seeker Participant who is referred by a Job Services Australia Provider',
        'ET1' => 'ET1 |  General C.E.A.P.',
        'ET2' => 'ET2 |  Breaking The Cycle Program',
        'ETP' => 'ETP |  Entitlement Program',
        'ETR' => 'ETR |  ACCRED TRAINING - EMPLOYMENT & YOUTH INIT- ETRF STRATEGY',
        'F' => 'F |  Overseas Full Fee Paying Students',
        'F2' => 'F2 |  Competitive Tendering - General Competitively Funded Programs',
        'F3' => 'F3 |  User Choice - Apprentices',
        'F4' => 'F4 |  Competitive Tendering - Food',
        'F5' => 'F5 |  Competitive Tendering - Offender Training',
        'F6' => 'F6 |  Program Initiatives - Workskills for Youth',
        'F7' => 'F7 |  Competitive Tendering - Forestry Industry Purchasing Pilot',
        'F8' => 'F8 |  Program Initiatives - Young Offenders Vocational Training',
        'FEE' => 'FEE |  Fee-for-Service Activities',
        'FFD' => 'FFD |  Fee for Service (Domestic)',
        'FFI' => 'FFI |  International full fee paying student',
        'FFO' => 'FFO |  Revenue from another registered training organisation',
        'FFS' => 'FFS |  Domestic Full Fee Paying Student',
        'FLO' => "FLO |  FFS - ICAN\FLO students",
        'FQP' => 'FQP |  Skills for Jobs in Regions - Full Qualification Project',
        'FSA' => 'FSA |  Foundation Skills Accredited',
        'FSN' => 'FSN |  Foundation Skills Non-Accredited',
        'FSP' => 'FSP |  Skills for Jobs in Regions - Skill Sets Program',
        'G' => 'G |  Access Training Program',
        'GK' => 'GK |  User Choice - Trainees',
        'GMP' => 'GMP |  General & Non-Accredited ACE Program',
        'GOP' => 'GOP |  General ACE B Providers',
        'GS1' => 'GS1 |  Year 12 graduates (high-priority qualifications only)',
        'GS2' => 'GS2 |  Full Fee',
        'GS3' => 'GS3 |  Partial Exemption',
        'GS4' => 'GS4 |  Full Exemption',
        'HLT' => 'HLT |  Student accessing VET FEE-HELP loan',
        'HS1' => 'HS1 |  CSQ HIGHER LEVEL SKILLS',
        'IAS' => 'IAS |  Indigenous Advancement Strategy (Cwth)',
        'ICN' => "ICN |  ICAN\FLO students",
        'IEC' => 'IEC |  Indigenous Education - Cluster',
        'IFF' => 'IFF |  Strategic Investment Fund Fees (Private Providers)',
        'IFN' => 'IFN |  Strategic Investment Fund No Fees',
        'IFP' => 'IFP |  Indigenous Funding Pool',
        'IHW' => 'IHW |  Indigenous Health Workforce',
        'IP3' => 'IP3 |  Industry Partnerships Strategy',
        'IP5' => 'IP5 |  Industry Partnerships Strategy',
        'IPH' => 'IPH |  Industry Partnerships Strategy',
        'IRL' => 'IRL |  INDUSTRIAL RELATIONS LICENCING. This covers the loadshifting, crane licencing etc',
        'IRR' => 'IRR |  Course as a Workplace Health and Safety Representative',
        'IRW' => 'IRW |  Course as a Workplace Health and Safety Officer',
        'ISF' => 'ISF |  Industry Skills Funding (Cwth)',
        'IT3' => 'IT3 |  Indigenous Training Strategy',
        'ITD' => 'ITD |  Information Technology (IT) Course Discounts',
        'ITH' => 'ITH |  Indigenous Training Strategy',
        'ITN' => 'ITN |  LCSA - IT courses for NESB seniors',
        'IWR' => 'IWR |  Recertification as a Workplace Health and Safety Officer',
        'JEN' => 'JEN |  Jobs First Employment Non-Accredited',
        'JEQ' => 'JEQ |  Jobs First Employment Projects - Accredited',
        'JFS' => 'JFS |  Jobs First STL Projects',
        'JNA' => 'JNA |  Northern Futures 2014',
        'JNE' => 'JNE |  NEDA 2014',
        'JRV' => 'JRV |  Skills for Jobs in Regions - Riverland',
        'JWA' => 'JWA |  Skills for Jobs in Regions - Western Adelaide',
        'L' => 'L |  Diploma and Above Apprentice/Trainee',
        'LA' => 'LA |  Traineeships (1997)',
        'LB' => 'LB |  Traineeships (1998)',
        'LC' => 'LC |  Traineeships (1999)',
        'LCP' => "LCP |  Apprenticeships/ Traineeships Contestable Pool: For use by non-TAFE RTO's",
        'LEC' => 'LEC |  Life Experience Counts',
        'LLN' => 'LLN |  User Choice - Language Literacy Numeracy',
        'LMA' => 'LMA |  Labour Market Adjustment Initiatives',
        'LMR' => 'LMR |  Labour Market Training Program',
        'LMT' => 'LMT |  Labour Market Training Program (funded through Training Advisory Councils)',
        'LNA' => 'LNA |  Adult Literacy and Numeracy (SA Works)',
        'LQ' => 'LQ |  One-Off Additional Apprenticeship Funding',
        'LSG' => 'LSG |  Skills for Growth - Apprentice/Trainee',
        'M' => 'M |  Advanced English for Migrant Program',
        'MG' => 'MG |  Competitive Tendering - Mining',
        'MSI' => 'MSI |  Manufacturing starter initiative has been introduced',
        'MUR' => 'MUR |  Murraylands Regional Development Board Inc',
        'N' => 'N |  Fee for Service (including ACE)',
        'N00' => 'N00 |  DIRECT GRANT (Profile)',
        'NEI' => 'NEI |  NEIS Program (Commonwealth Dept Educ)',
        'NGL' => 'NGL |  Single and Teenage Parents Training Initiative - Apprentice/Trainee – meets the Guaranteed Access Cohort eligibility criteria',
        'NGP' => 'NGP |  Single and Teenage Parents Training Initiative – General (not Apprentice/Trainee) –meets the Guaranteed Access Cohort eligibility criteria',
        'NOA' => 'NOA |  Northern Futures',
        'NPA' => 'NPA |  National Partnership Agreement on Skills Reform (Cwth)',
        'NSL' => 'NSL |  Single and Teenage Parents Training Initiative – Apprentice/Trainee – meets the Guaranteed Secondary Access Cohort eligibility criteria',
        'NSP' => 'NSP |  Single and Teenage Parents Training Initiative – General (not Apprentice/Trainee) –meets the Guaranteed Secondary Access Cohort eligibility criteria',
        'NWD' => 'NWD |  National Workforce Development Fund - Job seeker',
        'O' => 'O |  Overseas Full Fee Paying Client',
        'OAT' => 'OAT |  Other Aboriginal',
        'OBV' => 'OBV |  Any other DET Courses not funded through BACE',
        'OCF' => 'OCF |  Other Commonwealth Funding',
        'OEQ' => 'OEQ |  Other Equity Courses',
        'OFF' => 'OFF |  Overseas Off Shore fee paying student',
        'OFO' => 'OFO |  Other Workplace Courses',
        'OLL' => 'OLL |  Other Literacy or Language Courses',
        'OTH' => 'OTH |  Any Other Courses',
        'OVC' => 'OVC |  Contracted Training Program (CTP)',
        'OVE' => 'OVE |  Other Vocational Education and Training Courses (eg - HELP etc)',
        'P' => 'P |  General (Not Apprentice/Trainee) - meets VTG eligibility criteria',
        'P1' => 'P1 |  Competitive Tendering - Disability',
        'P2' => 'P2 |  Competitive Tendering - South Sea Islanders',
        'P5' => 'P5 |  Competitive Tendering - Non English Speaking in Small Business',
        'P7' => 'P7 |  Competitive Tendering - Rural Participation in Small Business',
        'P8' => 'P8 |  Competitive Tendering - Indigenous People',
        'PAA' => 'PAA |  Kickstart Pre-Apprenticeships Program',
        'PAC' => 'PAC |  Parents and Carers',
        'PBJ' => 'PBJ |  PRODUCTIVITY BROKERAGE JOBSEEKER',
        'PBW' => 'PBW |  Productivity Brokerage Existng Worker',
        'PDC' => 'PDC |  Cluster professional development activities',
        'PDE' => 'PDE |  Other Equity Related Professional Development Course',
        'PDI' => 'PDI |  IESIP Professional Development',
        'PDL' => 'PDL |  Other Language & Literacy Related Professional Development Courses',
        'PDO' => 'PDO |  Any Other Professional Development Courses',
        'PDV' => 'PDV |  Other Vocational Education & Training Related Professional Development',
        'PED' => 'PED |  Prisoner Education',
        'PEW' => 'PEW |  Productivity Places Program - Existing Worker (Enterprise)',
        'PGEW' => 'PGEW |  Productivity General Existing Workers',
        'PGW' => 'PGW |  PRODUCTIVITY GOVERNMENT WORKERS',
        'POS' => 'POS |  Private Overseas Student Programs',
        'PP3' => 'PP3 |  Pre-apprenticeship Program',
        'PPE' => 'PPE |  Productivity Places Program (PPP) Existing Worker',
        'PPI' => 'PPI |  Industry Pre-apprenticeship Program',
        'PPJ' => 'PPJ |  Productivity Places Program (PPP) Job Seeker',
        'PPP' => 'PPP |  Tauondi',
        'PRJ' => 'PRJ |  Projects',
        'PRW' => 'PRW |  PRODUCTIVITY RTO EXISTING WORKERS',
        'PSG' => 'PSG |  Skills for Growth - General (not Apprentice/Trainee)',
        'PSI' => 'PSI |  PRE-APPRENTICESHIP INITIATIVE NO FEES',
        'PSJ' => 'PSJ |  SQW PPP Skilling Queenslanders for Work',
        'PSP' => 'PSP |  Priorities Support Program (PSP)',
        'PT1' => 'PT1 |  Post Trade Training Program',
        'Q' => 'Q |  Special STA Funded Projects',
        'QC' => 'QC |  Queensland Catholic Education Commission (School Apprent)',
        'QIL' => 'QIL |  Agriculture-related training for Apprentices/Trainees (Agriculture related training)',
        'QIP' => 'QIP |  Institute of Land and Food Resources - Profile',
        'QWT' => 'QWT |  Australians Working Together (QLD)',
        'RAW' => 'RAW |  Regions at Work',
        'RBR' => 'RBR |  Regions Barossa',
        'RCO' => 'RCO |  Regions City of Onkaparinga',
        'RFK' => 'RFK |  Regions Adelaide Hills, Fleurieu and Kangaroo Island',
        'RFN' => 'RFN |  Regions Far North',
        'RIV' => 'RIV |  Riverland Development Corporation',
        'RLC' => 'RLC |  Regions Limestone coast',
        'RLN' => 'RLN |  Responsive Language Literacy And Numeracy Program',
        'RMR' => 'RMR |  Regions Murraylands and Riverland',
        'RNE' => 'RNE |  Regions North East',
        'RNF' => 'RNF |  Regions Northern Futures',
        'RVL' => 'RVL |  State Training Authority (traineeships)',
        'RVP' => 'RVP |  State Training Authority (non traineeships)',
        'RWE' => 'RWE |  Regions RDA Whyalla and Eyre Peninsula',
        'RWF' => 'RWF |  Regions RDA Western Futures',
        'RWL' => 'RWL |  Retrenched Worker 25 years and over (Apprentice/Trainee)',
        'RWN' => 'RWN |  Regional Workforce Development Initiative',
        'RWP' => 'RWP |  Retrenched Worker 25 years and over (General Non Apprentice/Trainee)',
        'RWT' => 'RWT |  Retrenched Worker Training',
        'RXP' => 'RXP |  SA Productivity Places Existing Workers',
        'RYM' => 'RYM |  Regions Yorke and Mid North',
        'S' => 'S |  State government funded, including:',
        'S1' => 'S1 |  Training delivered by a sub-contractor 2009/2010 ATTP funding',
        'S4A' => 'S4A |  Skills for All',
        'SA' => 'SA |  Access Employment (QLD)',
        'SAC' => 'SAC |  SCHOOL AGE CARE STRATEGY',
        'SAT' => 'SAT |  School Based Apprent / Trainee',
        'SAW' => 'SAW |  Works Special Projects',
        'SBA' => 'SBA |  Aust School Based Apprentice',
        'SC1' => 'SC1 |  CSQ Short Courses Program',
        'SCL' => 'SCL |  Retrenched employees – Apprentice/Trainee',
        'SCP' => 'SCP |  Retrenched employees – General (not Apprentice/Trainee)',
        'SEE' => 'SEE |  Skills for Education and Employment (Cwth)',
        'SFA' => 'SFA |  Skills First Apprenticeships',
        'SJ3' => 'SJ3 |  Training delivered to participants under the Single and Teenage Parent Program (STPP)',
        'SJF' => 'SJF |  Strategic Employment Fund',
        'SPP' => 'SPP |  School Private Provider',
        'SQI' => 'SQI |  Training funded under Skilling Queenslander for Work',
        'SQT' => 'SQT |  WorkReady - General',
        'SQW' => 'SQW |  Skilling Queenslanders for Work Projects',
        'SRI' => 'SRI |  Skilled Capital',
        'SRS' => 'SRS |  Skills Recognition Services',
        'SSG' => 'SSG |  Skills for Growth - Fee for Service',
        'SSP' => 'SSP |  Jobseeker',
        'SSQ' => 'SSQ |  SKILLING SOLUTIONS QUEENSLAND RPL',
        'SSR' => 'SSR |  Fixed Subsidy Payments (Fee-for-service)',
        'ST3' => 'ST3 |  When training delivered to participants under the Single and Teenage Parent Program STPP)',
        'ST5' => 'ST5 |  Training delivered to participants under the Single and Teenage Parent Program (STPP) ST5',
        'STS' => 'STS |  Job seeker participant who is referred by State Training Services of the NSW Department and Training',
        'SV2' => 'SV2 |  SMART VET - AVIATION STRATEGY - UNDER PINNING KNOWLEDGE',
        'SV3' => 'SV3 |  SMART VET - AVIATION STRATEGY - ON-THE-JOB',
        'SVQ' => 'SVQ |  RPL FAST TRACK PROGRAM AND SKILLING SOLUTIONS QUEENSLAND',
        'SVR' => 'SVR |  FEE-FOR-SEVICE FOR RPL FAST TRACK PROGRAM',
        'SWQ' => 'SWQ |  Skills in the Workplace - Qualifications',
        'SWS' => 'SWS |  Skills in the Workplace - Skills Sets',
        'T' => 'T |  Tender',
        'T02' => 'T02 |  TAFE User Choice',
        'T08' => 'T08 |  General Competitive Funded Programs',
        'TAL' => 'TAL |  Teaching and Leadership',
        'TCP' => 'TCP |  TRAINING IN COMMUNITIES PROGRAM',
        'TEX' => 'TEX |  TAFE Tasmania Codes',
        'TFW' => 'TFW |  Training for Work',
        'TGS' => 'TGS |  Training Guarantee for SACE Students',
        'TIF' => 'TIF |  Training Initiatives for Indigenous Adults in Regional & Remote Communities',
        'TLS' => 'TLS |  Training Loan Scheme',
        'TRA' => 'TRA |  Trade Skills Assessment and Gap Training',
        'TSL' => 'TSL |  Trade Support Loans (Cwth)',
        'TSS' => 'TSS |  Tech Savvy Seniors',
        'U' => 'U |  User Choice Training',
        'UCP' => 'UCP |  User Choice Pending',
        'USC' => 'USC |  User Choice',
        'USG' => 'USG |  Upper Spencer Gulf',
        'VCE' => 'VCE |  VCE Programs (Adults, Distance Education)',
        'VIS' => 'VIS |  Vet in Schools - auspicing',
        'VJJ' => 'VJJ |  VET/Juvenile Justice',
        'VSB' => 'VSB |  Vet in Schools Business Case C3G',
        'VSS' => 'VSS |  VET in Schools (secondary school students)',
        'W2W' => 'W2W |  Welfare to Work',
        'WAP' => 'WAP |  Workers Assisstance Program',
        'WAS' => 'WAS |  Workplace Assessor Strategy',
        'WDE' => 'WDE |  National Workforce Development - Existing Workers',
        'WDJ' => 'WDJ |  National Workforce Development - Job Seekers',
        'WEA' => 'WEA |  Western Futures 2014',
        'WEL' => 'WEL |  Workplace English Language and Literacy',
        'WJP' => 'WJP |  Community Jobs Programs',
        'WPP' => 'WPP |  Workforce Participation Program',
        'WRG' => 'WRG |  WorkReady - General',
        'WSR' => 'WSR |  Works, Services, Repairs and Maintenance',
        'WSV' => 'WSV |  Work Skills Vouchers',
        'WTP' => 'WTP |  Workers in Transition non Apprenticeship/Trainees',
        'Y' => 'Y |  Training Activity subcontracted from another RTO',
        'YAP' => 'YAP |  Youth Access Program',
        'YCC' => 'YCC |  Youth Conversation Corps',
        'YCL' => 'YCL |  Youth Compact - General (not Apprentice/Trainee) TAFE only',
        'YCP' => 'YCP |  Youth Compact - General (Not Apprentice/Trainee) TAFE only',
        'YRL' => 'YRL |  15-19; 20-24 up-skilling (Apprentice/Trainee)',
        'YRP' => 'YRP |  Youth Compact - General (not Apprentice/Trainee)(non-TAFE RTOs only)',
        'Z06' => 'Z06 |  Joint Indigenous Funding Pool (JIFP)',
        'Z10' => 'Z10 |  Innovation Fund (for use by TAFE Institutions only)',
        'Z20' => 'Z20 |  VET in VCE',
        'Z22' => 'Z22 |  Equity Group Programs',
        'Z30' => 'Z30 |  ACE- Schools Partnership Program',
        'Z35' => 'Z35 |  Skills Store RPL assessment - Government funded',
        'Z36' => 'Z36 |  Skills Store RPL assessment - Fee for service',
        'Z45' => 'Z45 |  Jobs for Young People',
        'Z50' => 'Z50 |  Youth Employment Scheme Pre-Employment Training',
        'Z55' => 'Z55 |  Youth Employment Scheme (Apprenticeship/Traineeship)',
        'Z60' => 'Z60 |  Private Sector Skills Development Program Pre-Employment Training',
        'Z65' => 'Z65 |  Private Sector Skills Development Program (Apprenticeship/Traineeship)',
        'Z70' => 'Z70 |  Interstate Apprentices',
        'Z75' => 'Z75 |  NSW Registered Apprentices (TAFE only)',
        'Z80' => 'Z80 |  Go For IT Program (non Apprenticeship/Traineeship)',
        'Z85' => 'Z85 |  Go for IT Program (Apprenticeship/Traineeship)',
        'Z90' => 'Z90 |  Community Jobs Program',
        'Z98' => 'Z98 |  Offender Education & Training in ACE',
        'Z99' => 'Z99 |  Youth Pathways Program',
        'ZC' => 'ZC |  Corrections delivery contracted directly with TAFE institutes',
        'ZP' => 'ZP |  Corrections Funding through private prisons',
    ],
    'arrHolidayType' => [
        //        '' => '- - Select Holiday Type - -',
        '1' => 'Public Holiday',
    ],
    'arrCourseTemplateSearch' => [
        '0' => 'All',
        '1' => 'Course',
    ],
    'arrKnowledgeLevel' => [
        '' => '- - Select Knowledge Level - -',
        '1' => 'Advanced',
        '2' => 'Intermediate',
    ],
    'arrStatus' => [
        // '' => '- - Select Status - -',
        '1' => 'Active',
        '2' => 'InActive',
    ],
    'collegeEmailType' => [
        // ''  => '- - Select From - -',
        '0' => 'marketing_email',
        '1' => 'admission_email',
        '2' => 'it_email',
        '3' => 'acedemic_email',
        '4' => 'account_email',
        '5' => 'college_email_setup',
    ],
    'arrUserType' => [
        // '' => '- - Select User Type - -',
        '1' => 'staff',
        '2' => 'student',
    ],
    'arrRegisterFilter' => [
        'anyword' => 'Any Word in Detail',
        'category' => 'Category',
        'requested_by' => 'Requested By',
        'logged_by' => 'Lodged Person',
        'lodged_date' => 'Lodged Date',
        'between_dates' => 'Lodged Between Dates',
        'status' => 'Case Status',
    ],
    'arrStaffCommunicationLogStatus' => [
        // '' => '- - Select Status - -',
        'Informed' => 'Informed',
        'Resolved' => 'Resolved',
    ],
    'arrStaffCommunicationLogType' => [
        '' => '- - Select Type - -',
        'General' => 'General',
    ],
    'arrAttendanceType' => [
        // '' => '- - Select Type - -',
        '1' => 'Four-Hourly',
        '2' => 'Three-Hourly',
        '3' => 'Two-Hourly',
        '4' => 'Hourly',
        '5' => 'One Session',
    ],
    'arrClassType' => [
        // '' => '- - Select Class - -',
        '1' => 'Class',
        '2' => 'Lab',
        '3' => 'Lecture',
        '4' => 'Practicle',
        '5' => 'Seminar',
        '6' => 'Supervision',
        '7' => 'Tutorial',
        '8' => 'Virtual Class',
        '9' => 'Workshop',
        '10' => 'Online',
    ],
    'arrInstallmentDuration' => [
        '' => 'Duration',
        'day' => 'Day',
        'week' => 'Week',
        'month' => 'Month',
        'year' => 'Year',
    ],
    'arrAgentStatus' => [
        '' => '- - Select Agent Status - -',
        '1' => 'Active',
        '2' => 'Inactive',
        '3' => 'New Application Request',
        '4' => 'Preliminary',
        '5' => 'Principal Agent',
        '6' => 'Terminated',
        '7' => 'All',
    ],
    'arrCommissionCategory' => [
        '' => '- - Select Commission Category - -',
        '1' => 'Student Case',
        '2' => 'General',
        '3' => 'Email',
    ],
    'arrCommissionStatus' => [
        '' => '- - Select Commission Status - -',
        '1' => 'Informed',
        '2' => 'Email Sent',
        '3' => 'Resolved',
    ],
    'arrModeQfDelivery' => [
        '' => '- - Select Mode Of Delivery - -',
        '1' => 'Online',
        '2' => 'On-Campus',
    ],
    'arrCourseDeliveryMode' => [
        'online' => 'Online',
        'facetoface' => 'Face-to-Face',
        'hybrid' => 'Blended',
    ],
    'arrAgentStatus' => [
        // '' => '- - Select Status - -',
        '1' => 'Active',
        '2' => 'Preliminary',
        '0' => 'Inactive',
    ],
    'arrSelectReportType' => [
        '' => '- - Please select a query - -',
        '1' => 'Timetable by Semester',
        '2' => 'Consolidated TimeTable by Date',
        '3' => 'Timetable Order By ',
        '4' => 'Timetable By ',
    ],
    'arrTimeTableReportOrderBy' => [
        '' => '- - Please select order by - -',
        '1' => 'Teacher',
        '2' => 'Subject',
        '3' => 'Room',
        '4' => 'Day',
    ],
    'arrTimeTableReportSelectedBy' => [
        '' => '- - Please select seleted by - -',
        '1' => 'Student',
        '2' => 'Teacher',
        '3' => 'Subject',
        '4' => 'Room',
        '5' => 'Day',
        '6' => 'Training Location/Venue',
    ],
    'arrDays' => [
        'Monday' => 'Monday',
        'Tuesday' => 'Tuesday',
        'Wednesday' => 'Wednesday',
        'Thursday' => 'Thursday',
        'Friday' => 'Friday',
        'Saturday' => 'Saturday',
        'Sunday' => 'Sunday',
    ],
    'arrMonth' => [
        '' => '- - Select Month - -',
        '01' => 'January',
        '02' => 'February',
        '03' => 'March',
        '04' => 'April',
        '05' => 'May',
        '06' => 'June',
        '07' => 'July',
        '08' => 'August',
        '09' => 'September',
        '10' => 'October',
        '11' => 'November',
        '12' => 'December',
    ],
    'arrAcademic' => [
        '1' => 'Subject Completion Summary',
        '2' => 'Unit Completion Summary',
    ],
    //    'diaryCategory' => [
    //        '' => '- - Select Category - -',
    //        'Academic' => 'Academic',
    //        'Assessment' => 'Assessment',
    //        'Exam' => 'Exam',
    //        'General' => 'General',
    //        'Payment' => 'Payment',
    //    ],
    'diaryStatus' => [
        // '' => '- - Select Status - -',
        'Assessed' => 'Assessed',
        'Done' => 'Done',
        'In Progress' => 'In Progress',
        'Informed' => 'Informed',
        'Not resolved' => 'Not resolved',
        'Recorded' => 'Recorded',
        'Resolved' => 'Resolved',
        'Reviewed' => 'Reviewed',
        'Escalated' => 'Escalated',
    ],
    'arrCourseStatus' => [
        // '' => '- - Select Status - -',
        'Agent Apply' => 'Agent Apply',
        'Cancelled' => 'Cancelled',
        'Completed' => 'Completed',
        'Converted' => 'Converted',
        'Current Student' => 'Current Student',
        'Deferred' => 'Deferred',
        'Did Not Commence' => 'Did Not Commence',
        'Enrolled' => 'Enrolled',
        'Expired Offer' => 'Expired Offer',
        'Finished' => 'Finished',
        'Graduated' => 'Graduated',
        'New Application Request' => 'New Application Request',
        'New Course Request' => 'New Course Request',
        'Offered' => 'Offered',
        'Placed' => 'Placed',
        'Reported' => 'Reported',
        'Withdrawn' => 'Withdrawn',
        'Transferred' => 'Transferred',
        'Transitioned' => 'Transitioned',
        'Suspended' => 'Suspended',
    ],
    /*
    order in which the courses having status has priority while displaying the records/reports
    */
    'arrCourseStatusOrder' => [
        /*
        'Current Student',
        'Enrolled',
        'Completed',
        'Graduated',
        'Finished',
        'Converted',
        'Agent Apply',
        'Cancelled',
        'Deferred',
        'Withdrawn',
        'Transferred',
        'Transitioned',
        'Suspended',
        'Placed',
        'Reported',
        'Offered',
        'Expired Offer',
        'Did Not Commence',
        'New Application Request',
        'New Course Request',
        'StatusValue',
        */
        'Current Student',
        'Enrolled',
        'Offered',
        'Agent Apply',
        'Graduated',
        'Finished',
        'Completed',
        'Transferred',
        'Deferred',
        'Withdrawn',
        'Transitioned',
        'Cancelled',
        'Expired Offer',
        'New Application Request',
        'New Course Request',
        'Placed',
        'Reported',
        'Suspended',
        'Converted',
        'Did Not Commence',
    ],
    'arrCourseStatusMail' => [
        // '' => '- - Select Status - -',
        'All' => 'All',
        // 'UnApproved' => 'UnApproved',
        // 'Withdrawn' => 'Withdrawn',
        'Converted' => 'Converted',
        'Pending' => 'Pending',
        'ReConsider' => 'ReConsider',
        'New Application Request' => 'New Application Request',
        // 'Rejected' => 'Rejected',
        'Offered' => 'Offered',
        // 'Agent Apply' => 'Agent Apply',
    ],
    'arrIntakeYear' => $arrIntakeYearData,
    'arrIntakeYearEdit' => $arrEditIntakeYearData,
    'arrYearData' => $arrYearData,
    'arrCommissionRate' => [
        '' => '- - Select Commission Rate - -',
        '1' => '0%+No GST',
        '2' => '10%+No GST',
    ],
    'arrPaymentType' => [
        '' => '- - Select Payment Type - -',
        '1' => 'Initial Payment With Schedule',
        '2' => 'Initial Payment Only',
        '3' => 'Schedule Only',
    ],
    'keyForSchedulePaymentType' => '3', // Use from above array "arrPaymentType" & "3" for Schedule Only
    'arrCourseDeferReason' => [
        '' => '- - Select Reason - -',
        '1' => 'Compassionate Leave',
        '2' => 'Medical Treatment',
    ],
    'arrCourseDeferType' => [
        '' => '- - Select Type - -',
        '1' => 'Approved Leave - AL',
        '2' => 'Approved Holiday - AH',
        '3' => 'Deferral - DF',
        '4' => 'Suspension - SP',
    ],
    'arrFundingSourceNat' => [
        '' => '- - Select Funding Source NAT - -',
        '11' => '11 -Commonwealth and state general purpose recurrent',
        '13' => '13 -Commonwealth specific purpose programs',
        '15' => '15 -State specific purpose programs',
        '20' => '20 -Domestic full fee-paying client',
        '30' => '30 -International full fee-paying client',
        '31' => '31 -International onshore client',
        '32' => '32 -International offshore client',
        '80' => '80 -Revenue earned from another training organisation',
    ],
    //    'arrSelectFinalOutcome' => [
    //        '' => '- - Select - -',
    //        '1' => 'Enrolled',
    //        '2' => 'QLD: @@ - DO NOT REPORT IN AVETMISS (CANCEL)', //CANCEL
    //        '3' => 'QLD: 51 - Recognition of prior learning - granted (RPL)',
    //        '4' => 'QLD: 52 - Recognition of prior learning - not granted (RPL-N)',
    //        '5' => 'QLD: 60 - Credit Transfer (CT)',
    //        '6' => 'QLD: 65 - Gap Training (Superseded Qualifications) (65)',
    //        '7' => 'QLD: 70 - Continuing enrolment (CE)',
    //        '8' => 'QLD: 81 - Non-assessable enrolment - Satisfactorily completed (NA-C)',
    //        '9' => 'QLD: 82 - Non-assessable enrolment - Withdrawn or not satisfactorily completed (NA-NYC)',
    //        '10' => 'QLD: 90 - Result not available (R)',
    //    ],
    'arrSelectFinalOutcome' => [
        '' => '- - Select Final Outcome - -',
        'Enrolled' => 'Enrolled',
        'CANCEL' => '@@ - DO NOT REPORT IN AVETMISS (CANCEL)',
        'C' => '20 - Competency achieved/pass (C)',
        'NYC' => '30 - Competency not achieved/fail (NYC)',
        'WD' => '40 - Withdrawn (WD)',
        'INC' => '41 — Incomplete due to RTO closur',
        'RPL' => '51 - Recognition of prior learning - granted (RPL)',
        'RPL-N' => '52 - Recognition of prior learning - not granted (RPL-N)',
        'CT' => '60 - Credit Transfer (CT)',
        'SS' => '61 - Superseded subject',
        'GT' => '65 - Gap Training (Superseded Qualifications) (GT)',
        'NS' => '66 - Did Not Start (NS)',
        'CE' => '70 - Continuing enrolment (CE)',
        'NA-C' => '81 - Non-assessable enrolment - Satisfactorily completed (NA-C)',
        'NA-NYC' => '82 - Non-assessable enrolment - Withdrawn or not satisfactorily completed (NA-NYC)',
        'NYS' => '85 - Not Yet Started (NYS)',
        'R' => '90 - Result not available (R)',
        'D' => 'D - Deferred (Smart & Skilled Program Only) (D)',
        'TNC' => 'TNC - Training Not Completed (Smart & Skilled Program Only) (TNC)',
        'RW' => 'Result Withheld',

    ],

    'arrSelectFinalOutcomeNew' => [
        // 'Enrolled' => 'Enrolled',
        'CANCEL' => '@@ - DO NOT REPORT IN AVETMISS (CANCEL)',
        'C' => '20 - Competency achieved/pass (C)',
        'NYC' => '30 - Competency not achieved/fail (NYC)',
        'WD' => '40 - Withdrawn (WD)',
        'INC' => '41 — Incomplete due to RTO closur',
        'RPL' => '51 - Recognition of prior learning - granted (RPL)',
        'RPL-N' => '52 - Recognition of prior learning - not granted (RPL-N)',
        'CT' => '60 - Credit Transfer (CT)',
        'SS' => '61 - Superseded subject',
        'GT' => '65 Training (Superseded Qualifications) (GT)',
        'NS' => '66 - Did Not Start (NS)',
        'CE' => '70 - Continuing enrolment (CE)',
        'NA-C' => '81 - Non-assessable enrolment - Satisfactorily completed (NA-C)',
        'NA-NYC' => '82 - Non-assessable enrolment - Withdrawn or not satisfactorily completed (NA-NYC)',
        'NYS' => '85 - Not Yet Started',
        'R' => '90 - Result not available (R)',
        'D' => 'D - Deferred (Smart & Skilled Program Only) (D)',
        'TNC' => 'TNC - Training Not Completed (Smart & Skilled Program Only) (TNC)',
        'RW' => 'Result Withheld',

    ],
    /*  THIS FUNCTION IS USED FOR XLSX FILE ONLY  Exports NAT txt files for AVETMISS reports */
    'arrSelectFinalOutcomeXlsx' => [
        'C' => '20',
        'NYC' => '30',
        'WD' => '40',
        'RPL' => '51',
        'RPL-N' => '52',
        'CT' => '60',
        'NS' => '66',
        'CE' => '70',
        'Enrolled' => '70',
        'NA-C' => '81',
        'NA-NYC' => '82',
        'R' => '90',
        'NYS' => '85',
        'GT' => '65',
    ],
    'arrCommissionPeriod' => [
        '' => '- - Select Commission Period - -',
        '1' => 'Full Course',
        '2' => '2 Semester',
        '3' => '1 Year',
        '4' => 'Once Off',
        '5' => 'No Commission + No GST',
    ],
    'arrPaidDuration' => [
        '' => '- - Select Commission Period - -',
        '1' => 'Day',
        '2' => 'Week',
        '3' => 'Month',
        '4' => 'Year',
    ],
    'arrPaidMode' => [
        '' => '--Select Mode--',
        '1' => 'Agent Deducted',
        '2' => 'APF',
        '3' => 'Bad Debt',
        '4' => 'Bank Cheque',
        '5' => 'Bank Draft',
        '6' => 'Cash',
        '7' => 'Cheque',
        '8' => 'Course Transfer',
        '9' => 'Credit Card',
        '10' => 'Direct Debit',
        '11' => 'Direct Deposit',
        '12' => 'EFTPOS',
        '13' => 'Internet Transfer',
        '14' => 'Traveller Cheque',
        '15' => 'TT',
        '16' => 'Other',
    ],
    'arrCourseCommencing' => [
        '' => '- - Select Course Commencing - -',
        '3' => '3 -Commencing enrolment in the qualification or course',
        '4' => '4 -Continuing enrolment in the qualification or course from a previous year',
        '8' => '8 -Unit of competency or module enrolment only',
    ],
    'arrSpecificFunding' => [
        '' => 'Blank value (Not specified)',
        '11' => '11: NWDF - Job seeker',
        '12' => '12: NWDF - Existing worker',
        // '13' => '13: NWDF - Critical Skills Investment Fund (Invalid for activity from 2015)',
        '14' => '14: Industry Skills Fund',
        '21' => '21: Skills for Education and Employment Program',
        // '31' => '31: Sole Parent National Partnership (Invalid for activity from 2015)',
        '32' => '32: NPA Skills Reform',
        '41' => '41: Workplace English Language and Literacy',
        '51' => '51: Productivity Places Program - Job seeker (Flagged for removal)',
        '52' => '52: Productivity Places Program - Existing worker (Flagged for removal)',
        '61' => '61: Australian Apprenticeships Access',
        '62' => '62: Trade Support Loans',
        // '91' => '91: Dual award - VET AMC (Australian Maritime College use only)',
        // '92' => '92: Dual award - HE AMC (Australian Maritime College use only)',
        // '93' => '93: Commonwealth funded - VET AMC (Australian Maritime College use only)',
        '99' => '99: Other Commonwealth government funding',
    ],
    'arrVETInSchoolFlag' => [
        'N' => 'N',
        'Y' => 'Y',
    ],
    'arrFeeExemptionType' => [
        '' => '- - Select Exemption Type - -',
        'N' => 'N - No Concession',
        'Y' => 'Y - Has Fee Exemption/Concession',
    ],
    'arrInterventionType' => [
        '' => '- - Select Intervention - -',
        '1' => 'Course',
        '2' => 'Subject',
        '3' => 'Unit',
    ],
    'arrCaseType' => [
        '' => '- - Select Case - -',
        '1' => 'Case Open',
        '2' => 'Case Close',
    ],
    'arrAssessmentCompetency' => [
        '' => '- - Select Competency - -',
        'S' => 'S',
        'NYS' => 'NYS',
        // 'C' => 'C',
        // 'NYC' => 'NYC',
    ],
    'arrAgentStatus' => [
        // '' => '- - Select Status - -',
        '1' => 'Active',
        '0' => 'Inactive',
        '3' => 'New Application Request',
        '2' => 'Preliminary',
        '5' => 'Principal Agent',
        '6' => 'Terminated',
    ],
    'exitTypeArr' => [
        '' => '- - Select Type - -',
        '1' => 'Course Exit',
        '2' => 'College Exit',
    ],
    'newCollage' => [
        '2' => 'Not Going TO Another Collage',
        '1' => 'Add Collage',
    ],
    'arrSenctionType' => [
        '1' => 'Academic',
        '2' => 'Payment',
    ],
    'arrServiceInvoiceType' => [
        'Airport Pickup' => 'Airport Pickup',
        'Guardianship' => 'Guardianship',
        'Accomodation' => 'Homestay Accommodation',
        'Home Placement' => 'Homestay Placement',
    ],
    'arrOtherServiceType' => [
        '' => '- - Select Other Sevice Type - -',
        'College Shirt' => 'College Shirt',
        'EFT' => 'EFT',
        'MaterialFee' => 'Material Fee',
        'Other' => 'Other',
    ],
    'arrOtherServiceTypeMiscellaneousPayment' => [
        '' => '- - Select Other Sevice Type - -',
        'College Shirt' => 'College Shirt',
        'EFT' => 'EFT',
        'MaterialFee' => 'Material Fee',
        'Other' => 'Other',
        'Enrollment' => 'Enrollment',
        'Oshc' => 'Oshc',
    ],
    'paymentStatus' => [
        'paid' => 'Normal(Paid)',
        'unpaid' => 'Schedule(Not Paid)',
    ],
    'agentVewOption' => [
        '' => '- - Select View Option - -',
        '1' => 'View and Approve Commission Available',
        '2' => 'Process and Pay Commission',
    ],
    'agentSearchBy' => [
        '' => '- - Select Search Option - -',
        '1' => 'Full Payment Schedule Paid',
        '2' => 'Partial Payment Schedule Paid',
    ],
    'dateOption' => [
        '' => '- - Select Search Option - -',
        '1' => 'Till Date',
        '2' => 'Betweem Two Days',
    ],
    'paymentHistoryYear' => [$paymentHistoryYear],
    'paymentHistoryViewType' => [
        '' => '- - Select Search Option - -',
        '1' => 'Auto Deducted',
        '2' => 'Invoiced Commission',
        '3' => 'Other Type',
    ],
    'bankDepositType' => [
        '' => 'Deposit Type',
        'Fees' => 'Fees',
        'Other' => 'Other',
    ],
    'arrStudentAccountType' => [
        '' => '- - Select Account Type - -',
        '1' => 'Receivable Account',
        '2' => 'Bank Account',
    ],
    'arrStudentFeeType' => [
        '' => '- - Select Fee Type - -',
        '1' => 'Tuition Fee',
        '2' => 'Miscellaneous Fee',
    ],
    'arrCompliance' => [
        '' => '- - Select Type - -',
        '1' => 'Student Id',
        '2' => 'Course Id',
        '3' => 'Intervention Type',
        '4' => 'Record Date Between Dates',
        '5' => 'Due Date Between Dates',
        '6' => 'Outcome Date Between Dates',
        '7' => 'Status',
        '8' => 'Semester',
    ],
    'arrIntervention' => [
        '' => '- - Select Type - -',
        'is_catchUp' => 'Catch up-class',
        'is_course' => 'Course Progress',
        'is_semester' => 'Semester Progress',
        'is_subject' => 'Subject Progress',
    ],
    'arrProviderSearchBy' => [
        'Id' => 'Id',
        'Name' => 'Name',
        'Type' => 'Type',
    ],
    'arrHour' => $arrHour,
    'arrMonth' => $arrMonth,
    'arrMinute' => $arrMinute,
    'arrDuration' => $arrDuration,
    'arrAssessmentGroup' => [
        '' => '- - Select Group - -',
        'No Group' => 'No Group',
        'A' => 'A',
        'B' => 'B',
        'C' => 'C',
        'D' => 'D',
        'E' => 'E',
        'F' => 'F',
        'G' => 'G',
        'H' => 'H',
        'I' => 'I',
        'J' => 'J',
        'K' => 'K',
        'L' => 'L',
        'M' => 'M',
        'N' => 'N',
        'O' => 'O',
        'P' => 'P',
        'Q' => 'Q',
        'R' => 'R',
        'S' => 'S',
        'T' => 'T',
        'U' => 'U',
        'V' => 'V',
        'W' => 'W',
        'X' => 'X',
        'Y' => 'Y',
        'Z' => 'Z',
    ],
    'arrCompetency' => [
        '' => '',
        'C' => 'C',
        'NYC' => 'NYC',
        'S' => 'S',
        'NYS' => 'NYS',
        'NA' => 'NA',
    ],
    'arrCompetencyVocational' => [
        '' => '',
        'NYS' => 'NYS',
        'C' => 'C',
        'NYC' => 'NYC',
        'S' => 'S',
        'NA' => 'NA',
    ],
    'teacherTimeTableFind' => [
        '1' => 'By Semester',
        '2' => 'Between Dates',
    ],
    'arrConvertStatus' => [
        '1' => 'COE expired but not completed all the course subjects as yet',
        '2' => 'COE expired and completed all the course subjects',
        '3' => 'COE expired and completed all the course subjects/units by course template',
    ],
    'arrProviderPaymentServiceType' => [
        '' => '- - Select Sevice Type - -',
        'Airport Pickup' => 'Airport Pickup',
        'Guardianship' => 'Guardianship',
        'Accomodation' => 'Homestay Accommodation',
        'OSHC' => 'OSHC',
    ],
    'arrProviderPaymentTransactionType' => [
        '' => '- - Select Transaction Type - -',
        'Paid' => 'Provider Paid Only',
        'Unpaid' => 'Provider Unpaid Only',
        'All' => 'All Transaction',
    ],
    'arrReportName' => [
        'Student enrolment details by id/firstname/lastname/passport/dob' => 'Student enrolment details by id/firstname/lastname/passport/dob',
    ],
    'arrLetter' => [
        'Family Spouse Invitation Letter' => 'Family Spouse Invitation Letter',
        'VFH-COE' => 'VFH-COE',
    ],
    'pdfConstant' => [
        '1' => 'With Watermark',
        '2' => 'Without Watermark',
    ],
    'arrContractType' => [
        '1' => 'Full Time',
        '2' => 'Part Time',
        '3' => 'School-based',
    ],
    'arrTrainingStatus' => [
        '1' => 'Proposed',
        '2' => 'Approved',
    ],
    'crditAllocationType' => [
        '1' => 'Advanced Paid',
        '2' => 'Agent Credit',
    ],
    'traineeActivityLogType' => [
        '1' => 'Workshop',
        '2' => 'On the job Assessment',
        '3' => 'Practical',
    ],
    'setupStaffType' => [
        'All' => 'All',
        'Teacher' => 'Teacher',
    ],
    'studentTypeBulkEnrollBySubject' => [
        'Continuing Student' => 'Continuing Student',
        'New Student' => 'New Student',
    ],
    'freeHelpStudyType' => [
        '' => 'Blank -  Not specified',
        '20' => '20 - Advanced Diploma',
        '21' => '21 - Diploma',
        '80' => '80 - VET Graduate Certificate',
        '81' => '81 - VET Graduate Diploma',
    ],
    /* Wet Fee Help Constant array */
    'arrCitizenshipCode' => [
        '' => '- - Select Citizenship code - -',
        '1' => '1-  Australian citizen (including Australian citizens with dual citizenship)',
        '2' => '2 - New Zealand citizen or a diplomatic or consular representative of New Zealand, a member of the staff of such a representative or the spouse or dependent relative of such a representative, whether or not the individual has Australian Permanent Resident status. An individual who meets these requirements, and is also an Australian citizen, must be reported as an Australian citizen.',
        '8' => '8 - Students/applicants with permanent humanitarian visa (except those meeting the definition for value ‘2’)',
        '3' => '3 - Students/applicants with permanent visa other than permanent humanitarian visa (except those meeting the definition for value ‘2’)',
        '4' => '4 - Student/applicant has a temporary entry permit or is a diplomat or a dependent of a diplomat (except New Zealand) and resides in Australia during the unit of study',
        '5' => '5 - Not one of the above categories and student/applicant is residing outside Australia during the unit of study/time of application',
        'P' => 'P - Students/applicants with Pacific Engagement Visa (PEV)',
    ],
    /* Attended Year12 Constant array */
    'arrAttendedYear12' => [
        '' => '- - Select Attended Year12 - -',
        '1' => 'UNKNOWN',
        '2' => 'AttendedYear12',
        '3' => 'DidNotAttendYear12',
    ],
    /* Attended HEP Constant array */
    'arrAttendedHep' => [
        '' => '- - Select Attended Hep - -',
        '1' => 'UNKNOWN',
        '2' => 'AttendedPreviousHep',
        '3' => 'DidNotAttendPreviousHep',
    ],
    /* Attendance Type array */
    'arrAttendanceTypeAttendance' => [
        '' => '- - Select attendance type - -',
        '1' => '(1)Full-time',
        '2' => '(2)Part-time',
    ],
    /* Higher Education Type array */
    'arrHigherEducation' => [
        '' => '- - Select Higher Education - -',
        '1' => '(02)A complete Higher education postgraduate level course',
        '2' => '(03)A complete Higher education bachelor level course',
        '3' => '(04)A complete Higher education sub-degree level course',
        '4' => '(05)An incomplete Higher education course',
        '5' => '(07)A complete final year of secondary education course at school or through a Registered Training Organisation',
        '6' => '(08)other qualification, complete or incomplete',
        '7' => '(09)No prior educational attainment',
        '8' => '(10)A complete VET award course',
        '9' => '(11)An incomplete VET award course',
        '10' => '(00)Overseas student',
        '11' => '(01)Not a commencing student',
    ],
    /* Attadance Type array */
    'arrReasonCode' => [
        '' => '- - Select Reason Code - -',
        '1' => '(1)Remission due to special circumstances (Higher Ed only)',
        '2' => '(2)Revised record due to administrative error (Higher Ed only)',
        '3' => '(3)New Debt (No longer used)',
        '4' => '(4)Deletions due to Administrative error​​​ (VET only)',
        '5' => '(5)Additional record or new record reported for the first time (VET only)',
    ],
    /* Student Status Code */
    'arrStudentStatusCode' => [
        '' => '- - Select Status Code - -',
        '401' => '(401) Deferred all/part of tuition fee through VET FEE-HELP - non State Government subsidised',
        '402' => '(402) Deferred all/part of tuition fee through VET FEE-HELP - Restricted Access Arrangement',
        '403' => '(403) Deferred all/part of tuition fee through VET FEE-HELP - Victorian State Government subsidised',
        '404' => '(404) Deferred all/part of tuition fee through VET FEE-HELP - New South Wales State Government subsidised',
        '405' => '(405) Deferred all/part of tuition fee through VET FEE-HELP - Queensland State Government subsidised',
        '406' => '(406) Deferred all/part of tuition fee through VET FEE-HELP - South Australian State Government subsidised',
        '407' => '(407) Deferred all/part of tuition fee through VET FEE-HELP - Western Australian State Government subsidised',
        '408' => '(408) Deferred all/part of tuition fee through VET FEE-HELP - Tasmania State Government subsidised',
        '409' => '(409) Deferred all/part of tuition fee through VET FEE-HELP - Northern Territory Government subsidised',
        '410' => '(410) Deferred all/part of tuition fee through VET FEE-HELP - Australian Capital Territory Government subsidised',
        '501' => '(501) Paid full tuition fee - non-State Government subsidised',
        '502' => '(502) Paid full tuition fee - Restricted Access Arrangement',
        '503' => '(503) Paid full tuition fee - Victorian State Government subsidised',
        '504' => '(504) Paid full tuition fee - New South Wales State Government subsidised',
        '505' => '(505) Paid full tuition fee - Queensland State Government subsidised',
        '506' => '(506) Paid full tuition fee - South Australian State Government subsidised',
        '507' => '(507) Paid full tuition fee - Western Australian State Government subsidised',
        '508' => '(508) Paid full tuition fee - Tasmania State Government subsidised',
        '509' => '(509) Paid full tuition fee - Northern Territory Government subsidised',
        '510' => '(510) Paid full tuition fee - Australian Capital Territory Government subsidised',
    ],
    /* Basis For Admission array */
    'arrBasisForAdmission' => [
        '' => '- - Select Basic Admission - -',
        '1' => '(01)Not a commencing student',
        '2' => '(31)A higher education course',
        '3' => '(33)Secondary education undertaken at school, VET or other Higher Education Provider',
        '4' => '(34)A VET award course other than a secondary education course',
        '5' => '(36)Mature age special entry provisions',
        '6' => '(37)A professional qualification',
        '7' => '(29)Other basis',
    ],
    'arrYearArrival' => $arrArrivialYear,
    // WE HAVE ALREADY CTEATE YABLE FOT THIS -> rto_employment_status
    /* Wet Fee Help Employment Status */
    'arrEmploymentStatus' => [
        '' => '- - Select Employment Status - -',
        '1' => '(99)No information provided',
        '2' => '(01)Full-time employee',
        '3' => '(02)Part-time employee',
        '4' => '(03)Self-employed - not employing others',
        '5' => '(04)Employer',
        '6' => '(05)Employed - unpaid worker in a family business',
        '7' => '(06)Unemployed - seeking full-time work',
        '8' => 'Unemployed - seeking part-time work',
        '9' => 'Not employed - not seeking employment',
    ],
    'modeOfAttendance' => [
        '1' => '1 - Internal Mode of Attendance at an on-shore or off-shore campus',
        '2' => '2 - External Mode of Attendance at an on-shore or off-shore campus',
        '3' => '3 - Multi-modal Mode of Attendance',
        '5' => '5 - Award granted on the basis of original work submission (Higher Ed Only)',
        '6' => '6 - Employer based (VET providers only)',
    ],
    'arrReportType' => [
        '1' => 'Final Report (Yearly)',
        '2' => 'Interim Report (Monthly)',
        '3' => 'Interim Report By Specific Period',
    ],
    'arrAvetMissExportsType' => [
        '1' => 'AVETMISS for State',
        '2' => 'AVETMISS for National',
    ],
    'arrMonthName' => [
        '01' => 'Jan',
        '02' => 'Feb',
        '03' => 'March',
        '04' => 'April',
        '05' => 'May',
        '06' => 'Jun',
        '07' => 'July',
        '08' => 'Aug',
        '09' => 'Sep',
        '10' => 'Oct',
        '11' => 'Nov',
        '12' => 'Dec',
    ],
    'arrAvetMissFileName' => [
        '1' => 'nat00010',
        '2' => 'nat00010A',
        '3' => 'nat00020',
        '4' => 'nat00030',
        '5' => 'nat00030A',
        '6' => 'nat00060',
        '7' => 'nat00080',
        '8' => 'nat00085',
        '9' => 'nat00090',
        '10' => 'nat00100',
        '11' => 'nat00120',
        '12' => 'nat00130',
    ],
    /*  TRANING Organisation type array */
    'arrTraningOrgazinationType' => [
        '21' => '21 - School - Government',
        '25' => '25 - School - Catholic',
        '27' => '27 - School - Independent',
        '31' => '31 - Technical and Further Education institute',
        '41' => '41 - University - Government',
        '43' => '43 - University - Non-Government Catholic',
        '45' => '45 - University - Non-Government Independent',
        '51' => '51 - Enterprise - Government',
        '53' => '53 - Enterprise - Non-government',
        '61' => '61 - Community based adult education provider',
        '91' => '91 - Education/training business or centre: Privately operated registered training organisation',
        '93' => '93 - Professional association',
        '95' => '95 - Industry association',
        '97' => '97 - Equipment and/or product manufacturer or supplier',
        '99' => '99 - Other - not elsewhere classified',
    ],

    'arrSchoolType' => [
        '21' => '21 - School(Government)',
        '25' => '25 - School(Catholic)',
        '27' => '27 - School(Independent)',
        '31' => '31 - Technical and Further Education institute',
        '61' => '61 - Community based adult education provider',
        '91' => '91 - Privately operated registered training organisation',
        '92' => '92 - Home school arrangement',
    ],

    'arrTraningOrgazinationState' => [
        '01' => 'NSW',
        '02' => 'VIC',
        '03' => 'QLD',
        '04' => 'SA',
        '05' => 'WA',
        '06' => 'TAS',
        '07' => 'NT',
        '08' => 'ACT',
        '09' => 'OAT or dependencies',
        '99' => 'Other-Overseas',
    ],
    'arrTraningOrgazinationIndustryCode' => [
        '44' => 'Accommodation (44)',
        '72' => 'Administrative Services (72)',
        '82' => 'Adult, Community and Other Education (82)',
        '01' => 'Agriculture (01)',
        '05' => 'Agriculture, Forestry and Fishing Support Services (05)',
        '49' => 'Air and Space Transport (49)',
        '02' => 'Aquaculture (02)',
        '64' => 'Auxiliary Finance and Insurance Services (64)',
        '18' => 'Basic Chemical and Chemical Product Manufacturing (18)',
        '33' => 'Basic Material Wholesaling (33)',
        '12' => 'Beverage and Tobacco Product Manufacturing (12)',
        '56' => 'Broadcasting (except Internet) (56)',
        '73' => 'Building Cleaning, Pest Control and Other Support Services (73)',
        '30' => 'Building Construction (30)',
        '06' => 'Coal Mining (06)',
        '38' => 'Commission-Based Wholesaling (38)',
        '70' => 'Computer System Design and Related Services (70)',
        '32' => 'Construction Services (32)',
        '90' => 'Creative and Performing Arts Activities (90)',
        '76' => 'Defence (76)',
        '26' => 'Electricity Supply (26)',
        '10' => 'Exploration and Other Mining Support Services (10)',
        '22' => 'Fabricated Metal Product Manufacturing (22)',
        '62' => 'Finance (62)',
        '04' => 'Fishing, Hunting and Trapping (04)',
        '45' => 'Food and Beverage Services (45)',
        '11' => 'Food Product Manufacturing (11)',
        '41' => 'Food Retailing (41)',
        '03' => 'Forestry and Logging (03)',
        '40' => 'Fuel Retailing (40)',
        '25' => 'Furniture and Other Manufacturing (25)',
        '92' => 'Gambling Activities (92)',
        '27' => 'Gas Supply (27)',
        '36' => 'Grocery, Liquor and Tobacco Product Wholesaling (36)',
        '31' => 'Heavy and Civil Engineering Construction (31)',
        '89' => 'Heritage Activities (89)',
        '84' => 'Hospitals (84)',
        '63' => 'Insurance and Superannuation Funds (63)',
        '57' => 'Internet Publishing and Broadcasting (57)',
        '59' => 'Internet Service Providers, Web Search Portals and Data Processing Services (59)',
        '60' => 'Library and Other Information Services (60)',
        '24' => 'Machinery and Equipment Manufacturing (24)',
        '34' => 'Machinery and Equipment Wholesaling (34)',
        '85' => 'Medical and Other Health Care Services (85)',
        '08' => 'Metal Ore Mining (08)',
        '55' => 'Motion Picture and Sound Recording Activities (55)',
        '39' => 'Motor Vehicle and Motor Vehicle Parts Retailing (39)',
        '35' => 'Motor Vehicle and Motor Vehicle Parts Wholesaling (35)',
        '09' => 'Non-Metallic Mineral Mining and Quarrying (09)',
        '20' => 'Non-Metallic Mineral Product Manufacturing (20)',
        '43' => 'Non-Store Retailing and Retail Commission-Based Buying and/or Selling (43)',
        '100' => 'Not specified or Blank value for Funding Source Identifier-State is not LSG, PSG or SSG (100)',
        '07' => 'Oil and Gas Extraction (07)',
        '37' => 'Other Goods Wholesaling (37)',
        '99' => 'Other Non-Metallic Mineral Mining and Quarrying (99)',
        '42' => 'Other Store-Based Retailing (42)',
        '50' => 'Other Transport (50)',
        '95' => 'Personal and Other Services (95)',
        '17' => 'Petroleum and Coal Product Manufacturing (17)',
        '19' => 'Polymer Product and Rubber Product Manufacturing (19)',
        '51' => 'Postal and Courier Pick-up and Delivery Services (51)',
        '80' => 'Preschool and School Education (80)',
        '21' => 'Primary Metal and Metal Product Manufacturing (21)',
        '16' => 'Printing (including the Reproduction of Recorded Media) (16)',
        '96' => 'Private Households Employing Staff and Undifferentiated Goods- and Service-Producing Activities of Households for Own Use (96)',
        '69' => 'Professional, Scientific and Technical Services (Except Computer System Design and Related Services) (69)',
        '67' => 'Property Operators and Real Estate Services (67)',
        '75' => 'Public Administration (75)',
        '77' => 'Public Order, Safety and Regulatory Services (77)',
        '54' => 'Publishing (except Internet and Music Publishing) (54)',
        '15' => 'Pulp, Paper and Converted Paper Product Manufacturing (15)',
        '47' => 'Rail Transport (47)',
        '66' => 'Rental and Hiring Services (except Real Estate) (66)',
        '94' => 'Repair and Maintenance (94)',
        '86' => 'Residential Care Services (86)',
        '46' => 'Road Transport (46)',
        '87' => 'Social Assistance Services (87)',
        '91' => 'Sports and Recreation Activities (91)',
        '58' => 'Telecommunications Services (58)',
        '81' => 'Tertiary Education (81)',
        '13' => 'Textile, Leather, Clothing and Footwear Manufacturing (13)',
        '23' => 'Transport Equipment Manufacturing (23)',
        '52' => 'Transport Support Services (52)',
        '53' => 'Warehousing and Storage Services (53)',
        '29' => 'Waste Collection, Treatment and Disposal Services (29)',
        '28' => 'Water Supply, Sewerage and Drainage Services (28)',
        '48' => 'Water Transport (48)',
        '14' => 'Wood Product Manufacturing (14)',
    ],
    'arrTopicList' => [
        '23' => 'Academic',
        '59' => 'Facilities',
        '60' => 'Administration',
        '61' => 'Marketing',
        '62' => 'Complaints',
    ],
    // user activity category
    'arrActivityCategory' => [
        '' => 'All activities',
        '1' => 'Application',
        '2' => 'VFH',
        '3' => 'Online Test',
        '4' => 'Account Login',
        '5' => 'Unspecified',
    ],
    // user activity: user type
    'arrActivityUserType' => [
        '' => 'All user types',
        '0' => 'Anonymous',
        '14' => 'Agent',
        '10' => 'Staff',
        '13' => 'Student',
        '14' => 'Unknown',
    ],
    'arrPayPeriodType' => [
        '1' => 'Academic Pay Period',
        '2' => 'Admin Pay Period',
    ],
    'arrPayPeriodFrequency' => [
        '1' => 'Weekly',
        '2' => 'Fortnightly',
        '3' => 'Monthly',
    ],
    'recurringInterval' => [
        '1' => 'Once Only',
        '2' => 'Weekly',
        '3' => 'Fortnightly',
        '4' => 'Monthly',
    ],
    'arrAssessmentFrequency' => [
        '1' => 'Weekly',
        '2' => 'Fortnightly',
        '3' => 'Monthly',
        '4' => 'Once Off',
    ],
    'arrAssignAccessTo' => [
        '1' => 'Class',
        '2' => 'Course',
        '3' => 'User',
        '4' => 'Role',
    ],
    'arrAboriginalTorresStrait' => [
        '' => '- - Select aboriginal status - -',
        '3' => '3 | Yes, Both Aboriginal and Torres Strait Islander',
        '1' => '1 | Yes, only Aboriginal',
        '2' => '2 | Yes, only Torres Strait Islander',
        '4' => '4 | No, Neither Aboriginal nor Torres Strait Islander',
        '@' => '@ | Not stated/Prefer not to say',
    ],
    'arrEnglishSpeakingRating' => [
        '1' => 'Very Well',
        '2' => 'Well',
        '3' => 'Not Well',
        '4' => 'Not At All',
        '@' => 'Not Stated',
    ],
    'arrSelectTypeEmail' => [
        'Student Course' => 'Student Course',
        'Particular Student' => 'Particular Student',
    ],
    'arrModeList' => [
        '1' => 'Lecture',
        '2' => 'Lab',
        '3' => 'Practicle',
        '4' => 'Supervision',
        '5' => 'Tutorial',
    ],
    'arrMailingListType' => [
        'all' => 'All Active Staff',
        'position' => 'Staff Position',
    ],
    'arrNvrDataType' => [
        '1' => 'Organisation details',
        '2' => 'Qualification activity',
        '3' => 'Units and modules activity',
    ],
    'arrPermissionList' => [
        '1' => 'Activate/Deactivate Staff/Teacher Comm Log',
        '2' => 'Activate/Deactivate Student Comm Log',
        '3' => 'Allow staff to delete record in student intervention register',
        '4' => 'Allow staff to do CRM admin task (Assign Staff, Setup, Monthly Target)',
        '5' => 'Allow Staff to View/Update Student Tax File Number (TFN)',
        '6' => 'Allow to delete student results if attendance exists',
        '7' => 'Allow to Modify Schedule Invoice',
        '8' => 'Allow to Send SMS',
        '9' => 'Allow to update student course info (override)',
        '10' => 'Allow to Update Subject Outcome for Withdrawn/Cancelled Course',
        '11' => 'Approve Assessment',
        '12' => 'Approve Trainer Timesheet',
        '13' => 'Certificate Generation',
        '14' => 'Delete Student Account',
        '15' => 'Deny Attendance Entry',
        '16' => 'Deny Attendance View',
        '17' => 'Deny staff to add student subject enrolment',
        '18' => 'Deny staff to update student course info',
        '19' => 'Deny staff to update student profile info',
        '20' => 'Deny staff to update/delete student results',
        '21' => 'Deny staff to view agent commission',
        '22' => 'Deny staff to view student profile letter',
        '23' => 'Deny Student Email Feedback',
        '24' => 'Deny Student to Update Address',
        '25' => 'Deny Student To View Attendance',
        '26' => 'Generate Bulk Certificate',
        '27' => 'Generate Invoice',
        '28' => 'Modify Agent Comm Log',
        '29' => 'Modify Student Comm Log',
        '30' => 'Modify Transaction',
        '31' => 'Process Agent Payment Only',
        '32' => 'Process Student Payment Only',
        '33' => 'Process Trainer Timesheet',
        '34' => 'Redirect staff to CRM dash board after login',
        '35' => 'Setup Letter and Email Template',
        '36' => 'Student Sanction',
        '37' => 'USI Process/Generation',
        '38' => 'View CRM Dashboard',
        '39' => 'View Transcript',
    ],
    'arrPagePermissionList' => [
        '1' => 'Deny Agent to upload ECoE or Additional Documents',
        '2' => 'Deny Agent to Send Feedback',
        '3' => 'Deny Agent to Apply for Student',
        '4' => 'Deny Agent to Generate Payment Advice',
        '5' => 'Deny Agent to View Student List',
        '6' => 'Deny Agent to View Offer List',
        '7' => 'Deny Agent to View Commission Payment',
        '8' => 'Deny Agent to View Commission Rate',
        '9' => 'Deny Agent to View Documents',
        '10' => 'Deny Agent to View Reports',
        '11' => 'Deny Student to View Assessment',
        '12' => 'Allow student to update AVETMISS data',
        '13' => 'Deny Student To Update Address ',
        '14' => 'Deny Student to Email to Trainer',
        '15' => 'Deny Student to View Evaluation',
        '16' => 'Deny Student Email Feedback ',
        '17' => 'Deny Student to Online Assessment Submission',
        '18' => 'Deactivate online test feature in student portal',
        '19' => 'Deny Student to View Results',
        '20' => 'Deny Student to View Study Plan',
        '21' => 'Deny Student to View Class Timetable',
        '22' => 'Deny Student To Submit USI Consent Form',
        '23' => 'Deny Student To View Attendance',
        '24' => 'Deny Student to Download Course Material',
        '25' => 'Deny Student to Download College Document',
        '26' => 'Deny Student to View Payment Details',
        '27' => 'Deny Student to View Warning Log',
    ],
    'arrPagePermissionList1' => [
        'Agt_AddDocECoe' => 'Deny Agent to upload ECoE or Additional Documents',
        'Agt_Feedback' => 'Deny Agent to Send Feedback',
        'Agt_NewApply' => 'Deny Agent to Apply for Student',
        'Agt_PaymentAdvice' => 'Deny Agent to Generate Payment Advice',
        'Agt_StdList' => 'Deny Agent to View Student List',
        'Agt_StdOfferList' => 'Deny Agent to View Offer List',
        'Agt_ViewAgentPayment' => 'Deny Agent to View Commission Payment',
        'Agt_ViewCommission' => 'Deny Agent to View Commission Rate',
        'Agt_ViewDocument' => 'Deny Agent to View Documents',
        'Agt_ViewReports' => 'Deny Agent to View Reports',
        'Std_Assessment' => 'Deny Student to View Assessment',
        'Std_EditAVETMISS' => 'Allow student to update AVETMISS data',
        'Std_EditDetails' => 'Deny Student To Update Address ',
        'Std_EmailToTrainer' => 'Deny Student to Email to Trainer',
        'Std_EvaluationForm' => 'Deny Student to View Evaluation',
        'Std_FeedbackComplaint' => 'Deny Student Email Feedback ',
        'Std_OnlineAssment_Submission' => 'Deny Student to Online Assessment Submission',
        'Std_OnlineTest' => 'Deactivate online test feature in student portal',
        'Std_Results' => 'Deny Student to View Results',
        'Std_StudyPlan' => 'Deny Student to View Study Plan',
        'Std_Timetable' => 'Deny Student to View Class Timetable',
        'Std_USIConsentForm' => 'Deny Student To Submit USI Consent Form',
        'Std_ViewAttendance' => 'Deny Student To View Attendance',
        'Std_ViewCourseMaterial' => 'Deny Student to Download Course Material',
        'Std_ViewDocuments' => 'Deny Student to Download College Document',
        'Std_ViewPayment' => 'Deny Student to View Payment Details',
        'Student_CommLogWarning' => 'Deny Student to View Warning Log',
    ],
    'arrQualifictionLevelList' => [
        'None' => 'None',
        'High School' => 'High School',
        'Certificate' => 'Certificate',
        'Diploma' => 'Diploma',
        'Bachelor Degree' => 'Bachelor Degree',
    ],
    'arrActivityList' => [
        '1' => 'TEST - ACTIVITY - TYPE',
    ],
    'arrActivityTypeList' => [
        'C' => 'Class/Training Activity',
        'E' => 'Extra Activity',
    ],
    'arrAttendanceListStatus' => [
        'All' => 'All',
        'Current' => 'Current',
    ],
    'arrAttendanceListStudentStatus' => [
        // '' => 'All Students',
        'Current Student' => 'Current Student',
    ],
    'arrAllocateProviderSearch' => [
        'ID' => 'ID',
        'Name' => 'Name',
        'Type' => 'Type',
    ],
    'arrAssignmentStatus' => [
        'Assigned' => 'Assigned',
        'Not Assigned' => 'Not Assigned',
    ],
    'arrBultEnrolmentByGroup' => [
        'StudentId' => 'StudentId',
        'StudentName' => 'Student Name',
        'StartDate' => 'Start Date',
    ],
    'arrAscendingDescending' => [
        'asc' => 'Ascending',
        'desc' => 'Descending',
    ],
    'arrAgentStudentSearch' => [
        'Offer ID' => 'Offer ID',
        'Date Applied' => 'Date Applied',
        'First Name' => 'First Name',
        'Last Name' => 'Last Name',
        'Course' => 'Course',
        'Status' => 'Status',
        'Application' => 'Application by FirstName/LastName/PassportNo (Not Submitted)',
    ],
    'arrLetterCategory' => [
        '1' => 'Academic',
        '2' => 'Attendance',
        '3' => 'DocX',
        '4' => 'LetterForm',
        '5' => 'OSHC',
        '6' => 'Payment',
    ],
    'arrLetterRecipient' => [
        'Student' => 'Student',
        'Agent' => 'Agent',
    ],
    'arrLetterTrackColor' => [
        'Blue' => 'Blue',
        'Red' => 'Red',
    ],
    'arrLetterOrderBy' => [
        'priority' => 'Priority',
        'letter' => 'Letter',
    ],
    'arrDocudmentType' => [
        'Additional Documents' => 'Additional Documents',
        'ECoE Request Documents' => 'ECoE Request Documents',
    ],
    'arrLetterOperator' => [
        '+' => '+',
        '-' => '-',
    ],
    'arrAgentServiceType' => [
        '1' => 'View approved commission with due dates between two dates',
        '2' => 'All processed commission',
    ],
    'arrCommissionList' => [
        '1' => 'Commission Ready To Process',
        '2' => 'Commission Paid or Deducted',
    ],
    'arrTypeofreport' => [
        '' => 'Please select a report to generate',
        '1' => '1. Student conversion report between two dates',
        '2' => '2. Student summary (offered, enrolled, current)',
        '3' => '3. Student nationality report',
        '4' => '4. List of Current Students',
        '5' => '5. List of Inactive student',
    ],
    'arrFilterCommunication' => [
        '1' => 'All Logs',
        '2' => 'Log Status',
        '3' => 'Log Type',
    ],
    'arrMonthDevide' => [
        '1' => 'January - June',
        '2' => 'July - December',
    ],
    'arrUsiFilter' => [
        'all' => 'All (Include both has/not have USI)',
        'has_usi' => 'Has USI',
        'no_usi' => 'No USI',
        'has_verified_usi' => 'Has Verified USI',
        'has_unverified_usi' => 'Has Unverified USI',
    ],
    'arrDvsInfoFilter' => [
        'all' => 'All (Include both provide/not provide DVS info)',
        'has_dvs' => 'Provided DVS',
        'no_dvs' => 'Not provide DVS',
    ],
    'arrSearchByFieldForUSI' => [
        'generated_stud_id' => 'Student ID',
        'first_name' => 'First Name',
        'family_name' => 'Last Name',
        'student_name' => 'Student Name (First and/or Last Name)',
        'DOB' => 'Date of Birth',
        'student_type' => 'Student Origin',
        // 'course' => 'Course',
        'status' => 'Status',
        // 'agent' => 'Agent',
        'start_date' => 'Course Start Date',
        'offer_id' => 'Offer No',
        'email' => 'Email',
        'current_mobile_phone' => 'Mobile',
    ],
    'arrDVSDocumentTypeList' => [
        'DriverLicense' => "Driver's License",
        'Medicare' => 'Medicare Card',
        'AustralianPassport' => 'Passport (Australian)',
        'Visa' => 'Visa (Non-Australian Passport)',
        'BirthCert' => 'Birth Certificate (Australian)',
        'Descent' => 'Certificate of Registration by Descent',
        'Citizenship' => 'Citizenship Certificate',
        'ImmiCard' => 'ImmiCard',
    ],
    'arrDistributionType' => [
        'studentClassList' => 'Student Class List',
        'studentCourse' => 'Student Course',
        'courseDuration' => 'Student Course Between Dates',
        'perticularStudent' => 'Particular Student',
        'courseFinishDate' => 'Student Course Finish Date Between Dates',
    ],
    'arrTeacherTimesheetReportType' => [
        'class_proceed' => 'View Processed Timesheet',
        'class_submitted' => 'View Not Processed Timesheet',
        'extra_proceed' => 'View Processed Extra Activity Timesheet',
        'extra_submitted' => 'View Not Processed Extra Activity Timesheet',
    ],
    'arrFormType' => [
        '1' => 'Evaluation',
        '2' => 'Evaluation (Send Request)',
        '3' => 'Performance',
        '4' => 'AQTF Servey',
    ],
    'arrFormCategory' => [
        '1' => 'Course Type',
        '2' => 'Course',
        '3' => 'Subject',
        '4' => 'None',
    ],
    'arrFormAccessRoll' => [
        'Student' => 'Student',
        'Admin' => 'Admin',
        'Teacher' => 'Teacher',
        'Agent' => 'Agent',
        'Employer' => 'Employer',
    ],
    'arrQuestionType' => [
        'Radio' => 'Radio-Text',
        'CheckBox' => 'Check Box',
        'Text' => 'Text',
        'DD/MM/YYYY' => 'DD/MM/YYYY',
    ],
    'arrFormStudentBy' => [
        '1' => 'Class',
        '2' => 'Course',
        '3' => 'Course and Start Date Between Two Dates',
        '4' => 'Course and Finish Date Between Two Dates',
    ],
    'arrSelectedFilter' => [
        'semester' => 'Selected Semester',
        'subject' => 'Selected Subject',
        'room' => 'Selected Room',
        'teacher' => 'Selected Teacher',
        'period' => 'Selected Period(Term & Week)',
    ],
    'arrCountryFilter' => [
        '' => 'All Countries',
        'absvalue' => 'ABS Value',
        'name' => 'Country',
        'countrylevel' => 'Country Level',
        'region' => 'Region',
    ],
    'arrVenueFilter' => [
        'venue_code' => 'Venue Code',
        'state' => 'State',
        'country' => 'Country',
        'postcode' => 'Post Code',
    ],
    'arrCollegeMaterialsFilter' => [
        'name' => 'Order By file/directory name',
        'timeLine' => 'Order By Last Write Time',
    ],
    'arrProvidePaymentFilter' => [
        'studentId' => 'Student ID',
        'studentName' => 'Student Name',
        'invoiceNo' => 'Invoice No',
        'due_date' => 'Services Start Date',
    ],
    'arrFromEmailId' => [
        '0' => env('MAIL_USERNAME'),
        '1' => env('MAIL_NAME'),
        '2' => env('MAIL_FROM_ADDRESS'),
    ],
    'arrDateType' => [
        '1' => 'Date only',
        '2' => 'Date Time',
    ],
    'arrFacilityPriceType' => [
        '1' => 'Per Services',
        '2' => 'Per Week',
    ],
    'arrCurrentVisaStatus' => [
        '1' => 'Application in Progress',
        '2' => 'Application Submitted',
        '3' => 'VISA Granted',
        '4' => 'VISA Refused',
    ],
    'arrServicesProviderFilter' => [
        'All' => 'All Provider',
        'Active' => 'Active Only',
        'Company' => 'Company Name',
        'Contact' => 'Contact Name',
        'Email' => 'Email',
        'Mobile' => 'Mobile',
        'Phone' => 'Phone',
    ],
    'arrCollegeRootFolder' => [
        'Advert' => 'Advert',
        'AgentFiles' => 'AgentFiles',
        'CollegeLogo' => 'CollegeLogo',
        'AVETMISS' => 'AVETMISS',
        'DeletedFiles' => 'DeletedFiles',
        'Forms' => 'Forms',
        'StudentMailAttach' => 'StudentMailAttach',
        'StaffMailAttach' => 'StaffMailAttach',
        'AgentMailAttach' => 'AgentMailAttach',
        'OfferFiles' => 'OfferFiles',
        'ApplicationForm' => 'ApplicationForm',
        'OrgFiles' => 'OrgFiles',
        'PrismSetup' => 'PrismSetup',
        'ReportImage' => 'ReportImage',
        // 'StaffFiles' => 'StaffFiles',     //when add new clg that time not generate, but where add staff file that time create folder and working fine
        'StudentFiles' => 'StudentFiles',
        'StudentPics' => 'StudentPics',
        'TempFiles' => 'TempFiles',
        'Templates' => 'Templates',
        'TermsAndConditions' => 'TermsAndConditions',
        'TimesheetInvoice' => 'TimesheetInvoice',
        'StudentCOE' => 'StudentCOE',
        'LetterFile' => 'LetterFile',
    ],
    'arrSurveyContactStatusDiv' => [
        'A' => 'Available for survey use',
        'C' => 'Correctional facility(address or enrollment)',
        'D' => 'Deceased student',
        'E' => 'Exluded from survey use',
        'I' => 'Invalid address/ltinerant student(very low likelihood of response)',
        'M' => 'Minor - under age of 15(not to be surveyed)',
        'O' => 'Overseas (address of enrolment)',
    ],
    'arrVslCourseType' => [
        '0' => 'All VSL courses',
        '1' => 'Active VSL courses',
        '2' => 'Inactive VSL courses',
    ],
    'arrVetFeeReportingType' => [
        'VET Student Loan' => 'VET Student Loan',
    ],
    'arrServiceRequestAllocation' => [
        'with' => 'With Start Date Assigned',
        'without' => 'Without Start Date Assigned',
        'withprovider' => 'Provider Assigned',
        'withoutprovider' => 'Without Provider Assigned',
        'all' => 'All Data',
    ],
    'arrIdentityOfUSI' => [
        '1' => "Australian Driver's Licence",
        '2' => 'Non-Australian Passport (with Australian Visa)',
        '3' => 'Medicare Card',
        '4' => 'Immicard',
        '5' => 'Australian Birth Certificate',
        '6' => 'Citizenship Certificate',
        '7' => 'Australian Passport',
        '8' => 'Certificate of Registration by Descent',
    ],
    'arrSurveyContactStatus' => [
        'A' => 'Available for survey use',
        'I' => 'Invalid address/ltinerant student',
        'C' => 'Correctional facility (address or enrolment)',
        'M' => 'Minor-under age of 15 (not to be surveyed)',
        'D' => 'Deceased student',
        'O' => 'Overseas (address or enrolment)',
        'E' => 'Excluded from survey use',
    ],
    'arrDeliveryMode' => [
        'YNN' => 'Internal only',
        'NYN' => 'External only',
        'NNY' => 'Workplace-based only',
        'YYN' => 'Combination of internal and external',
        'YNY' => 'Combination of internal and workplace-based',
        'NYY' => 'Combination of external and workplace-based ',
        'YYY' => 'Combination of all modes',
        'NNN' => 'Not applicable (RPL or credit transfer)',
    ],
    'unitDeliveryModeInputType' => 'dropdown', // can be dropdown or checkbox
    'arrPredominantDeliveryMode' => [
        'YNN' => ['I' => 'Internal delivery'],
        'NYN' => ['E' => 'External delivery'],
        'NNY' => ['W' => 'Workplace-based delivery'],
        'YYN' => ['I' => 'Internal delivery', 'E' => 'External delivery'],
        'YNY' => ['E' => 'Internal delivery', 'W' => 'Workplace-based delivery'],
        'NYY' => ['E' => 'External delivery', 'W' => 'Workplace-based delivery'],
        'YYY' => ['I' => 'Internal delivery', 'E' => 'External delivery', 'W' => 'Workplace-based delivery'],
        'NNN' => ['N' => 'Not applicable'],
    ],
    'arrPdfTemplateType' => [
        '1' => 'Offer Letter',
        '2' => 'Domestic Offer Letter',
        '3' => 'IIE Offer Letter',
    ],
    'arrOfferLetterPdfTemplateSlug' => [
        '{college_name}' => 'college_name',
        '{college_legal_name}' => 'college_legal_name',
        '{college_ABN}' => 'college_ABN',
        '{college_CRICOS_code}' => 'college_CRICOS_code',
        '{college_RTO_code}' => 'college_RTO_code',
        '{college_street_address}' => 'college_street_address',
        '{college_street_suburb}' => 'college_street_suburb',
        '{college_street_state}' => 'college_street_state',
        '{college_street_postcode}' => 'college_street_postcode',
        '{college_contact_phone}' => 'college_contact_phone',
        '{college_contact_email}' => 'college_contact_email',
        '{college_account_email}' => 'college_account_email',
        '{college_logo}' => 'college_logo',
        '{college_signature}' => 'college_signature',
        '{college_url}' => 'college_url',
        '{application_reference_id}' => 'application_reference_id',
        '{current_date}' => 'current_date',
        '{student_name_title}' => 'student_name_title',
        '{student_first_name}' => 'student_first_name',
        '{student_family_name}' => 'student_family_name',
        '{student_gender}' => 'student_gender',
        '{student_date_of_birth}' => 'student_date_of_birth',
        '{student_email}' => 'student_email',
        '{nationality_of_student}' => 'nationality_of_student',
        '{student_passport_no}' => 'student_passport_no',
        '{student_current_street_no}' => 'student_current_street_no',
        '{student_current_street_name}' => 'student_current_street_name',
        '{student_current_city}' => 'student_current_city',
        '{student_current_state}' => 'student_current_state',
        '{student_current_postcode}' => 'student_current_postcode',
        '{student_country_name}' => 'student_country_name',
        '{student_country_of_birth}' => 'student_country_of_birth',
        '{ielts_score}' => 'ielts_score',
        '{student_course_detail_table}' => 'student_course_detail_table',
        '{initial_payment}' => 'initial_payment',
        '{material_fee}' => 'material_fee',
        '{remaining_payment}' => 'remaining_payment',
        '{student_course_payment_schedule_fee}' => 'student_course_payment_schedule_fee',
        '{student_work_placement_detail_table}' => 'student_work_placement_detail_table',
        '{student_initial_payment_required_table}' => 'student_initial_payment_required_table',
        '{student_course_special_condition_table}' => 'student_course_special_condition_table',
        '{college_bank_name}' => 'college_bank_name',
        '{college_bank_account_name}' => 'college_bank_account_name',
        '{college_bank_BSB}' => 'college_bank_BSB',
        '{college_bank_account_number}' => 'college_bank_account_number',
        '{student_offer_id}' => 'student_offer_id',
        '{college_bank_swift_code}' => 'college_bank_swift_code',
        '{college_bank_branch}' => 'college_bank_branch',
        '{student_payment_schedule_table}' => 'student_payment_schedule_table',
        '{student_parent_guardian_table}' => 'student_parent_guardian_table',
        '{student_course_detail_with_study_design_2_table}' => 'student_course_detail_with_study_design_2_table',
        '{student_course_details_design_2_table}' => 'student_course_details_design_2_table',
        '{student_course_details_design_3_table}' => 'student_course_details_design_3_table',
        '{student_course_fees_and_charges_design_2_table}' => 'student_course_fees_and_charges_design_2_table',
        '{student_payment_schedule_design_2_table}' => 'student_payment_schedule_design_2_table',
        '{student_course_total_tuition_fees_domestic_table}' => 'student_course_total_tuition_fees_domestic_table',
        '{student_course_initial_payment_required_domestic_table}' => 'student_course_initial_payment_required_domestic_table',
        '{total_fee_payble_by_student}' => 'total_fee_payble_by_student',
    ],
    'arrFinalOutcome' => [
        '20' => 'Pass',
        '30' => 'Fail',
    ],
    'arrUnitOfStudyStatus' => [
        '1' => '1 - Withdrew without academic penalty',
        '2' => '2 - Failed',
        '3' => '3 - Successfully completed all the requirements',
        '4' => '4 - Unit of study to be commenced later in the year or still in process of completing or completion status not yet determined',
        '5' => '5 - Recognition of prior learning (VET only)',
        '6' => '6 - Withdrew due to medical reasons',
    ],
    'arrTaskPriority' => [
        'low' => 'Low',
        'medium' => 'Medium',
        'high' => 'High',
        'critical' => 'Critical',
    ],
    'arrTaskPriorityColor' => [
        'low' => '#70b23f',
        'medium' => '#f0d723',
        'high' => '#ff7f4f',
        'critical' => '#ea6747',
    ],
    'arrTaskStatus' => [
        'todo' => 'Todo',
        'inprogress' => 'In Progress',
        'inreview' => 'In Review',
        'done' => 'Done',
    ],
    'arrTaskStatusColor' => [
        'todo' => '#3ab6cd',
        'inprogress' => '#a9dbd2',
        'inreview' => '#70b23f',
        'done' => '#f0d723',
    ],

    'arrStatusbgColor' => [
        'New Application Request' => 'gray',
        'Reconsider' => '#f0d723',
        'Rejected' => '#ea6747',
        'Offered' => '#70b23f',
        'Pending' => '#3ab6cd',
        'Incomplete' => '#c33d43',
        '' => '598d83',
    ],
    'notificationConfig' => [
        'taskassign' => [
            'label' => 'Assign Tasks',
            'bgColorClass' => 'bg-primary-blue-500',
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 20 20"><path fill="#fff" fill-rule="evenodd" d="M16.707 5.293c.39.39.39 1.024 0 1.414l-8 8c-.39.39-1.024.39-1.414 0l-4-4c-.39-.39-.39-1.024 0-1.414.39-.39 1.024-.39 1.414 0L8 12.586l7.293-7.293c.39-.39 1.024-.39 1.414 0z" clip-rule="evenodd"></path></svg>',
        ],
        'taskcomments' => [
            'label' => 'Task Comments',
            'bgColorClass' => 'bg-cyan-500',
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="#fff" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5c-1.105 0-2-.895-2-2V6c0-1.105.895-2 2-2h14c1.105 0 2 .895 2 2v8c0 1.105-.895 2-2 2h-5l-5 5v-5z"></path></svg>',
        ],
        'taskchangestatus' => [
            'label' => 'Task Status',
            'bgColorClass' => 'bg-yellow-500',
            'svgicon' => '<svg class="text-white h-6 w-6" width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>',
        ],
    ],
    'arrBasisForAdmissionCode' => [
        '31' => '31 - A higher education award course (Australian or overseas equivalent; complete or incomplete)',
        '32' => '32 - An enabling or bridging course delivered by a higher education provider (complete or incomplete)',
        '34' => '34 - A VET award course or VET delivered enabling or bridging course other than a secondary education course (Australian or equivalent; complete or incomplete)',
        '40' => '40 - Work and life experience',
        '41' => '41 - Recent secondary education (whether undertaken at school, VET or another Australian provider) – ATAR only',
        '42' => '42 - Recent secondary education (whether undertaken at school, VET or another Australian provider) – ATAR plus other criteria',
        '43' => '43 - Recent secondary education was undertaken (whether undertaken at school, VET or another Australian or overseas provider) – only other criteria, ATAR not considered',
    ],
    'arrModeOfAttendanceCode' => [
        '01' => '01 - Internal Mode of Attendance at an on-shore or off-shore campus (includes classroom based)',
        '02' => '02 - External Mode of Attendance at an on-shore or off-shore campus (includes electronic based, on line and correspondence)',
        '03' => '03 - Multi-modal Mode of Attendance',
        '06' => '06 - Employer based (VET providers only)',
    ],
    'arrTypeofAttendanceCode' => [
        '1' => '1 - Full-time attendance',
        '2' => '2 - Part-time attendance',
    ],
    'arrRemissionReasonCode' => [
        '01' => '01 - Remission due to special circumstances',
        '06' => '06 - Remission under an approved tuition assurance arrangement',
        '12' => '12 - Remission/Re-credit of HELP due to no tax file number',
        '16' => '16 - Not a genuine student',
        '17' => '17 - Provider completes Request for Commonwealth assistance form',
        '18' => '18 - Student not entitled/eligible',
        '19' => '19 - Student not academically suited',
    ],
    'arrHighestAttainmentCode' => [
        '110' => '110 - Doctoral Degree',
        '120' => '120 - Master Degree',
        '200' => '200 - Graduate Diploma or Graduate Certificate',
        '300' => '300 - Bachelor Degree',
        '410' => '410 - Advanced Diploma and Associate Degree',
        '420' => '420 - Diploma',
        '511' => '511 - Certificate IV',
        '514' => '514 - Certificate III',
        '521' => '521 - Certificate II',
        '524' => '524 - Certificate I',
        '000' => '000 - None of the above',
    ],
    'arrCourseFeeType' => [
        '00' => 'Does not apply to this course',
        '01' => 'Course offers only Commonwealth Supported places',
        '02' => 'Course offers only Domestic Fee-paying places',
        '03' => 'Course offers both Commonwealth Supported places and Domestic Fee-paying places',
    ],
    'arrStudyType' => [
        '01' => 'Higher Doctorate',
        '02' => 'Doctorate by research',
        '03' => 'Masters (Research)',
        '04' => 'Masters (Coursework)',
        '05' => 'Postgraduate Qualifying or Preliminary',
        '06' => 'Graduate Diploma/ Postgraduate Diploma (pass or honours) involving new academic, professional or vocational area',
        '07' => 'Graduate Diploma/ Postgraduate Diploma (pass or honours) extending skills and knowledge in a professional area previously studied',
        '08' => 'Bachelors Graduate Entry',
        '09' => 'Bachelors Honours',
        '10' => 'Bachelors Pass',
        '11' => 'Graduate Certificate',
        '12' => 'Doctorate by coursework',
        '13' => 'Associate degree',
        '14' => 'Masters (Extended)',
        '20' => 'Advanced Diploma',
        '21' => 'Diploma',
        '22' => 'Other undergraduate award course',
        '30' => 'Enabling course',
        '41' => 'Cross Institutional program for undergraduate courses at home Higher Education Provider',
        '42' => 'Cross Institutional program for postgraduate courses at home Higher Education Provider',
        '50' => 'Non-award course (including Bridging for overseas trained professionals)',
        '60' => 'Open Universities Australia undergraduate studies',
        '61' => 'Open Universities Australia postgraduate studies',
        '80' => 'VET Graduate Certificate',
        '81' => 'VET Graduate Diploma',
        '99' => 'Other',
    ],
    'arrTypeOfOperation' => [
        '01' => 'A stand-alone arrangement',
        '02' => 'A partnership arrangement',
    ],
    'arrEducationOfParent1' => [
        '20' => '20 - Postgraduate qualification (e.g. Postgraduate Diploma, Masters, PhD)',
        '21' => '21 - Bachelor Degree',
        '22' => '22 - Other post school qualification (e.g. VET Certificate, Associate Degree or Diploma',
        '23' => '23 - Completed Year 12 schooling or equivalent',
        '24' => '24 - Did not complete Year 12 schooling or equivalent',
        '25' => '25 - Completed Year 10 schooling or equivalent',
        '26' => '26 - Did not complete Year 10 schooling or equivalent',
        '49' => '49 - Don’t know',
        '99' => '99 - No information provided by student',
    ],
    'arrPrincipalModeOfDeliveryOffshoreCourse' => [
        '01' => 'Internal',
        '02' => 'External – with face-to-face component',
        '03' => 'External – with no face-to-face component',
        '04' => 'Multi-modal',
    ],
    'arrEducationOfParent2' => [
        '20' => "20 - Postgraduate qualification (e.g. Postgraduate Diploma, Master's, PhD)",
        '21' => '21 - Bachelor Degree',
        '22' => '22 - Other post school qualification (e.g. VET Certificate, Associate Degree or Diploma)',
        '23' => '23 - Completed Year 12 schooling or equivalent',
        '24' => '24 - Did not complete Year 12 schooling or equivalent',
        '25' => '25 - Completed Year 10 schooling or equivalent',
        '26' => '26 - Did not complete Year 10 schooling or equivalent',
        '49' => '49 - Don’t know',
        '99' => '99 - No information provided by the student',
    ],
    'arrOffshoreDeliveryIndicator' => [
        '01' => 'Course is delivered partially offshore',
        '02' => 'Course is delivered fully offshore',
    ],
    'arrLevelLeftSchool' => [
        '09' => '09 - Year 9',
        '10' => '10 - Year 10',
        '11' => '11 - Year 11',
        '12' => '12 - Year 12',
        '99' => '99 - High school level is unknown',
    ],
    'arrAdditionalEntranceCriteria' => [
        '1' => 'Entry Cut-off score is the only consideration for entrance into this course',
        '2' => 'Entry Cut-off score is not the only consideration for entrance into this course',
        '3' => 'Entry Cut-off score is not a consideration for entrance into this course',
    ],
    'arrSpecialCourseType' => [
        '21' => 'A general nursing course required for initial registration',
        '22' => 'A course providing initial teacher training',
        '23' => 'A course of study in medicine, completion of which would allow provisional registration as a medical practitioner by an authority of a State, a Territory or the Commonwealth',
        '25' => 'A course of study in veterinary science, completion of which would satisfy the academic requirements for registration as a veterinary surgeon or veterinary practitioner by an authority of a State, a Territory or the Commonwealth',
        '26' => 'Course of study in dentistry, completion of which would satisfy the academic requirements for registration as a dentist by an authority of a State, a Territory or the Commonwealth',
        '27' => 'Course of study in clinical psychology (as defined in the Commonwealth Grant Scheme Guidelines)',
        '28' => 'A course of study in aviation listed in the FEE-HELP Guidelines 2017',
    ],
    'arrCombinedCourseOfStudy' => [
        'true' => 'Course is a combined course of study',
        'false' => 'Course is not a combined course of study',
    ],
    'arrCourseRecurringTypes' => [
        'day' => 'Daily',
        'week' => 'Weekly',
        'fortnight' => 'Fortnightly',
        'month' => 'Monthly',
        'quarter' => 'Quarterly',
        'half year' => 'Half Yearly',
        'year' => 'Yearly',
    ],
    'arrCourseAccrediationTypes' => [
        'accrediated' => 'Accrediated Course',
        'unaccrediated' => 'Non-Accrediated Course',
        'none' => 'Not Defined',
    ],
    'arrDisabilityInformation' => [
        '11' => '11 - Hard To Hearing/deaf/Deaf',
        '12' => '12 - Physical disability',
        '13' => '13 - Intellectual disability',
        '14' => '14 - Specific learning disability',
        '15' => '15 - Mental health condition',
        '16' => '16 - Acquired brain injury',
        '17' => '17 - Low Vision/Blind',
        '18' => '18 - Medical condition',
        '19' => '19 - Other disability',
        '20' => '20 - Neurological condition',
        '99' => '99 - Not specified',
    ],
    'arrWorkExpIndustryCode' => [
        '1' => '1 - Student is undertaking work experience in industry where learning and performance is not directed by the Provider, but support is received from the Provider',
        '2' => '2 - Student is undertaking work experience in industry where learning and performance is not directed by, and support is not received from, the provider',
    ],
    'arrCourseOutcomeCode' => [
        '1' => '1 - Completed',
        '2' => '2 - Withdrawn',
        '3' => '3 - Enrolment cancelled',
        '4' => '4 - Approved leave',
        '5' => '5 - Completed but continuing a related course',
        '6' => '6 - Transfer to complete a related course',
        '7' => '7 - Transfer to continue a related course',
    ],
    'arrStudStatusCode' => [
        '201' => '201 - Deferred all or part of the student contribution through HECS-HELP',
        '204' => '204 - Paid full student contribution up-front',
        '230' => '230 - Deferred all or part of Award or Enabling course tuition fee through FEE-HELP',
        '231' => '231 - Deferred all or part of tuition fee through FEE-HELP - Employer reserved place',
        '232' => '232 - Deferred all or part of OUA tuition fee through FEE-HELP',
        '233' => '233 - Deferred all or part of BOTP tuition fee through FEE-HELP',
        '240' => '240 - OS-HELP for study in non-Asian countries',
        '241' => '241 - OS-HELP for study in Asia',
        '242' => '242 - OS-HELP Language study',
        '260' => '260 - Student in a Commonwealth supported place with an Exemption scholarship (no student contribution to be charged)',
        '261' => '261 - A domestic student enrolled in an enabling course (i.e. bridging or supplementary programme)',
        '262' => '262 - Student undertaking Work Experience in Industry (WEI) where learning and performance is not directed by, and support is not received from, the Provider and for which a student contribution cannot be charged',
        '263' => '263 - Students undertaking a replacement unit that will not consume student learning entitlement',
        '270' => '270 - Student in a non-Commonwealth supported place with an Exemption scholarship (no tuition fee to be charged)',
        '271' => '271 - Student undertaking Work Experience in Industry (WEI) where learning and performance is not directed by, and support is not received from, the provider and for which a tuition fee cannot be charged',
        '280' => '280 - Deferred all or part of SA-fee for a Course of Study through SA-HELP',
        '281' => '281 - Deferred all or part of SA-fee for a Bridging Course for Overseas Trained Professional through SA-HELP',
        '301' => '301 - A domestic student enrolled in a non-award course (other than an Enabling course)',
        '302' => '302 - Paid full Award or Enabling course tuition fee',
        '303' => '303 - Paid full tuition fee - for Employer reserved place',
        '304' => '304 - Paid full OUA tuition fee',
        '305' => '305 - Paid full BOTP tuition fee',
        '310' => '310 - A fee-paying overseas student who is not sponsored under a foreign aid program, and including students with these awards: SOPF (Special Overseas Postgraduate Fund); Australian-European Awards Program; and the Commonwealth Scholarship and Fellowship Plan',
        '311' => '311 - A fee-paying overseas student who is sponsored under a foreign aid program. Includes those with Australian Development Cooperation Scholarships (ADCOS) and any other Australian foreign aid program for which students are enrolled in higher education providers by the Department of Foreign Affairs and Trade',
        '331' => '331 - A domestic or overseas student receiving a Research Training Program (RTP) Fees Offset as defined in the Commonwealth Scholarships Guidelines (Research)',
        '401' => '401 - Deferred all/part of tuition fee through VET FEE-HELP / VET Student Loans - non State Government subsidised',
        '402' => '402 - Deferred all/part of tuition fee through VET FEE-HELP / VET Student Loans - Restricted Access Arrangement',
        '403' => '403 - Deferred all/part of tuition fee through VET FEE-HELP / VET Student Loans - Victorian State Government subsidised',
        '404' => '404 - Deferred all/part of tuition fee through VET FEE-HELP / VET Student Loans - New South Wales State Government subsidised',
        '405' => '405 - Deferred all/part of tuition fee through VET FEE-HELP / VET Student Loans - Queensland State Government subsidised',
        '406' => '406 - Deferred all/part of tuition fee through VET FEE-HELP / VET Student Loans - South Australian State Government subsidised',
        '407' => '407 - Deferred all/part of tuition fee through VET FEE-HELP / VET Student Loans - Western Australian State Government subsidised',
        '408' => '408 - Deferred all/part of tuition fee through VET FEE-HELP / VET Student Loans - Tasmania State Government subsidised',
        '409' => '409 - Deferred all/part of tuition fee through VET FEE-HELP / VET Student Loans - Northern Territory Government subsidised',
        '410' => '410 - Deferred all/part of tuition fee through VET FEE-HELP / VET Student Loans - Australian Capital Territory Government subsidised',
        '501' => '501 - Paid full tuition fee - non-State Government subsidised',
        '502' => '502 - Paid full tuition fee - Restricted Access Arrangement',
        '503' => '503 - Paid full tuition fee - Victorian State Government subsidised',
        '504' => '504 - Paid full tuition fee - New South Wales State Government subsidised',
        '505' => '505 - Paid full tuition fee - Queensland State Government subsidised',
        '506' => '506 - Paid full tuition fee - South Australian State Government subsidised',
        '507' => '507 - Paid full tuition fee - Western Australian State Government subsidised',
        '508' => '508 - Paid full tuition fee - Tasmania State Government subsidised',
        '509' => '509 - Paid full tuition fee - Northern Territory Government subsidised',
        '510' => '510 - Paid full tuition fee - Australian Capital Territory Government subsidised',
    ],
    'arrReportingYear' => $arrReportingYear,
    'arrReportingPeriod' => [
        '00' => 'Full Year',
        '01' => 'Date between 1 January and 30 June',
        '02' => 'Date between 1 July and 31 December',
    ],
    'arrSoftwareProductName' => 'Galaxy360',
    'arrSoftwareVendorEmail' => '<EMAIL>',
    'applicationUrl' => env('APPLICATION_URL').'authenticate/',
    'aws_region' => env('AWS_REGION'),
    'aws_access_key_id' => env('AWS_ACCESS_KEY_ID'),
    'aws_secret_access_key' => env('AWS_SECRET_ACCESS_KEY'),
    'aws_domain_hostedzone_id' => env('AWS_DOMAIN_HOSTEDZONE_ID'),
    'sub_doamin_post_fix' => env('SUB_DOMAIN_POST_FIX'),
    'aws_lb_hostedzone_id' => env('AWS_LB_HOSTEDZONE_ID'),
    'aws_lb' => env('AWS_LB'),
    'frontend_domain' => env('FRONTEND_DOMAIN'),
    'app_environment' => env('APP_ENV'),
    'traninggov' => [
        'COURSE_WS_TRAINING_USERNAME' => env('COURSE_WS_TRAINING_USERNAME'),
        'COURSE_WS_TRAINING_PASSWORD' => env('COURSE_WS_TRAINING_PASSWORD'),
        'COURSE_WS_TRAINING_WSDL' => env('COURSE_WS_TRAINING_WSDL'),
        'COURSE_WS_TRAINING_MOMURL' => env('COURSE_WS_TRAINING_MOMURL'),
        'COURSE_WS_TRAINING_ORGURL' => env('COURSE_WS_TRAINING_ORGURL'),
        'COURSE_WS_TRAINING_CLASURL' => env('COURSE_WS_TRAINING_CLASURL'),
        'COURSE_WS_TRAINING_PAGESIZE' => env('COURSE_WS_TRAINING_PAGESIZE', 20),
    ],
    'usi' => [
        'USI_AUTH_URL' => env('USI_AUTH_URL'),
        'USI_AUTH_PASSWORD' => env('USI_AUTH_PASSWORD'),
        'USI_CHECK_URL' => env('USI_CHECK_URL'),
    ],
    // TCSI
    'reportingType' => [
        'hep' => 'HEP',
        'vsl' => 'VSL',
        'pir' => 'PIR',
    ],
    'providerType' => [
        'HEP' => 'HEP',
        'VSL' => 'VSL',
        'PIR' => 'PIR',
        'VET' => 'VET',
    ],
    'submissionTypeWithReportingType' => [
        'hep' => [
            '1' => 'Course of Study',
            '2' => 'Course',
            '3' => 'Campus',
            '4' => 'Course on Campus',
            '5' => 'Student',
            '13' => 'Revise first reported addresses',
            '14' => 'Commonwealth Scholarships',
            '6' => 'Course Admission',
            '7' => 'Course Admission HDR',
            '15' => 'Exit Award',
            '16' => 'Aggregate awards',
            '8' => 'Unit Enrolment',
            '17' => 'Unit Enrolments (AOU)',
            '9' => 'SA-HELP Loan',
            '10' => 'OS-HELP Loan',
            '11' => 'Staff-Full Time',
            '12' => 'Casual_Staff_Actuals',
            '18' => 'Casual_Staff_Estimates',
            '19' => 'Course_Application',
            '20' => 'Course_Preferences',
            '21' => 'Course_Offers',
            '22' => 'Deletions (non university)',
        ],
        'vsl' => [
            '2' => 'Courses',
            '3' => 'Delivery Locations',
            '5' => 'Student',
            '13' => 'Revise First Reported Adresses',
            '6' => 'Course Admissions',
            '8' => 'Unit Enrolments',
            '22' => 'Deletions',
        ],
        'pir' => [
            '1' => 'Course of Study',
            '2' => 'Course',
            '3' => 'Campus',
            '4' => 'Course on Campus',
            '5' => 'Student',
            '6' => 'Course Admission',
            '7' => 'Course Admission HDR',
            '8' => 'Unit Enrolment',
            '11' => 'Staff-Full Time',
            '12' => 'Casual_Staff_Actuals',
            '18' => 'Casual_Staff_Estimates',
            '22' => 'Deletions (non university)',
        ],
    ],
    'submissionType' => [
        '1' => 'Course of Study',
        '2' => 'Course',
        '3' => 'Campus',
        '4' => 'Course on Campus',
        '5' => 'Student',
        '6' => 'Course Admission',
        '7' => 'Course Admission HDR',
        '8' => 'Unit Enrolment',
        '9' => 'SA-HELP Loan',
        '10' => 'OS-HELP Loan',
        '11' => 'Staff-Full Time',
        '12' => 'Staff Casual Actual',
    ],
    'reportingYearType' => [
        '' => '- - Select Type - -',
        'full' => 'Full Year',
        'half' => 'Half Year',
    ],
    'TcsiStatusArr' => [
        'all' => 'All',
        'processed' => 'Processed',
        'not_processed' => 'Not Processed',
    ],
    'testMailData' => [
        'to' => '<EMAIL>',
        'subject' => 'Test Mail ('.date('Y-m-d').')',
        'body' => 'This is test mail at '.date('Y-m-d H:i:s'),
    ],
    // Suffix text for cache key
    'cacheKeySuffix' => [
        'globalSearch' => '.global_search_data',
        'studentList' => '.student_data_table',
        'timetableDashboard' => '.timetable_dashboard_data',
    ],
    'includeSuperseededCourses' => env('INCLUDE_SUPERSEEDED_COURSE_IN_SEARCH', false),
    /* constats to filter the courses in manage courses page */
    'courseStatusFilter' => [
        ['id' => 'active', 'text' => 'Currently Active'],
        ['id' => 'inactive', 'text' => 'Currently Inactive'],
        ['id' => 'incomplete', 'text' => 'Incomplete Courses'],
    ],
    'courseDurationFilter' => [
        ['id' => '10', 'text' => '0 - 10 Weeks'],
        ['id' => '20', 'text' => '10 - 20 Weeks'],
        ['id' => '30', 'text' => '20 - 30 Weeks'],
        ['id' => '40', 'text' => '30 - 40 Weeks'],
        ['id' => '50', 'text' => '40 - 50 Weeks'],
        ['id' => '50+', 'text' => 'More than 50 Weeks'],
    ],
    'sortTypes' => [
        ['id' => 'recent', 'text' => 'Recent First'],
        ['id' => 'oldest', 'text' => 'Oldest First'],
        ['id' => 'recentlyupdated', 'text' => 'Recently Updated'],
        ['id' => 'recentlyupdateddesc', 'text' => 'Oldest Updated'],
        ['id' => 'aplhabet', 'text' => 'Alphabetically - A to Z'],
        ['id' => 'aplhabetdesc', 'text' => 'Alphabetically - Z to A'],
    ],
    'scheduleType' => [
        ['value' => '1', 'label' => 'Semester'],
        ['value' => '2', 'label' => 'Subject'],
        ['value' => '3', 'label' => 'Credit Point'],
    ],
    'trainingGovApiCacheRefreshWindow' => 45, // result cached will be refreshed after given days
    'vet_course_type_id' => 2,
    'short_course_type_id' => 16,
    'highered_course_type_id' => 17,
    'studentInviteEmail' => 'Student Invitation',
    'studentShortCoursePaid' => 'Short Course Invoice Paid',
    'studentShortCoursePaymentReminder' => 'Short Course Payment Reminder',
    'studentIdFormate' => ['alphabeat', 'yeardigit', 'countrycode'],
    'batchfilterparameters' => ['type', 'year', 'campus', 'course', 'semester', 'subject', 'batch', 'student'],
    'markattendancefilterparameters' => ['type', 'year', 'course', 'semester', 'subject', 'batch'],
    'studentattendancefilterparameters' => ['course', 'semester', 'term'],
    'studentevaluationfilterparameters' => ['semester', 'term', 'form', 'course'],
    'email_batch_size' => 10,
    'enrollment_student_size' => 1,
    'E561CreditBasisCode' => [
        '0100' => '0100 - Credit/RPL was offered for prior higher education study only',
        '0200' => '0200 - Credit/RPL was offered for prior VET study only',
        '0300' => '0300 - Credit/RPL was offered for a combination of prior higher education and VET study',
        '0400' => '0400 - Credit/RPL was offered for study undertaken at an education provider outside Australia',
        '0500' => '0500 - Credit/RPL was offered for work experience undertaken inside or outside Australia',
        '0600' => '0600 - Other',
    ],
    'teamsConditions' => [
        'short_course' => 'Short Course',
        'admin' => 'Admin',
        'agent' => 'Agent',
        'student' => 'Student',
    ],
    'certificateTempFontPath' => public_path('/fonts/certificate/temp/'),
    'privacyPolicy' => [
        'short_course' => 'Short Course',
        'admin' => 'Admin',
        'agent' => 'Agent',
        'student' => 'Student',
    ],
    /*
    'batchfilterparameters' => [
                                array(
                                    "name" => "type",
                                    "field" => "course_type",
                                    "defaults" => "all",
                                ),
                                array(
                                    "name" => "year",
                                    "field" => "year",
                                    "defaults" => "all",
                                ),
                                array(
                                    "name" => "campus",
                                    "field" => "campus_id",
                                    "defaults" => "all",
                                ),
                                array(
                                    "name" => "course",
                                    "field" => "course_id",
                                    "defaults" => "all",
                                ),
                                array(
                                    "name" => "semester",
                                    "field" => "semester_id",
                                    "defaults" => "all",
                                ),
                                array(
                                    "name" => "subject",
                                    "field" => "subject_id",
                                    "defaults" => "all",
                                ),
                                array(
                                    "name" => "batch",
                                    "field" => "batch_id",
                                    "defaults" => "latest",
                                ),
                                array(
                                    "name" => "student",
                                    "field" => "student_id",
                                    "defaults" => "first",
                                ),
                            ],
    'markattendancefilterparameters' => [
                                array(
                                    "name" => "type",
                                    "field" => "course_type",
                                    "defaults" => "all",
                                ),
                                array(
                                    "name" => "year",
                                    "field" => "year",
                                    "defaults" => "all",
                                ),
                                array(
                                    "name" => "course",
                                    "field" => "course_id",
                                    "defaults" => "all",
                                ),
                                array(
                                    "name" => "semester",
                                    "field" => "semester_id",
                                    "defaults" => "all",
                                ),
                                array(
                                    "name" => "subject",
                                    "field" => "subject_id",
                                    "defaults" => "all",
                                ),
                                array(
                                    "name" => "batch",
                                    "field" => "batch_id",
                                    "defaults" => "latest",
                                ),
                            ]
        */
    'staff_invitation_response_waiting_time' => env('STAFF_INVITATION_RESPONSE_WAITING_TIME', 7),

    'allowlocalhostforshortcourseapi' => env('ALLOW_LOCALHOST_FOR_SHORTCOURSE_API', false),
    'arrWorkPlacementComponentTypes' => [
        'hours' => 'Hours',
        'days' => 'Days',
        'weeks' => 'Weeks',
        'months' => 'Months',
        'service_periods' => 'Service Periods',
    ],
    'arrSubjectCalmingForCredit' => [
        1 => 1,
        2 => 2,
        3 => 3,
        4 => 4,
        5 => 5,
        6 => 6,
        7 => 7,
        8 => 8,
    ],
    'emailTotalAttachmentAllowed' => 10,

    'arrCourseCancelReason' => [
        '1' => 'Student Non Commencement',
        '2' => 'Course Progress',
        '3' => 'Non Payment Of Fees',
        '4' => 'Academic Misconduct',
        '5' => 'Student Misconduct',
        '6' => 'Student Requested Change',
        '7' => 'Provider Initiated Cancellation',
    ],
    'shortCourseResponseDays' => 1,
    'shortCourseResponseHours' => 24,
    'additionalDropdownOptions' => [
        '1' => 'Repeat 1 time',
        '2' => 'Repeat 2 times',
        '3' => 'Repeat 3 times',
        '4' => 'Repeat 4 times',
        '5' => 'Repeat 5 times',
        '6' => 'Repeat 6 times',
        '7' => 'Repeat 7 times',
        '8' => 'Repeat 8 times',
        '9' => 'Repeat 9 times',
        '10' => 'Repeat 10 times',
    ],

];
