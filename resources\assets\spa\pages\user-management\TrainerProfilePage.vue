<script setup>
import { ref } from 'vue';
import Layout from '@spa/pages/Layouts/Layout';
import { Head, router, usePage } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import TrainerViewComponent from '@spa/modules/users/trainers/TrainerViewComponent.vue';
import TeacherAutoComplete from '@spa/modules/teacher/TeacherAutoComplete.vue';
import Breadcrumb from '@spa/components/Breadcrumb/Breadcrumb.vue';
import HeaderSearchNavigation from '@spa/modules/users/partials/HeaderSearchNavigation.vue';
import { useTeacherStore } from '@spa/stores/modules/teacher/useTeacherStore.js';

const $page = usePage();
const selected = ref(null);
const store = useTeacherStore();

const handleNavigation = (direction) => {
    if (direction === 'prev' && store.formData?.prev_id) {
        router.visit(route('spa.manage-users.trainers.profile', [store.formData.secure_prev_id]));
    } else if (direction === 'next' && store.formData?.next_id) {
        router.visit(route('spa.manage-users.trainers.profile', [store.formData.secure_next_id]));
    }
};

const handleSelect = (event) => {
    if (event.item?.secure_id) {
        router.visit(route('spa.manage-users.trainers.profile', [event.item.secure_id]));
    }
};
</script>
<template>
    <Layout
        :no-spacing="true"
        :full-view-port="false"
        :loading="false"
        :title="'User Profile'"
        :show-header="true"
        :show-tabs="true"
        :active-tab="activeTab"
    >
        <Head title="User Profile" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="User Profile" :back="false" />
        </template>
        <template #buttonGroup>
            <HeaderSearchNavigation @navigation="handleNavigation" :store="store">
                <template #search>
                    <TeacherAutoComplete v-model="selected" @select="handleSelect" />
                </template>
            </HeaderSearchNavigation>
        </template>
        <template #breadcrumb>
            <Breadcrumb
                :breadcrumbs="[
                    {
                        name: 'Manage Users ',
                        route: route('spa.manage-users.team-members'),
                    },
                    {
                        name: 'Trainers',
                        route: route('spa.manage-users.trainers'),
                    },
                    {
                        name: 'View User',
                    },
                ]"
                @breadcrumbClick="
                    (item) => {
                        router.visit(item.route);
                    }
                "
                :show-home="false"
                :show-border="false"
                :transparent="true"
            />
        </template>
        <div class="h-full px-8 py-6">
            <TrainerViewComponent />
        </div>
    </Layout>
</template>

<style scoped></style>
