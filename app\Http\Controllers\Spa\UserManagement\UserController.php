<?php

namespace App\Http\Controllers\Spa\UserManagement;

use App\Events\ImpersonatingUser;
use App\Http\Controllers\Controller;
use App\Http\Requests\UserManagement\UserFilerRequest;
use App\Http\Resources\UserManagement\RolesWithPermissionResource;
use App\Http\Resources\UsersListResource;
use App\Http\Responses\AfterLoginTrait;
use App\Services\api\v3\UserManagementService;
use App\Services\UsersFilterService;
use App\Users;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Notifications\NotificationService;
use Notifications\Types\NotificationType;
use Support\DTO\MetaKey;

class UserController extends Controller
{
    use AfterLoginTrait;

    public $service;

    public $filterService;

    public function __construct(
        UserManagementService $userManagementService,
        UsersFilterService $usersFilterService,
    ) {
        $this->service = $userManagementService;
        $this->filterService = $usersFilterService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(UserFilerRequest $request, $type = 'all')
    {
        $filters = $request->DTO();
        $filters->type = $type;

        $columns = [
            [
                'field' => 'name',
                'title' => 'USER NAME',
                'minResizableWidth' => 100,
                'width' => 150,
            ],
            [
                'field' => 'roles',
                'title' => 'ASSIGNED ROLE',
                'cell' => 'roles_cell',
                'minResizableWidth' => 100,
            ],
            [
                'field' => 'email',
                'title' => 'EMAIL ADDRESS',
                'minResizableWidth' => 100,
            ],
            [
                'field' => 'last_active',
                'title' => 'LAST ACTIVE',
                'minResizableWidth' => 100,
            ],
            [
                'field' => 'created_date',
                'title' => 'CREATED DATE',
                'minResizableWidth' => 100,
            ],
            [
                'field' => 'status',
                'title' => 'GALAXY STATUS',
                'minResizableWidth' => 100,
            ],
            [
                'field' => 'actions',
                'title' => 'ACTION',
                'minResizableWidth' => 100,
            ],
        ];

        $permissionsGrid = $this->service->getPermissionsGrid($type);
        $allPermissionGroups = collect($permissionsGrid)->flatMap(fn ($collection) => collect($collection)->pluck('name'))->all();

        $dummyRolesGrid = $this->service->getDummyRolesGrid($type, $allPermissionGroups);

        $pageData = [
            'data' => [
                'title' => 'User Management',
                'keywords' => 'User Management',
                'description' => 'User Management',
                'mainmenu' => 'users',
                'currentTab' => 'all',
            ],
            'system_roles' => $this->service->getSystemRoles($type),
            'dummy_role' => $dummyRolesGrid,
            'roles' => fn () => RolesWithPermissionResource::collection($this->service->getAllRoles($type)),
            'permissions' => fn () => $permissionsGrid,
            'type' => $type,
            'filters' => $filters,
            'dataGrid' => [
                'columns' => $columns,
                'users' => fn () => UsersListResource::collection($this->service->getUsersList($filters)),
            ], // students grid
            'pagefilters' => fn () => $this->filterService->getPageFilters($filters, ['course', 'coursestatus']), // students grid
        ];

        return Inertia::render('usermanagement/UserList', $pageData);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    public function getRolesPermissions(Request $request)
    {
        $roles = $request->input('roles');
        $roles = array_map(fn ($value) => decryptIt($value), $roles);
        $permissionsGrid = $this->service->getPermissionGridForRoles($roles);
        dd($roles);
    }

    public function impersonate(Request $request, $id)
    {
        if (! authUser() || ! authUser()->isSadmin()) {
            abort(403, 'You are not authorized to impersonate this user');
        }
        $user = Users::findOrFail(decryptIt($id));

        activity('auth')
            ->causedBy(auth()->user())
            ->performedOn($user)
            ->log('Impersonating user '.$user->name);
        activity('auth')
            ->causedBy($user)
            ->performedOn(auth()->user())
            ->log('Impersonated by '.auth()->user()->name);
        $admin = auth()->user();
        auth()->logout();
        auth()->loginUsingId($user->id);
        session()->put(MetaKey::ADMIN_IMPERSONATING, encryptIt($admin->id));
        event(new ImpersonatingUser($user));
        app(NotificationService::class)->send(
            users: $user,
            type: NotificationType::ADMIN_IMPERSONATING,
            dto: $admin
        );

        // dd($request->user());
        session()->flash('success', 'You are now impersonating '.$user->name.'.');

        return $this->logAndRedirect($request);
    }
}
