<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="false"
        :has-bulk-actions="true"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :has-actions="true"
        :has-more-actions="true"
        :enable-selection="true"
        :has-refresh-action="false"
        :has-all-select="true"
        :search-placeholder="btnLabel(store.userType, 'Search')"
        :has-sticky-action="true"
        :actions-config="{
            width: 80,
        }"
        :has-settings="true"
    >
        <template #settings>
            <FilterBlockWrapper class="justify-between" label="Show Archive">
                <Switch v-model="store.filters.onlyArchived" size="small" />
            </FilterBlockWrapper>
            <div class="mt-4 flex items-center justify-end gap-2 border-gray-200">
                <Button
                    @click="store.filters.onlyArchived = false"
                    variant="link"
                    size="sm"
                    class="text-sm"
                >
                    Reset
                </Button>
            </div>
        </template>
        <template #bulk-actions>
            <Button
                v-if="
                    userAuth.can([userAuth.Access.USERS_CREATE, userAuth.Access.USERS_ACCESS]) &&
                    store.userType !== 'student'
                "
                variant="primary"
                :label="btnLabel(store.userType, 'Add')"
                @click="
                    () => {
                        router.visit(
                            route('spa.manage-users.save', {
                                user_type: store.userType,
                            }),
                            {
                                preserveState: false,
                                preserveScroll: false,
                            }
                        );
                    }
                "
            >
                <icon :name="'plus'" :fill="'currentColor'" :width="'16'" :height="'16'" />
                <span>
                    {{ btnLabel(store.userType, 'Add') }}
                </span>
            </Button>
            <DropdownButton
                v-if="
                    userAuth.can([userAuth.Access.USERS_CREATE, userAuth.Access.USERS_ACCESS]) &&
                    store.userType !== 'student'
                "
                variant="secondary"
                :autoHide="true"
                :buttonLabel="btnLabel(store.userType, 'Import')"
            >
                <template #dropdown-content>
                    <button class="btn-menu-item" @click="showUploadModal = true">
                        <span>Upload CSV File</span>
                    </button>
                    <button
                        class="btn-menu-item"
                        @click="
                            () => {
                                router.visit(route('spa.manage-users.team-members.add-manually'));
                            }
                        "
                    >
                        <span>Add Manually</span>
                    </button>
                </template>
            </DropdownButton>
        </template>
        <template #header-actions>
            <template v-if="!store.filters.onlyArchived">
                <button
                    v-if="
                        userAuth.can([userAuth.Access.USERS_ACCESS, userAuth.Access.USERS_EDIT]) &&
                        roleAssignable
                    "
                    class="btn-menu-item"
                    @click="openModal(ACTIONS.ASSIGN_ROLES)"
                >
                    <span>Assign Roles</span>
                </button>

                <button class="btn-menu-item" @click="openModal(ACTIONS.SEND_INVITE)">
                    <span>Send Login Invitation</span>
                </button>
                <div class="my-1 border-t border-gray-200"></div>
                <button
                    v-if="userAuth.can([userAuth.Access.USERS_ACCESS, userAuth.Access.USERS_EDIT])"
                    class="btn-menu-item"
                    @click="openModal(ACTIONS.RESET_PASSWORD)"
                >
                    <span>Reset Password</span>
                </button>

                <button
                    v-if="userAuth.can([userAuth.Access.USERS_ACCESS, userAuth.Access.USERS_EDIT])"
                    class="btn-menu-item"
                    @click="openModal(ACTIONS.DISABLE_USER)"
                >
                    <span>Disable User</span>
                </button>

                <button class="btn-menu-item" @click="openModal(ACTIONS.EXPORT_USERS)">
                    <span>Export Selected Users</span>
                </button>
            </template>
        </template>
        <!--        Columns -->
        <template #body-cell-name="{ props }">
            <button class="flex items-center gap-2" @click="store.viewProfile(props.dataItem)">
                <Avatar :label="props.dataItem.initials" class="shrink-0" />
                <span class="text-left font-medium text-gray-900">{{ props.dataItem?.name }}</span>
            </button>
        </template>
        <template #body-cell-user_roles="{ props }">
            <div class="flex flex-wrap items-center gap-1">
                <span v-if="props.dataItem?.roles?.length === 0">-</span>
                <template v-else>
                    <template
                        v-for="(role, index) in props.dataItem?.roles?.slice(0, 2)"
                        :key="index"
                    >
                        <Badge :variant="'default'">
                            {{ role.name }}
                        </Badge>
                    </template>
                    <template v-if="props.dataItem?.roles?.length > 2">
                        <VMenu :placement="'right'">
                            <Badge
                                :variant="'secondary'"
                                :shape="'full'"
                                :pt="{ root: 'size-5 px-0' }"
                            >
                                +{{ props.dataItem?.roles?.length - 2 }}
                            </Badge>
                            <template #popper>
                                <div class="flex flex-col gap-2 py-2">
                                    <template
                                        v-for="(role, index) in props.dataItem?.roles?.slice(2)"
                                        :key="index"
                                    >
                                        <span class="min-w-24 px-2">{{ role.name }}</span>
                                    </template>
                                </div>
                            </template>
                        </VMenu>
                    </template>
                </template>
            </div>
        </template>
        <template #body-cell-user_status="{ props }">
            <UserStatusBadge :status="props.dataItem?.user_status" />
        </template>
        <template #body-cell-code="{ props }">
            <CopyToClipboard :text="props.dataItem?.code" />
        </template>
        <template #body-cell-status="{ props }">
            <Badge :variant="'default'" :shape="'full'">
                {{ props.dataItem?.status || 'Inactive' }}
            </Badge>
        </template>
        <template #body-cell-last_login="{ props }">
            <FormatDate :date="props.dataItem?.last_login" />
        </template>
        <template #body-cell-created_by="{ props }">
            {{ props.dataItem?.created_by_name || '-' }}
        </template>
        <template #body-cell-super_agent="{ props }">
            {{ props.dataItem?.super_agent || '-' }}
        </template>
        <template #body-cell-agent_type="{ props }">
            <Badge :variant="'default'" :shape="'full'">
                {{ props.dataItem?.agent_type || '-' }}
            </Badge>
        </template>
        <template #actions="{ row }">
            <template v-if="row.deleted_at">
                <GridActionMenuItem
                    v-if="
                        userAuth.can([userAuth.Access.USERS_ACCESS, userAuth.Access.USERS_DELETE])
                    "
                    @click="() => store.confirmRestore(row)"
                    :item="{
                        icon: 'sync',
                        label: 'Restore User',
                        id: 'restore-user',
                    }"
                />
            </template>
            <template v-else>
                <template v-if="row.id">
                    <GridActionMenuItem
                        v-if="
                            userAuth.can([userAuth.Access.USERS_ACCESS, userAuth.Access.USERS_VIEW])
                        "
                        @click="store.viewProfile(row)"
                        :item="{
                            icon: 'eye',
                            label: 'View Profile',
                            id: 'view-profile',
                        }"
                    />
                    <slot name="actions" :row="row" :position="'after-edit'" />
                    <GridActionMenuItem
                        v-if="
                            userAuth.can([userAuth.Access.USERS_ACCESS, userAuth.Access.USERS_EDIT])
                        "
                        @click="() => handleEdit(row)"
                        :item="{
                            icon: 'user-edit',
                            label: 'Edit Info',
                            id: 'edit-info',
                        }"
                    />
                </template>
                <div class="my-1 border-t border-gray-200"></div>
                <GridActionMenuItem
                    v-if="
                        row.user_id &&
                        userAuth.can([userAuth.Access.USERS_ACCESS, userAuth.Access.USERS_EDIT]) &&
                        otherRolesAssignable(row?.roles)
                    "
                    @click="() => openModal(ACTIONS.ASSIGN_ROLES, row)"
                    :item="{
                        icon: 'user-assign',
                        label: 'Assign Roles',
                        id: 'assign-roles',
                    }"
                />
                <GridActionMenuItem
                    v-if="
                        row.user_id &&
                        userAuth.can([userAuth.Access.USERS_ACCESS, userAuth.Access.USERS_EDIT])
                    "
                    @click="() => openModal(ACTIONS.RESET_PASSWORD, row)"
                    :item="{
                        icon: 'password',
                        label: 'Reset Password',
                        id: 'reset-password',
                    }"
                />
                <GridActionMenuItem
                    v-if="userAuth.can([userAuth.Access.USERS_ACCESS, userAuth.Access.USERS_EDIT])"
                    @click="() => openModal(ACTIONS.SEND_INVITE, row)"
                    :item="{
                        icon: 'mail',
                        label: !row.user_id ? 'Send Invite' : 'Re-Send Invite',
                        id: 'send-invite',
                    }"
                />
                <div class="my-1 border-t border-gray-200" v-if="row.user_id"></div>
                <a
                    v-if="row?.user_id && userAuth.can([userAuth.Access.ALL])"
                    class="btn-menu-item"
                    :href="route('spa.users.impersonate', [row?.secure_user_id])"
                >
                    <span class="text-gray-500">
                        <icon :name="'impersonate'" :width="16" :height="16" />
                    </span>
                    <span class="text-sm text-gray-700">Impersonate User</span>
                </a>
                <GridActionMenuItem
                    v-if="
                        row.user_id &&
                        row.user_status?.trim() !== 'Disabled' &&
                        userAuth.can([userAuth.Access.USERS_ACCESS, userAuth.Access.USERS_EDIT])
                    "
                    @click="() => openModal(ACTIONS.DISABLE_USER, row)"
                    :item="{
                        icon: 'archive',
                        label: 'Disable User',
                        id: 'disable-user',
                    }"
                />
                <GridActionMenuItem
                    v-if="
                        row.user_id &&
                        row.user_status?.trim() === 'Disabled' &&
                        userAuth.can([userAuth.Access.USERS_ACCESS, userAuth.Access.USERS_EDIT])
                    "
                    @click="() => openModal(ACTIONS.ENABLE_USER, row)"
                    :item="{
                        icon: 'archive',
                        label: 'Enable User',
                        id: 'enable-user',
                    }"
                />
                <div class="my-1 border-t border-gray-200" v-if="$slots.actions"></div>
                <!--            Agent Only Actions -->
                <slot name="actions" :row="row" :position="'before-delete'" />
                <GridActionMenuItem
                    v-if="
                        userAuth.can([userAuth.Access.USERS_ACCESS, userAuth.Access.USERS_DELETE])
                    "
                    @click="
                        () =>
                            store.confirmDelete(row, {
                                header: 'Archive User?',
                                message:
                                    'Are you sure you want to archive this user? You can restore it later.',
                            })
                    "
                    :item="{
                        icon: 'trash',
                        label: 'Archive User',
                        id: 'remove-user',
                        variant: 'destructive',
                    }"
                />
            </template>
        </template>
        <!--        HeaderCell -->
        <template #filter-user_status="{ props }">
            <GridColumnCheckboxFilter
                :value="store.filters?.user_status"
                :options="addOptionsCount(props.options)"
                @update:value="store.filters.user_status = $event"
                @apply="store.fetchPaged()"
            >
                <template #option="{ option }">
                    <UserStatusBadge :status="option.label" />
                </template>
            </GridColumnCheckboxFilter>
        </template>
        <template #filter-role_id="{ props, shown }">
            <div class="p-3">
                <UserGroupCheckbox v-model="store.filters.user_roles" />
            </div>
        </template>
        <template #filter-super_agent_id="{ props, shown }">
            <GridColumnCheckboxFilter
                :value="store.filters?.super_agent"
                :options="addOptionsCount(props.options)"
                @update:value="store.filters.super_agent = $event"
                @apply="store.fetchPaged()"
            />
        </template>
    </AsyncGrid>
    <ActionsPopup :selected-action="selectedAction" :store="store" />
    <AssignRolesForm
        :userTypeStore="store"
        :row="getSelectedRows"
        @closed="handleAssignRolesClose"
    />
    <StaffUploadModel
        v-model="showUploadModal"
        @success="
            () => {
                store.fetchPaged();
            }
        "
    ></StaffUploadModel>
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted, computed, watch } from 'vue';
import { useUsersStore } from '@spa/stores/modules/users/useUsersStore.js';
import Avatar from '@spa/components/Avatar/Avatar.vue';
import Badge from '@spa/components/badges/Badge.vue';
import GridActionMenuItem from '@spa/components/AsyncComponents/Grid/Partials/GridActionMenuItem.vue';
import DropdownButton from '@spa/components/Buttons/DropdownButton.vue';
import ActionsPopup from '@spa/modules/users/ActionsPopup.vue';
import { router } from '@inertiajs/vue3';
import { storeToRefs } from 'pinia';
import AssignRolesForm from '@spa/modules/users/partials/AssignRolesForm.vue';
import FormatDate from '@spa/components/FormatDate.vue';
import StaffUploadModel from '@spa/modules/users/partials/StaffUploadModel.vue';
import GridColumnCheckboxFilter from '@spa/components/AsyncComponents/Grid/Partials/GridColumnCheckboxFilter.vue';
import UserGroupCheckbox from '@spa/modules/user-group/UserGroupCheckbox.vue';
import UserStatusBadge from '@spa/modules/users/partials/UserStatusBadge.vue';
import { useUserAuth } from '@spa/stores/modules/useUserAuth.js';
import CopyToClipboard from '@spa/components/CopyAction/CopyToClipboard.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import { Switch } from '@progress/kendo-vue-inputs';
import Button from '@spa/components/Buttons/Button.vue';

const ACTIONS = {
    ASSIGN_ROLES: 'assign_roles',
    SEND_INVITE: 'send_invite',
    RESET_PASSWORD: 'reset_password',
    DISABLE_USER: 'disable_user',
    ENABLE_USER: 'enable_user',
    EXPORT_USERS: 'export_users',
    IMPERSONATE_USER: 'impersonate_user',
};

const props = defineProps({
    store: {
        type: Object,
        required: true,
    },
    columns: {
        type: Array,
        default: [
            {
                field: 'name',
                name: 'name',
                title: 'Name',
                width: '256px',
                replace: true,
                sortable: true,
                filterable: true,
                canHide: true,
                filterableConfig: {
                    type: 'string',
                },
            },
            {
                field: 'email',
                name: 'email',
                title: 'Email',
                width: '256px',
                replace: true,
                sortable: true,
                filterable: true,
                canHide: true,
                filterableConfig: {
                    type: 'string',
                },
            },
            {
                field: 'status',
                name: 'status',
                title: 'Status',
                width: '150px',
                replace: true,
                sortable: true,
                filterable: true,
                filterableConfig: {
                    type: 'select',
                    options: ['active', 'inactive', 'pending'],
                },
            },
            {
                field: 'user_status',
                name: 'user_status',
                title: 'User Status',
                width: '150px',
                replace: true,
                sortable: true,
                filterable: true,
                filterableConfig: {
                    type: 'select',
                    options: ['active', 'inactive', 'pending'],
                },
            },
            {
                field: 'last_login',
                name: 'last_login',
                title: 'Last Login',
                width: '200px',
                replace: true,
                filterable: true,
                filterableConfig: {
                    type: 'date',
                },
            },
            {
                field: 'created_by',
                name: 'created_by',
                title: 'Created By',
                width: '200px',
                replace: true,
                filterable: true,
                filterableConfig: {
                    type: 'string',
                },
            },
        ],
    },
    initFilters: {
        type: Function,
        default: () => {},
    },
    type: {
        type: String,
        default: 'staffs',
    },
});

const userStore = useUsersStore();

const userAuth = useUserAuth();

const selectedAction = ref(null);

const { selected, userType, excluded, allSelected } = storeToRefs(userStore);

const selectedRow = ref(null);

const searchPlaceholder = computed(() => {
    return `Search for ${props.type.replace('-', ' ')}`;
});
const roleAssignable = computed(() => {
    return !(
        props.type === 'agent-staff' ||
        props.type === 'agent' ||
        props.type === 'employer' ||
        props.type === 'serviceprovider' ||
        props.type === 'placementprovider' ||
        props.type === 'student'
    );
});
const btnLabel = (userType, prefix = 'Add') => {
    const labelMapping = {
        staff: 'Team Member',
        teacher: 'Trainers',
        agent: 'Educational Agency',
        employer: 'Employer',
        serviceprovider: 'Service Provider',
        placementprovider: 'Placement Provider',
        student: 'Student',
        'agent-staff': 'Agent Staff',
    };
    return `${prefix} ${labelMapping[userType] || 'User'}`;
};

// Methods
const openModal = (action, row = null) => {
    if (!action) {
        return;
    }
    userStore.bulkAssign = false;
    selectedAction.value = action;
    selectedRow.value = row;
    if (row) {
        selected.value = [row];
    } else {
        selected.value = props.store.selected;
        userStore.bulkAssign = true;
    }
    if (action === ACTIONS.ASSIGN_ROLES) {
        if (otherRolesAssignable(row?.roles)) {
            userStore.formDialog = true;
        }
    } else {
        userStore.actionDialog = true;
    }
};
const getSelectedRows = computed(() => {
    if (selectedRow.value) {
        return selectedRow.value;
    }
    return props.store.selected ?? [];
});
const showUploadModal = ref(false);
onMounted(() => {
    userStore.userType = props.store.userType;
    userStore.getUserMeta(props.store.userType);
});
const handleEdit = (row) => {
    console.log('row', row);
    if (row.user_type === 'student') {
        window.open(
            route('student-profile-view', [row.secure_id]) + '?action=profile_detail',
            '_blank'
        );
        return;
    }
    let profileRoute = 'spa.manage-users.save.edit';
    const routeMapper = {
        staff: 'team-members',
        teacher: 'trainers',
        agent: 'agents',
        employer: 'employers',
        serviceprovider: 'service-providers',
        placementprovider: 'placement-providers',
        student: 'students',
        'agent-staff': 'agent-staffs',
    };
    profileRoute = `spa.manage-users.${routeMapper[row.user_type]}.profile`;
    router.visit(route(profileRoute, [row.secure_id]) + '#profile_details', {
        preserveState: false,
        preserveScroll: false,
    });
};

const addOptionsCount = (options) => {
    return options.map((option) => {
        return {
            ...option,
            count: userStore.statusCounts[option.value],
        };
    });
};

const otherRolesAssignable = (roles) => {
    if (roles) {
        const superAdminRole = roles.find((role) => role.is_permanent);
        return superAdminRole === undefined;
    }
    return true;
};
watch(
    () => props.store.excluded,
    (newVal) => {
        excluded.value = newVal;
    }
);

watch(
    () => props.store.allSelected,
    (newVal) => {
        allSelected.value = newVal;
    }
);
watch(
    () => userStore.formDialog,
    (val) => {
        if (!val) {
            selectedRow.value = null;
        }
    },
    { deep: true }
);
</script>
