<script setup>
import { ref } from 'vue';
import Layout from '@spa/pages/Layouts/Layout';
import { Head, router, usePage } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import SetupProviderAutoComplete from '@spa/modules/service-providers/setup-provider/SetupProviderAutoComplete.vue';
import Breadcrumb from '@spa/components/Breadcrumb/Breadcrumb.vue';
import ServiceProviderViewComponent from '@spa/modules/users/service-providers/ServiceProviderViewComponent.vue';
import { useSetupProviderStore } from '@spa/stores/modules/service-providers/useSetupProviderStore.js';
import HeaderSearchNavigation from '@spa/modules/users/partials/HeaderSearchNavigation.vue';

const $page = usePage();

const selected = ref(null);
const store = useSetupProviderStore();

const handleNavigation = (direction) => {
    if (direction === 'prev' && store.formData?.prev_id) {
        router.visit(
            route('spa.manage-users.service-providers.profile', [store.formData.secure_prev_id])
        );
    } else if (direction === 'next' && store.formData?.next_id) {
        router.visit(
            route('spa.manage-users.service-providers.profile', [store.formData.secure_next_id])
        );
    }
};

const handleSelect = (event) => {
    if (event.item?.secure_id) {
        router.visit(route('spa.manage-users.service-providers.profile', [event.item.secure_id]));
    }
};
</script>
<template>
    <Layout
        :no-spacing="true"
        :full-view-port="false"
        :loading="false"
        :title="'User Profile'"
        :show-header="true"
        :show-tabs="true"
        :active-tab="activeTab"
    >
        <Head title="User Profile" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="User Profile" :back="false" />
        </template>
        <template #buttonGroup>
            <HeaderSearchNavigation @navigation="handleNavigation" :store="store">
                <template #search>
                    <SetupProviderAutoComplete v-model="selected" @select="handleSelect" />
                </template>
            </HeaderSearchNavigation>
        </template>
        <template #breadcrumb>
            <Breadcrumb
                :breadcrumbs="[
                    {
                        name: 'Manage Users ',
                        route: route('spa.manage-users.team-members'),
                    },
                    {
                        name: 'Service Providers',
                        route: route('spa.manage-users.service-providers'),
                    },
                    {
                        name: 'View User',
                    },
                ]"
                @breadcrumbClick="
                    (item) => {
                        router.visit(item.route);
                    }
                "
                :show-home="false"
                :show-border="false"
                :transparent="true"
            />
        </template>
        <div class="flex h-full flex-col px-8 py-6">
            <ServiceProviderViewComponent />
        </div>
    </Layout>
</template>

<style scoped></style>
