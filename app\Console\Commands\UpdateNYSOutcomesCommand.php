<?php

namespace App\Console\Commands;

use App\Model\v2\StudentSubjectEnrolment;
use Illuminate\Console\Command;

class UpdateNYSOutcomesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'outcomes:nys-to-ce';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Daily: For enrolments with final_outcome 85 (NYS) whose activity_start_date is today, set final_outcome to 70 (CE).';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $today = date('Y-m-d');

        $count = 0;

        // Update individually so Eloquent events fire and activity logs are recorded
        StudentSubjectEnrolment::with(['student', 'unit'])
            ->where('final_outcome', 'NYS')
            ->whereDate('activity_start_date', $today)
            ->chunkById(500, function ($enrolments) use (&$count) {
                foreach ($enrolments as $enrolment) {
                    $enrolment->final_outcome = 'CE'; // CE maps to national code 70
                    if ($enrolment->save()) {
                        $count++;
                    }
                }
            });

        $this->info("Updated {$count} enrolment(s) from NYS(85) to CE(70) for activity_start_date {$today}.");

        return Command::SUCCESS;
    }
}
