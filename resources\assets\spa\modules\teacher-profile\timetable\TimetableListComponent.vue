<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="false"
        :has-export="true"
        :has-filters="true"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :has-actions="false"
        :enableSelection="true"
    >
        <template #bulk-actions>
            <Button variant="secondary" class="h-9" @click="store.exportTimetable()">
                <file-icon name="csv" fill="currentColor" />
                Export
            </Button>
        </template>
        <template #filters>
            <FilterBlockWrapper label="Semester">
                <DropDownList
                    v-model="store.filters.semester"
                    :data-items="store.filterData?.semesterTimetable || []"
                    :text-field="'text'"
                    :data-item-key="'id'"
                    :valueField="'id'"
                    :valuePrimitive="true"
                    :default-item="{ id: '', text: 'All Semesters' }"
                    :popup-settings="{
                        animate: false,
                    }"
                ></DropDownList>
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Between Dates">
                <FormDateRangePicker v-model="store.filters.dateRange" />
            </FilterBlockWrapper>
        </template>
        <template #body-cell-subject="{ props }">
            <span>{{ props.dataItem?.subject?.subject_code }}</span>
            -
            <span>{{ props.dataItem?.subject?.subject_name }}</span>
        </template>
        <template #body-cell-room="{ props }">
            <span class="capitalize">{{ props.dataItem?.classroom?.room_name }}</span>
        </template>
        <template #body-cell-time_range="{ props }">
            <span>{{ props.dataItem?.start_time }} - {{ props.dataItem?.finish_time }}</span>
        </template>
        <template #body-cell-class_duration="{ props }">
            <span
                >{{ convertJsDateFormat(props.dataItem?.start_week) }} -
                {{ convertJsDateFormat(props.dataItem?.end_week) }}</span
            >
        </template>
        <template #body-cell-class_type="{ props }">
            <Badge :variant="getBadgeVariant(props.dataItem?.class_type)">
                {{ props.dataItem?.class_type }}
            </Badge>
        </template>
        <template #body-cell-semester="{ props }">
            <span>{{ props.dataItem?.semester?.semester_name }}</span>
        </template>
    </AsyncGrid>
    <TimetableForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted, watch } from 'vue';
import { useTimetableStore } from '@spa/stores/modules/timetable/useTimetableStore.js';
import TimetableForm from '@spa/modules/teacher-profile/timetable/TimetableForm.vue';
import Badge from '@spa/components/badges/Badge.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import FormDateRangePicker from '@spa/components/KendoInputs/FormDateRangePicker.vue';
import Button from '@spa/components/Buttons/Button.vue';
import { convertJsDateFormat } from '@spa/composables/dateTimeComposables.js';

const store = useTimetableStore();

const props = defineProps({
    teacherId: Number,
});

const columns = [
    {
        field: 'timetable_date',
        title: 'Class Date',
        width: 150,
        formatCellData: (val) => {
            return val ? convertJsDateFormat(val) : 'N/A';
        },
    },
    {
        field: 'semester_id',
        name: 'semester',
        title: 'Semester',
        width: 150,
        replace: true,
    },
    {
        field: 'subject_id',
        name: 'subject',
        title: 'Subject',
        width: 300,
        replace: true,
        sortable: true,
    },
    {
        field: 'term',
        title: 'Term',
        width: 90,
    },
    {
        field: 'batch',
        title: 'Batch',
        width: 200,
        sortable: true,
    },
    {
        field: 'class_type',
        title: 'Mode',
        name: 'class_type',
        sortable: true,
        replace: true,
        width: 100,
    },
    {
        field: 'classroom_id',
        name: 'room',
        title: 'Room',
        replace: true,
        sortable: true,
        width: 100,
    },
    {
        field: 'day',
        title: 'Day',
        sortable: true,
        width: 150,
    },
    {
        field: 'start_week_id',
        name: 'class_duration',
        title: 'Class Duration',
        replace: true,
        sortable: true,
        width: 150,
    },
    {
        field: 'start_time',
        name: 'time_range',
        title: 'Time',
        replace: true,
        sortable: true,
        width: 150,
    },
    {
        field: 'attendance_type',
        title: 'Attendance Type',
        width: 150,
    },
    // Add more columns as needed
];

const getBadgeVariant = (value) => {
    let badgeMapping = {
        Class: 'info',
        Lab: 'purple',
        Lecture: 'primary',
        Practicle: 'secondary',
        Seminar: 'warning',
        Supervision: 'success',
        Tutorial: 'primary',
        'Virtual Class': 'secondary',
        Workshop: 'purple',
        Online: 'primary-100',
    };
    return badgeMapping[value] || 'default';
};

const initFilters = () => {
    store.filters = {
        teacherId: props.teacherId || null,
        semester: null,
        dateRange: null,
    };
};

watch(
    () => props.teacherId,
    (newVal) => {
        initFilters();
        store.getFilterData({
            teacher_id: newVal,
        });
    },
    { immediate: true }
);

onMounted(() => {
    initFilters();
});
</script>
