<script setup>
import AsyncSelect from '@spa/components/AsyncComponents/Select/AsyncSelect.vue';
import { ref, computed, watch } from 'vue';
import { useTimesheetApprovalStore } from '@spa/stores/modules/timesheet-approval/timesheetApprovalStore.js';

const props = defineProps({
    modelValue: [String, Number, Array, Object],
    financialYear: [String, Number],
    label: String,
    className: String,
    optionValue: {
        type: String,
        default: 'value',
    },
    optionLabel: {
        type: String,
        default: 'text',
    },
    disabled: Boolean,
    clearable: {
        type: Boolean,
        default: true,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    readonly: Boolean,
    useChips: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: 'Select Pay Period',
    },
});

const emit = defineEmits(['update:modelValue']);
const timesheetStore = useTimesheetApprovalStore();

const payPeriods = ref([]);
const loading = ref(false);

const vModel = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});

// Create a store-like object that AsyncSelect expects
const store = computed(() => ({
    all: payPeriods.value,
    loading: loading.value,
    fetchPaged: () => {
        // PayPeriodSelect doesn't need to fetch data automatically
        // The data is loaded when financial year is selected
        return Promise.resolve();
    },
}));

// Watch for financial year changes to fetch pay periods
// watch(
//     () => props.financialYear,
//     async (newFinancialYear) => {
//         if (newFinancialYear) {
//             try {
//                 loading.value = true;
//                 const response = await timesheetStore.getPayPeriods(newFinancialYear);
//                 payPeriods.value = response.data || [];
//
//                 // Reset selected value when financial year changes
//                 vModel.value = null;
//             } catch (error) {
//                 console.error('Error fetching pay periods:', error);
//                 payPeriods.value = [];
//             } finally {
//                 loading.value = false;
//             }
//         } else {
//             payPeriods.value = [];
//             vModel.value = null;
//         }
//     },
//     { immediate: true }
// );
</script>

<template>
    <AsyncSelect
        :label="label"
        :className="className"
        :optionValue="optionValue"
        :optionLabel="optionLabel"
        :disabled="disabled || !financialYear"
        :store="store"
        v-model="vModel"
        :clearable="clearable"
        :multiple="multiple"
        :readonly="readonly"
        :useChips="useChips"
        :placeholder="placeholder"
    />
</template>

<style scoped></style>
