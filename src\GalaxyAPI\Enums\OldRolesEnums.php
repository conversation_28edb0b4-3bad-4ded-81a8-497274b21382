<?php

namespace GalaxyAPI\Enums;

use GalaxyAPI\Traits\EnumUtilitiesTrait;

enum OldRolesEnums: int
{
    use EnumUtilitiesTrait;

    public const allow_fetch = true;

    case TYPE_ACCOUNTANT = 1;
    case TYPE_ADMIN = 2;
    case TYPE_DOS = 3;
    case TYPE_DOSELICOS = 4;
    case TYPE_DOSHS = 5;
    case TYPE_DOSVET = 6;
    case TYPE_IT = 7;
    case TYPE_MARKETING = 8;
    case TYPE_SADMIN = 9;
    case TYPE_STAFF = 10;
    case TYPE_STUDENTSERVICE = 11;
    case TYPE_TEACHER = 12;
    case TYPE_STUDENT = 13;
    case TYPE_AGENT = 14;
    case TYPE_AGENT_STAFF = 15;

    public function label(): string
    {
        return match ($this) {
            self::TYPE_ACCOUNTANT => 'team-members',
            self::TYPE_ADMIN => 'master_admin',
            self::TYPE_DOS => 'team-members',
            self::TYPE_DOSELICOS => 'team-members',
            self::TYPE_DOSHS => 'team-members',
            self::TYPE_DOSVET => 'team-members',
            self::TYPE_IT => 'team-members',
            self::TYPE_MARKETING => 'team-members',
            self::TYPE_SADMIN => 'master_admin',
            self::TYPE_STAFF => 'team-members',
            self::TYPE_STUDENTSERVICE => 'service-providers',
            self::TYPE_TEACHER => 'teachers',
            self::TYPE_STUDENT => 'students',
            self::TYPE_AGENT => 'agents',
            self::TYPE_AGENT_STAFF => 'agent-staffs',
        };
    }

    public static function getNewRoleFromOld(int $oldRole): ?string
    {
        foreach (self::cases() as $case) {
            if ($case->value === $oldRole) {
                return $case->label();
            }
        }

        return null;
    }
}
