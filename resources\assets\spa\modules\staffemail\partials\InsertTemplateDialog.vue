<template>
    <div class="flex items-center">
        <span class="text-xs text-gray-600">{{
            selectedTemplate ? selectedTemplate.name : 'No template Selected'
        }}</span>
        <button class="btn-secondary ml-2" type="button" @click="visible = true">
            <icon name="template" :width="14" :height="14" />
            Insert Template
        </button>
    </div>
    <SidebarDrawer
        :visible-dialog="visible"
        :hide-on-overlay-click="true"
        :fixed-action-bar="true"
        :width="'60%'"
        :max-width="'1160px'"
        :primary-btn-label="'Select'"
        :secondary-btn-label="'Cancel'"
        @drawerclose="visible = false"
        @drawersaved="handleSave"
        :position="'center'"
    >
        <template #title>
            <h2 class="text-xl font-medium">Select Template</h2>
        </template>
        <template #content>
            <div class="grid h-96 grid-cols-4 gap-6">
                <div class="col-span-4" v-if="store.all.length === 0">
                    <NoData
                        :pt="{ root: 'h-full' }"
                        title="No Templates Found"
                        subtitle="No templates found for this recipient."
                    >
                        <template #button>
                            <Button variant="primary" @click="redirectToCreate()"
                                >Add Template</Button
                            >
                        </template>
                    </NoData>
                </div>
                <template v-else>
                    <div class="col-span-1 space-y-1 overflow-y-auto">
                        <template v-for="(template, index) in store.all" :key="template.id">
                            <button
                                class="group grid w-full cursor-pointer items-start justify-start space-y-1 rounded-md px-2 py-3 hover:bg-primary-blue-50"
                                :class="{
                                    'bg-primary-blue-50 text-primary-blue-500':
                                        selectedTemplate && selectedTemplate?.id === template?.id,
                                }"
                                @click="handleSelect(template)"
                            >
                                <div class="text-xs text-gray-800">
                                    {{ template.name }}
                                </div>
                            </button>
                        </template>
                    </div>
                    <div class="col-span-3 overflow-y-auto">
                        <div class="flex h-full flex-col space-y-4 overflow-y-auto">
                            <h3 class="text-2xl font-medium leading-none text-gray-900">
                                {{ selectedTemplate?.name }}
                            </h3>
                            <div
                                class="flex-1 overflow-y-auto text-gray-700"
                                v-html="selectedTemplate?.content"
                            ></div>
                        </div>
                    </div>
                </template>
            </div>
        </template>
    </SidebarDrawer>
</template>

<script setup>
import SidebarDrawer from '@spa/components/KendoModals/SidebarDrawer.vue';
import { ref, onMounted, watch } from 'vue';
import { useEmailTemplateStore } from '@spa/stores/modules/emailtemplate/useEmailTemplateStore.js';
import NoData from '@spa/components/NoData/NoData.vue';
import Button from '@spa/components/Buttons/Button.vue';
const props = defineProps({
    recipient: Number,
});
const store = useEmailTemplateStore();

const visible = ref(false);
const selectedTemplate = ref(null);

const handleSelect = (template) => {
    selectedTemplate.value = template;
};

const handleSave = () => {
    visible.value = false;
    emit('select', selectedTemplate.value);
};

const emit = defineEmits(['select']);

const initFilters = () => {
    store.filters = {
        recipient: props.recipient,
    };
};

const redirectToCreate = () => {
    window.open(route('email-template-list'), '_blank');
};

onMounted(() => {
    // store.fetchPaged();
    initFilters();
});

watch(
    () => visible.value,
    (newVal) => {
        if (newVal && store.all.length > 0) {
            selectedTemplate.value = store.all[0];
        }
    }
);
</script>

<style></style>
