<template>
    <UsersListComponent :store="store" :columns="columns" :initFilters="initFilters" type="staff">
        <template #actions="{ row, position }">
            <GridActionMenuItem
                v-if="position === 'before-delete'"
                @click="openModal(row)"
                :item="{
                    icon: 'edit',
                    label: 'Update Staff Code',
                    id: 'update-staff-code',
                }"
            />
        </template>
    </UsersListComponent>
    <StaffCodeForm :selectedRow="selectedRow" />
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import UsersListComponent from '@spa/modules/users/UsersListComponent.vue';
import { useTeamMemberStore } from '@spa/stores/modules/team-member/useTeamMemberStore.js';
import { useStaffStore } from '@spa/stores/modules/staff/useStaffStore.js';
import StaffCodeForm from '@spa/modules/users/staffs/partials/StaffCodeForm.vue';
import GridActionMenuItem from '@spa/components/AsyncComponents/Grid/Partials/GridActionMenuItem.vue';
// const store = useTeamMemberStore();
const store = useStaffStore();
const selectedRow = ref(null);
//const activeTab = 'team-members';
// activeTab === 'trainers
// activeTab === 'agents
// activeTab === 'agent-staffs
// activeTab === 'employers
// activeTab === 'service-providers
// activeTab === 'placement-providers
//activeTab === 'students
const columns = [
    {
        field: 'name',
        name: 'name',
        title: 'Name',
        width: '256px',
        sortable: true,
        replace: true,
        locked: true,
    },
    {
        field: 'code',
        name: 'code',
        title: 'Staff Number',
        width: '150px',
        replace: true,
    },
    {
        field: 'email',
        name: 'email',
        title: 'Email',
        width: '232px',
    },
    {
        field: 'role_id',
        name: 'user_roles',
        title: 'Role(s)',
        width: '256px',
        replace: true,
        sortable: true,
        filterable: true,
        customFilter: true,
    },
    {
        field: 'user_status',
        name: 'user_status',
        title: 'User Status',
        width: '150px',
        replace: true,
        sortable: true,
        filterable: true,
        customFilter: true,
        filterableConfig: {
            type: 'select',
            options: [
                { value: 1, label: 'Active' },
                { value: 0, label: 'Inactive' },
                { value: 2, label: 'Pending' },
                { value: 3, label: 'Disabled' },
            ],
        },
    },
    {
        field: 'last_login',
        name: 'last_login',
        title: 'Last Login',
        width: '200px',
        replace: true,
        // filterable: true,
        // filterableConfig: {
        //     type: 'date',
        // },
    },
    {
        field: 'created_by',
        name: 'created_by',
        title: 'Created By',
        width: '200px',
        replace: true,
        // filterable: true,
        // filterableConfig: {
        //     type: 'string',
        // },
    },
];

const openModal = (row) => {
    store.actionDialog = true;
    selectedRow.value = row;
};

const initFilters = () => {
    store.filters = {
        // onlyArchived: 1
    };
};

onMounted(() => {
    initFilters();
});
</script>
