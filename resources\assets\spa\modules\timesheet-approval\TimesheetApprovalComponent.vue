<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :show-refresh-button="false"
        :show-filter-button="false"
        :add-permissions="null"
        :enableSelection="true"
        :has-create-action="false"
        :has-export="false"
        :has-filters="true"
        :filter-columns="3"
        :has-actions="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
    >
        <template #filters>
            <FilterBlockWrapper label="Activity Type">
                <ActivityTypeSelect v-model="store.filters.activityType" />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Financial Year">
                <FinancialYearSelect v-model="store.filters.financialYear" />
            </FilterBlockWrapper>
            <!--            <FilterBlockWrapper label="Pay Period">-->
            <!--                <PayPeriodSelect-->
            <!--                    v-model="store.filters.payPeriod"-->
            <!--                    :financial-year="store.filters.financialYear"-->
            <!--                    :disabled="!store.filters.financialYear"-->
            <!--                />-->
            <!--            </FilterBlockWrapper>-->
            <FilterBlockWrapper label="Teacher">
                <TeacherSelect v-model="store.filters.teacher" />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Status">
                <StatusSelect v-model="store.filters.status" />
            </FilterBlockWrapper>
        </template>

        <template #bulk-actions>
            <Button
                @click="handleBulkApproval"
                :loading="approvalLoading"
                severity="success"
                icon="pi pi-check"
                size="small"
                :disabled="store.selected.length === 0"
            >
                Approve Selected
            </Button>
        </template>

        <template #body-cell-subject_code="{ props }">
            {{ props.dataItem.subject_code }}
        </template>
        <template #body-cell-room_id="{ props }">
            {{ props.dataItem.room_id }}
        </template>
        <template #body-cell-mode="{ props }">
            {{ props.dataItem.mode }}
        </template>
        <template #body-cell-start_time="{ props }">
            {{ props.dataItem.start_time }}
        </template>
        <template #body-cell-finish_time="{ props }">
            {{ props.dataItem.finish_time }}
        </template>
        <template #body-cell-duration="{ props }">
            {{ props.dataItem.duration }}
        </template>
        <template #body-cell-break_time="{ props }">
            {{ props.dataItem.break_time }}
        </template>
        <template #body-cell-total="{ props }">
            {{ props.dataItem.total }}
        </template>
        <template #body-cell-status="{ props }">
            <span
                class="inline-flex rounded-full px-2 py-1 text-xs font-semibold"
                :class="getStatusClass(props.dataItem.status)"
            >
                {{ props.dataItem.status }}
            </span>
        </template>
        <template #body-cell-manual="{ props }">
            {{ props.dataItem.manual ? 'Yes' : 'No' }}
        </template>
        <template #body-cell-time_type="{ props }">
            {{ props.dataItem.time_type }}
        </template>
        <template #body-cell-payable="{ props }">
            {{ props.dataItem.payable }}
        </template>
        <template #body-cell-comment="{ props }">
            {{ props.dataItem.comment }}
        </template>
    </AsyncGrid>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useTimesheetApprovalStore } from '@spa/stores/modules/timesheet-approval/timesheetApprovalStore.js';
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import Button from '@spa/components/Buttons/Button.vue';

import ActivityTypeSelect from './partials/ActivityTypeSelect.vue';
import FinancialYearSelect from './partials/FinancialYearSelect.vue';
import PayPeriodSelect from './partials/PayPeriodSelect.vue';
import TeacherSelect from './partials/TeacherSelect.vue';
import StatusSelect from './partials/StatusSelect.vue';
import useConfirm from '@spa/services/useConfirm';

const props = defineProps({
    filters: {
        type: Object,
        default: () => ({}),
    },
});

const store = useTimesheetApprovalStore();
const confirm = useConfirm();

// Reactive data
const approvalLoading = ref(false);

// Grid columns
const columns = [
    {
        name: 'subject_code',
        title: 'Subject Code',
        field: 'timesheet_id',
        sortable: true,
        replace: true,
        width: 120,
    },
    {
        name: 'room_id',
        title: 'Room Id',
        field: 'room_id',
        replace: true,
        width: 120,
    },
    {
        name: 'mode',
        title: 'Mode',
        field: 'mode',
        replace: true,
        width: 120,
    },
    {
        name: 'class_date',
        title: 'Class Date',
        field: 'class_date',
        width: 120,
        formatCellData: (val) => {
            return val ? convertJsDateFormat(val) : 'N/A';
        },
    },
    {
        name: 'start_time',
        title: 'Start Time',
        field: 'start_time',
        replace: true,
        width: 120,
    },
    {
        name: 'finish_time',
        title: 'Finish Time',
        field: 'finish_time',
        replace: true,
        width: 120,
    },
    {
        name: 'duration',
        title: 'Class Duration (W/B)',
        field: 'duration',
        replace: true,
        width: 120,
    },
    {
        name: 'break_time',
        title: 'Break',
        field: 'break_time',
        replace: true,
        width: 120,
    },
    {
        name: 'total',
        title: 'Class Duration (N/B)',
        field: 'total',
        replace: true,
        width: 120,
    },
    {
        name: 'status',
        title: 'Status',
        field: 'status',
        replace: true,
        width: 120,
    },
    {
        name: 'manual',
        title: 'Manual (Yes/No)',
        field: 'manual',
        replace: true,
        width: 120,
    },
    {
        name: 'time_type',
        title: 'Daily OR Hourly',
        field: 'time_type',
        replace: true,
        width: 120,
    },
    {
        name: 'payable',
        title: 'Total/Hourly Payable',
        field: 'payable',
        replace: true,
        width: 120,
    },
    {
        name: 'comment',
        title: 'Comment',
        field: 'comment',
        replace: true,
        width: 120,
    },
];

const initFilters = () => {
    console.log('props.filter', props.filters);
    store.filters = props.filters || {};
};

const handleBulkApproval = async () => {
    if (store.selected.length === 0) {
        store.notifyError('Please select at least one timesheet to approve.');
        return;
    }

    await confirm.require({
        message: `Are you sure you want to approve ${store.selected.length} selected timesheet(s)?`,
        header: 'Confirm Approval',
        variant: 'info',
        icon: 'pi pi-exclamation-triangle',
        accept: async () => {
            try {
                approvalLoading.value = true;
                const timesheetIds = store.selected.map((item) => item.id);

                const response = await store.approveTimesheets(timesheetIds);

                if (response.success) {
                    store.notifySuccess('Selected timesheets approved successfully.');
                    // Refresh the grid data
                    store.fetchPaged();
                    store.selected = [];
                }
            } catch (error) {
                console.error('Error approving timesheets:', error);
                store.notifyError('Error approving timesheets. Please try again.');
            } finally {
                approvalLoading.value = false;
            }
        },
    });
};

// Utility functions
const getStatusClass = (status) => {
    switch (status?.toLowerCase()) {
        case 'submitted':
            return 'bg-yellow-100 text-yellow-800';
        case 'approved':
            return 'bg-green-100 text-green-800';
        case 'rejected':
            return 'bg-red-100 text-red-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

onMounted(() => {
    initFilters();
});
</script>
