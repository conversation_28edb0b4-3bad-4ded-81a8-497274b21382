<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rto_country', function (Blueprint $table) {
            $table->string('official_name', 255)->nullable()->after('name');
            $table->string('iso3', 10)->nullable()->after('countrycode');
            $table->string('capital', 150)->nullable()->after('nationality');
            $table->decimal('latitude', 10, 6)->nullable()->after('capital');
            $table->decimal('longitude', 10, 6)->nullable()->after('latitude');
            $table->string('continent', 50)->nullable()->after('longitude');
            $table->string('phone_code', 10)->nullable()->after('continent');
            $table->string('phone_mask', 50)->nullable()->after('phone_code');
            $table->string('flag', 100)->nullable()->after('phone_mask');
            $table->string('currency', 100)->nullable()->after('flag');
            $table->string('currency_name', 100)->nullable()->after('currency');
            $table->string('currency_symbol', 100)->nullable()->after('currency_name');
            $table->string('official_language', 150)->nullable()->after('currency_symbol');
            $table->string('tld', 10)->nullable()->after('official_language');
            $table->string('region_detail', 255)->nullable()->after('region');
            $table->boolean('updated_standard')->default(0)->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rto_country', function (Blueprint $table) {
            $table->dropColumn([
                'official_name',
                'iso3',
                'capital',
                'latitude',
                'longitude',
                'continent',
                'phone_code',
                'phone_mask',
                'flag',
                'currency',
                'currency_name',
                'currency_symbol',
                'official_language',
                'tld',
                'region_detail',
                'updated_standard',
            ]);
        });
    }
};
