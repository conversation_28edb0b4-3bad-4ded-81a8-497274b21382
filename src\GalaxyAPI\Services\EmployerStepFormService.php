<?php

namespace GalaxyAPI\Services;

use App\Model\v2\Employer;
use App\Users;
use GalaxyAPI\Enums\UserStatusEnum;
use GalaxyAPI\Interfaces\UserTypeStepFormInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class EmployerStepFormService implements UserTypeStepFormInterface
{
    public function __construct(protected UserService $userService) {}

    public function map(string $step, array $data, Model $model): Model
    {
        if ($step === 'review') {
            return $model;
        }

        /** @var Employer $model */
        return match ($step) {
            'user_info' => $this->saveEmployerInfoStep($model, $data),
            'address_postal' => $this->saveAddressAndPostalStep($model, $data),
            default => throw new \InvalidArgumentException("Unsupported step: $step for Employer"),
        };
    }

    private function saveEmployerInfoStep(Employer $model, array $data)
    {
        $user = null;
        $collegeId = Auth::user()->college_id;
        if (! $model->user_id && isset($data['email'])) {
            $user = Users::where('email', $data['email'])->first();
            if (! $user) {
                $userData = [
                    'college_id' => $collegeId,
                    'name' => trim(($data['name'] ?? '')),
                    'username' => $data['email'],
                    'email' => $data['email'],
                    'phone' => $data['phone'] ?? null,
                    'mobile' => $data['mobile'] ?? null,
                    'role_id' => 16,
                    'password' => Hash::make(Str::random(12)),
                    'status' => UserStatusEnum::PENDING->value,
                ];
                $user = Users::create($userData);
            }
            $model->user_id = $user->id;
        }

        $file = $data['user_image'] ?? null;
        if ($file) {
            $this->userService->uploadProfile($user->id, $file);
        }

        $model->fill([
            'employer_name' => $data['name'] ?? null,
            'trading_name' => $data['trading_name'] ?? null,
            'contact_person' => $data['emergency_contact_name'] ?? null,
            'contact_person_phone_1' => $data['emergency_phone_1'] ?? null,
            'contact_person_phone_2' => $data['emergency_phone_2'] ?? null,
            'contact_person_email' => $data['emergency_email'] ?? null,
            'contact_person_address' => $data['emergency_address'] ?? null,
            'industries_id' => $data['industries_id'] ?? null,
            'email' => $data['email'] ?? null,
            'fax' => $data['fax'] ?? null,
            'status' => $data['status'] ?? null,
            'phone' => $data['phone'] ?? null,
            'mobile' => $data['mobile'] ?? null,
        ]);

        if (! $model->college_id) {
            $model->college_id = auth()->user()->college_id ?? 1;
        }

        if (! $model->exists) {
            $model->created_by = auth()->id();
        }

        $model->updated_by = auth()->id();

        return $model;
    }

    private function saveAddressAndPostalStep(Employer $model, array $data)
    {
        $model->fill([
            'address' => $data['residential_address'] ?? null,
            'suburb' => $data['residential_city'] ?? null,
            'postcode' => $data['residential_postcode'] ?? null,
            'state' => $data['residential_state'] ?? null,
            'country_id' => $data['residential_country'] ?? null,
            'ABN' => $data['residential_abn'] ?? null,
        ]);

        $model->updated_by = auth()->id();

        return $model;
    }
}
