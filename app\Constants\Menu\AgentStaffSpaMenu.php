<?php

namespace App\Constants\Menu;

class AgentStaffSpaMenu
{
    public const AGENT_STAFF_MENU_ITEMS = [
        [
            'label' => 'Student Application',
            'url' => 'incompleted-application',
            'mainmenu' => ['students'],
            'activeurls' => [
                'spa.agentstaff.incompleted-application',
                'spa.agentstaff.completed-application',
            ],
            'svgicon' => '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path class="stroke-none newicon" d="M0.796875 4.40625C0.458333 4.30208 0.289062 4.04167 0.289062 3.625C0.289062 3.20833 0.458333 2.94792 0.796875 2.84375L8.375 0.578125C8.58333 0.526042 8.80469 0.5 9.03906 0.5C9.27344 0.5 9.49479 0.526042 9.70312 0.578125L17.2812 2.84375C17.6198 2.94792 17.7891 3.20833 17.7891 3.625C17.7891 4.04167 17.6198 4.30208 17.2812 4.40625L14.2344 5.30469C14.5208 5.98177 14.6641 6.67188 14.6641 7.375C14.6641 8.9375 14.1172 10.2656 13.0234 11.3594C11.9297 12.4531 10.6016 13 9.03906 13C7.47656 13 6.14844 12.4531 5.05469 11.3594C3.96094 10.2656 3.41406 8.9375 3.41406 7.375C3.41406 6.67188 3.55729 5.98177 3.84375 5.30469L2.00781 4.75781V5.96875C2.32031 6.15104 2.47656 6.41146 2.47656 6.75C2.47656 7.0625 2.33333 7.32292 2.04688 7.53125L2.63281 9.95312C2.65885 10.0573 2.65885 10.1484 2.63281 10.2266C2.63281 10.3047 2.60677 10.3698 2.55469 10.4219C2.5026 10.474 2.4375 10.5 2.35938 10.5H0.71875C0.614583 10.5 0.523438 10.4479 0.445312 10.3438C0.393229 10.2135 0.393229 10.0833 0.445312 9.95312L1.03125 7.53125C0.744792 7.32292 0.601562 7.0625 0.601562 6.75C0.601562 6.41146 0.757812 6.15104 1.07031 5.96875V4.48438L0.796875 4.40625ZM9.03906 11.125C10.0807 11.125 10.9661 10.7604 11.6953 10.0312C12.4245 9.30208 12.7891 8.41667 12.7891 7.375C12.7891 6.85417 12.6719 6.34635 12.4375 5.85156L9.70312 6.67188C9.26042 6.80208 8.81771 6.80208 8.375 6.67188L5.64062 5.85156C5.40625 6.34635 5.28906 6.85417 5.28906 7.375C5.28906 8.41667 5.65365 9.30208 6.38281 10.0312C7.11198 10.7604 7.9974 11.125 9.03906 11.125ZM8.92188 2.375L4.74219 3.625L8.92188 4.875C9 4.875 9.07812 4.875 9.15625 4.875L13.3359 3.625L9.15625 2.375C9.07812 2.375 9 2.375 8.92188 2.375ZM12.75 13.0391C14.1562 13.0911 15.3411 13.625 16.3047 14.6406C17.2943 15.6302 17.7891 16.8281 17.7891 18.2344V18.625C17.7891 19.1458 17.6068 19.5885 17.2422 19.9531C16.8776 20.3177 16.4349 20.5 15.9141 20.5H2.16406C1.64323 20.5 1.20052 20.3177 0.835938 19.9531C0.471354 19.5885 0.289062 19.1458 0.289062 18.625V18.2344C0.289062 16.8281 0.770833 15.6302 1.73438 14.6406C2.72396 13.625 3.92188 13.0911 5.32812 13.0391L9.03906 16.125L12.75 13.0391ZM8.10156 18.625V17.7656L4.74219 14.9922C3.98698 15.1745 3.36198 15.5651 2.86719 16.1641C2.39844 16.763 2.16406 17.4531 2.16406 18.2344V18.625H8.10156ZM15.9141 18.625V18.2344C15.9141 17.4531 15.6667 16.763 15.1719 16.1641C14.7031 15.5651 14.0911 15.1745 13.3359 14.9922L9.97656 17.7656V18.625H15.9141Z" fill="currentColor"/> </svg>',

        ],
        [
            'label' => 'Create Application',
            'url' => 'create-student-application',
            'mainmenu' => ['students'],
            'activeurls' => ['spa.agentstaff.create-student-application'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M4 4a2 2 0 0 1 2-2h6.172a2 2 0 0 1 1.414.586l5.11 5.109a3 3 0 0 0-.302.267L16.356 10H14a2 2 0 0 1-2-2V3.5H6a.5.5 0 0 0-.5.5v16a.5.5 0 0 0 .5.5h12a.5.5 0 0 0 .5-.5v-2.85l1.5-1.5V20a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2zm10 4.5h3.38L13.5 4.62V8a.5.5 0 0 0 .5.5m-.803 6.072L19.1 8.669a2.286 2.286 0 1 1 3.232 3.233l-5.902 5.902a2.7 2.7 0 0 1-1.248.707l-1.83.457A1.1 1.1 0 0 1 13 19H7.75a.75.75 0 0 1 0-1.5h4.321l.42-1.68c.118-.473.362-.904.706-1.248"/></svg>',
        ],
        [
            'label' => 'Document',
            'url' => '/spa/agent/document',
            'mainmenu' => ['students', '', 'commission', 'document', 'finance', 'reports', 'communication', 'partner_agent'],
            'activeurls' => ['spa.agent.document'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18.5 20C18.5 20.275 18.276 20.5 18 20.5H6C5.724 20.5 5.5 20.275 5.5 20V4C5.5 3.725 5.724 3.5 6 3.5H12V8C12 9.104 12.896 10 14 10H18.5V20ZM13.5 4.621L17.378 8.5H14C13.724 8.5 13.5 8.275 13.5 8V4.621ZM19.414 8.414L13.585 2.586C13.559 2.56 13.527 2.54 13.5 2.516C13.429 2.452 13.359 2.389 13.281 2.336C13.241 2.309 13.195 2.291 13.153 2.268C13.082 2.228 13.012 2.184 12.937 2.152C12.74 2.07 12.528 2.029 12.313 2.014C12.266 2.011 12.22 2 12.172 2H12.171H12H6C4.896 2 4 2.896 4 4V20C4 21.104 4.896 22 6 22H18C19.104 22 20 21.104 20 20V10V9.828C20 9.298 19.789 8.789 19.414 8.414Z" fill="currentColor"/>
            </svg>',
        ],

    ];
}
