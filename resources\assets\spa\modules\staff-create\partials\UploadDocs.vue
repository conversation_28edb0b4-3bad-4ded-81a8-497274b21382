+
<script setup>
import { computed } from 'vue';
import FormUploader from '@spa/components/KendoInputs/FormUploader.vue';
import AgentDocUpload from '@spa/modules/staff-create/partials/AgentDocUpload.vue';
import { getValidationMessage } from '@spa/composables/formComposables.js';

const props = defineProps({
    userType: {},
    modelValue: {},
});
const userType = computed(() => {
    return props.userType ?? route().params.user_type;
});
const emit = defineEmits(['update:modelValue']);
const formData = computed({
    get() {
        return props.modelValue || {};
    },
    set(value) {
        emit('update:modelValue', value);
    },
});
</script>
<template>
    <div>
        <h3 class="mb-6 text-base font-semibold text-gray-800">Documents</h3>
        <AgentDocUpload :modelValue="formData" v-if="userType === 'agent'" />

        <div class="grid grid-cols-1 gap-8 xl:grid-cols-3" v-else>
            <!-- Proof of Identification -->
            <div>
                <FormUploader
                    v-model="formData.proof_of_identification"
                    name="proof_of_identification"
                    label="Proof of Identification"
                    accept=".pdf,.doc,.docx,.jpg,.png"
                    :max-file-size="30000000"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'proof_of_identification')"
                />
            </div>

            <!-- Resume -->
            <div>
                <FormUploader
                    v-model="formData.resume"
                    name="resume"
                    label="Resume"
                    accept=".pdf,.doc,.docx"
                    :max-file-size="30000000"
                />
            </div>

            <!-- Active Contract -->
            <div>
                <FormUploader
                    v-model="formData.active_contract"
                    name="active_contract"
                    label="Active Contract"
                    accept=".pdf,.doc,.docx"
                    :max-file-size="30000000"
                />
            </div>
        </div>

        <!-- Other Documents -->
        <div class="mt-8">
            <FormUploader
                v-model="formData.other_documents"
                name="other_documents"
                label="Other Documents"
                accept=".pdf,.doc,.docx,.jpg,.png"
                :max-file-size="30000000"
                :multiple="true"
            />
            <p class="mt-4 text-sm text-gray-600">
                Please ensure the file name is clear and descriptive of its content.
            </p>
        </div>
    </div>
</template>

<style scoped></style>
