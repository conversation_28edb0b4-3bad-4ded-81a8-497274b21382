<?php

namespace Support\Auth;

use App\Users;
use <PERSON><PERSON>\Permission\Models\Permission as BasePermission;

class Permission extends BasePermission
{
    const DEFAULT_GUARD = 'web';

    const TYPE_STAFF = 'staff';

    const TYPE_STUDENT = 'student';

    const TYPE_AGENT = 'agent';

    protected $fillable = ['name', 'label', 'module', 'parent_id', 'type'];

    // protected $guard_name = 'web';

    protected $table = 'galaxy_app_permissions';

    public function group_permissions()
    {
        return $this->hasMany(Permission::class, 'module', 'module');
    }

    public function permissionOf()
    {
        return $this->belongsToMany(UserGroup::class, 'galaxy_app_role_permissions', 'permission_id', 'role_id');
    }

    public function children()
    {
        return $this->hasMany(Permission::class, 'parent_id', 'id');
    }

    public function parent()
    {
        return $this->belongsTo(Permission::class, 'parent_id', 'id');
    }

    public function scopeModule($query, $module)
    {
        return $query->where('module', $module);
    }

    public function isRoot()
    {
        return $this->parent_id === null;
    }

    public function scopeType($query, $type)
    {
        return $query->where('type', $type);
    }

    public static function GetGroups()
    {
        return self::groupBy('module')->get();
    }

    public function scopeFilterQuery(
        $query,
        $value
    ) {
        if (empty($value)) {
            return $query;
        }

        return $query->where(function ($q) use ($value) {
            $q->where('name', 'like', "%$value%")
                ->orWhere('label', 'like', "%$value%");
        });
    }

    public function scopeFilterModule(
        $query,
        $value
    ) {
        if (empty($value)) {
            return $query;
        }

        return $query->where('module', $value);
    }

    public function scopeUserRole(
        $query,
        $value = null
    ) {
        $userId = $value['user'] ?? 0;
        $roleId = $value['role'] ?? 0;
        $user = Users::with(['roles' => function ($query) use ($roleId) {
            $query->with(['roleType']);
            if ($roleId) {
                $query->where('id', $roleId);
            }
        }])->find($userId);
        $roleAlias = '';
        if ($user) {
            $roleFound = $user->roles ? $user->roles->first() : null;
        }
        $roleType = ($roleFound) ? $roleFound->roleType->role_alias : '';

        return $query->where('type', $roleType);
    }

    public static function getPermissionsListForUserRole($userId = null, $roleId = null, $rowsPerPage = 25, $newrole = false)
    {
        $user = Users::with(['roles' => function ($query) use ($roleId) {
            $query->with(['roleType', 'parentrole']);
            if ($roleId) {
                $query->where('id', $roleId);
            }
        }])->find($userId);
        $roleFound = null;

        if ($user && $user->roles) {
            $roleFound = $user->roles->first();
        }

        if (! $roleFound && $newrole) {
            $roleFound = UserGroup::find($roleId);
        }
        $roleType = ($roleFound && $roleFound->roleType) ? $roleFound->roleType->role_alias : '';

        $permissions = $defaultPermissions = [];
        $hasParent = false;
        $rolePermissionsFinal = [];
        if ($roleFound) {
            $hasParent = (bool) ($roleFound->parentrole ?? false);
            $permissions = $roleFound->permissions()->get();
            $defaultPermissions = $hasParent ? ($roleFound->parentrole->permissions()->get() ?? collect()) : $permissions;

            // Ensure we're working with collections
            $permissions = $permissions ?? collect();
            $defaultPermissions = $defaultPermissions ?? collect();
            // dd($permissions, $defaultPermissions);

            $allPermissions = $permissions->merge($defaultPermissions)->pluck('name')->toArray();
            $permissions = $permissions->pluck('name')->toArray();
            $defaultPermissions = $defaultPermissions->pluck('name')->toArray();

            foreach ($allPermissions as $permission) {
                if (in_array($permission, $permissions)) {
                    // this has access
                    $status = $original = $result = 1;
                    // now check if it is other than default
                    if (in_array($permission, $defaultPermissions)) {
                        // permission has access and it is same as default
                        $default = 1;
                        $event = 'hasaccess';
                    } else {
                        // permission has access but it is different from default
                        $default = 0;
                        $event = 'revoked';
                    }
                } else {
                    // this permission has no access
                    $status = $original = $result = 0;
                    if (! in_array($permission, $defaultPermissions)) {
                        // permission has access and it is same as default
                        $default = 0;
                        $event = 'revoked';
                    } else {
                        // permission has access but it is different from default
                        $default = 1;
                        $event = 'added';
                    }
                }
                $rolePermissionsFinal[$permission] = ['s' => $status, 'd' => $default, 'o' => $original, 'e' => $event, 'r' => $result];
            }
        }

        $query = self::initFilters()->where('type', $roleType);

        $data = $query->paginate($rowsPerPage);
        $data->through(function ($item) use ($rolePermissionsFinal) {
            $item->permission_status = $rolePermissionsFinal[$item->name] ?? ['s' => 0, 'd' => 0, 'o' => 0, 'e' => 'noaccess', 'r' => 0];

            return $item;
        });

        return $data;

    }
}
