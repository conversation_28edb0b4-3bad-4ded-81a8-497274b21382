<template>
    <FieldWrapper :class="rootClass">
        <Label
            v-if="label"
            :class="labelClass"
            :editor-id="componentId"
            :editor-valid="valid"
            :disabled="disabled"
            :optional="optional"
        >
            {{ label }}
            <div v-if="hint && hintOnLabel" class="font-light italic text-gray-400">
                ({{ hint }})
            </div>
            <span v-if="indicateRequired" class="ml-1 text-red-500">*</span>
        </Label>

        <div :class="wrapClass">
            <div class="relative">
                <Editor
                    :tools="tools"
                    :content-style="contentStyle"
                    :default-content="initialContent"
                    :default-edit-mode="defaultEditMode"
                    ref="editorRef"
                    :preserve-whitespace="preserveWhitespace"
                    :resizable="resizable"
                    @change="handleChange"
                    @blur="handleBlur"
                    @focus="handleFocus"
                    @paste-html="handlePasteHtml"
                />
            </div>
            <Error v-if="!valid" :class="errorClass">
                {{ validationMessage }}
            </Error>
            <Hint v-else-if="showHint" :class="hintClass">
                {{ hint }}
            </Hint>
        </div>
    </FieldWrapper>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { twMerge } from 'tailwind-merge';
import { Editor } from '@progress/kendo-vue-editor';

const props = defineProps({
    modelValue: String,
    label: {
        type: String,
        default: '',
    },
    validationMessage: {
        type: String,
        default: '',
    },
    valid: {
        type: Boolean,
        default: true,
    },
    indicateRequired: {
        type: Boolean,
        default: false,
    },
    hint: {
        type: String,
        default: '',
    },
    hintOnLabel: {
        type: Boolean,
        default: false,
    },
    orientation: {
        type: String,
        default: 'vertical',
        validator: (value) => ['vertical', 'horizontal'].includes(value),
    },
    class: {
        type: String,
        default: '',
    },
    id: {
        type: String,
        default: '',
    },
    tools: {
        type: Array,
        default: [
            'FormatBlock',
            ['Bold', 'Italic', 'Underline'],
            ['Undo', 'Redo'],
            ['Link', 'Unlink'],
            ['AlignLeft', 'AlignCenter', 'AlignRight'],
            ['InsertTable'],
            ['OrderedList', 'UnorderedList', 'Indent', 'Outdent'],
        ],
    },
    defaultContent: {
        type: String,
        default: '',
    },
    defaultEditMode: {
        type: String,
        default: 'div',
    },
    contentStyle: {
        type: Object,
        default: () => ({ height: '400px' }),
    },
    preserveWhitespace: {
        type: Boolean,
        default: false,
    },
    resizable: {
        type: Boolean,
        default: false,
    },
    pt: {
        type: Object,
        default: {},
    },
});

const emit = defineEmits(['update:modelValue', 'change', 'blur', 'focus', 'paste-html']);

// Reactive state
const editorRef = ref(null);
const isInternalUpdate = ref(false);

// Initial content - only set once on mount
const initialContent = computed(() => {
    return props.defaultContent || props.modelValue || '<p></p>';
});

// Generate a unique ID if none provided
const componentId = computed(() => {
    return props.id || `field-${Math.random().toString(36).substr(2, 9)}`;
});

const showHint = computed(() => {
    return !props.valid && props.hint && !props.hintOnLabel;
});

// CSS class computations
const rootClass = computed(() => {
    const baseClass = 'tw-form__fieldwrapper ck-content';
    const orientationClass = props.orientation === 'horizontal' ? 'field-horizontal' : '';
    return twMerge(baseClass, orientationClass, props.pt.root, props.class);
});

const wrapClass = computed(() => {
    return twMerge('k-form-field-wrap', props.pt.wrap);
});

const labelClass = computed(() => {
    const baseClass = 'tw-form__label mb-1 font-medium leading-5 text-gray-700';
    return twMerge(baseClass, props.pt.label);
});

const errorClass = computed(() => {
    return twMerge('text-red-500 text-sm mt-1', props.pt.error);
});

const hintClass = computed(() => {
    return twMerge('text-gray-500 text-sm mt-1', props.pt.hint);
});

// Event handlers
const handleChange = (event) => {
    isInternalUpdate.value = true;
    const newValue = event.html || event.target?.value || '';
    emit('update:modelValue', newValue);
    emit('change', event);
    nextTick(() => {
        isInternalUpdate.value = false;
    });
};

const handleBlur = (event) => {
    emit('blur', event);
};

const handleFocus = (event) => {
    emit('focus', event);
};

const handlePasteHtml = (event) => {
    emit('paste-html', event);
};

// Watch for external modelValue changes (not from internal edits)
watch(
    () => props.modelValue,
    (newValue) => {
        if (!isInternalUpdate.value && editorRef.value) {
            const currentContent = getHTML();
            // Only update if content is actually different
            if (currentContent !== newValue) {
                nextTick(() => {
                    setHTML(newValue || '<p></p>');
                });
            }
        }
    }
);

// Expose editor methods and additional utilities
const getEditor = () => editorRef.value;

const setHTML = (html) => {
    if (editorRef.value && editorRef.value.setHTML) {
        editorRef.value.setHTML(html);
    }
};

const getHTML = () => {
    if (editorRef.value && editorRef.value.getHTML) {
        return editorRef.value.getHTML();
    }
    return '';
};

// Expose methods to parent component
defineExpose({
    getEditor,
    setHTML,
    getHTML,
});
</script>
