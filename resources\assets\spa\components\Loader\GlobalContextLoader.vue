<template>
    <div :class="rootClass" v-bind="$attrs">
        <!-- If custom #loading slot provided -->
        <template v-if="isContextLoading && $slots.loading">
            <div v-if="overlay">
                <slot />
                <div class="absolute inset-0 z-10">
                    <slot name="loading" />
                </div>
            </div>
            <template v-else>
                <slot name="loading" />
            </template>
        </template>

        <!-- If predefined loaders matched -->
        <template v-else-if="isContextLoading && matchedType !== 'fallback'">
            <div v-if="overlay">
                <slot />
                <div class="absolute inset-0 z-10">
                    <component :is="getLoaderComponent" />
                </div>
            </div>
            <component v-else :is="getLoaderComponent" />
        </template>

        <!-- Default slot -->
        <div v-else>
            <slot />
        </div>

        <!-- Fallback overlay -->
        <div
            v-if="isContextLoading && matchedType === 'fallback'"
            class="pointer-events-none absolute inset-0 z-50 bg-white/50"
        >
            <Spinner :size="'2xl'" :pt="{ root: 'items-start' }" loadingText="" />
        </div>
    </div>
</template>

<script setup>
import { computed, h } from 'vue';
import SkeletonList from '@spa/components/Skeleton/SkeletonList.vue';
import SkeletonBatchInfo from '@spa/components/Skeleton/SkeletonBatchInfo.vue';
import SkeletonCardGrid from '@spa/components/Skeleton/SkeletonCardGrid.vue';
import SkeletonCommentCard from '@spa/components/Skeleton/SkeletonCommentCard.vue';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import Spinner from './Spinner.vue';
import { twMerge } from 'tailwind-merge';

const props = defineProps({
    context: { type: String, required: true },
    type: { type: String, default: '' },
    size: { type: String, default: null },
    overlay: { type: Boolean, default: false },
    pt: { type: Object, default: () => {} },
});

const loaderStore = useLoaderStore();
const isContextLoading = computed(() => {
    console.log(props.context, loaderStore.isContextLoading(props.context));
    return loaderStore.isContextLoading(props.context);
});
const isCourseItem = computed(() => /^course-item-\d+$/.test(props.context));

const getSkeletonSize = computed(() => {
    if (props.size) return props.size;
    if (isCourseItem.value) return 'single';
    if (['semester-list'].includes(props.context)) return 'list';
    return null;
});

const rootClass = computed(() => {
    return twMerge('relative', props.pt?.root);
});

const matchedType = computed(() => {
    const ctx = props.context;
    const type = props.type;

    if (type) return type;

    if (
        ['semester-list'].includes(ctx) ||
        isCourseItem.value ||
        type === 'skeleton-list' ||
        type === 'semester-list'
    )
        return 'skeleton-list';

    if (['batch-info-card', 'mark-table', 'filters-changed'].includes(ctx)) return 'batch-info';
    if (ctx === 'comment-card' || type === 'comment-card') return 'comment-card';
    if (ctx === 'stat-cards' || type === 'stat-cards') return 'card-grid';
    if (ctx === 'course-load' || type === 'course-load') return 'course-loader';

    return 'fallback';
});

const getLoaderComponent = computed(() => {
    switch (matchedType.value) {
        case 'skeleton-list':
            return {
                render() {
                    return h(SkeletonList, { size: getSkeletonSize.value });
                },
            };
        case 'batch-info':
            return {
                render() {
                    return h(SkeletonBatchInfo, { loader: true });
                },
            };
        case 'comment-card':
            return SkeletonCommentCard;
        case 'card-grid':
            return SkeletonCardGrid;
        case 'course-loader':
            return {
                render() {
                    return h('div', {
                        class: 'mb-2 h-5 w-3/4 animate-pulse rounded bg-gray-200',
                    });
                },
            };
        default:
            return null;
    }
});
</script>
