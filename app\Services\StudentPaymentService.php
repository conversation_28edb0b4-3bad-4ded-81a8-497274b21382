<?php

namespace App\Services;

use App;
use App\DTO\studentProfile\AddMiscellaneousPayment;
use App\DTO\studentProfile\AgentCommission;
use App\DTO\studentProfile\PayUpfrontFee;
use App\DTO\studentProfile\RecordPayment;
use App\DTO\studentProfile\StudentScholarship as ScholarshipDTO;
use App\Exceptions\ApplicationException;
use App\Helpers\Helpers;
use App\Model\SendMail;
use App\Model\v2\AgentCommission as AgentCommissionModel;
use App\Model\v2\CreditBonusAllocation;
use App\Model\v2\EmailTemplate;
use App\Model\v2\InvoiceNumber;
use App\Model\v2\InvoiceSetting;
use App\Model\v2\PaymentMode;
use App\Model\v2\ResceiptNumber;
use App\Model\v2\SetupProviderFacility;
use App\Model\v2\SetupServices;
use App\Model\v2\SetupServicesCategory;
use App\Model\v2\SetupServicesName;
use App\Model\v2\StudentAdditionalServiceRequest;
use App\Model\v2\StudentAgentCommission;
use App\Model\v2\StudentCertificateRegister;
use App\Model\v2\StudentCommunicationLog;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentInitialPayment;
use App\Model\v2\StudentInitialPaymentDetails;
use App\Model\v2\StudentInitialPaymentTransaction;
use App\Model\v2\StudentInvoiceCredit;
use App\Model\v2\StudentMiscellaneousPayment;
use App\Model\v2\StudentPaymentAgentCredit;
use App\Model\v2\StudentProfileOshc;
use App\Model\v2\StudentScholarship;
use App\Model\v2\StudentServicePayment;
use App\Model\v2\TransactionNumber;
use App\Repositories\CommonRepository;
use App\Repositories\StudentPaymentRepository;
use App\Repositories\StudentProfileCommonRepository;
use App\Repositories\StudentRepository;
use App\Traits\CommonTrait;
use App\Traits\ResponseTrait;
use Config;
use Domains\Xero\Entities\Invoice;
use Domains\Xero\Events\DeleteingXeroInvoiceableModel;
use Domains\Xero\Events\PaymentTransactionReadyToSyncToXero;
use Domains\Xero\Facades\Xero;
use Domains\Xero\Models\XeroInvoice;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Support\Models\Activity;

// use DateTime;
// use Exception;

class StudentPaymentService
{
    use CommonTrait;
    use ResponseTrait;

    protected $studentRepository;

    protected $studentPaymentRepository;

    protected $commonRepository;

    protected $studentMiscellaneousPayment;

    protected $studentInvoiceCredit;

    protected $studentCourse;

    protected $emailTemplate;

    protected $studentProfileCommonRepository;

    protected $studentInitialPaymentDetails;

    protected $invoiceNumber;

    protected $studentCourseService;

    public function __construct(
        CommonRepository $commonRepository,
        StudentProfileCommonRepository $studentProfileCommonRepository,
        StudentCourses $studentCourses,
        PaymentMode $paymentMode,
        InvoiceNumber $invoiceNumber,
        ResceiptNumber $resceiptNumber,
        TransactionNumber $transactionNumber,
        StudentInitialPaymentDetails $studentInitialPaymentDetails,
        StudentInitialPaymentTransaction $studentInitialPaymentTransaction,
        StudentInitialPayment $studentInitialPayment,
        StudentServicePayment $studentServicePayment,
        StudentAdditionalServiceRequest $studentAdditionalServiceRequest,
        StudentMiscellaneousPayment $studentMiscellaneousPayment,
        StudentScholarship $studentScholarship,
        StudentInvoiceCredit $studentInvoiceCredit,
        StudentAgentCommission $studentAgentCommission,
        EmailTemplate $emailTemplate,
        StudentCourseService $studentCourseService
    ) {
        $this->commonRepository = $commonRepository;
        $this->studentProfileCommonRepository = $studentProfileCommonRepository;
        $this->studentRepository = new StudentRepository($studentCourses);
        $this->studentPaymentRepository = new StudentPaymentRepository($studentCourses);
        $this->studentCourse = new StudentPaymentRepository($studentCourses);
        $this->paymentMode = new StudentPaymentRepository($paymentMode);
        $this->invoiceNumber = new StudentPaymentRepository($invoiceNumber);
        $this->resceiptNumber = new StudentPaymentRepository($resceiptNumber);
        $this->transactionNumber = new StudentPaymentRepository($transactionNumber);
        $this->studentInitialPaymentDetails = new StudentPaymentRepository($studentInitialPaymentDetails);
        $this->studentInitialPaymentTransaction = new StudentPaymentRepository($studentInitialPaymentTransaction);
        $this->studentInitialPayment = new StudentPaymentRepository($studentInitialPayment);
        $this->studentServicePayment = new StudentPaymentRepository($studentServicePayment);
        $this->studentAdditionalServiceRequest = new StudentPaymentRepository($studentAdditionalServiceRequest);
        $this->studentMiscellaneousPayment = new StudentPaymentRepository($studentMiscellaneousPayment);
        $this->studentScholarship = new StudentPaymentRepository($studentScholarship);
        $this->studentInvoiceCredit = new StudentPaymentRepository($studentInvoiceCredit);
        $this->studentAgentCommission = new StudentPaymentRepository($studentAgentCommission);
        $this->emailTemplate = new StudentPaymentRepository($emailTemplate);
        $this->studentCourseService = $studentCourseService;
    }

    public function getAllPaymentsData($request)
    {
        $result = $this->studentPaymentRepository->getAllPaymentsData($request);
        $result['xeroConnect'] = Xero::isConnected() ? 1 : 0;
        $result['reSchedule'] = false;

        return $result;
    }

    private function isEntitySyncOrNot($entityClass, $entityId)
    {
        $isEntitySync = 0;
        if (Xero::isConnected()) {
            $entity = $entityClass::find($entityId);
            $isEntitySync = ($entity && $entity->xeroContact && ! empty($entity->xeroContact->getXeroId())) ? 1 : 0;
        }

        return $isEntitySync;
    }

    public function getInitialOrSchedulePaymentData($request)
    {
        $isReSchedule = false;
        $isXeroConnect = Xero::isConnected() ? 1 : 0;
        if ($request->payment_type == 'Schedual') {
            $isReSchedule = StudentInitialPaymentDetails::where('student_course_id', $request->student_course_id)->exists();
        }
        $result = $this->studentPaymentRepository->getInitialOrSchedulePaymentData($request);
        $result['xeroConnect'] = $isXeroConnect;
        $result['reSchedule'] = $isReSchedule;
        $result['isStudentSync'] = $this->isEntitySyncOrNot(App\Model\v2\Student::class, $request->student_id);

        return $result;
    }

    public function getXeroFailedPaymentsData($request)
    {
        return $this->studentPaymentRepository->getXeroFailedPaymentsData($request);
    }

    public function getAgentCommissionData($request)
    {
        $results = $this->studentPaymentRepository->getAgentCommissionData($request);
        $res = utf8ize($results['data']);
        $agentId = $res[0]['agent_id'] ?? null;
        $syncOption = 'directSync';
        $isXeroConnect = Xero::isConnected();
        $isAgentSync = 0;

        if ($isXeroConnect) {
            $syncOption = InvoiceSetting::where([
                'college_id' => $request->college_id,
                'key' => 'agent_commission_sync_option',
            ])->value('value') ?? $syncOption;
        }

        return [
            'data' => $res,
            'total' => $results['total'],
            'xeroConnect' => ($isXeroConnect) ? 1 : 0,
            'syncOption' => $syncOption,
            'isAgentSync' => $this->isEntitySyncOrNot(App\Model\v2\Agent::class, $agentId),
        ];
    }

    public function getAgentBonusData($request)
    {
        $results = $this->studentPaymentRepository->getAgentBonusData($request);
        $res = utf8ize($results['data']);

        return [
            'data' => $res,
            'total' => $results['total'],
        ];
    }

    public function getPaymentRefundData($request)
    {
        return [
            'data' => $this->studentPaymentRepository->getPaymentRefundData($request),
            'total' => $this->studentPaymentRepository->getPaymentRefundData($request, true),
        ];
    }

    public function getStudentScholarshipData($request)
    {
        $result = $this->studentPaymentRepository->getStudentScholarshipData($request);

        return [
            'data' => $result['data'],
            'total' => $result['total'],
            'xeroConnect' => Xero::isConnected() ? 1 : 0,
        ];
    }

    public function getMiscellaneousPaymentData($request)
    {
        $result = $this->studentPaymentRepository->getMiscellaneousPaymentData($request);
        $result['xeroConnect'] = Xero::isConnected() ? 1 : 0;

        // $result['isStudentSync'] = $this->isEntitySyncOrNot(App\Model\v2\Student::class, $request->student_id);
        return $result;
    }

    public function getMiscellaneousPaymentFromData($request)
    {
        $collegeId = Auth::user()->college_id;

        return [
            'arrOtherServiceTypeMiscellaneousPayment' => $this->convertKendoFormat(Config::get('constants.arrOtherServiceTypeMiscellaneousPayment')),
            'receipt_number' => ResceiptNumber::where('college_id', $collegeId)->value('resceipt_number'),
            'invoice_number' => InvoiceNumber::where('college_id', $collegeId)->value('invoice_number'),
            'paymentStatus' => $this->getPaymentStatusList(),
            'oshcProvider' => $this->studentCourseService->getOshcProvider($request),
            'paymentModeList' => $this->paymentMode->getAll(['id as Id', 'name as Name'], 'name'),
        ];
    }

    public function getServicePaymentData($request)
    {
        $result = $this->studentPaymentRepository->getServicePaymentData($request);
        $result['xeroConnect'] = Xero::isConnected() ? 1 : 0;

        // $result['isStudentSync'] = $this->isEntitySyncOrNot(App\Model\v2\Student::class, $request->student_id);
        return $result;
    }

    public function getServicePaymentFormData($request)
    {
        $collegeId = auth()->user()->college_id;

        return [
            'invoiceNumber' => InvoiceNumber::where('college_id', $collegeId)->value('invoice_number'),
            'receiptNumber' => ResceiptNumber::where('college_id', $collegeId)->value('resceipt_number'),
            'arrServicesName' => SetupServicesName::select('services_name as Name', 'id as Id')->where('college_id', $request->college_id)->get()->toArray(),
            'paymentStatus' => $this->getPaymentStatusList(),
            'paymentMode' => $this->paymentMode->getAll(['id as Id', 'name as Name'], 'name'),
        ];
    }

    private function getPaymentStatusList()
    {
        $list = Config::get('constants.paymentStatus');
        if (Xero::isConnected()) {
            unset($list['paid']);
        }

        return $this->convertKendoFormat($list);
    }

    public function getPaymentStatementData($request)
    {
        $result = $this->studentPaymentRepository->getPaymentStatementData($request);

        return [
            'data' => $result['data'],
            'total' => $result['total'],
        ];
    }

    public function getPaymentModeList($request)
    {
        return $this->paymentMode->getAll(['id as Id', 'name as Name'], 'name');
    }

    public function getInvoiceNumber()
    {
        $collegeId = Auth::user()->college_id;

        return $this->invoiceNumber->getWhereVal(['college_id' => $collegeId], 'invoice_number');
    }

    public function getSyncLogData($request)
    {
        // TODO::GNG-2081
        $studCourseData = $this->studentCourse->find($request->student_course_id);

        $data = Activity::with(['causer'])
            ->xeroLogForStudentCourse($studCourseData->student_id, $studCourseData->course_id)
            ->orderBy('id', 'desc')->paginate(10)->toArray();

        foreach ($data['data'] as $key => $value) {
            $data['data'][$key] = $value;
            $data['data'][$key]['created_at'] = Helpers::convertDateTimeToReadableFormat($value['created_at']); // date(Config::get('app.dateFormatFrontSidePHP')." h:i A", strtotime($value['created_at']));
        }

        return $data;
    }

    public function getTabSyncLogData($request)
    {

        $studCourseData = $this->studentCourse->find($request->student_course_id);

        $data = Activity::with(['causer'])
            ->logForStudentCoursePayments(
                $request->type,
                $studCourseData->student_id,
                $studCourseData->course_id,
                $studCourseData->agent_id
            )
            ->orderBy('id', 'desc')
            ->paginate(10)
            ->toArray();

        /*$data['data'] = array_map(function ($value) {
            return [
                'description' => nl2br($value['description']),
                'properties'  => $this->setAttributeFiled($value['properties']),
                'created_at'  => Helpers::convertDateTimeToReadableFormat($value['created_at']),
            ] + $value;
        }, $data['data']);*/

        foreach ($data['data'] as $key => $value) { // dd($value['properties']);
            $data['data'][$key] = $value;
            $data['data'][$key]['description'] = nl2br($value['description']);
            $data['data'][$key]['properties'] = $this->setAttributeFiled($value['properties']);
            $data['data'][$key]['created_at'] = Helpers::convertDateTimeToReadableFormat($value['created_at']); // $this->convertDisplayDate($value['created_at']);
        }

        return $data;
    }

    /*public function setAttributeFiled($dataItems)
   {
        // Extract attributes and old values
        $attributes = isset($dataItems['attributes']) ? $dataItems['attributes']:'';
        $oldValues =  isset($dataItems['old']) ? $dataItems['old']:'';
        // Construct new array
        $newArray = [];
        if(!empty($attributes)){
            foreach ($attributes as $fieldName => $value) {
                if ($this->isDate($value)) {
                    $value = (new DateTime($value))->format('d-m-Y');
                }
                $oldValue = !empty($oldValues[$fieldName]) ? $oldValues[$fieldName] : '-';
                if ($this->isDate($oldValue)) {
                    $oldValue = (new DateTime($oldValue))->format('d-m-Y');
                }

                $newArray[] = [
                    'field_name' => $this->convertToReadable($fieldName),
                    'attributes' => $value,
                    'old' => $oldValue
                ];
            }
        }
        return $newArray;
   }

    protected function isDate($value)
    {
        if (!$value) {
            return false;
        }

        try {
            new DateTime($value);
        } catch (Exception $e) {
            return false;
        }

        return true;
    }

    public function convertToReadable($fieldName)
    {
        $words = explode('_', $fieldName);
        $formattedName = implode(' ', array_map('ucfirst', $words));
        return $formattedName;
    }*/

    public function getRecordPaymentDetails($request)
    {
        $collegeId = Auth::user()->college_id;
        $studentId = $request->student_id;
        $studentCourseId = $request->student_course_id;
        $paymentDetailId = $request->payment_detail_id;

        if (empty($request->payment_detail_id)) {
            $isExist = StudentInitialPaymentDetails::whereNot('payment_status', 'paid')
                ->where([
                    'student_course_id' => $studentCourseId,
                    'college_id' => $collegeId,
                ])->exists();
            if (! $isExist) {
                return ['status' => 'error', 'message' => 'No unpaid records found.'];
            }
        }

        $courseId = $this->studentCourse->getWhereVal(['id' => $studentCourseId], 'course_id');

        $data = [];

        // Get initial payment details
        if (empty($paymentDetailId)) {
            $initialPendingPaymentDetails = [];
            $initialPaymentDetails = StudentInitialPaymentDetails::whereNot('payment_status', 'paid')
                ->where([
                    'student_course_id' => $studentCourseId,
                    'college_id' => $collegeId,
                ])->get();
        } else {
            $initialPaymentDetails = $this->studentInitialPaymentDetails->getWhereRow(['id' => $paymentDetailId]);
            $initialPendingPaymentDetails = $this->studentPaymentRepository->getInitialPendingPaymentDetails($paymentDetailId, $studentCourseId, $studentId, $collegeId);
        }

        // Get receipt number
        $data['resceiptNumber'] = $this->resceiptNumber->getWhereVal(['college_id' => $collegeId], 'resceipt_number');

        // Process initial payment details
        $arrInitialPaymentDetail = [];
        foreach ($initialPaymentDetails as $paymentDetail) {
            $arrInitialPaymentDetail[] = [
                'id' => $paymentDetail->id,
                'Tuition Fee' => $paymentDetail->upfront_fee_to_pay,
                'invoice_credit' => $paymentDetail->invoice_credit,
                'commission' => number_format(($paymentDetail->upfront_fee_to_pay - $paymentDetail->upfront_fee_pay - $paymentDetail->invoice_credit) * $paymentDetail->commission_value / 100, 2, '.', ''),
                'gst_amount' => ($paymentDetail->GST == 'GST') ? number_format(number_format(($paymentDetail->upfront_fee_to_pay - $paymentDetail->upfront_fee_pay - $paymentDetail->invoice_credit) * $paymentDetail->commission_value / 100, 2, '.', '') * 10 / 100, 2, '.', '') : '0.00',
                'paid' => $paymentDetail->upfront_fee_pay,
                'payment_status' => $paymentDetail->payment_status,
                'arr_type' => 'InitialPayment',
                'commissionPer' => $paymentDetail->commission_value,
                'GST' => $paymentDetail->GST,
            ];
        }

        $arrInitialPendingPaymentDetail = [];
        foreach ($initialPendingPaymentDetails as $pendingPaymentDetail) {
            $arrInitialPendingPaymentDetail[] = [
                'id' => $pendingPaymentDetail->id,
                'Tuition Fee' => $pendingPaymentDetail->upfront_fee_to_pay,
                'invoice_credit' => $pendingPaymentDetail->invoice_credit,
                'commission' => number_format(($pendingPaymentDetail->upfront_fee_to_pay - $pendingPaymentDetail->upfront_fee_pay - $pendingPaymentDetail->invoice_credit) * $pendingPaymentDetail->commission_value / 100, 2, '.', ''),
                'gst_amount' => ($pendingPaymentDetail->GST == 'GST') ? number_format(number_format(($pendingPaymentDetail->upfront_fee_to_pay - $pendingPaymentDetail->upfront_fee_pay - $pendingPaymentDetail->invoice_credit) * $pendingPaymentDetail->commission_value / 100, 2, '.', '') * 10 / 100, 2, '.', '') : '0.00',
                'paid' => $pendingPaymentDetail->upfront_fee_pay,
                'payment_status' => $pendingPaymentDetail->payment_status,
                'arr_type' => 'InitialPayment',
                'commissionPer' => $pendingPaymentDetail->commission_value,
                'GST' => $pendingPaymentDetail->GST,
            ];
        }
        // $query =  StudentScholarship::where('college_id', '=', $collegeId)
        //     ->where('student_id', '=', $studentId)
        //     ->where('course_id', '=', $courseId)
        //     ->select(DB::raw('sum(scholarship_amount) - sum(used_scholarship_amount)  AS total_amount'))
        //     ->get();

        // $totalCreditAmount = StudentInitialPayment::select('student_credit')->where(['college_id'=>$collegeId,'student_id'=>$request->student_id])->first()->toArray();
        // $data['totalCreditAmount'] = $totalCreditAmount['student_credit'];

        $data['totalCreditAmount'] = $this->studentScholarship->getStudentCreditAmount($collegeId, $studentId);
        $data['scholarship_amount'] = $this->studentScholarship->getStudentScholarshipAmount($collegeId, $studentId, $courseId);
        $data['arrInitialPaymentDetail'] = $arrInitialPaymentDetail;
        $data['arrInitialPendingPaymentDetail'] = $arrInitialPendingPaymentDetail;

        $agentId = $this->studentCourse->getWhereVal(['id' => $studentCourseId], 'agent_id');
        $totalAgentCredit = 0;
        if ($agentId) {
            $totalAgentCredit = CreditBonusAllocation::where('college_id', $collegeId)
                ->where('agent_id', $agentId)
                ->selectRaw('SUM(amount - credit_used) as totalAgentCredit')
                ->value('totalAgentCredit') ?? 0;
        }
        $data['arrBonusAllocationAgentCredit'] = $totalAgentCredit;

        return $data;
    }

    public function getStudentUpfrontDetails($request)
    {
        $data = [];
        $college_id = $request->college_id;
        $studentId = $request->student_id;
        $studentCourseId = $request->student_course_id;
        $course_id = $this->studentCourse->find($studentCourseId)->course_id;
        $is_up_front_fee = $this->studentPaymentRepository->checkStudentUpFrontFee($college_id, $studentId, $studentCourseId, $course_id);
        $data['is_up_front_fee'] = ! empty($is_up_front_fee) ? 1 : 0;
        $data['upfrontFeeDetails'] = $is_up_front_fee;
        $data['receiptNumber'] = $this->resceiptNumber->getWhereVal(['college_id' => $college_id], 'resceipt_number');
        $data['initialPaymentDetails'] = $this->studentInitialPayment->getWhere([
            'college_id' => $college_id,
            'student_id' => $studentId,
            'course_id' => $course_id,
            'student_course_id' => $studentCourseId,
        ]);
        $data['arrStudentUpfrontFeeDetail'] = $this->studentPaymentRepository->getStudentCourse($studentId, $studentCourseId);

        return $data;
    }

    public function saveRecordPaymentDetails(RecordPayment $request)
    {
        // dd($request->toArray());

        $collegeId = Auth::user()->college_id;
        $payTimeCheck = $request->pay_time_check;
        $processingAmount = $request->processing_amount;
        $agentBonus = $request->agent_bonus;
        $badDebt = $request->bad_debt;
        $payTimeCheckKey = array_keys($payTimeCheck);
        $gstStatus = $request->gst;
        $creditAmount = $request->credit_amount;
        $scholarshipAmount = $request->scholarship_amount;

        DB::beginTransaction();
        try {
            for ($i = 0; $i < count($payTimeCheckKey); $i++) {
                $keyValue = $payTimeCheckKey[$i];
                if (array_key_exists($keyValue, $processingAmount) && $processingAmount[$keyValue] > 0) {
                    $agentBonusVal = array_key_exists($keyValue, $agentBonus) ? $agentBonus[$keyValue] : 0;
                    if ($gstStatus == true) {
                        if (array_key_exists($keyValue, $gstStatus)) {
                            $gstStatusVal = 'GST';
                        }
                    } else {
                        $gstStatusVal = 'NO GST';
                    }
                    $badDebtVal = array_key_exists($keyValue, $badDebt) ? $badDebt[$keyValue] : 0;
                    $transactionNumber = $this->transactionNumber->getWhereVal(['college_id' => $collegeId], 'transaction_number');
                    $studentCourse = $this->studentInitialPaymentDetails->find($keyValue);
                    $studentCourseId = $studentCourse->student_course_id;
                    $studentId = $studentCourse->student_id;
                    $courseId = $studentCourse->course_id;
                    $agentId = $studentCourse->agent_id;

                    $updateUpfrontFeePay = $this->updateUpfrontFeePay($keyValue, $processingAmount[$keyValue], $agentBonusVal, $gstStatusVal, $badDebtVal, $transactionNumber, $request);

                    $newPaidAmount = $updateUpfrontFeePay['finalUpfrontFeePayAmount'];

                    // if($creditAmount > $newPaidAmount){
                    //     $newCreditAmount = $newPaidAmount;
                    //     $creditAmount = abs($creditAmount - $newPaidAmount) ;
                    //     $depositedAmount = 0;
                    // }else{
                    //     $newCreditAmount = $creditAmount;
                    //     $depositedAmount = ($newPaidAmount - $creditAmount);
                    // }
                    $newCreditAmount = ($creditAmount) ? ($creditAmount / count($payTimeCheckKey)) : 0;
                    $scholarshipAmount = ($scholarshipAmount) ? ($scholarshipAmount / count($payTimeCheckKey)) : 0;
                    $depositedAmount = ($newPaidAmount - $newCreditAmount - $scholarshipAmount);

                    if ($depositedAmount < 0) {
                        $depositedAmount = ($request->hidden_deposite_amount) ? ($request->hidden_deposite_amount / count($payTimeCheckKey)) : 0;

                        /*if($newCreditAmount > $newPaidAmount){
                            $newCreditAmount = $newPaidAmount;
                        }

                        if($scholarshipAmount > $newPaidAmount){
                            $scholarshipAmount = $newPaidAmount;
                        }*/
                    }

                    /*if ($request->scholarship_amount) {
                        $scholarshipAmount = $request->scholarship_amount ?? 0;
                        if($scholarshipAmount > 0){
                            $scholarshipAmount = ($newPaidAmount > $scholarshipAmount) ? $scholarshipAmount : $newPaidAmount;
                        }
                        $request->scholarship_amount = $scholarshipAmount;
                    }*/

                    $savePaymentTransaction = $this->savePaySchedulePaymentTransaction($keyValue, $processingAmount[$keyValue], $studentId, $courseId, $request, $transactionNumber, $agentBonusVal, $gstStatusVal, $badDebtVal, $newPaidAmount, $studentCourseId, $newCreditAmount, $depositedAmount, $scholarshipAmount);
                    /*if ($request->agent_credit_amount > 0 && $i == 0) {
                        $this->addAgentCredit($collegeId, $agentId, $request->agent_credit_amount, $savePaymentTransaction->id);
                    }*/

                    // Update if scholarship amount is used
                    if ($request->scholarship_amount) {
                        // $PaidScholarshipAmount = $request->scholarship_amount ?? 0;
                        $PaidScholarshipAmount = $savePaymentTransaction->scholarship ?? 0;
                        $this->updatePaidScholarshipAmount($collegeId, $studentId, $courseId, $PaidScholarshipAmount);
                    }
                    // Update transaction number after insert
                    // if ($request->input('receipt_number') == $request->input('receipt_number_hidden') && $request->input('pay_time_check') > 1) {
                    //     $editReceiptNumber = $objRtoResceiptNumber->editResceiptNumber();
                    // }
                    $this->studentPaymentRepository->editTransactionNumber();
                    $this->studentPaymentRepository->editResceiptNumber();
                }
            }
            if ($request->credit_amount) {
                $saveStudentCredit = $this->studentPaymentRepository->updateStudentCredit($request->student_id, $request->credit_amount);
            }
            if (! empty($request->late_payment_fee) || ! empty($request->hidden_apply_surcharge)) {
                $keyValue = $payTimeCheckKey[0];
                $transactionNumber = $this->transactionNumber->getWhereVal(['college_id' => $collegeId], 'transaction_number');
                $studentCourse = $this->studentInitialPaymentDetails->find($keyValue);
                $studentCourseId = $studentCourse->student_course_id;
                $studentId = $studentCourse->student_id;
                $courseId = $studentCourse->course_id;
                $invoiceNumber = $studentCourse->invoice_number;

                $lateFeeAmount = $request->late_payment_fee;
                $surchargeAmount = $request->hidden_apply_surcharge;
                if ($lateFeeAmount > 0) {
                    $this->studentPaymentRepository->saveMiscellaneousPaySchedule($studentId, $courseId, $request, $lateFeeAmount, 'Late Fee', $invoiceNumber, $studentCourseId, $transactionNumber);
                }
                if ($surchargeAmount > 0) {
                    $this->studentPaymentRepository->saveMiscellaneousPaySchedule($studentId, $courseId, $request, $surchargeAmount, 'Surcharge', $invoiceNumber, $studentCourseId, $transactionNumber);
                }
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    private function addAgentCredit($collegeId, $agentId, $mainPaidAmount, $payTransactionId)
    {

        $userId = Auth::user()->id;
        $allocations = CreditBonusAllocation::where([
            'college_id' => $collegeId,
            'agent_id' => $agentId,
        ])->get();

        foreach ($allocations as $allocation) {

            if ($allocation->credit_used >= $allocation->amount) {
                continue;
            }

            $payableAmount = $allocation->amount - $allocation->credit_used;
            $paidAmount = min($payableAmount, $mainPaidAmount);

            $mainPaidAmount -= $paidAmount;

            if ($paidAmount <= 0) {
                continue;
            }
            $allocation->increment('credit_used', $paidAmount, ['updated_by' => $userId]);

            StudentPaymentAgentCredit::create([
                'payment_transaction_id' => $payTransactionId,
                'amount' => $paidAmount,
                'credit_bonus_id' => $allocation->id,
                'college_id' => $collegeId,
                'created_by' => $userId,
                'updated_by' => $userId,
            ]);

            if ($mainPaidAmount <= 0) {
                break;
            }
        }
    }

    public function updateStudentCreditAmount($collegeId, $studentId, $creditAmount)
    {
        if ($creditAmount > 0) {
            StudentInitialPayment::where([
                'college_id' => $collegeId,
                'student_id' => $studentId,
            ])->decrement('student_credit', $creditAmount);
            /*
            $whereArr = ['college_id' => $collegeId, 'student_id' => $studentId];
            $studentInitialPayment = StudentInitialPayment::where($whereArr)->first();
            if($studentInitialPayment){
                $totalCredit = $studentInitialPayment->student_credit;
                if($totalCredit > 0 && ($totalCredit - $creditAmount) >= 0){
                    $newCreditAmount = $totalCredit - $creditAmount;
                    StudentInitialPayment::where($whereArr)->update([
                        'student_credit' => $newCreditAmount
                    ]);
                }
            }*/
        }
    }

    public function updatePaidScholarshipAmount($collegeId, $studentId, $courseId, $PaidScholarshipAmount)
    {
        $scholarshipAmt = $this->studentScholarship->getWhere(['college_id' => $collegeId, 'student_id' => $studentId, 'course_id' => $courseId, 'is_transfer' => 0]);
        if (count($scholarshipAmt) > 0) {
            for ($i = 0; $i < count($scholarshipAmt); $i++) {
                $objUpdateScholarship = $this->studentScholarship->find($scholarshipAmt[$i]['id']);
                $remianingScholarship = $scholarshipAmt[$i]['scholarship_amount'] - $scholarshipAmt[$i]['used_scholarship_amount'];
                if ($remianingScholarship > 0) {
                    $PaidScholarshipAmount = $PaidScholarshipAmount - $remianingScholarship;
                    if ($PaidScholarshipAmount >= 0) {
                        $objUpdateScholarship->used_scholarship_amount = $objUpdateScholarship->used_scholarship_amount + $remianingScholarship;
                        $objUpdateScholarship->save();
                        if ($PaidScholarshipAmount == 0) {
                            break;
                        }
                    } else {
                        $PaidScholarshipAmount = $scholarshipAmt[$i]['scholarship_amount'] + $PaidScholarshipAmount;
                        $objUpdateScholarship->used_scholarship_amount = $PaidScholarshipAmount;
                        $objUpdateScholarship->save();
                        break;
                    }
                }
            }
        }
    }

    public function updateUpfrontFeePay($paymentDetailId, $upfrontFeePayAmount, $agentBonusVal, $gstStatusVal, $badDebtVal, $transactionNumber, $request)
    {
        $loginUserId = auth()->user()->id;
        $objUpfrontFeePay = $this->studentInitialPaymentDetails->find($paymentDetailId);
        $upFrontFeeAmount = $objUpfrontFeePay->upfront_fee_pay + $objUpfrontFeePay->invoice_credit + $upfrontFeePayAmount;
        if ($upFrontFeeAmount > $objUpfrontFeePay->upfront_fee_to_pay) {
            $studentId = $objUpfrontFeePay->student_id;
            $studentCredit = $upFrontFeeAmount - $objUpfrontFeePay->upfront_fee_to_pay;
            // if  express_credit_amount radio button is checked and is student_credit then allow to insert
            // if ($request->input('express_credit_amount') == true && $request->input('express_credit_amount') == "student_credit") {
            $saveStudentCredit = $this->studentPaymentRepository->saveStudentCredit($studentId, $studentCredit);
            // }

            $finalUpfrontFeePayAmount = $objUpfrontFeePay->upfront_fee_to_pay - $objUpfrontFeePay->upfront_fee_pay - $objUpfrontFeePay->invoice_credit;
        } else {
            $finalUpfrontFeePayAmount = $upfrontFeePayAmount;
        }

        $objUpfrontFeePay->upfront_fee_pay += $finalUpfrontFeePayAmount;
        $objUpfrontFeePay->agent_bonus += $agentBonusVal;
        $objUpfrontFeePay->bonus_gst = $gstStatusVal;
        $objUpfrontFeePay->bad_debt += $badDebtVal;
        $objUpfrontFeePay->payment_mode = $request->payment_mode;

        if (($objUpfrontFeePay->upfront_fee_pay + $objUpfrontFeePay->invoice_credit) >= $objUpfrontFeePay->upfront_fee_to_pay) {
            $objUpfrontFeePay->payment_status = 'paid';
        } elseif ($objUpfrontFeePay->upfront_fee_pay > 0) {
            $objUpfrontFeePay->payment_status = 'partially paid';
        }

        if ($objUpfrontFeePay->upfront_fee_pay >= $objUpfrontFeePay->upfront_fee_to_pay) {
            $scheduleAmount = $objUpfrontFeePay->upfront_fee_to_pay;
            $agentCommissionAmount = str_replace(',', '', number_format(($scheduleAmount * $objUpfrontFeePay->commission_value / 100), 2));
            if ($agentCommissionAmount > 0) {

                $gstAmount = ($objUpfrontFeePay->GST == 'GST') ? number_format($agentCommissionAmount * 10 / 100, 2) : 0;
                $isDeducted = ($request->agent_commission_deducted == 'yes') ? true : false;

                $arrAgentData = [
                    'college_id' => Auth::user()->college_id,
                    'student_id' => $objUpfrontFeePay->student_id,
                    'course_id' => $objUpfrontFeePay->course_id,
                    'invoice_no' => $objUpfrontFeePay->invoice_number,
                    'transaction_no' => 0,
                    'student_course_id' => $objUpfrontFeePay->student_course_id,
                    'agent_id' => $objUpfrontFeePay->agent_id,
                    'commission_payable' => $agentCommissionAmount,
                    'GST' => ! empty($objUpfrontFeePay->GST) ? 'GST' : 'NO GST',
                    'gst_amount' => $gstAmount,
                    'commission_paid' => ($isDeducted) ? $agentCommissionAmount + $gstAmount : 0,
                    'is_approved' => ($isDeducted) ? 1 : 0,
                    'is_process' => ($isDeducted) ? 1 : 0,
                    'mode' => ($isDeducted) ? 1 : $request->payment_mode,
                    'paid_date' => ($isDeducted) ? date('Y-m-d', strtotime($request->payment_date)) : null,
                    'due_date' => $objUpfrontFeePay->due_date, // TODO::GNG-314
                    'bonus_type' => 'Tuition Fee',
                    'bonus_amount' => ($agentBonusVal != '') ? $agentBonusVal : 0,
                    'bonus_gst' => ($gstStatusVal != '') ? $gstStatusVal : 0,
                    'comm_to_refund' => 0,
                    'GST_to_refund' => 0,
                    'refund_amount' => 0,
                    'remarks' => ($request->remarks != '') ? $request->remarks : '',
                    'is_commission_deducted' => ($isDeducted) ? 1 : 0,
                    'created_by' => $loginUserId,
                    'updated_by' => $loginUserId,
                ];
                $this->studentPaymentRepository->savePayAgentCommission($arrAgentData);
            }
        }

        /*$agentCommissionPayableAmount = str_replace(',', '', number_format(($finalUpfrontFeePayAmount * $objUpfrontFeePay->commission_value / 100), 2));
        if($agentCommissionPayableAmount > 0){

            $gstAmount = ($objUpfrontFeePay->GST == "GST") ? number_format($agentCommissionPayableAmount * 10 / 100, 2) : 0;

            $arrAgentData['college_id']             = Auth::user()->college_id;
            $arrAgentData['student_id']             = $objUpfrontFeePay->student_id;
            $arrAgentData['course_id']              = $objUpfrontFeePay->course_id;
            $arrAgentData['invoice_no']             = $this->getInvoiceNumber();
            $arrAgentData['transaction_no']         = $transactionNumber;
            $arrAgentData['student_course_id']      = $objUpfrontFeePay->student_course_id;
            $arrAgentData['agent_id']               = $objUpfrontFeePay->agent_id;
            $arrAgentData['commission_payable']     = $agentCommissionPayableAmount;
            $arrAgentData['GST']                    = !empty($objUpfrontFeePay->GST) ? "GST" : "NO GST";
            $arrAgentData['gst_amount']             = $gstAmount;
            $arrAgentData['commission_paid']        = ($request->agent_commission_deducted == "yes") ? $agentCommissionPayableAmount + $gstAmount : 0;
            $arrAgentData['is_approved']            = ($request->agent_commission_deducted == "yes") ? 1 : 0;
            $arrAgentData['is_process']             = ($request->agent_commission_deducted == "yes") ? 1 : 0;
            $arrAgentData['mode']                   = ($request->agent_commission_deducted == "yes") ? 1 : $request->payment_mode;
            $arrAgentData['paid_date']              = ($request->agent_commission_deducted == "yes") ? date('Y-m-d', strtotime($request->payment_date)) : null;
            $arrAgentData['due_date']               = $objUpfrontFeePay->due_date; //TODO::GNG-3144
            $arrAgentData['bonus_type']             = "Tuition Fee";
            $arrAgentData['bonus_amount']           = ($agentBonusVal != "") ? $agentBonusVal : 0;
            $arrAgentData['bonus_gst']              = ($gstStatusVal != "") ? $gstStatusVal : 0;
            $arrAgentData['comm_to_refund']         = 0;
            $arrAgentData['GST_to_refund']          = 0;
            $arrAgentData['refund_amount']          = 0;
            $arrAgentData['remarks']                = ($request->remarks != '') ? $request->remarks : '';
            $arrAgentData['is_commission_deducted'] = ($request->agent_commission_deducted == "yes") ? 1 : 0;
            $arrAgentData['created_by']             = $loginUserId;
            $arrAgentData['updated_by']             = $loginUserId;
            $saveAgentCommission = $this->studentPaymentRepository->savePayAgentCommission($arrAgentData);
            if($saveAgentCommission){
                $this->saveInvoiceNumber();
            }
        }*/

        $objUpfrontFeePay->created_by = $loginUserId;
        $objUpfrontFeePay->save();

        return ['finalUpfrontFeePayAmount' => $finalUpfrontFeePayAmount];
    }

    public function savePaySchedulePaymentTransaction($paymentDetailId, $upfrontFeePayAmount, $studentId, $courseId, $request, $transactionNumber, $agentBonusVal, $gstStatusVal, $badDebtVal, $newPaidAmount, $studentCourseId, $creditAmount, $depositedAmount, $scholarshipAmount)
    {
        $loginData = Auth::user();
        $studentPaymentTransactionData = [
            'college_id' => $loginData->college_id,
            'student_id' => $studentId,
            'course_id' => $courseId,
            'student_course_id' => $studentCourseId,
            'initial_payment_detail_id' => $paymentDetailId,
            'receipt_no' => ($request->receipt_number != '') ? $request->receipt_number : 0,
            'transection_no' => $transactionNumber,
            'payment_date' => date('Y-m-d', strtotime($request->payment_date)),
            'paid_amount' => $newPaidAmount,
            'deposited_amount' => $depositedAmount,
            'agent_commission_deduct' => ($request->agent_commission_deducted == 'yes') ? 1 : 0,
            'payment_mode' => ($request->payment_mode != '') ? $request->payment_mode : null,
            'bank_deposit_date' => date('Y-m-d', strtotime($request->bank_deposit_date)),
            'remarks' => ($request->remarks != '') ? $request->remarks : null,
            'amount_refund' => 0,
            'agent_bonus' => $agentBonusVal,
            'bonus_gst' => $gstStatusVal,
            'bonus_paid_date' => date('Y-m-d', strtotime($request->payment_date)),
            'bad_debt' => $badDebtVal,
            'reversed' => 0,
            'receipt_sent' => 0,
            // 'student_credit' => $request->filled('use_student_credit_checkbox') ? $request->input('student_credit_amount', 0) : 0,
            // 'scholarship' => $request->filled('scholarship_amount_checkbox') ? $request->input('scholarship_amount', 0) : 0,
            'created_by' => $loginData->id,
            'updated_by' => $loginData->id,
        ];

        // $scholarshipAmount = 0;
        // if(!empty($request->scholarship_amount) && $request->scholarship_amount > 0){
        //     $scholarshipAmount = ($newPaidAmount > $request->scholarship_amount) ? $request->scholarship_amount : $newPaidAmount;
        // }
        $studentPaymentTransactionData['scholarship'] = $scholarshipAmount;

        $studentPaymentTransactionData['student_credit'] = $creditAmount;
        $saveStudentPaymentTransactionData = $this->studentPaymentRepository->saveStudentPaymentTransaction($studentPaymentTransactionData);
        if ($saveStudentPaymentTransactionData) {
            event(new PaymentTransactionReadyToSyncToXero($saveStudentPaymentTransactionData->id));
        }

        $communicationLogData = [
            'college_id' => $loginData->college_id,
            'comment_by' => $loginData->id,
            'student_id' => $studentId,
            'student_course_id' => $studentCourseId,
            'today_date' => date('l, d F Y'),
            'type' => '',
            'log_type' => 'payment',
            'status' => '',
            'log' => '$ '.$request->hidden_deposite_amount,
            'activity_log' => 'payment',
            'visiblity' => 1,
            'created_by' => $loginData->id,
            'updated_by' => $loginData->id,
        ];
        $saveAgentCommission = $this->studentPaymentRepository->saveCommunicationLog($communicationLogData);

        return $saveStudentPaymentTransactionData;
    }

    public function saveStudentPayUpfrontDetails(PayUpfrontFee $data)
    {
        $request = $data->toArray();
        $studentId = $request['student_id'];
        $collegeId = $request['college_id'];
        $student_course_id = $request['student_course_id'];
        $course_id = $request['course_id'] = $this->studentCourse->find($student_course_id)->course_id;

        if ($request['upfront_fee_pay'] > $request['upfront_fee_to_pay']) {
            $returnData['type'] = 'session_error';
            $returnData['message'] = 'Upfront Fee Pay should be smaller than Upfront Fee To Pay';

            return $returnData;
        }

        $resceiptNumber = $this->resceiptNumber->getWhereVal(['college_id' => $collegeId], 'resceipt_number');
        $arrStudentCourse = $this->studentPaymentRepository->getStudentcourse($studentId, $student_course_id);
        $request['tution_fee'] = $arrStudentCourse[0]->course_fee;
        $request['agent_id'] = $arrStudentCourse[0]->agent_id;
        $agentCommission = $this->studentPaymentRepository->getAgentCommissionRate($collegeId, $request['commisson_period'], $course_id, $request['agent_id'], $request['invoice_start_date']);
        $request['apllied_commission'] = $request['commission_rate'] = isset($agentCommission[0]['commission']) ? $agentCommission[0]['commission'] : 0;
        $request['GST'] = isset($agentCommission[0]['gst']) ? $agentCommission[0]['gst'] : 'NO GST';
        $request['created_by'] = $request['updated_by'] = Auth::user()->id;
        $request['invoice_due_date'] = date('Y-m-d', strtotime($request['payment_date']));
        $request['invoice_start_date'] = date('Y-m-d', strtotime($request['payment_date']));
        try {
            if (! empty($request['editId'])) {
                $returnData = $this->checkPaymentUpdateValidation($request);
                if ($returnData['type'] == 'session_success') {
                    $UpdatedpaymentDetailData = $this->setEditedRequestData($request);

                    $checkRecord = ['id' => $request['editId']];
                    $this->studentPaymentRepository->updateStudentInitialPaymentDetail($checkRecord, $UpdatedpaymentDetailData);

                    $checkRecord = ['initial_payment_detail_id' => $request['editId']];
                    $this->studentPaymentRepository->UpdateStudentInitialPaymentTransaction($checkRecord, $UpdatedpaymentDetailData);

                    $returnData['type'] = 'session_success';
                    $returnData['message'] = 'Student Upfront Fee Updated Successfully';
                }

                return $returnData;
            }

            $checkRecord = ['college_id' => $collegeId, 'student_id' => $studentId, 'course_id' => $course_id, 'student_course_id' => $student_course_id];
            $saveStudentInitialPayment = $this->studentPaymentRepository->saveStudentInitialPayment($checkRecord, $request);
            $arrStudentInitialPayment = $this->studentInitialPayment->getWhere(['college_id' => $collegeId, 'student_id' => $studentId, 'course_id' => $course_id, 'student_course_id' => $student_course_id]);
            $requestDataFromSaveStudentInitialPaymentDetails = $this->getRequestDataFromSaveStudentInitialPaymentDetails($request, $arrStudentInitialPayment, $resceiptNumber);
            $checkRecord = ['id' => $request['editId']];
            $arrStudentDetails = $this->studentPaymentRepository->saveStudentInitialPaymentDetails($requestDataFromSaveStudentInitialPaymentDetails);
            $arrInvoiceNumber = $this->saveInvoiceNumber();
            DB::commit();
            $returnData['type'] = 'session_success';
            $returnData['message'] = 'Student Upfront Fee Save Successfully';

            return $returnData;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getRequestDataFromSaveStudentInitialPaymentDetails($request, $arrStudentInitialPayment, $resceiptNumber)
    {
        $request['agent_bonus'] = isset($request['agent_bonus']) ? $request['applied_commission_text'] : 0;
        $request['bonus_gst'] = isset($request['bonus_gst']) ? $request['bonus_gst'] : '';
        $request['bonus_gst_amount'] = (isset($request['agent_bonus']) && $request['bonus_gst'] == 'GST') ? ($request['applied_commission_text'] * 10) / 100 : '';
        $invoice_number = $this->invoiceNumber->getWhereVal(['college_id' => $request['college_id']], 'invoice_number');
        $request['invoice_number'] = $invoice_number;
        $request['bad_debt'] = 0;
        $request['bonus_paid_date'] = $request['paid_date'] = date('Y-m-d', strtotime($request['payment_date']));
        $request['invoiced_start_date'] = date('Y-m-d', strtotime($arrStudentInitialPayment[0]['invoice_start_date']));
        $request['due_date'] = date('Y-m-d', strtotime($arrStudentInitialPayment[0]['invoice_due_date']));
        $request['accrued_fee'] = 0;
        $request['commission_value'] = $arrStudentInitialPayment[0]['apllied_commission'];
        $commission = ($request['upfront_fee_to_pay'] * $arrStudentInitialPayment[0]['apllied_commission']) / 100;
        $commissionFormat = number_format($commission, 2, '.', '');
        $request['commission'] = $request['commission_payable'] = $commissionFormat;
        $request['gst_amount'] = ($arrStudentInitialPayment[0]['GST'] == 'GST') ? number_format($commissionFormat * 10 / 100, 2, '.', '') : 0;
        $request['payment_type'] = 'Initial';

        if ($request['upfront_fee_pay'] >= $request['upfront_fee_to_pay']) {
            $paymentStatus = 'paid';
        } elseif ($request['upfront_fee_pay'] > 0) {
            $paymentStatus = 'partially paid';
        } else {
            $paymentStatus = 'unpaid';
        }
        $request['payment_status'] = $paymentStatus;
        $request['refunded_amount'] = 0;
        $request['agentCommissionFormat'] = number_format((($request['upfront_fee_pay'] * $arrStudentInitialPayment[0]['apllied_commission']) / 100), 2, '.', '');
        $request['transactionNumber'] = $request['transection_no'] = $this->transactionNumber->getWhereVal(['college_id' => $request['college_id']], 'transaction_number');
        $request['transaction_no'] = 0;
        $request['CHQ_NO'] = 0;
        $request['comm_to_refund'] = 0;
        $request['GST_to_refund'] = 0;
        $request['refund_amount'] = 0;
        $request['remarks'] = ! empty($request['comment']) ? $request['comment'] : null;
        $request['is_commission_deducted'] = isset($request['agent_commission_deduct']) ? 1 : 0;
        $request['commission_paid'] = isset($request['agent_commission_deduct']) ? ((int) $request['commission_payable'] + $request['gst_amount']) : 0;
        $request['mode'] = isset($request['agent_commission_deduct']) ? 1 : (($request['payment_mode'] != '') ? $request['payment_mode'] : null);
        $request['is_approved'] = isset($request['agent_commission_deduct']) ? 1 : 0;
        $request['is_process'] = isset($request['agent_commission_deduct']) ? 1 : 0;
        $request['created_by'] = Auth::user()->id;
        $request['updated_by'] = Auth::user()->id;
        $request['receipt_no'] = $resceiptNumber;
        $request['payment_date'] = ($request['payment_date'] != '') ? date('Y-m-d', strtotime($request['payment_date'])) : null;
        $request['agent_commission_deduct'] = isset($request['agent_commission_deduct']) ? 1 : 0;
        $request['bank_deposit_date'] = ($request['payment_date'] != '') ? date('Y-m-d', strtotime($request['payment_date'])) : null;
        $request['remarks'] = ($request['remarks'] != '') ? $request['remarks'].': Last updated by '.Auth::user()->username.'@ '.date('d/m/Y') : null;
        $request['scholarship'] = 0;
        $request['amount_refund'] = 0;
        $request['bonus_paid_date'] = ($request['payment_date'] != '') ? date('Y-m-d', strtotime($request['payment_date'])) : null;
        $request['reversed'] = 0;
        $request['receipt_sent'] = 0;
        $request['comment_by'] = Auth::user()->id;
        $request['today_date'] = date('l,d F Y');
        $request['type'] = '';
        $request['log_type'] = 'payment';
        $request['status'] = '';
        $request['log'] = '$ '.number_format($request['upfront_fee_pay'], 2);
        $request['activity_log'] = 'payment';
        $request['view_by'] = '';
        $request['visiblity'] = 1;
        $request['paid_amount'] = $request['upfront_fee_pay'];
        $request['deposited_amount'] = '0';                           // to Do

        return $request;
    }

    public function setEditedRequestData($request)
    {
        $request['payment_date'] = $request['invoiced_start_date'] = $request['due_date'] = date('Y-m-d', strtotime($request['payment_date']));
        $request['paid_amount'] = $request['upfront_fee_pay'];
        $request['agent_commission_deduct'] = isset($request['agent_commission_deduct']) ? 1 : 0;
        $request['agent_bonus'] = isset($request['agent_bonus']) ? $request['applied_commission_text'] : 0;
        $request['bonus_gst'] = isset($request['bonus_gst']) ? $request['bonus_gst'] : '';
        $request['bonus_gst_amount'] = (isset($request['agent_bonus']) && $request['bonus_gst'] == 'GST') ? ($request['applied_commission_text'] * 10) / 100 : '';
        $request['payment_type'] = 'Initial';

        if ($request['upfront_fee_pay'] >= $request['upfront_fee_to_pay']) {
            $paymentStatus = 'paid';
        } elseif ($request['upfront_fee_pay'] > 0) {
            $paymentStatus = 'partially paid';
        } else {
            $paymentStatus = 'unpaid';
        }
        $request['payment_status'] = $paymentStatus;

        return $request;
    }

    public function checkPaymentUpdateValidation($request)
    {
        $collageId = Auth::user()->college_id;
        $submitRecoredDetail = $this->studentInitialPaymentDetails->find($request['editId']);
        if ($submitRecoredDetail->upfront_fee_pay > $request['upfront_fee_to_pay']) {
            $returnData['type'] = 'session_error';
            $returnData['message'] = 'Student Already Paid '.$submitRecoredDetail->upfront_fee_pay;

            return $returnData;
        }
        $invoiceNo = '';
        $student_id = $submitRecoredDetail->student_id;
        $course_id = $submitRecoredDetail->course_id;
        $student_course_id = $submitRecoredDetail->student_course_id;
        $otherDetailForthiscourse = $this->studentPaymentRepository->studentPaymentDetailsGet($collageId, $course_id, $student_id, $invoiceNo, $student_course_id);
        $getCourseDetail = $this->studentPaymentRepository->getStudentCourse($student_id, $student_course_id);
        $courseAccutleFee = $getCourseDetail[0]->course_fee;
        $courseFinishdate = strtotime($getCourseDetail[0]->finish_date);
        $invoiceStartDate = strtotime($request['payment_date']);
        $invoiceDueDate = strtotime($request['payment_date']);
        if ($courseFinishdate < $invoiceStartDate || $courseFinishdate < $invoiceDueDate) {
            $returnData['type'] = 'session_error';
            $returnData['message'] = 'Date is out of course Range.';

            return $returnData;
        }
        $totalinvoiceAmount = 0;
        $totalAmountWithoutThisTransaction = 0;
        for ($i = 0; $i < count($otherDetailForthiscourse); $i++) {
            $totalinvoiceAmount += $otherDetailForthiscourse[$i]['upfront_fee_to_pay'];
            if ($request['editId'] != $otherDetailForthiscourse[$i]['id']) {
                $totalAmountWithoutThisTransaction += $otherDetailForthiscourse[$i]['upfront_fee_to_pay'];
            }
        }

        $currentCourseFees = $totalAmountWithoutThisTransaction + $request['upfront_fee_to_pay'];

        if ($courseAccutleFee < $currentCourseFees) {
            $returnData['type'] = 'session_error';
            $returnData['message'] = 'Course Fees is Greater Then Course Actual Fees.';

            return $returnData;
        }

        $returnData['type'] = 'session_success';
        $returnData['message'] = 'Course Schedule Update successfully.';

        return $returnData;
    }

    public function getPaidPaymentData($request)
    {
        $result = $this->studentPaymentRepository->getPaidPaymentTransaction($request);

        return [
            'data' => $result['data'],
            'total' => $result['total'],
        ];
    }

    public function getAgentCommission($request)
    {
        $whereArr = [
            'college_id' => $request['college_id'],
            'student_id' => $request['student_id'],
            'student_course_id' => $request['student_course_id'],
        ];

        $data['totalPayAmount'] = $this->studentInitialPaymentDetails->getWhereSum($whereArr, 'upfront_fee_pay');
        $data['agentCommissionPaid'] = $this->studentPaymentRepository->getAgentTotalCommissionPaid($whereArr);
        $data['agentCommissionPayable'] = $this->studentPaymentRepository->getAgentCommissionPayable($whereArr);
        // $data['agentName'] = $this->studentPaymentRepository->getAgentName($request['student_course_id']);

        $agentData = $this->studentPaymentRepository->getAgentData($request['student_course_id']);
        $data['agentName'] = (! empty($agentData) && isset($agentData->agency_name)) ? trim($agentData->agency_name) : null;
        $data['agentId'] = (! empty($agentData) && isset($agentData->id)) ? trim($agentData->id) : null;

        return $data;
    }

    public function modifyAgentCommissionData($request)
    {
        return $this->studentPaymentRepository->modifyAgentCommissionData($request);
    }

    public function payAgentCommission($request)
    {
        // return $this->studentPaymentRepository->paidAgentCommissionData($request);
        $primaryId = $request->agent_commission_id;
        $commissionPaid = (float) $request->commission_paid;
        $paymentMode = $request->payment_mode;
        $paidDate = $request->paid_date;
        $remarks = $request->remarks ?? '';

        $studAgentComm = $this->studentAgentCommission->find($primaryId);
        if (! $studAgentComm) {
            return ['status' => 'error', 'message' => 'Agent commission record not found.'];
        }

        $totalPayable = $studAgentComm->commission_payable + $studAgentComm->gst_amount;

        if ($commissionPaid > $totalPayable) {
            return ['status' => 'error', 'message' => 'Paid amount exceeds the total payable amount.'];
        }

        if ($commissionPaid <= 0) {
            return ['status' => 'error', 'message' => 'Please enter a valid payment amount.'];
        }

        $updateData = [
            'commission_paid' => $commissionPaid,
            'mode' => $paymentMode,
            'paid_date' => $paidDate,
            'remarks' => $remarks,
            'is_process' => ($commissionPaid >= $totalPayable) ? 1 : 0,
            'updated_by' => auth()->user()->id,
        ];

        $studAgentComm->update($updateData);
        // TODO:: Add agent credit/advance paid amount related logic here

        return ['status' => 'success', 'message' => 'Agent commission paid successfully.'];
    }

    public function saveAgentCommission(AgentCommission $data)
    {
        $postData = $data->toArray();
        unset($postData['commission_payable']);
        unset($postData['invoice_no']);
        unset($postData['due_date']);
        $primaryId = $postData['agent_commission_id'];
        $gstAmount = (! empty($postData['gst_amount'])) ? (float) $postData['gst_amount'] : 0;

        $getData = $this->studentAgentCommission->find($primaryId);
        if ($getData) {
            if ($postData['commission_paid'] > ($gstAmount + $getData->commission_payable)) {
                return ['type' => 'error', 'message' => 'Paid amount exceeds the limit.'];
            } elseif ($postData['commission_paid'] == ($gstAmount + $getData->commission_payable)) {
                $postData['is_approved'] = 1;
                $postData['is_process'] = 1;
                // $postData['commission_payable'] = $getData->commission_payable;
            } elseif ($postData['commission_paid'] < ($gstAmount + $getData->commission_payable)) {
                $postData['is_process'] = 0;
            }
        } else {
            return ['type' => 'error', 'message' => 'Data not found'];
        }

        $postData['mode'] = $postData['payment_mode'];

        DB::beginTransaction();
        try {
            $this->studentAgentCommission->update($postData, $primaryId);
            DB::commit();

            return ['type' => 'success'];
            // return $res;
        } catch (\Exception $e) {
            DB::rollBack();

            return ['type' => 'error', 'message' => $e->getMessage()];
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function aproveAgentCommissionData($request)
    {
        $data = is_array($request) ? $request : $request->all();
        $agentCommission = StudentAgentCommission::find($data['id']);

        if (! $agentCommission) {
            return ['type' => 'error', 'message' => 'Added commission data not found.'];
        }

        if ($agentCommission->is_approved == StudentAgentCommission::STATUS_APPROVED) {
            return ['type' => 'error', 'message' => 'This commission already approved'];
        }

        $isOverCommValidity = $this->studentPaymentRepository->checkValidAgentCommissionDaterange($data['id']);
        if ($isOverCommValidity) {
            // return ['type' => 'error', 'message' => 'Invoice payment date is outside the agent commission validity.'];
            return ['type' => 'error', 'message' => 'Invoice payment date is not within the agent commission payment period.'];
        }

        DB::beginTransaction();
        try {
            $agentCommission->is_approved = StudentAgentCommission::STATUS_APPROVED;
            $agentCommission->remarks = (! empty($data['remarks'])) ? $data['remarks'] : null;
            $agentCommission->save();
            DB::commit();

            return ['type' => 'success', 'message' => 'Approved successfully'];
        } catch (\Exception $e) {
            DB::rollBack();

            return ['type' => 'error', 'message' => $e->getMessage()];
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function bulkAproveAgentCommissionData($request)
    {
        $successCount = $failCount = $validityOverInvCount = 0;
        $remarks = (! empty($request->remarks)) ? $request->remarks : null;
        foreach ($request->ids as $primaryId) {
            $isOverCommValidity = $this->studentPaymentRepository->checkValidAgentCommissionDaterange($primaryId);
            if ($isOverCommValidity) {
                $validityOverInvCount++;
            }
        }

        if ($validityOverInvCount > 0) {
            // return ['type' => 'error', 'message' => 'Invoice payment date is outside the agent commission validity.'];
            return ['type' => 'error', 'message' => 'Invoice payment date is not within the agent commission payment period.'];
        }

        foreach ($request->ids as $primaryId) {
            $agentCommission = StudentAgentCommission::find($primaryId);
            if ($agentCommission->is_approved == StudentAgentCommission::STATUS_APPROVED) {
                $failCount++;
            } else {
                DB::beginTransaction();
                try {
                    $agentCommission->is_approved = StudentAgentCommission::STATUS_APPROVED;
                    $agentCommission->remarks = $remarks;
                    $agentCommission->save();
                    DB::commit();
                    $successCount++;
                } catch (\Exception $e) {
                    DB::rollBack();
                    $failCount++;
                    // throw new ApplicationException($e->getMessage());
                }
            }
        }

        if ($successCount > 0 && $failCount > 0) {
            return ['type' => 'success', 'message' => "Successfully Approved: $successCount | Failed: $failCount"];
        } elseif ($successCount > 0) {
            return ['type' => 'success', 'message' => 'Selected items approved successfully.'];
        } elseif ($failCount > 0) {
            return ['type' => 'error', 'message' => 'Selected items already approved/failed.'];
        } else {
            return ['type' => 'error', 'message' => 'failed'];
        }

        /*return [
            'type' => 'success',
            'success_count' => $successCount,
            'success_msg' => ($failCount > 0) ? "Total $successCount items approved successfully." : 'Selected items approved successfully.',
            'error_count' => $failCount,
            'error_msg' => ($successCount > 0) ? "Total $failCount items already approved/failed." : 'Selected items already approved.',
            'message' => "Successfully Approved: $successCount | Failed: $failCount"
        ];*/

    }

    public function disapproveAgentCommissionData($request)
    {
        $data = is_array($request) ? $request : $request->all();
        $agentCommission = StudentAgentCommission::find($data['id']);

        if (! $agentCommission) {
            return ['type' => 'error', 'message' => 'Commission data not found.'];
        }

        if ($agentCommission->is_approved == StudentAgentCommission::STATUS_NOT_APPROVED) {
            return ['type' => 'error', 'message' => 'This commission is already not approved'];
        }

        DB::beginTransaction();
        try {
            $agentCommission->is_approved = StudentAgentCommission::STATUS_NOT_APPROVED;
            $agentCommission->is_process = 0;
            $agentCommission->remarks = null;
            $agentCommission->save();
            DB::commit();

            return ['type' => 'success', 'message' => 'Disapproved successfully'];
        } catch (\Exception $e) {
            DB::rollBack();

            return ['type' => 'error', 'message' => $e->getMessage()];
        }
    }

    public function processAgentCommissionData($request)
    {
        $data = is_array($request) ? $request : $request->all();
        $agentCommission = StudentAgentCommission::find($data['id']);

        if (! $agentCommission) {
            return ['type' => 'error', 'message' => 'Commission data not found.'];
        }

        if ($agentCommission->is_approved == 0) {
            return ['type' => 'error', 'message' => 'This commission is not approved. Please approve it first.'];
        }

        if ($agentCommission->is_process == 1) {
            return ['type' => 'error', 'message' => 'This commission is already processed'];
        }

        DB::beginTransaction();
        try {
            $agentCommission->mode = $data['mode'];
            $agentCommission->commission_paid = $data['commission_paid'];
            $agentCommission->paid_date = $data['paid_date'];
            $agentCommission->remarks = $data['remarks'];
            $agentCommission->is_process = $data['is_process'];
            $agentCommission->updated_by = auth()->id();
            $agentCommission->save();

            DB::commit();

            return ['type' => 'success', 'message' => 'Commission paid successfully'];
        } catch (\Exception $e) {
            DB::rollBack();

            return ['type' => 'error', 'message' => 'Failed to process commission payment: '.$e->getMessage()];
        }
    }

    public function saveStudentScholarshipDetails(ScholarshipDTO $data)
    {
        DB::beginTransaction();
        try {
            $data->course_id = $this->studentCourse->find($data->student_course_id)->course_id;
            $data->created_by = $data->updated_by = Auth::user()->id;
            // $res = StudentScholarship::create($data->toArray());
            $res = $this->studentScholarship->create($data->toArray());
            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function updateStudentScholarshipDetails(ScholarshipDTO $data, $isFullyEditable = true)
    {
        $postData = $data->toArray();
        $scholarship = $this->studentScholarship->find($postData['scholarship_id']);
        /*if($postData['scholarship_amount'] < $scholarship->used_scholarship_amount){
            return ['type' => 'error', 'message' => 'Added scholarship amount already used'];
            //return $this->errorResponse('Added scholarship amount already used', 'data', []);
        }*/

        DB::beginTransaction();
        try {
            $scholarshipId = $postData['scholarship_id'];
            // $res = StudentScholarship::where('id', $scholarshipId)->update($postData);
            if (! $isFullyEditable) {
                $updateArr = ['comment' => $postData['comment']];
                $res = $this->studentScholarship->update($updateArr, $scholarshipId);
            } else {
                unset($postData['scholarship_id']);
                $res = $this->studentScholarship->update($postData, $scholarshipId);
            }
            DB::commit();

            return ['type' => 'success', 'message' => 'Student Scholarship Details Save Successfully', 'data' => $res];
        } catch (\Exception $e) {
            DB::rollBack();

            // throw new ApplicationException($e->getMessage());
            return ['type' => 'error', 'message' => $e->getMessage()];
        }
    }

    public function getPaymentTransactionDetails($data)
    {
        return $this->studentPaymentRepository->getPaymentTransactionDetails($data['detailId']);
    }

    public function isValidForDeletePaymentTransaction($transactionId)
    {
        $res = StudentAgentCommission::from('rto_student_agent_commission')
            ->leftjoin('rto_student_initial_payment_details', 'rto_student_initial_payment_details.invoice_number', '=', 'rto_student_agent_commission.invoice_no')
            ->leftjoin('rto_student_initial_payment_transaction', 'rto_student_initial_payment_transaction.initial_payment_detail_id', '=', 'rto_student_initial_payment_details.id')
            ->where('rto_student_initial_payment_transaction.id', $transactionId)
            ->with(['xeroInvoice'])
            ->first();

        if ($res) {
            if ($res->is_approved == StudentAgentCommission::STATUS_APPROVED) {
                return ['type' => 'error', 'message' => 'Can not delete! Agent Commission already Approved.'];
            } elseif ($res->commission_paid > 0) {
                return ['type' => 'error', 'message' => 'Can not delete! Already commission paid'];
            } elseif (($res->xeroInvoice != null && ! empty($res->xeroInvoice->xero_invoice_id))) {
                return ['type' => 'error', 'message' => 'Can not delete! Commission data already synced'];
            }
        }

        return ['type' => 'success'];
    }

    public function getPaymentTransactionData($data)
    {
        $collegeId = Auth::user()->college_id;
        $paymentTransactionId = $data['detailId'];
        $objPaymentTransactionData = $this->studentPaymentRepository->getPaymentTransactionData($paymentTransactionId);
        $receiptNo = $objPaymentTransactionData[0]->receipt_no;
        $studentId = $objPaymentTransactionData[0]->student_id;
        $courseId = $objPaymentTransactionData[0]->course_id;

        $objMiscellaneousPaymentData = $this->studentMiscellaneousPayment->getWhereRow(['college_id' => $collegeId, 'resceipt_number' => $receiptNo]);
        $objPaymentTransactionDiscription = $this->studentInitialPaymentTransaction->getWhereRow(['college_id' => $collegeId, 'student_id' => $studentId, 'course_id' => $courseId]);

        $arrDiscriptionData = [];
        $countVar = 0;
        for ($i = 0; $i < count($objMiscellaneousPaymentData); $i++) {
            $arrDiscriptionData[$countVar]['description'] = $objMiscellaneousPaymentData[$i]->payment_type;
            $arrDiscriptionData[$countVar]['amount'] = $objMiscellaneousPaymentData[$i]->amount;
            $arrDiscriptionData[$countVar]['amount_paid'] = 0;
            $countVar++;
        }

        for ($i = 0; $i < count($objPaymentTransactionDiscription); $i++) {
            $arrDiscriptionData[$countVar]['description'] = 'Tution Fee';
            $arrDiscriptionData[$countVar]['amount'] = $objPaymentTransactionDiscription[$i]->paid_amount;
            $arrDiscriptionData[$countVar]['amount_paid'] = 0;
        }

        return [
            'transactionData' => $objPaymentTransactionData,
            'gridData' => $arrDiscriptionData,
        ];
    }

    public function saveEditPaymentTransaction($data)
    {
        if (isset($data['commission_after_bonus'])) {
            // $paymentDetail = studentInitialPaymentDetails::find($data['initial_payment_detail_id']);
            $paymentDetail = $this->studentInitialPaymentDetails->find($data['initial_payment_detail_id']);
            $commissionVal = isset($paymentDetail->commission_value) ? $paymentDetail->commission_value : 0;
            $amountVal = $data['transection_amount'] - $data['agent_bonus'];
            $amountVal = ($amountVal > 0) ? $amountVal : 0;
            $agentCommission['commission_payable'] = number_format(($amountVal * ($commissionVal / 100)), 2);
        }
        DB::beginTransaction();
        try {
            unset($data['upfront_fee_to_pay']);
            unset($data['transection_amount']);
            $transaction_id = $data['transaction_id'];
            unset($data['transaction_id']);

            // $saveTransaction = StudentInitialPaymentTransaction::where('id', $transaction_id)->update($data);
            $saveTransaction = $this->studentInitialPaymentTransaction->update($data, $transaction_id);
            $agentCommission = ['bonus_amount' => $data['agent_bonus'], 'bonus_gst' => $data['bonus_gst'], 'created_by' => Auth::user()->id, 'updated_by' => Auth::user()->id];
            // $objAgentCommission = studentAgentCommission::where(['transaction_no' => $data['transection_no']])->update($agentCommission);
            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);

            throw new ApplicationException($e->getMessage());
        }
    }

    public function savePaymentRefundDetails($data)
    {
        if (
            $data['agent_refunded'] != ($data['gst_refund_amount'] + $data['agent_commission_refunds']) ||
            $data['administration_cost'] > $data['student_refund_amount'] ||
            $data['student_net_received'] > $data['student_refund_amount']) {
            $returnData['type'] = 'session_error';
            $returnData['message'] = 'Please Enter Valid Amount';

            return $returnData;
        }
        $paymentTransactionId = $data['transaction_id'];
        $updatePaymentTransactionData = [
            'amount_refund' => $data['student_refund_amount'],
            'student_administration_cost' => $data['administration_cost'],
            'agent_commission_refund_amount' => $data['agent_commission_refunds'],
            'agent_refunded' => $data['agent_refunded'],
            'refund_remarks' => $data['remarks'],
            'student_net_receive' => $data['student_net_received'],
            'student_refund_mode' => $data['student_refund_mode'],
            'agent_refund_mode' => $data['agent_refund_mode'],
            'student_refund_date' => $data['student_refund_date'],
            'agent_refund_date' => $data['agent_refund_date'],
            'updated_by' => Auth::user()->id,
        ];

        $getPaymentTransaction = $this->studentInitialPaymentTransaction->find($paymentTransactionId);
        $paymentDetailId = $getPaymentTransaction->initial_payment_detail_id;
        $getPaymentDetail = $this->studentInitialPaymentDetails->find($paymentDetailId);

        $tempUpfrontFeePay = $getPaymentDetail->upfront_fee_pay + $getPaymentTransaction->amount_refund - $data['student_refund_amount'];
        if ($tempUpfrontFeePay >= $getPaymentDetail->upfront_fee_to_pay) {
            $paymentStatus = 'paid';
        } elseif ($tempUpfrontFeePay > 0) {
            $paymentStatus = 'partially paid';
        } else {
            $paymentStatus = 'unpaid';
        }
        $updatePaymentDetailsData = [
            'upfront_fee_pay' => $tempUpfrontFeePay,
            'payment_status' => $paymentStatus,
            'updated_by' => Auth::user()->id,
        ];

        // $agentCommissionDetail = $this->studentAgentCommission->getWhereRow(['college_id' => $data['college_id'], 'transaction_no' => $getPaymentTransaction->transection_no]);
        $agentCommissionDetail = $this->studentAgentCommission->getWhereRow(['college_id' => $data['college_id'], 'invoice_no' => $getPaymentDetail->invoice_number]);

        DB::beginTransaction();
        try {
            $updatePaymentTransaction = $this->studentInitialPaymentTransaction->update($updatePaymentTransactionData, $paymentTransactionId);
            $deductRefundAmount = $this->studentInitialPaymentDetails->update($updatePaymentDetailsData, $paymentDetailId);
            if ($agentCommissionDetail->toArray()) {
                $agentCommissionId = $agentCommissionDetail[0]->id;
                $updateAgentCommissionData = [
                    'is_approved' => 0,
                    'is_process' => 0,
                    'comm_to_refund' => number_format($data['agent_commission_refunds'], 2),
                    'GST_to_refund' => number_format($data['gst_refund_amount'], 2),
                    'refund_amount' => number_format($data['agent_refunded'], 2),
                ];
                $updateAgentCommission = $this->studentAgentCommission->update($updateAgentCommissionData, $agentCommissionId);
            }
            DB::commit();
            $returnData['type'] = 'session_success';
            $returnData['message'] = 'Payment Refunded Successfully';

            return $returnData;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function savePaymentTransactionReverse($data)
    {
        $paymentTransactionId = $data['transaction_id'];
        $updatePaymentTransactionData = [
            'reversed' => 1,
            'reverse_comment' => ($data['reverse_comment'] != '') ? $data['reverse_comment'] : null,
        ];

        $getPaymentTransaction = $this->studentInitialPaymentTransaction->find($paymentTransactionId);
        $paymentDetailId = $getPaymentTransaction->initial_payment_detail_id;
        $getPaymentDetail = $this->studentInitialPaymentDetails->find($paymentDetailId);

        $existPaidAmount = $getPaymentDetail->upfront_fee_pay - $getPaymentTransaction->paid_amount;
        $updatePaymentDetailsData = [
            'upfront_fee_pay' => $existPaidAmount,
            'payment_status' => ($existPaidAmount > 0) ? 'partially paid' : 'unpaid',
        ];

        /*$agentCommissionDetail = $this->studentAgentCommission->getWhereRow(['college_id' => $data['college_id'], 'transaction_no' => $getPaymentTransaction->transection_no]);
        $agentCommissionId = $agentCommissionDetail[0]->id;
        $updateAgentCommissionData = [
            'is_reversed' => 1,
            'comm_to_refund' => $agentCommissionDetail[0]->commission_payable,
            'GST_to_refund' => $agentCommissionDetail[0]->gst_amount
        ];*/

        DB::beginTransaction();
        try {
            $reverseTransaction = $this->studentInitialPaymentTransaction->update($updatePaymentTransactionData, $paymentTransactionId);
            $reversePaymentDetail = $this->studentInitialPaymentDetails->update($updatePaymentDetailsData, $paymentDetailId);
            $revertCreditAmount = $this->studentPaymentRepository->revertCreditAmount($data['college_id'], $data['student_id'], $getPaymentTransaction->student_credit);
            $deleteScholarshipAmount = $this->studentPaymentRepository->deletePaidScholarshipAmount($data['college_id'], $data['student_id'], $getPaymentDetail->course_id, $getPaymentTransaction->scholarship);
            // $objAgentCommission = $this->studentAgentCommission->WhereUpdate($updateAgentCommissionData, ['college_id' => $data['college_id'], 'transaction_no' => $getPaymentTransaction->transection_no]);

            $agentCommissionDetail = $this->studentAgentCommission->getWhereRow(['college_id' => $data['college_id'], 'invoice_no' => $getPaymentDetail->invoice_number])->first();
            if (isset($agentCommissionDetail)) {
                $agentCommissionId = $agentCommissionDetail[0]->id;
                $updateAgentCommissionData = [
                    'is_reversed' => 1,
                    'comm_to_refund' => $agentCommissionDetail[0]->commission_payable,
                    'GST_to_refund' => $agentCommissionDetail[0]->gst_amount,
                ];
                $objAgentCommission = $this->studentAgentCommission->WhereUpdate($updateAgentCommissionData, ['id' => $agentCommissionId]);
            }

            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deletePaymentRefundHistory($data)
    {
        $paymentTransactionId = $data['id'];
        $updatePaymentTransactionData = [
            'amount_refund' => 0,
            'student_administration_cost' => 0,
            'student_refund_mode' => null,
            'student_refund_date' => null,
            'agent_commission_refund_amount' => 0,
            'agent_refunded' => 0,
            'agent_refund_mode' => null,
            'agent_refund_date' => null,
            'refund_remarks' => null,
            'student_net_receive' => 0,
            'updated_by' => Auth::user()->id,
        ];
        $getPaymentTransaction = $this->studentInitialPaymentTransaction->find($paymentTransactionId);
        $paymentDetailId = $getPaymentTransaction->initial_payment_detail_id;
        $getPaymentDetail = $this->studentInitialPaymentDetails->find($paymentDetailId);
        $updatePaymentDetailsData['upfront_fee_pay'] = $getPaymentDetail->upfront_fee_pay + $getPaymentTransaction->amount_refund;
        if ($updatePaymentDetailsData['upfront_fee_pay'] >= $getPaymentDetail->upfront_fee_to_pay) {
            $updatePaymentDetailsData['payment_status'] = 'paid';
        } elseif ($updatePaymentDetailsData['upfront_fee_pay'] > 0) {
            $updatePaymentDetailsData['payment_status'] = 'partially paid';
        }
        $updatePaymentDetailsData['updated_by'] = Auth::user()->id;

        // $agentCommissionDetail = $this->studentAgentCommission->getWhereRow(['college_id' => Auth::user()->college_id, 'transaction_no' => $getPaymentTransaction->transection_no]);
        $agentCommissionDetail = $this->studentAgentCommission->getWhereRow(['college_id' => Auth::user()->college_id, 'invoice_no' => $getPaymentDetail->invoice_number]);
        $agentCommissionId = $agentCommissionDetail[0]->id;
        $updateAgentCommissionData = [
            'comm_to_refund' => 0,
            'GST_to_refund' => 0,
            'refund_amount' => 0,
        ];

        DB::beginTransaction();
        try {
            $deleteRefundInfoData = $this->studentInitialPaymentTransaction->update($updatePaymentTransactionData, $paymentTransactionId);
            $reversePaymentDetail = $this->studentInitialPaymentDetails->update($updatePaymentDetailsData, $paymentDetailId);
            $objAgentCommission = $this->studentAgentCommission->update($updateAgentCommissionData, $agentCommissionId);
            DB::commit();

            return $objAgentCommission;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function saveInvoiceCreditDetails($data)
    {
        if ($data['credit_amount'] <= 0) {
            return ['type' => 'session_error', 'message' => 'Credit amount must be greater than 0'];
        }
        if ($data['credit_amount'] > $data['hidden_credit_amount']) {
            return ['type' => 'session_error', 'message' => 'Credit Amount should be smaller than Schedule Amount'];
        }

        $data['course_id'] = $this->studentCourse->find($data['student_course_id'])->course_id;
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;

        $resData = StudentInitialPaymentDetails::find($data['initial_payment_detail_id']);
        if (! $resData) {
            return ['type' => 'session_error', 'message' => 'Record not found'];
        }
        $totalCredit = $resData->invoice_credit + $data['credit_amount'];

        DB::beginTransaction();
        try {
            $this->studentInvoiceCredit->create($data);

            if ($resData->upfront_fee_pay + $totalCredit >= $resData->upfront_fee_to_pay) {
                $resData->payment_status = 'paid';
            } elseif ($resData->upfront_fee_pay > 0) {
                $resData->payment_status = 'partially paid';
            } else {
                $resData->payment_status = 'unpaid';
            }
            $resData->invoice_credit = $totalCredit;
            $resData->save();

            $returnData['type'] = 'session_success';
            $returnData['message'] = 'Invoice Credit Save Successfully';
            DB::commit();

            return $returnData;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteStudentScholarshipData($request)
    {
        $getScholarshipInfo = StudentScholarship::where('id', $request['id'])->with(['xeroCreditNote'])->first();

        if ($this->checkValidStatusForDelete($getScholarshipInfo)) {
            if ($getScholarshipInfo['used_scholarship_amount'] != 0) {
                return ['type' => 'error', 'message' => 'Used scholarship can not be deleted'];
            }

            DB::beginTransaction();
            try {
                $getScholarshipInfo->delete();
                DB::commit();

                return ['type' => 'success', 'message' => 'student scholarship delete successfully'];
            } catch (\Exception $e) {
                DB::rollBack();

                return ['type' => 'error', 'message' => $e->getMessage()];
                // throw new ApplicationException($e->getMessage());
            }
        } else {
            return ['type' => 'error', 'message' => 'scholarship already synced with xero'];
        }

    }

    public function deleteBulkScholarshipPayment($request)
    {
        $successCount = $failCount = 0;
        foreach ($request->ids as $scholarshipId) {
            // $scholarship = $this->studentScholarship->find($scholarshipId);
            $scholarship = StudentScholarship::where('id', $scholarshipId)->with(['xeroCreditNote'])->first();
            if ($this->checkValidStatusForDelete($scholarship)) {
                if ($scholarship->used_scholarship_amount == 0 && $scholarship->is_transfer == 0) {
                    DB::beginTransaction();
                    try {
                        $scholarship->delete();
                        DB::commit();
                        $successCount++;
                    } catch (\Exception $e) {
                        DB::rollBack();
                        $failCount++;
                        // throw new ApplicationException($e->getMessage());
                    }
                } else {
                    $failCount++;
                    // echo "Used scholarship can not be deleted ($scholarship->scholarship_name)";
                }
            } else {
                $failCount++;
                // echo "Cannot delete Scholarship: $scholarship->scholarship_name (Status: $scholarship->xeroCreditNote->xero_invoice_status) <br/>";
            }
        }

        return [
            'success_count' => $successCount,
            'success_msg' => ($failCount > 0) ? "Total $successCount scholarship delete successfully" : 'Selected Scholarship Delete Successfully',
            'error_count' => $failCount,
            'error_msg' => ($failCount > 0) ? (($successCount > 0) ? "Total $failCount scholarship failed to delete" : 'Selected scholarship failed to delete') : '',
        ];
    }

    /*
    TODO: check the payment status before delete, if already paid should not allow to delete.
    TODO: check if  all related transactions and commissions for this invoice are removed correctly
    TODO: remove xero residual data as well
    */
    public function deletePaymentScheduleForOldVersion($dataArr, $initialPaymentsDataArr)
    {
        $collegeId = $dataArr['college_id'];
        $studentId = $dataArr['student_id'];
        $courseId = $dataArr['course_id'];
        $whereArr = ['college_id' => $collegeId, 'student_id' => $studentId, 'course_id' => $courseId];
        $countPaymentDetail = StudentInitialPaymentDetails::where($whereArr)->count();

        DB::beginTransaction();
        try {
            foreach ($initialPaymentsDataArr as $initialPaymentDetails) {
                $paymentDetailId = $initialPaymentDetails->id;
                if ($initialPaymentDetails->payment_type == 'Initial') {
                    continue;
                }
                /*if($initialPaymentDetails->payment_type == 'Initial'){
                    $getTransactionDataArr = $this->studentInitialPaymentTransaction->getWhere(['initial_payment_detail_id' => $paymentDetailId], 'id');
                    foreach ($getTransactionDataArr as $transactionData){
                        $reqData = [
                            'college_id'        => $collegeId,
                            'student_id'        => $studentId,
                            'transaction_id'    => $transactionData['id'],
                            'reason_to_delete'  => 'Due to reschedule'
                        ];
                        $this->deletePaymentTransaction($reqData);
                    }
                }*/

                $deleteTransaction = $this->studentPaymentRepository->deletePaymentDetailsByTransaction($collegeId, $paymentDetailId);
                if (is_array($deleteTransaction)) {
                    // $arrTransactionNo = $deleteTransaction['arrTransactionNo'];
                    $arrInvoiceNo = $deleteTransaction['arrInvoiceNo'];
                    $scholarshipAmount = $deleteTransaction['scholarshipAmount'];
                    $creditAmount = $deleteTransaction['creditAmount'];
                    /*foreach ($arrTransactionNo as $rowTransactionNo) {
                        StudentAgentCommission::where(['college_id' => $collegeId, 'transaction_no' => $rowTransactionNo])->delete();
                    }*/
                    foreach ($arrInvoiceNo as $invoiceNo) {
                        StudentAgentCommission::where(['college_id' => $collegeId, 'invoice_no' => $invoiceNo])->delete();
                    }

                    $this->studentPaymentRepository->deletePaidScholarshipAmount($collegeId, $studentId, $courseId, $scholarshipAmount);
                    $this->studentPaymentRepository->revertCreditAmount($collegeId, $studentId, $creditAmount);

                    if ($countPaymentDetail == 0) {
                        $deletePaidAmount = StudentInitialPayment::where($whereArr)->forceDelete();
                        // $deletePaidAmount = $this->studentInitialPayment->whereDelete($whereArr);
                        $deleteMiscellaneousTransaction = StudentMiscellaneousPayment::where($whereArr)->delete();
                        $arrStudentServicePayment = StudentServicePayment::where($whereArr)->delete();
                    }
                }
                // $deletePaidAmount = $this->studentInitialPaymentDetails->delete($paymentDetailId);
                StudentInitialPaymentDetails::where('id', $paymentDetailId)->forceDelete();
            }
            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    /*public function deletePaymentSchedule($request)
    {
        $paymentDetailId = $request['id'];
        $collegeId = Auth::user()->college_id;
        $initialPaymentDetails = $this->studentInitialPaymentDetails->find($paymentDetailId);

        if(!$initialPaymentDetails->canDelete()){
            throw new ApplicationException("Cannot delete this invoice. It is already paid.");
        }
        $studentId = $initialPaymentDetails->student_id;
        $courseId = $initialPaymentDetails->course_id;
        $countPaymentDetail = StudentInitialPaymentDetails::where(['college_id' => $collegeId, 'student_id' => $studentId, 'course_id' => $courseId])->count();
        DB::beginTransaction();
        try {
            // fix for undefined $deletePaidAmount when count payment is 0.
            $deletePaidAmount = 0;
            $deleteTransaction = $this->studentPaymentRepository->deletePaymentDetailsByTransaction($collegeId, $paymentDetailId);
            if (is_array($deleteTransaction)) {
                $arrTransactionNo = $deleteTransaction['arrTransactionNo'];
                $scholarshipAmount = $deleteTransaction['scholarshipAmount'];
                foreach ($arrTransactionNo as $rowTransactionNo) {
                    $deleteAgentCommission = StudentAgentCommission::where(['college_id' => $collegeId, 'transaction_no' => $rowTransactionNo])->delete();
                }
                $deleteScholarshipAmount = $this->studentPaymentRepository->deletePaidScholarshipAmount($collegeId, $studentId, $courseId, $scholarshipAmount);
                $arrStudentServicePayment = StudentServicePayment::where(['college_id' => $collegeId, 'student_id' => $studentId, 'course_id' => $courseId])->delete();

                if ($countPaymentDetail == 0) {
                    //$deletePaidAmount = StudentInitialPayment::where(['college_id' => $collegeId, 'student_id' => $studentId, 'course_id' => $courseId])->delete();
                    $deletePaidAmount = $this->studentInitialPayment->whereDelete([
                        'college_id' => $collegeId,
                        'student_id' => $studentId,
                        'course_id' => $courseId
                    ]);
                    $deleteMiscellaneousTransaction = StudentMiscellaneousPayment::where(['college_id' => $collegeId, 'student_id' => $studentId, 'course_id' => $courseId])->delete();
                }
            }
            // event(new DeleteingXeroInvoiceableModel($initialPaymentDetails));
            //$deletePaidAmount = StudentInitialPaymentDetails::destroy($paymentDetailId);
            $initialPaymentDetails->delete();
            // $deletePaidAmount = $this->studentInitialPaymentDetails->delete($paymentDetailId);
            DB::commit();
            return $deletePaidAmount;
        } catch (\Exception $e) {
            DB::rollBack();
            // safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }*/

    public function deletePaymentDetails($collegeId, $initialPaymentDetails)
    {
        $paymentDetailId = $initialPaymentDetails->id;
        $studentId = $initialPaymentDetails->student_id;
        $courseId = $initialPaymentDetails->course_id;
        $whereArr = ['college_id' => $collegeId, 'student_id' => $studentId, 'course_id' => $courseId];

        $deletePaidAmount = 0;
        $deleteTransaction = $this->studentPaymentRepository->deletePaymentDetailsByTransaction($collegeId, $paymentDetailId);
        if (is_array($deleteTransaction)) {
            // $arrTransactionNo = $deleteTransaction['arrTransactionNo'];
            $arrInvoiceNo = $deleteTransaction['arrInvoiceNo'];
            $scholarshipAmount = $deleteTransaction['scholarshipAmount'];
            $creditAmount = $deleteTransaction['creditAmount'];
            /*foreach ($arrTransactionNo as $rowTransactionNo) {
                $deleteAgentCommission = StudentAgentCommission::where(['college_id' => $collegeId, 'transaction_no' => $rowTransactionNo])->delete();
            }*/
            foreach ($arrInvoiceNo as $invoiceNo) {
                $deleteAgentCommission = StudentAgentCommission::where(['college_id' => $collegeId, 'invoice_no' => $invoiceNo])->delete();
            }
            $revertCreditAmount = $this->studentPaymentRepository->revertCreditAmount($collegeId, $studentId, $creditAmount);
            $deleteScholarshipAmount = $this->studentPaymentRepository->deletePaidScholarshipAmount($collegeId, $studentId, $courseId, $scholarshipAmount);
            $arrStudentServicePayment = StudentServicePayment::where($whereArr)->delete();
        }
        // event(new DeleteingXeroInvoiceableModel($initialPaymentDetails));
        // $initialPaymentDetails->forceDelete();
        StudentInvoiceCredit::where('initial_payment_detail_id', $paymentDetailId)->delete();
        StudentInitialPaymentDetails::where('id', $paymentDetailId)->forceDelete();

        if (StudentInitialPaymentDetails::withTrashed()->where($whereArr)->count() == 0) {
            StudentInitialPayment::withTrashed()->where($whereArr)->forceDelete();
            // StudentMiscellaneousPayment::withTrashed()->where($whereArr)->delete();
        }

        return $deletePaidAmount;
    }

    public function deletePaymentSchedule($request)
    {
        $paymentDetailId = $request['id'];
        $collegeId = Auth::user()->college_id;
        // $initialPaymentDetails = $this->studentInitialPaymentDetails->findWithTrashed($paymentDetailId);
        $initialPaymentDetails = StudentInitialPaymentDetails::withTrashed()->where('id', $paymentDetailId)->with(['xeroInvoice'])->first();
        if (! $initialPaymentDetails->canDelete()) {
            throw new ApplicationException('Cannot delete this invoice. It is already paid.');
        }
        if (! $this->checkValidStatusForDelete($initialPaymentDetails)) {
            throw new ApplicationException('Cannot delete this invoice. It is already paid/approved.');
        }

        DB::beginTransaction();
        try {
            $result = $this->deletePaymentDetails($collegeId, $initialPaymentDetails);
            DB::commit();

            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteBulkPaymentSchedule($request)
    {
        // TODO::GNG-2777
        $collegeId = Auth::user()->college_id;
        $successCount = $failCount = 0;
        $isAllowForceDeleteInv = $request->isAllowForceDeleteInv ? true : false;

        // $isXeroConnect = (Xero::isConnected()) ? true : false;
        foreach ($request->ids as $paymentDetailId) {
            // $initialPaymentDetails = $this->studentInitialPaymentDetails->findWithTrashed($paymentDetailId);
            $initialPaymentDetails = StudentInitialPaymentDetails::withTrashed()->where('id', $paymentDetailId)->with(['xeroInvoice'])->first();
            if ($initialPaymentDetails && ($isAllowForceDeleteInv || $initialPaymentDetails->canDelete())) {
                // if($isAllowForceDeleteInv || $this->checkValidStatusForDelete($initialPaymentDetails)){
                if ($this->checkValidStatusForDelete($initialPaymentDetails, $isAllowForceDeleteInv)) {

                    DB::beginTransaction();
                    try {
                        $this->deletePaymentDetails($collegeId, $initialPaymentDetails);
                        DB::commit();
                        $successCount++;
                    } catch (\Exception $e) {
                        DB::rollBack();
                        $failCount++;
                        // throw new ApplicationException($e->getMessage());
                    }
                } else {
                    $failCount++;
                    // echo "Cannot delete invoice: $initialPaymentDetails->invoice_number (Status: $initialPaymentDetails->xeroInvoice->xero_invoice_status) <br/>";
                }
            } else {
                $failCount++;
                // echo "Cannot delete invoice: ". $initialPaymentDetails->invoice_number ." It is already paid. <br/>";
            }
        }

        return [
            'success_count' => $successCount,
            'success_msg' => ($failCount > 0) ? "Total $successCount invoice delete successfully" : 'Selected Items Delete Successfully',
            'error_count' => $failCount,
            'error_msg' => ($failCount > 0) ? (($successCount > 0) ? "Total $failCount invoice failed to delete" : ' System does not allow deletion of paid/approved invoices') : '',
        ];
    }

    public function deletePaymentTransaction($request)
    {
        $paymentTransactionId = $request['transaction_id'];
        DB::beginTransaction();
        try {
            $updatePaymentTransactionData = $this->updatedTransactionAfterDelete($request);
            // $updatePaymentTransaction = StudentInitialPaymentTransaction::where('id', $paymentTransactionId)->update($updatePaymentTransactionData);
            $updatePaymentTransaction = $this->studentInitialPaymentTransaction->update($updatePaymentTransactionData, $paymentTransactionId);
            // $getPaymentTransaction = StudentInitialPaymentTransaction::find($paymentTransactionId);
            $getPaymentTransaction = $this->studentInitialPaymentTransaction->find($paymentTransactionId);
            $paymentDetailId = $getPaymentTransaction->initial_payment_detail_id;
            // $getPaymentDetails = StudentInitialPaymentDetails::find($paymentDetailId);
            $getPaymentDetails = $this->studentInitialPaymentDetails->find($paymentDetailId);

            $updatePaymentDetailsData = $this->updatedPaymentDetailsAfterDelete($getPaymentDetails, $getPaymentTransaction);
            // $updatePaymentTransaction = StudentInitialPaymentDetails::where('id', $paymentDetailId)->update($updatePaymentDetailsData);
            $updatePaymentTransaction = $this->studentInitialPaymentDetails->update($updatePaymentDetailsData, $paymentDetailId);
            // $deleteAgentCommission = StudentAgentCommission::where(['college_id' => $request['college_id'], 'transaction_no' => $getPaymentTransaction->transection_no])->delete();
            $deleteAgentCommission = StudentAgentCommission::where(['college_id' => $request['college_id'], 'invoice_no' => $getPaymentDetails->invoice_number])->delete();
            $updateMiscellaneousPaymentData = [
                'payment_status' => 'unpaid',
                'paid_amount' => 0,
                'resceipt_number' => null,
                'transaction_number' => null,
                'agent_bonus' => 0,
                'bad_debt' => 0,
                'paid_on' => null,
            ];
            $deleteMiscellaneousTransaction = StudentMiscellaneousPayment::where(['college_id' => $request['college_id'], 'transaction_number' => $getPaymentTransaction->transection_no])->update($updateMiscellaneousPaymentData);
            $deleteScholarShip = $this->studentPaymentRepository->deletePaidScholarshipAmount($request['college_id'], $request['student_id'], $getPaymentTransaction->course_id, $getPaymentTransaction->scholarship);

            // TODO::If you want to delete transaction then verify that they already over paid amount or not. If over paid then over amount added on credit section. So revert their credit also.
            if ($getPaymentTransaction->student_credit > 0) {
                StudentInitialPayment::where([
                    'college_id' => $request['college_id'],
                    'student_id' => $request['student_id'],
                ])->increment('student_credit', $getPaymentTransaction->student_credit);
            }

            if ($getPaymentTransaction->deposited_amount > $getPaymentTransaction->paid_amount) {
                $revertCreditAmount = $getPaymentTransaction->deposited_amount - $getPaymentTransaction->paid_amount;
                if ($revertCreditAmount > 0) {
                    $existCreditAmount = $this->studentInitialPayment->getWhereRow(['student_id' => $getPaymentTransaction->student_id])->first()->student_credit;
                    if ($existCreditAmount > 0) {
                        $availableCreditAmount = $existCreditAmount - $revertCreditAmount;
                        $this->studentInitialPayment->whereUpdate(['student_credit' => $availableCreditAmount], ['student_id' => $getPaymentTransaction->student_id]);
                    }
                }
            }

            DB::commit();

            return $deleteScholarShip;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function updatedTransactionAfterDelete($request)
    {
        // $transactionData['scholarship'] = 0;
        $transactionData['amount_refund'] = 0;
        $transactionData['agent_bonus'] = $transactionData['bad_debt'] = 0;
        $transactionData['student_administration_cost'] = $transactionData['agent_refunded'] = 0;
        $transactionData['agent_commission_refund_amount'] = $transactionData['student_net_receive'] = 0;
        $transactionData['bonus_gst'] = $transactionData['bonus_paid_date'] = $transactionData['refund_remarks'] = null;
        $transactionData['student_refund_mode'] = $transactionData['student_refund_date'] = null;
        $transactionData['agent_refund_mode'] = $transactionData['agent_refund_date'] = null;
        $transactionData['is_delete'] = 1;
        $transactionData['reason_to_delete'] = $request['reason_to_delete'];
        $transactionData['updated_by'] = Auth::user()->id;

        return $transactionData;
    }

    public function updatedPaymentDetailsAfterDelete($getPaymentDetails, $getPaymentTransaction)
    {
        $paymentDetailData['upfront_fee_pay'] = $getPaymentDetails->upfront_fee_pay - ($getPaymentTransaction->paid_amount - $getPaymentTransaction->amount_refund);
        $paymentDetailData['agent_bonus'] = $getPaymentDetails->agent_bonus - $getPaymentTransaction->agent_bonus;
        // $paymentDetailData['bonus_gst'] = $getPaymentDetails->bonus_gst - $getPaymentTransaction->bad_debt;
        // $paymentDetailData['payment_status'] = "unpaid";
        $paymentDetailData['payment_status'] = ($paymentDetailData['upfront_fee_pay'] > 0) ? 'partially paid' : 'unpaid';
        $paymentDetailData['updated_by'] = Auth::user()->id;

        return $paymentDetailData;
    }

    public function addStudentServicesDetails($request)
    {
        $request = $request->input();
        $request['course_id'] = $this->studentCourse->find($request['student_course_id'])->course_id;
        $request['collegeId'] = Auth::user()->college_id;
        $request['created_by'] = $request['updated_by'] = Auth::user()->id;
        $request['student_price'] = $request['amount'];
        $request['comment'] = ! empty($request['remarks']) ? $request['remarks'] : null;
        $request['offer_id'] = StudentCourses::where('id', $request['student_course_id'])->value('offer_id');
        DB::beginTransaction();
        try {
            if (! empty($request['service_id'])) {
                // Update Record
                $additional_services_id = $request['additional_services_id'];
                $studentAdditionalServiceRequest = $this->setStudentAdditionalServiceRequest($request);
                // StudentAdditionalServiceRequest::where('id', $additional_services_id)->update($studentAdditionalServiceRequest);
                $studAdditionalService = StudentAdditionalServiceRequest::find($additional_services_id);
                $studAdditionalService->update($studentAdditionalServiceRequest);

                $requested_service_id = $request['service_id'];
                $updateStudentAdditionalServicePaymentRequestData = $this->updateStudentAdditionalServicePaymentData($request);
                // StudentServicePayment::where('id', $requested_service_id)->update($updateStudentAdditionalServicePaymentRequestData);
                $studServicePayment = StudentServicePayment::find($requested_service_id);
                $studServicePayment->update($updateStudentAdditionalServicePaymentRequestData);

                $res = ['status' => 'success', 'message' => 'Record Update Successfully'];
            } else {
                $request['additional_services_id'] = StudentAdditionalServiceRequest::create($request)->id;
                if (! empty($request['additional_services_id'])) {
                    // $this->saveInvoiceNumber();
                    $request = $this->saveStudentAdditionalServicePaymentData($request);
                    $insertData = StudentServicePayment::create($request);
                    if ($insertData) {
                        $this->studentPaymentRepository->updateInvoiceNumber($insertData->invoice_number);
                        $this->studentPaymentRepository->editTransactionNumber();

                        if ($insertData->payment_status == 'paid') {
                            $this->studentPaymentRepository->editResceiptNumber();
                        }
                    }
                    $res = ['status' => 'success', 'message' => 'Record saved successfully'];
                } else {
                    $res = ['status' => 'error', 'message' => 'Fail to save'];
                }
            }

            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            // safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function saveRecordServicePayment($request)
    {
        // $request = $request->input();
        DB::beginTransaction();
        try {

            $id = $request->id;
            // StudentServicePayment::where('id', $id)->update($request);
            $studentServicePayment = StudentServicePayment::find($id);
            $studentServicePayment->fill($request->all());
            $studentServicePayment->save();
            $res = ['status' => 'success', 'message' => 'Record Update Successfully'];

            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            // safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function setStudentAdditionalServiceRequest($request)
    {
        $newRequest['college_id'] = $request['college_id'];
        $newRequest['student_id'] = $request['student_id'];
        $newRequest['course_id'] = $request['course_id'];
        $newRequest['offer_id'] = $request['offer_id'];
        $newRequest['service_name_id'] = $request['service_name_id'];
        $newRequest['category_id'] = $request['category_id'];
        $newRequest['facility_id'] = $request['facility_id'];
        $newRequest['student_price'] = $request['student_price'];
        $newRequest['service_start_date'] = $request['service_start_date'];
        $newRequest['service_end_date'] = $request['service_end_date'];
        $newRequest['comment'] = $request['comment'];

        return $newRequest;
    }

    public function updateStudentAdditionalServicePaymentData($request)
    {
        $collegeId = $request['college_id'];
        $invoice_number = $request['invoice_number'];
        // $invoice_number =   invoiceNumber::where(['college_id' => $collegeId])->value('invoice_number');
        $resceiptNumber = resceiptNumber::where(['college_id' => $collegeId])->value('resceipt_number');
        $request['deduct_agent_rebate'] = isset($request['deduct_agent_rebate']) ? 'on' : 'off';
        $request['is_deduct_agent'] = ($request['deduct_agent_rebate'] == 'on') ? 1 : 0;
        $request['resceipt_number'] = ($request['payment_status'] == 'unpaid') ? null : ((empty($request['receipt_no'])) ? $request['receipt_no'] : $resceiptNumber);
        $request['paid_date'] = ($request['payment_status'] == 'unpaid' || empty($request['payment_date'])) ? null : date('Y-m-d', strtotime($request['payment_date']));
        $request['pay_date'] = ($request['payment_status'] == 'unpaid' || empty($request['payment_date'])) ? null : date('Y-m-d', strtotime($request['payment_date']));
        $request['payment_mode'] = ($request['payment_status'] == 'unpaid') ? null : $request['payment_mode'];
        $request['payment_status'] = ($request['payment_status'] == 'unpaid') ? 'unpaid' : 'paid';
        $request['paid_amount'] = ($request['payment_status'] == 'unpaid') ? '0' : $request['amount'];
        $request['bad_debt'] = ($request['payment_status'] == 'unpaid' || empty($request['bad_debt'])) ? null : $request['bad_debt'];
        $request['bank_deposite_date'] = ($request['payment_status'] == 'unpaid' || empty($request['bank_deposit_date'])) ? null : date('Y-m-d', strtotime($request['bank_deposit_date']));
        $request['payment_type'] = ($request['payment_status'] == 'unpaid' || empty($request['payment_type'])) ? null : $request['payment_type'];
        $request['invoice_number'] = $request['invoice_number'] ?? $invoice_number;
        $request['due_date'] = (! empty($request['due_date'])) ? date('Y-m-d', strtotime($request['due_date'])) : null;
        $request['is_gst'] = (! empty($request['gst_amount'])) ? '1' : '0';
        $request['invoice_sent_date'] = (empty($request['invoice_sent_date'])) ? null : date('Y-m-d', strtotime($request['invoice_sent_date']));
        $request['rebate_agent_date'] = (empty($request['rebate_agent_date']) || $request['is_deduct_agent'] == 0) ? null : date('Y-m-d', strtotime($request['rebate_agent_date']));
        $request['rebate_amount'] = (empty($request['rebate_amount']) || $request['is_deduct_agent'] == 0) ? null : $request['rebate_amount'];
        $request['remarks'] = $request['comment'];
        unset($request['service_id']);
        unset($request['service_name_id']);
        unset($request['category_id']);
        unset($request['facility_id']);
        unset($request['service_provider_id']);
        unset($request['service_start_date']);
        unset($request['service_end_date']);
        unset($request['receipt_no']);
        unset($request['payment_date']);
        unset($request['bank_deposit_date']);
        unset($request['collegeId']);
        unset($request['student_price']);
        unset($request['comment']);
        unset($request['offer_id']);
        unset($request['GST']);
        unset($request['deduct_agent_rebate']);

        return $request;
    }

    public function saveStudentAdditionalServicePaymentData($request)
    {
        $collegeId = $request['college_id'];
        $invoice_number = invoiceNumber::where(['college_id' => $collegeId])->value('invoice_number');
        $resceiptNumber = resceiptNumber::where(['college_id' => $collegeId])->value('resceipt_number');
        $isUnpaid = ($request['payment_status'] == 'unpaid') ? true : false;

        $request['deduct_agent_rebate'] = isset($request['deduct_agent_rebate']) ? 'on' : 'off';
        $request['is_deduct_agent'] = ($request['deduct_agent_rebate'] == 'on') ? 1 : 0;
        $request['resceipt_number'] = ($isUnpaid) ? null : $resceiptNumber;
        $request['paid_date'] = ($isUnpaid || empty($request['payment_date'])) ? null : date('Y-m-d', strtotime($request['payment_date']));
        $request['pay_date'] = ($isUnpaid || empty($request['payment_date'])) ? null : date('Y-m-d', strtotime($request['payment_date']));
        $request['payment_mode'] = ($isUnpaid) ? null : $request['payment_mode'];
        $request['payment_status'] = ($isUnpaid) ? 'unpaid' : 'paid';
        $request['paid_amount'] = ($isUnpaid) ? '0' : $request['amount'];
        $request['bad_debt'] = ($isUnpaid || empty($request['bad_debt'])) ? null : $request['bad_debt'];
        $request['bank_deposite_date'] = ($isUnpaid || empty($request['bank_deposit_date'])) ? null : date('Y-m-d', strtotime($request['bank_deposit_date']));
        $request['payment_type'] = ($isUnpaid || empty($request['payment_type'])) ? null : $request['payment_type'];
        $request['invoice_number'] = $invoice_number;
        $request['due_date'] = (! empty($request['due_date'])) ? date('Y-m-d', strtotime($request['due_date'])) : null;
        $request['is_gst'] = (! empty($request['gst_amount'])) ? '1' : '0';
        $request['invoice_sent_date'] = (empty($request['invoice_sent_date'])) ? null : date('Y-m-d', strtotime($request['invoice_sent_date']));
        $request['rebate_agent_date'] = (empty($request['rebate_agent_date']) || $request['is_deduct_agent'] == 0) ? null : date('Y-m-d', strtotime($request['rebate_agent_date']));
        $request['rebate_amount'] = (empty($request['rebate_amount']) || $request['is_deduct_agent'] == 0) ? null : $request['rebate_amount'];

        return $request;
    }

    private function saveInvoiceNumber()
    {
        $whereArr = ['college_id' => Auth::user()->college_id];
        $invData = $this->invoiceNumber->getWhereFirst($whereArr);
        $incNumber = sprintf("%'03d", $invData['invoice_number'] + 1);
        $this->invoiceNumber->update(['invoice_number' => $incNumber], $invData['id']);
    }

    public function getStudentServicePaymentEdit($request)
    {
        $serviceData = $this->studentPaymentRepository->getStudentServicePaymentEditData($request->input('serviceId'), $request->input('studentId'), $isRefund = '');
        $collegeId = Auth::user()->college_id;

        return [
            'serviceData' => $serviceData,
            'invoiceNumber' => InvoiceNumber::where('college_id', $collegeId)->value('invoice_number'),
            'receiptNumber' => ResceiptNumber::where('college_id', $collegeId)->value('resceipt_number'),
            'arrServicesName' => SetupServicesName::select('services_name as Name', 'id as Id')->where('college_id', $collegeId)->get()->toArray(),
            'paymentStatus' => $this->getPaymentStatusList(),
            'paymentMode' => $this->paymentMode->getAll(['id as Id', 'name as Name'], 'name'),
            'categoryData' => SetupServicesCategory::where(['college_id' => $collegeId, 'service_name_id' => $serviceData[0]['service_name_id']])->get(['category_name as Name', 'id as Id']),
            'facilityData' => SetupServices::where(['college_id' => $collegeId, 'is_active' => '1', 'category_id' => $serviceData[0]['category_id']])->get(['facility_name as Name', 'id as Id']),
            'serviceProviderData' => SetupProviderFacility::from('rto_service_provider_facilities_setup as rspfs')->leftjoin('rto_setup_provider as rsp', 'rsp.id', '=', 'rspfs.service_provider_id')->where(['rspfs.college_id' => $collegeId, 'rspfs.category_id' => $serviceData[0]['category_id'], 'rspfs.is_active' => '1'])->get(['rsp.id as Id', 'rsp.company_name as Name'])->toArray(),
        ];
    }

    public function getStudentServicePaymentData($request)
    {
        $sql = StudentServicePayment::join('rto_student_additional_service_request as rsasr', 'rsasr.id', '=', 'rto_student_service_payment.additional_services_id')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'rto_student_service_payment.created_by')
            ->leftjoin('rto_setup_services_name', 'rto_setup_services_name.id', '=', 'rsasr.service_name_id')
            ->leftjoin('rto_payment_mode as rpm', 'rpm.id', '=', 'rto_student_service_payment.payment_mode')
            ->where(['rto_student_service_payment.id' => $request->input('id')]);

        return $sql->get([
            'rto_student_service_payment.*',
            'rto_student_service_payment.course_id as courseId',
            'rto_student_service_payment.refund_date as refund_date',
            'rsasr.service_name_id',
            'rsasr.category_id',
            'rsasr.facility_id',
            'rpm.name as payment_mode',
            'rsasr.service_provider_id',
            'rsasr.student_price',
            'rsasr.service_start_date',
            'rsasr.service_end_date',
            'rsasr.comment',
            'rto_setup_services_name.services_name',
        ])->toArray();
    }

    public function generateAgentProInvoicePdf($request, $isSave = false)
    {
        $getData = json_decode($request->data);
        $collegeId = $getData->college_id;
        $studentId = $getData->student_id;
        $studentCourseId = $getData->student_course_id;

        $courseId = $this->studentCourse->find($studentCourseId)->course_id;
        $arrStudentInfo = $this->studentRepository->getStudentDetail($studentId);
        $invoiceNo = (isset($getData->invoice_number)) ? $getData->invoice_number : $this->studentInitialPaymentDetails->findWithTrashed($getData->id)->invoice_number;

        //        $formattedInv = StudentInitialPaymentDetails::where('invoice_number', $invoiceNo)->get();
        //        dd($formattedInv->formatted_invoice_number);
        $data = [
            'arrState' => Config::get('constants.arrState'),
            'collegeLogo' => $this->commonRepository->getCollegeLogo($arrStudentInfo->college_logo),
            'arrStudentInfo' => $arrStudentInfo,
            'collegeData' => $this->commonRepository->getCollegeDetails($collegeId),
            'arrStudentCourse' => $this->studentPaymentRepository->getStudentPdfCourseDataV2($courseId, $studentId, $studentCourseId),
            'arrStudentPaymentDetail' => $this->studentPaymentRepository->studentPaymentDetailsGetV2($collegeId, $studentId, $studentCourseId, $invoiceNo),
            // 'arrMiscellaneousPayment'   => $this->studentPaymentRepository->getMiscellaneousPayment($collegeId, $invoiceNo),
            // 'arrStudentServicePayment'  => $this->studentServicePayment->getWhere(['college_id' => $collegeId, 'invoice_number' => $invoiceNo]),
            // 'arrAgentCommission'        => $this->studentPaymentRepository->getAgentCommissionValue($collegeId, $studentId, $courseId, $invoiceNo),
            'fileName' => "Agent_Invoice_$invoiceNo.pdf",
            'bladeFilePath' => 'v2.sadmin.student.pages.agent-pro-invoice-pdf',
        ];

        if ($isSave) {
            return $this->generateAndSavePdf($data, $studentId, $invoiceNo, $collegeId);
        }

        return $this->generateAndDownloadPdf($data);
    }

    public function updateAdditionalServicePaymentRefund($data)
    {
        DB::beginTransaction();
        try {
            $result = $this->studentServicePayment->update($data, $data['serviceId']);
            DB::commit();

            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function generateStudentScheduleInvoicePdf($request, $isSave = false)
    {
        $getData = json_decode($request->data);
        $collegeId = $getData->college_id;
        $studentId = $getData->student_id;
        $studentCourseId = $getData->student_course_id;
        $arrStudentInfo = $this->studentRepository->getStudentDetail($studentId);
        $courseId = $this->studentCourse->find($studentCourseId)->course_id;
        $invoiceNo = (isset($getData->invoice_number)) ? $getData->invoice_number : $this->studentInitialPaymentDetails->findWithTrashed($getData->id)->invoice_number;
        $arrStudentCourse = $this->studentPaymentRepository->getStudentPdfCourseDataV2($courseId, $studentId, $studentCourseId);

        $data = [
            'arrStudentInfo' => $arrStudentInfo,
            'collegeLogo' => $this->commonRepository->getCollegeLogo($arrStudentInfo->college_logo),
            'collegeData' => $this->commonRepository->getCollegeDetails($collegeId),
            'arrStudentPaymentDetail' => $this->studentPaymentRepository->studentPaymentDetailsGetV2($collegeId, $studentId, $studentCourseId, $invoiceNo),
            'arrMiscellaneousPayment' => $this->studentPaymentRepository->getMiscellaneousPayment($collegeId, $invoiceNo),
            'arrStudentServicePayment' => $this->studentServicePayment->getWhere(['college_id' => $collegeId, 'invoice_number' => $invoiceNo]),
            'arrStudentCourse' => ($arrStudentCourse->count() > 0) ? $arrStudentCourse[0] : [],
            'fileName' => "Schedule_Invoice_$invoiceNo.pdf",
            'bladeFilePath' => 'v2.sadmin.student.pages.student-schedule-invoice-pdf',
        ];

        if ($isSave) {
            return $this->generateAndSavePdf($data, $studentId, $invoiceNo, $collegeId);
        }

        return $this->generateAndDownloadPdf($data);
    }

    public function generateCombinedCourseInvoicePdf($request, $isSave = false)
    {
        $getData = json_decode($request->data);
        $collegeId = $getData->college_id;
        $studentId = $getData->student_id;
        $studentCourseId = $getData->student_course_id;
        $courseId = $this->studentCourse->find($studentCourseId)->course_id;
        $objStudentDetails = $this->studentRepository->getStudentDetails($studentId);
        $arrStudentCourse = $this->studentPaymentRepository->getStudentPdfCourseDataV2($courseId, $studentId, $studentCourseId);
        $objCurrentCountry = $this->studentRepository->getSelectedCountry($objStudentDetails[0]->current_country);
        $data = [
            'arrState' => Config::get('constants.arrState'),
            'pagetitle' => '',
            'objStudentDetails' => $objStudentDetails,
            'objCurrentCountry' => $objCurrentCountry,
            'collegeLogo' => $this->commonRepository->getCollegeLogo($objStudentDetails[0]->college_logo),
            'objCollegeDetails' => $this->commonRepository->getCollegeDetails($collegeId),
            'objStudentCourse' => ($arrStudentCourse->count() > 0) ? $arrStudentCourse[0] : [],
            'firstStudentCourseId' => $courseId,
            'fileName' => 'student-invoice.pdf',
            'bladeFilePath' => 'v2.sadmin.student.pages.student-combined-invoice',
        ];

        return $this->generateAndDownloadPdf($data);
    }

    public function generateStudentTaxReceiptPdf($request)
    {
        $getData = json_decode($request->data);
        $collegeId = Auth::user()->college_id;
        $primaryId = $getData->primaryId;
        if ($primaryId) {
            $resData = $this->studentPaymentRepository->getStudentPaymentTransactionDetails($primaryId);
            if (count($resData) > 0) {
                $studentId = $resData[0]['student_id'];
                $courseId = $resData[0]['course_id'];
                $studentCourseId = $resData[0]['student_course_id'];
                $receiptNo = $resData[0]['receipt_no'];
                $invoiceNo = $resData[0]['invoice_number'];
                $arrStudentInfo = $this->studentRepository->getStudentDetail($studentId);
                $collegeDetail = $this->commonRepository->getCollegeDetails($collegeId);
                $arrStudentCourse = $this->studentPaymentRepository->getStudentPdfCourseDataV2($courseId, $studentId, $studentCourseId);

                $data = [
                    'arrStudentInfo' => $arrStudentInfo,
                    'transactionData' => $resData,
                    'collegeLogo' => $this->commonRepository->getCollegeLogo($arrStudentInfo->college_logo),
                    'college_signature' => $this->commonRepository->getCollegeLogo($collegeDetail->college_signature),
                    'objCollegeDetails' => $collegeDetail,
                    'arrState' => Config::get('constants.arrState'),
                    'arrMiscellaneousPayment' => StudentMiscellaneousPayment::where('resceipt_number', $receiptNo)->get(),
                    'arrStudentCourse' => ($arrStudentCourse->count() > 0) ? $arrStudentCourse[0] : [],
                    'fileName' => 'student-receipt-'.$arrStudentInfo->generated_stud_id.'-'.$receiptNo.'.pdf',
                    'bladeFilePath' => 'v2.sadmin.student.pages.student-tax-receipt-pdf',
                ];

                return $this->generateAndDownloadPdf($data);
            }
        }
    }

    public function generateAgentTaxReceiptPdf($request)
    {
        $getData = json_decode($request->data);
        $collegeId = $getData->college_id;
        $studentId = $getData->student_id;
        $studentCourseId = $getData->student_course_id;
        $scheduleId = $getData->id;
        $arrStudentInfo = $this->studentRepository->getStudentDetail($studentId);

        $arrStudentDetailsGet = $this->studentPaymentRepository->studentPaymentDetailsGetPrimary($collegeId, $scheduleId);
        // dd($arrStudentDetailsGet);
        $courseId = $this->studentCourse->find($studentCourseId)->course_id;
        $arrStudentCourse = $this->studentPaymentRepository->getStudentPdfCourseDataV2($courseId, $studentId, $studentCourseId);
        $invoiceNo = (count($arrStudentDetailsGet) > 0) ? $arrStudentDetailsGet[0]->invoice_number : '';

        // $arrAgentCommissionRefund = $this->studentAgentCommission->getWhereRow(['college_id' => $collegeId, 'course_id' => $courseId, 'student_id' => $studentId, 'transaction_no' => $transactionNo]);
        $arrAgentCommissionRefund = $this->studentAgentCommission->getWhereRow(['college_id' => $collegeId, 'course_id' => $courseId, 'student_id' => $studentId, 'invoice_no' => $invoiceNo]);

        $collegeDetail = $this->commonRepository->getCollegeDetails($collegeId);

        $data = [
            'arrStudentInfo' => $arrStudentInfo,
            'arrStudentDetailsGet' => $arrStudentDetailsGet,
            'collegeLogo' => $this->commonRepository->getCollegeLogo($arrStudentInfo->college_logo),
            'college_signature' => $this->commonRepository->getCollegeLogo($collegeDetail->college_signature),
            'objCollegeDetails' => $collegeDetail,
            'arrState' => Config::get('constants.arrState'),
            'arrMiscellaneousPayment' => $this->studentPaymentRepository->getMiscellaneousPayment($collegeId, $invoiceNo),
            'arrStudentServicePayment' => $this->studentServicePayment->getWhere(['college_id' => $collegeId, 'invoice_number' => $invoiceNo]),
            'arrStudentCourse' => ($arrStudentCourse->count() > 0) ? $arrStudentCourse[0] : [],
            'arrAgentCommissionRefund' => $arrAgentCommissionRefund[0],
            'fileName' => 'agent-receipt-'.$arrAgentCommissionRefund[0]['invoice_no'].'.pdf',
            'bladeFilePath' => 'v2.sadmin.student.pages.student-agent-receipt-pdf',
        ];

        return $this->generateAndDownloadPdf($data);
    }

    public function saveMiscellaneousPaymentReverseTransactionPaymentRequest($request)
    {
        DB::beginTransaction();
        try {
            $res = $this->studentMiscellaneousPayment->update(['is_reversed' => $request->is_reversed, 'reversed_comment' => $request->reversed_comment], $request->id);
            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function saveMiscellaneousPayment($request)
    {
        $result = StudentMiscellaneousPayment::find($request['id']);
        if (! $result) {
            return ['status' => 'error', 'message' => 'Data not found'];
        }
        $getTransactionNumber = $this->transactionNumber->getWhereVal(['college_id' => auth()->user()->college_id], 'transaction_number');

        DB::beginTransaction();
        try {
            $updateArr = [
                'payment_status' => $request['payment_status'],
                'transaction_number' => $getTransactionNumber,
                'resceipt_number' => $request['resceipt_number'],
                'paid_on' => $request['paid_on'],
                'bank_deposite_date' => $request['bank_deposite_date'],
                'payment_mode' => $request['payment_mode'],
                'paid_amount' => $result['amount'],
            ];
            $res = $this->studentMiscellaneousPayment->update($updateArr, $request['id']);
            $this->studentPaymentRepository->editTransactionNumber();
            DB::commit();

            return ['status' => 'success', 'data' => $res];

        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);

            return ['status' => 'error', 'data' => $e->getMessage()];
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function studentMiscellaneousPaymentData($request)
    {
        $collegeId = Auth::user()->college_id;
        $data = $this->studentPaymentRepository->studentMiscellaneousPaymentData($request->id, $collegeId);
        $data[0]['resceiptNumber'] = $this->resceiptNumber->getWhereVal(['college_id' => $collegeId], 'resceipt_number');

        return [
            'miscellaneousPaymentData' => $data,
            'invoice_number' => InvoiceNumber::where('college_id', $collegeId)->value('invoice_number'),
            'arrOtherServiceTypeMiscellaneousPayment' => $this->convertKendoFormat(Config::get('constants.arrOtherServiceTypeMiscellaneousPayment')),
            'paymentStatus' => $this->getPaymentStatusList(),
            'oshcProvider' => $this->studentCourseService->getOshcProvider($request),
            'paymentModeList' => $this->paymentMode->getAll(['id as Id', 'name as Name'], 'name'),
        ];
    }

    public function studentServicePaymentData($request)
    {
        $serviceData = $this->studentPaymentRepository->getStudentServicePaymentEditData($request->input('id'), $request->input('studentId'), $isRefund = '');
        $collegeId = Auth::user()->college_id;

        return [
            'serviceData' => $serviceData,
            'invoiceNumber' => InvoiceNumber::where('college_id', $collegeId)->value('invoice_number'),
            'receiptNumber' => ResceiptNumber::where('college_id', $collegeId)->value('resceipt_number'),
            'arrServicesName' => SetupServicesName::select('services_name as Name', 'id as Id')->where('college_id', $collegeId)->get()->toArray(),
            'paymentStatus' => $this->getPaymentStatusList(),
            'paymentMode' => $this->paymentMode->getAll(['id as Id', 'name as Name'], 'name'),

        ];
    }

    public function generateReceiptAdditionalPaymentServicePdf($request, $isSave = false)
    {
        $getData = json_decode($request->data);
        $primaryId = $getData->primaryId;
        if ($primaryId) {
            $resData = $this->studentPaymentRepository->getStudentServicePaymentData($primaryId);
            if (count($resData) > 0 && isset($resData[0]['student_course_id'])) {
                $collegeId = $resData[0]['college_id'];
                $studentId = $resData[0]['student_id'];
                $courseId = $resData[0]['course_id'];
                $invoiceNo = $resData[0]['formatted_invoice_number'];
                $studentCourseId = $resData[0]['student_course_id'];

                $arrStudentInfo = $this->studentRepository->getStudentDetail($studentId);
                $arrStudentCourse = $this->studentPaymentRepository->getStudentPdfCourseDataV2($courseId, $studentId, $studentCourseId);
                $string = $arrStudentInfo->generated_stud_id.'_'.$arrStudentInfo->application_reference_id;
                $data = [
                    'invoiceNo' => $invoiceNo,
                    'arrStudentInfo' => $arrStudentInfo,
                    'objCollegeDetails' => $this->commonRepository->getCollegeDetails($collegeId),
                    'collegeLogo' => $this->commonRepository->getCollegeLogo($arrStudentInfo['college_logo']),
                    'arrState' => Config::get('constants.arrState'),
                    'arrStudentCourse' => $arrStudentCourse[0],
                    'arrPaidMode' => $this->paymentMode->getAll(['id as Id', 'name as Name'], 'name'),
                    'arrStudentDetailsGet' => $resData,
                    'fileName' => "serviceReceipt_$string.pdf",
                    'bladeFilePath' => 'v2.sadmin.student.pages.student-service-receipt-pdf',
                ];
                if ($isSave) {
                    return $this->generateAndSavePdf($data, $studentId, $invoiceNo, $collegeId);
                }

                return $this->generateAndDownloadPdf($data);
            }
        }
    }

    public function generateInvoicesAdditionalPaymentServicePdf($request)
    {
        $getData = json_decode($request->data);
        $primaryId = $getData->primaryId;
        if ($primaryId) {
            $resData = $this->studentPaymentRepository->getStudentServicePaymentData($primaryId);
            if (count($resData) > 0 && isset($resData[0]['student_course_id'])) {
                $collegeId = $resData[0]['college_id'];
                $studentId = $resData[0]['student_id'];
                $courseId = $resData[0]['course_id'];
                $invoiceNo = $resData[0]['formatted_invoice_number'];
                $studentCourseId = $resData[0]['student_course_id'];

                $arrStudentInfo = $this->studentRepository->getStudentDetail($studentId);
                $arrStudentCourse = $this->studentPaymentRepository->getStudentPdfCourseDataV2($courseId, $studentId, $studentCourseId);
                $string = $arrStudentInfo->generated_stud_id.'_'.$arrStudentInfo->application_reference_id;
                $data = [
                    'invoiceNo' => $invoiceNo,
                    'arrStudentInfo' => $arrStudentInfo,
                    'objCollegeDetails' => $this->commonRepository->getCollegeDetails($collegeId),
                    'collegeLogo' => $this->commonRepository->getCollegeLogo($arrStudentInfo->college_logo),
                    'arrState' => Config::get('constants.arrState'),
                    'arrStudentCourse' => $arrStudentCourse[0],
                    'arrPaidMode' => $this->paymentMode->getAll(['id as Id', 'name as Name'], 'name'),
                    'arrStudentDetailsGet' => $this->studentPaymentRepository->studentPaymentDetailsForStudentServicePaymentGet($collegeId, $courseId, $studentId, ''),
                    'arrStudentServicePayment' => $resData,
                    'fileName' => "ServiceInvoice_$string.pdf",
                    'bladeFilePath' => 'v2.sadmin.student.pages.student-service-invoice-pdf',
                ];

                return $this->generateAndDownloadPdf($data);
            }
        }
    }

    public function generateRefundReceiptAdditionalPaymentServicePdf($request)
    {
        $getData = json_decode($request->data);
        $studentId = $getData->student_id;
        $studentCourseId = $getData->student_course_id;
        $courseId = $this->studentCourse->getWhereVal(['id' => $studentCourseId], 'course_id');
        $collegeId = $getData->college_id;
        $serviceId = $getData->service_id;
        $arrStudentInfo = $this->studentRepository->getStudentDetail($studentId);
        // $arrStudentCourse = $this->studentPaymentRepository->getStudentPdfCourseDataV2($courseId, $studentId, $studentCourseId);
        // $arrStudentDetailsGet =  $this->studentPaymentRepository->studentPaymentDetailsForStudentServicePaymentGet($collegeId,$courseId, $studentId,'');
        $arrServicePaymentRefund = $this->studentPaymentRepository->getStudentServicePaymentEditData($serviceId, $studentId, $isRefund = '');
        $data = [
            'arrStudentInfo' => $arrStudentInfo,
            'objCollegeDetails' => $this->commonRepository->getCollegeDetails($collegeId),
            'collegeLogo' => $this->commonRepository->getCollegeLogo($arrStudentInfo->college_logo),
            'arrState' => Config::get('constants.arrState'),
            'arrPaidMode' => $this->paymentMode->getAll(['id as Id', 'name as Name'], 'name'),
            'arrServicePaymentRefund' => (object) $arrServicePaymentRefund[0],
            'fileName' => 'Student Service Payment Refund Receipt '.$arrStudentInfo->generated_stud_id.'.pdf',
            'bladeFilePath' => 'v2.sadmin.student.pages.student-service-payment-refund-receipt-pdf',
        ];

        return $this->generateAndDownloadPdf($data);
    }

    public function saveReverseTransactionAdditionalPaymentService($request)
    {
        DB::beginTransaction();
        try {
            $result = $this->studentServicePayment->update(['is_reversed' => $request->is_reversed, 'reversed_comment' => $request->reversed_comment], $request->id);
            DB::commit();

            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteTransactionAdditionalPaymentServiceData($request)
    {
        $result = false;
        DB::beginTransaction();
        try {
            // $studServicePayment = StudentServicePayment::withTrashed()->find($request->serviceId);
            // $result = $studServicePayment->forceDelete();
            $servicePayment = StudentServicePayment::withTrashed()->where('id', $request->serviceId)->with(['xeroInvoice'])->first();
            if ($this->checkValidStatusForDelete($servicePayment)) {
                $result = $servicePayment->forceDelete();
            }
            DB::commit();

            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteBulkServicePayment($request)
    {
        // TODO::GNG-2777
        $successCount = $failCount = 0;
        // $isXeroConnect = (Xero::isConnected()) ? true : false;
        foreach ($request->ids as $servicePaymentId) {
            // $studServicePayment = StudentServicePayment::find($servicePaymentId);
            $servicePayment = StudentServicePayment::withTrashed()->where('id', $servicePaymentId)->with(['xeroInvoice'])->first();
            if ($this->checkValidStatusForDelete($servicePayment)) {
                DB::beginTransaction();
                try {
                    $servicePayment->forceDelete();
                    DB::commit();
                    $successCount++;
                } catch (\Exception $e) {
                    DB::rollBack();
                    $failCount++;
                    // throw new ApplicationException($e->getMessage());
                }
            } else {
                $failCount++;
                // echo "Cannot delete invoice: $servicePayment->invoice_number (Status: $servicePayment->xeroInvoice->xero_invoice_status) <br/>";
            }
        }

        return [
            'success_count' => $successCount,
            'success_msg' => ($failCount > 0) ? "Total $successCount Service delete successfully" : 'Selected Items Delete Successfully',
            'error_count' => $failCount,
            'error_msg' => ($failCount > 0) ? (($successCount > 0) ? "Total $failCount Service failed to delete" : ' System does not allow deletion of paid/approved invoices') : '',
        ];
    }

    public function generateMiscellaneousPaymentRefundPdf($request)
    {
        $getData = json_decode($request->data);
        $primaryId = $getData->id;
        $collegeId = $getData->college_id;
        $studentId = $getData->student_id;
        $studentCourseId = $getData->student_course_id;
        $courseId = $this->studentCourse->getWhereVal(['id' => $studentCourseId], 'course_id');

        $arrMiscellaneousPayment = $this->studentMiscellaneousPayment->listStudentMiscellaneousPaymentPDF($collegeId, $studentId, $courseId, $primaryId);
        if (! $arrMiscellaneousPayment) {
            return 'Not found';
        }

        $arrStudentInfo = $this->studentRepository->getStudentDetail($studentId);
        $data = [
            'collegeLogo' => $this->commonRepository->getCollegeLogo($arrStudentInfo['college_logo']),
            'arrStudentInfo' => $arrStudentInfo,
            'arrMiscellaneousPayment' => $this->studentMiscellaneousPayment->listStudentMiscellaneousPaymentPDF($collegeId, $studentId, $courseId, $primaryId),
            'objCollegeDetails' => $this->commonRepository->getCollegeDetails($collegeId),
            'arrPaidMode' => $this->paymentMode->getPluck('name', 'id', 'name'),
            'fileName' => 'Miscellaneous_Payment_Refund_Receipt.pdf',
            'bladeFilePath' => 'v2.sadmin.student.pages.student-miscellaneous-payment-refund-receipt-pdf',
        ];

        return $this->generateAndDownloadPdf($data);
    }

    public function saveDeleteMiscellaneousTransactionData($request)
    {
        $res = false;
        DB::beginTransaction();
        try {
            $miscellaneous = StudentMiscellaneousPayment::withTrashed()->where('id', $request->transactionId)->with(['xeroInvoice'])->first();
            if ($this->checkValidStatusForDelete($miscellaneous)) {
                $res = $miscellaneous->forceDelete();
                // $res = StudentMiscellaneousPayment::where('id', $request->transactionId)->forceDelete();
                $this->studentPaymentRepository->updateMiscellaneousTransactionReasonAndReceipt($request->reason, $request->receipt_no, $request->amount);
            }
            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteBulkMiscellaneousPayment($request)
    {
        // TODO::GNG-2777
        $reason = 'Bulk Delete';
        $successCount = $failCount = 0;
        // $isXeroConnect = (Xero::isConnected()) ? true : false;
        foreach ($request->ids as $miscellaneousId) {
            // $miscellaneous = $this->studentMiscellaneousPayment->find($miscellaneousId);
            $miscellaneous = StudentMiscellaneousPayment::withTrashed()->where('id', $miscellaneousId)->with(['xeroInvoice'])->first();
            if ($this->checkValidStatusForDelete($miscellaneous)) {
                DB::beginTransaction();
                try {
                    $this->studentPaymentRepository->updateMiscellaneousTransactionReasonAndReceipt($reason, $miscellaneous->resceipt_number, $miscellaneous->amount);
                    $miscellaneous->forceDelete();
                    DB::commit();
                    $successCount++;
                } catch (\Exception $e) {
                    DB::rollBack();
                    $failCount++;
                    // throw new ApplicationException($e->getMessage());
                }
            } else {
                $failCount++;
                // echo "Cannot delete invoice: $miscellaneous->invoice_number (Status: $miscellaneous->xeroInvoice->xero_invoice_status) <br/>";
            }
        }

        return [
            'success_count' => $successCount,
            'success_msg' => ($failCount > 0) ? "Total $successCount Miscellaneous delete successfully" : 'Selected Items Delete Successfully',
            'error_count' => $failCount,
            'error_msg' => ($failCount > 0) ? (($successCount > 0) ? "Total $failCount Miscellaneous failed to delete" : ' System does not allow deletion of paid/approved invoices') : '',
        ];
    }

    private function generateAndDownloadPdf($data)
    {
        $pdf = App::make('dompdf.wrapper');
        $pdf->loadView($data['bladeFilePath'], $data);

        return $pdf->download($data['fileName'], $data);
    }

    public function deleteInvoiceCreditData($request)
    {
        $primaryId = $request['id'];
        $res = StudentInvoiceCredit::findOrFail($primaryId);
        if (! $res) {
            return $this->errorResponse('No record found', 'data', [], 200);
        }

        DB::beginTransaction();
        try {
            $getPaymentDetail = StudentInitialPaymentDetails::find($res->initial_payment_detail_id);
            if ($getPaymentDetail) {
                $totalCredit = $getPaymentDetail->invoice_credit - $res->credit_amount;
                $newTotal = $getPaymentDetail->upfront_fee_pay + $totalCredit;

                if ($newTotal >= $getPaymentDetail->upfront_fee_to_pay) {
                    $paymentStatus = 'paid';
                } elseif ($getPaymentDetail->upfront_fee_pay > 0) {
                    $paymentStatus = 'partially paid';
                } else {
                    $paymentStatus = 'unpaid';
                }

                $getPaymentDetail->invoice_credit = $totalCredit;
                $getPaymentDetail->payment_status = $paymentStatus;
                $getPaymentDetail->save();
            }
            $res->delete();
            DB::commit();

            return $this->successResponse('Invoice credit has been successfully deleted.', 'data', []);
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function sendMiscellaneousTransactionEmail($request)
    {

        $collegeId = Auth::user()->college_id;
        $studentId = $request->student_id;
        $studentCourseId = $request->student_course_id;
        $courseId = $this->studentCourse->getWhereVal(['id' => $studentCourseId], 'course_id');

        // Retrieve attachment files
        $attachment = $attachmentLogData = [];
        if ($request->file('attachment_file')) {
            [$attachment, $attachmentLogData] = $this->mailAttachment($request, true);
        }
        $agentAttachment = [];
        $emailCC = ($request->input('email_cc') != '') ? explode(',', $request->input('email_cc')) : [];
        $emailBCC = ($request->input('email_bcc') != '') ? explode(',', $request->input('email_bcc')) : [];
        $studentEmail = $request->student_email;
        $agencyEmail = $request->agency_email;

        if ($request->miscellaneousPaymentRequestId) {
            $primaryId = $request->miscellaneousPaymentRequestId;
        } elseif ($request->paymentScheduleId) {
            $primaryId = $request->paymentScheduleId;
        } elseif ($request->paymentServiceId) {
            $primaryId = $request->paymentServiceId;
        } elseif ($request->paymentHistoryId) {
            $primaryId = $request->paymentHistoryId;
        } elseif ($request->studCertificateId) {
            $primaryId = $request->studCertificateId;
        }
        // Retrieve existing attachments
        $emailExistAttachmentIdArr = ($request->input('existing_attachment_id') != '') ? explode(',', $request->input('existing_attachment_id')) : [];
        [$existAttachment, $existAttachmentLogData] = $this->retrieveExistingAttachments($emailExistAttachmentIdArr, $request->input('email_template_id'));

        if ($request->input('student_invoice') == 'on') {
            $studentInvoiceAttachment = $this->isStudentInvoice($collegeId, $studentId, $studentCourseId, $courseId, $primaryId, $request->invoice_number);
            $attachment[] = $studentInvoiceAttachment[0];
            $attachmentLogData[substr($studentInvoiceAttachment[1], strrpos($studentInvoiceAttachment[1], '/') + 1)] = $studentInvoiceAttachment[1];
        }
        if ($request->input('student_receipt') == 'on') {
            $studentReceiptAttachment = $this->isStudentReceipt($collegeId, $studentId, $studentCourseId, $courseId, $primaryId, $request->invoice_number);
            $attachment[] = $studentReceiptAttachment[0];
            $attachmentLogData[substr($studentReceiptAttachment[1], strrpos($studentReceiptAttachment[1], '/') + 1)] = $studentReceiptAttachment[1];
        }

        if ($request->input('invoice_sent') == 'on') {
            $InvoiceSentAttachment = $this->isInvoiceSent($collegeId, $studentId, $studentCourseId, $courseId, $primaryId, $request->invoice_number);
            $attachment[] = $InvoiceSentAttachment[0];
            $attachmentLogData[substr($InvoiceSentAttachment[1], strrpos($InvoiceSentAttachment[1], '/') + 1)] = $InvoiceSentAttachment[1];
            $allAttachments = array_merge($existAttachment, $attachment);
            $mailData = $this->prepareMailData($studentId, $courseId, $agencyEmail, $request->input('email_content'), $request->input('email_subject'), $allAttachments, $emailCC, $emailBCC);
            $mailStatus = (new SendMail)->sendSmtpMail($mailData);
        }
        if ($request->input('is_student_certificate') == 'on') {
            $certificateFile = $this->getStudentCertificateFile($primaryId);
            if ($certificateFile) {
                $attachment[] = $certificateFile[0];
                $attachmentLogData[substr($certificateFile[1], strrpos($certificateFile[1], '/') + 1)] = $certificateFile[1];
            }
        }

        $allAttachments = array_merge($existAttachment, $attachment);
        $mailData = $this->prepareMailData($studentId, $courseId, $studentEmail, $request->input('email_content'), $request->input('email_subject'), $allAttachments, $emailCC, $emailBCC);
        $mailData['from'] = $request->input('email_from');
        //        $allAttachmentsForLogFile = array_merge($existAttachmentLogData, $attachmentLogData);
        //        dd([$mailData, $allAttachmentsForLogFile]);

        $mailStatus = (new SendMail)->sendSmtpMail($mailData);
        if ($mailStatus == 'success') {
            if ($request->input('offer_comm_log') == 'on') {
                $allAttachmentsForLogFile = array_merge($existAttachmentLogData, $attachmentLogData);
                $this->saveCommunicationLog($request->all(), $mailData['id'], $mailData['body'], $mailData['to'], $mailData['from'], $allAttachmentsForLogFile, $mailData);
            }

            return true;
        }

        return false;
    }

    public function saveCommunicationLog($request, $studentId, $replacedContent, $emailTo, $emailFrom, $attachmentLogData = [], $mailData = [])
    {
        $userId = Auth::user()->id;
        $collegeId = Auth::user()->college_id;
        $todayDate = date('l,d F Y');
        $log = "Send to : $emailTo<br>";

        if (isset($request['email_cc']) && ! empty($request['email_cc'])) {
            $log .= 'CC : '.$request['email_cc'].'<br>';
        }
        if (isset($request['email_bcc']) && ! empty($request['email_bcc'])) {
            $log .= 'BCC : '.$request['email_bcc'].'<br>';
        }

        $log .= "From email : $emailFrom <br>";
        $log .= 'Subject : '.$mailData['subject'].'<br>';
        $log .= $replacedContent;

        if (count($attachmentLogData) > 0) {
            $attachmentList = [];
            foreach ($attachmentLogData as $name => $path) {
                $attachmentList[] = "<a href='$path' target='_blank'  class='font-medium text-blue-600 dark:text-blue-500 hover:underline' title='$name'>$name</a>";
            }
            $log .= 'Attachments : '.implode(', ', $attachmentList);
        }

        $data = [
            'today_date' => $todayDate,
            'college_id' => $collegeId,
            'student_id' => $studentId,
            'student_course_id' => $request['student_course_id'],
            'type' => 26,
            'log_type' => 'offer',
            'status' => 16,
            'log' => $log,
            'created_by' => $userId,
            'updated_by' => $userId,
        ];

        return StudentCommunicationLog::create($data);
    }

    private function getStudentCertificateFile($primaryId)
    {
        $studCertificateData = StudentCertificateRegister::Where(['id' => $primaryId])->get()->first();
        $filename = $studCertificateData->is_file_name;
        if (! empty($filename)) {
            $filePath = Config::get('constants.uploadFilePath.StudentCertificate');
            $destinationPath = Helpers::changeRootPath($filePath);

            return [$destinationPath['default'].$filename, $destinationPath['view'].$filename];
        }

        return false;
    }

    private function retrieveExistingAttachments($emailExistAttachmentIdArr, $emailTemplateId)
    {
        $existAttachment = [];
        $existAttachmentLogData = [];
        if (count($emailExistAttachmentIdArr) > 0) {
            $existFilePath = Config::get('constants.uploadFilePath.Templates');
            $existDestinationPath = Helpers::changeRootPath($existFilePath, $emailTemplateId);
            $docList = $this->studentProfileCommonRepository->emailTemplateDocModel($emailExistAttachmentIdArr);
            foreach ($docList as $doc) {
                $existAttachment[] = $existDestinationPath['default'].$doc['file'];
                $existAttachmentLogData[$doc['file']] = $existDestinationPath['view'].$doc['file'];

            }
        }

        return [$existAttachment, $existAttachmentLogData];
    }

    public function mailAttachment($request, $viewFlag = false)
    {
        $emailAttachments = $request->file('attachment_file');
        $filePath = Config::get('constants.uploadFilePath.TempMailAttachment');
        $destinationPath = Helpers::changeRootPath($filePath);
        $savedFileName = [];
        $mailFileName = [];
        $mailLogFileName = [];
        $counts = 0;
        $imageGet = 0;
        foreach ($emailAttachments as $emailAttachment) {
            $originalName = $emailAttachment->getClientOriginalName();
            $filename = date('YmdHsi').'-'.$originalName;
            $savedFileName[] = $filename;
            if ($imageGet == 0) {
                if (! is_dir($destinationPath['default'])) {
                    File::makeDirectory($destinationPath['default'], 0777, true, true);
                }
                $emailAttachment->move($destinationPath['default'], $filename);
                $mailFileName[] = $destinationPath['default'].$savedFileName[$counts];
                if ($viewFlag) {
                    $mailLogFileName[$originalName] = $destinationPath['view'].$savedFileName[$counts];
                }
                $counts++;
            }
        }
        if ($viewFlag) {
            return [$mailFileName, $mailLogFileName];
        }

        return $mailFileName;
    }

    public function isStudentInvoice($collegeId, $studentId, $studentCourseId, $courseId, $primaryId, $invoice_number)
    {
        $data2 = (object) [];
        $requestData = (object) [];
        $data2->college_id = $collegeId;
        $data2->student_id = $studentId;
        $data2->student_course_id = $studentCourseId;
        $data2->id = $primaryId;
        $data2->invoice_number = $invoice_number;
        $requestData->data = json_encode($data2);

        return $this->generateStudentScheduleInvoicePdf($requestData, true);
    }

    public function isInvoiceSent($collegeId, $studentId, $studentCourseId, $courseId, $primaryId, $invoice_number)
    {
        $data2 = (object) [];
        $requestData = (object) [];
        $data2->college_id = $collegeId;
        $data2->student_id = $studentId;
        $data2->student_course_id = $studentCourseId;
        $data2->id = $primaryId;
        $data2->invoice_number = $invoice_number;
        $requestData->data = json_encode($data2);

        return $this->generateAgentProInvoicePdf($requestData, true);
    }

    public function isStudentReceipt($collegeId, $studentId, $studentCourseId, $courseId, $primaryId, $invoice_number)
    {
        $data2 = (object) [];
        $requestData = (object) [];
        $data2->college_id = $collegeId;
        $data2->student_id = $studentId;
        $data2->student_course_id = $studentCourseId;
        $data2->id = $primaryId;
        $data2->primaryId = $primaryId;
        $data2->invoice_number = $invoice_number;
        $requestData->data = json_encode($data2);

        return $this->generateReceiptAdditionalPaymentServicePdf($requestData, true);
    }

    public function generateAndSavePdf($data, $studentId, $invoiceNo, $collegeId)
    {
        $pdf = App::make('dompdf.wrapper');
        $pdf->loadView($data['bladeFilePath'], $data);
        // return $pdf->download($data['fileName'], $data);
        $filePath = Config::get('constants.uploadFilePath.StudentMailAttach');
        $destinationPath = $this->changeRootPath($filePath, $collegeId, $studentId);
        if (! is_dir($destinationPath['default'])) {
            mkdir($destinationPath['default'], 0777);
        }
        $pdf->save($destinationPath['default'].$data['fileName']);

        // $savedFileNameList[] = $destinationPath['default'] . 'Schedule_Invoice_' . $invoiceNo . '.pdf';
        return [$destinationPath['default'].$data['fileName'], $destinationPath['view'].$data['fileName']];
    }

    public function getStudentAgentList($request)
    {
        return $this->studentCourse->getStudentAgentList($request->student_course_id);
    }

    // private function prepareMailData($scInfo, $studRow, $request, $existAttachment, $attachment, $emailCC, $emailBCC)
    private function prepareMailData($student_id, $course_id, $studentEmail, $email_content, $email_subject, $attachment, $emailCC, $emailBCC)
    {
        [$convertedSubject, $convertedData] = $this->getContentWithSubject($student_id, $course_id, $email_content, $email_subject);

        return [
            'id' => $student_id,
            'to' => $studentEmail,
            'cc' => $emailCC,
            'bcc' => $emailBCC,
            'subject' => $convertedSubject,
            'attachFile' => $attachment,
            'body' => $convertedData,
        ];
    }

    public function getContentWithSubject($studentId, $courseId, $content, $subject = '')
    {
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);
        $arrStudentCourse = $this->studentProfileCommonRepository->getStudentCoursesEmailContent($studentId, $courseId);
        $arrStudentEnrolledCourse = $this->studentProfileCommonRepository->getArrayStudentEnrolledCourseName($studentId);
        $arrStudentOfferedCourse = $this->studentProfileCommonRepository->getArrayStudentOfferedCourseName($studentId);
        if (! empty($arrStudentCourse)) {
            $row = $arrStudentCourse[0];
            $domain = url('/');
            $basePath = $destinationPath['default'];

            // $college_logo_url = str_replace('\\', '/', $basePath).$row['college_logo'];
            $usePublicImages = $row['allow_public_images'] ?? true;
            $college_logo_url = $this->getUploadedFileUrl($basePath.$row['college_logo'], $usePublicImages);
            $college_signature_url = $this->getUploadedFileUrl($basePath.$row['college_signature'], $usePublicImages);
            $dean_signature_url = $this->getUploadedFileUrl($basePath.$row['dean_signature'], $usePublicImages);
            $admission_manager_signature_url = $this->getUploadedFileUrl($basePath.$row['admission_manager_signature'], $usePublicImages);
            $student_support_signature_url = $this->getUploadedFileUrl($basePath.$row['student_support_signature'], $usePublicImages);

            $college_logo = '<img src="'.$college_logo_url.'" alt="College Logo" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $college_signature = '<img src="'.$college_signature_url.'" alt="College Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $dean_signature = '<img src="'.$dean_signature_url.'" alt="Dean/CEO Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $admission_manager_signature = '<img src="'.$admission_manager_signature_url.'" alt="Admission Manager Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $student_support_signature = '<img src="'.$student_support_signature_url.'" alt="Student Support Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';

            $enrolledCourseList = '';
            if (! empty($arrStudentEnrolledCourse)) {
                $enrolledCourseList = '<ul>';
                foreach ($arrStudentEnrolledCourse as $value) {
                    $enrolledCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
                }
                $enrolledCourseList .= '</ul>';
            }
            $offeredCourseList = '';
            if (! empty($arrStudentOfferedCourse)) {
                $offeredCourseList = '<ul>';
                foreach ($arrStudentOfferedCourse as $value) {
                    $offeredCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
                }
                $offeredCourseList .= '</ul>';
            }
            $imgArr = [
                'college_logo' => $college_logo,
                'college_signature' => $college_signature,
                'dean_signature' => $dean_signature,
                'admission_manager_signature' => $admission_manager_signature,
                'student_support_signature' => $student_support_signature,
            ];
            $dataArr = $this->dataBind($row, $enrolledCourseList, $offeredCourseList, $imgArr);
            foreach ($dataArr as $key => $value) {
                // TODO::GNG-5159 (Use regex to replace only placeholders that are not inside HTML attributes)
                $pattern = '/(?<!data-mention=")'.preg_quote($key, '/').'(?!")(?![^<]*>)/';
                $content = preg_replace($pattern, $value, $content);
                $subject = str_replace("$key", $value, $subject);
            }

            return [$subject, $content];
        } else {
            return false;
        }
        // return $student;
    }

    public function dataBind($row, $enrolledCourseList, $offeredCourseList, $imgArr)
    {
        return [
            '{AlterEmail1}' => $row['emergency_email'],
            '{AlterEmail2}' => $row['emergency_email'],
            '{CollegeLogo}' => $imgArr['college_logo'],
            '{CollegeEmail}' => $row['college_email'],
            '{Country}' => $row['country_name'],
            '{CountryBirth}' => $row['birth_country'],
            '{CurrentDate}' => date('d-m-Y'),
            // "{CurrentDate}"                 => $this->getCurrentDateTimeWithTimeZone($row['college_timezone'], 'd-m-Y'), //TODO::GN-2333,
            '{DoB}' => date('d-m-Y', strtotime($row['birth_date'])),
            '{DOB}' => date('d-m-Y', strtotime($row['birth_date'])),
            '{DoB Without Stroke}' => '******************',
            '{Email}' => $row['student_email'],
            '{ExpDate}' => date('d-m-Y', strtotime($row['visa_expiry_date'])),
            '{Fax}' => $row['fax'],
            '{Student ID}' => $row['generated_stud_id'],
            '{Student Name}' => $row['first_name'].' '.$row['family_name'],
            '{StudentId}' => $row['generated_stud_id'],
            '{FirstName}' => $row['first_name'],
            '{MiddleName}' => $row['middel_name'],
            '{LastName}' => $row['family_name'],
            '{Gender}' => $row['gender'],
            '{Mobile}' => $row['current_mobile_phone'],
            '{Nationality}' => $row['nationality'],
            '{NickName}' => $row['nickname'],
            '{PassportNo}' => $row['passport_no'],
            '{Phone}' => $row['current_mobile_phone'],
            '{Postcode}' => $row['current_postcode'],
            '{State}' => $row['current_state'],
            '{StreetAddress}' => $row['current_street_name'],
            '{StreetNumber}' => $row['current_street_no'],
            '{UnitDetail}' => $row['current_unit_detail'],
            '{BuildingName}' => $row['current_building_name'],
            '{Suburb}' => $row['current_city'],
            '{Title}' => $row['name_title'],
            '{UserName}' => $row['generated_stud_id'],
            '{VisaType}' => $row['visa_type'],
            '{CourseCode}' => $row['course_code'],
            '{CourseName}' => $row['course_name'],
            '{CollegeRtoCode}' => $row['RTO_code'],
            '{CollegeCircosCode}' => $row['CRICOS_code'],
            '{CollegeLegalName}' => $row['legal_name'],
            '{CollegeName}' => $row['entity_name'],
            '{CollegeSignature}' => $imgArr['college_signature'],
            '{DeanName}' => $row['dean_name'],
            '{DeanSignature}' => $imgArr['dean_signature'],
            '{AdmissionManagerSignature}' => $imgArr['admission_manager_signature'],
            '{StudentSupportSignature}' => $imgArr['student_support_signature'],
            '{CollegeContactPerson}' => $row['contact_person'],
            '{CollegeContactPhone}' => $row['contact_phone'],
            '{CollegeURL}' => $row['college_url'],
            '{CollegeABN}' => $row['college_ABN'],
            '{CollegeFax}' => $row['fax'],
            '{CourseType}' => $row['course_type'],
            '{Campus}' => $row['campus_name'],
            '{StudentType}' => $row['student_type'],
            '{TeacherFirstName}' => $row['teacher_first_name'],
            '{TeacherLastName}' => $row['teacher_last_name'],
            '{TeacherEmail}' => $row['teacher_email'],
            '{TeacherMobile}' => $row['teacher_mobile'],
            '{AgencyName}' => $row['agency_name'],
            '{AgentName}' => $row['agent_name'],
            '{AgentEmail}' => $row['agent_email'],
            '{AgentTelephone}' => $row['agent_telephone'],
            '{EnrolledCourseList}' => $enrolledCourseList,
            '{OfferedCourseList}' => $offeredCourseList,
        ];
    }

    public function addAgentCommissionInfoDetailsData($request)
    {
        $requestData = $request->all();
        $student_course_id2 = $request->student_course_id2;
        $courseId2 = $requestData['course_id'] = $this->studentCourse->getWhereVal(['id' => $request->student_course_id2], 'course_id');
        $objCheckStudentCourse = $this->studentCourse->getWhereFirst(['course_id' => $courseId2, 'student_id' => $request->student_id, 'id' => $student_course_id2], '*');
        $upfrountFeePay = $this->studentInitialPaymentDetails->getWhereSum(['course_id' => $courseId2, 'student_id' => $request->student_id, 'student_course_id' => $student_course_id2, 'payment_status' => 'paid'], 'upfront_fee_to_pay');
        $totalCourse = $objCheckStudentCourse['course_fee'] - $upfrountFeePay;
        /* Check student secound course fee and Transfar Amount */
        if ($totalCourse < $request->amount) {
            //            return ['status' => 'error', 'message' => 'Student course transfar amount is more than course fee. Please check transfer amount.'];
            return ['status' => 'error', 'message' => 'Transfar amount is grater than invoice amount.'];
        }
        $paymentDetailsId = 0;

        if ($student_course_id2 > 0) {
            DB::beginTransaction();
            try {
                if ($request->invoice_type == 1) {
                    $objCheckExistingInvoice = $this->studentInitialPaymentDetails->getWhere(['student_course_id' => $student_course_id2], '*');
                    if (count($objCheckExistingInvoice) == 0) {
                        return ['status' => 'error', 'message' => 'Student invoice is not generated. Please generate it first.'];
                    }
                    $objCheckExistingInvoicePaid = $this->studentInitialPaymentDetails->getWhere(['student_course_id' => $student_course_id2, 'payment_status' => 'unpaid'], '*');
                    if (count($objCheckExistingInvoicePaid) == 0) {
                        return ['status' => 'error', 'message' => 'Student all invoice is paid.'];
                    }
                    // $objRefundAmount1 = StudentInitialPaymentDetails::where('id',$request->selected_invoice_number)->get()->toArray();
                    $objRefundAmount1 = $this->studentInitialPaymentDetails->findData($request->selected_invoice_number);
                    if ($objRefundAmount1[0]['upfront_fee_to_pay'] < $request->amount) {
                        //                        return ['status' => 'error', 'message' => 'Student course transfar amount is more than selected transaction fee. Please check transfer amount.'];
                        return ['status' => 'error', 'message' => 'Transfar amount is grater than invoice amount.'];

                    }
                    $paymentDetailsId = $this->updateTransferPayment($requestData);
                } else {
                    if ($objCheckStudentCourse['course_fee'] == $upfrountFeePay) {
                        return ['status' => 'error', 'message' => 'Student all invoice is paid.'];
                    }
                    $paymentDetailsId = $this->saveTransferPayment($requestData);
                }
                if (! empty($paymentDetailsId)) {

                    $this->saveTransferPaymentTransaction($paymentDetailsId, $requestData);
                    // student agent commitionss refund
                    $this->updateTransferRefundPaymentTransaction($requestData);
                    $this->updateTransfered($requestData);
                    DB::commit();

                    return ['status' => 'success', 'message' => 'Payment Transfered Successfully'];
                } else {
                    return ['status' => 'success', 'message' => 'No Payment transferred'];
                }
            } catch (\Exception $e) {
                DB::rollBack();
                throw new ApplicationException($e->getMessage());
            }
        }
    }

    public function updateTransferPayment($data)
    {

        // $objRefundAmount1 = StudentInitialPaymentDetails::where('id',$data['selected_invoice_number'])->get()->toArray();
        $objRefundAmount1 = $this->studentInitialPaymentDetails->findData($data['selected_invoice_number']);
        $UpfrontFeePay = $data['amount'];
        $selectedInvoice = [];
        $objRefundAmount = $this->studentInitialPaymentDetails->getWhere(['invoice_number' => $objRefundAmount1[0]['invoice_number'], 'payment_status' => 'unpaid'], '*');

        $RefundAmount = [];
        if (! empty($objRefundAmount)) {

            for ($i = 0; $i < count($objRefundAmount); $i++) {

                if ($UpfrontFeePay != 0) {
                    $objRefundAmountUpdate = $this->studentInitialPaymentDetails->findData($objRefundAmount[$i]['id'], '*');
                    $RefundAmount['selectedInvoice'][] = $objRefundAmount[$i]['id'];
                    $paidingUpfrontFeePay = $objRefundAmountUpdate['upfront_fee_to_pay'] - $objRefundAmountUpdate['upfront_fee_pay'];
                    if ($UpfrontFeePay > $paidingUpfrontFeePay) {
                        $UpfrontFeePay = $UpfrontFeePay - $paidingUpfrontFeePay;
                        $RefundAmount['upfront_fee_pay'][] = $paidingUpfrontFeePay;
                        $objRefundAmountToUpdate['upfront_fee_pay'] = $objRefundAmountUpdate['upfront_fee_pay'] + $paidingUpfrontFeePay;
                    } else {
                        $UpfrontFeePay = $UpfrontFeePay;
                        $RefundAmount['upfront_fee_pay'][] = $UpfrontFeePay;
                        $objRefundAmountToUpdate['upfront_fee_pay'] = $objRefundAmountUpdate['upfront_fee_pay'] + $UpfrontFeePay;
                        $UpfrontFeePay = 0;
                    }
                    $objRefundAmountToUpdate['payment_status'] = ($objRefundAmountUpdate['upfront_fee_to_pay'] == $objRefundAmountToUpdate['upfront_fee_pay']) ? 'paid' : 'unpaid';
                    //                    $objRefundAmountToUpdate['payment_status'] = ($objRefundAmountUpdate['upfront_fee_to_pay'] == $objRefundAmount1[$i]['upfront_fee_to_pay']) ? 'paid' : 'unpaid';
                    $objRefundAmountToUpdate['updated_by'] = Auth::user()->id;
                    $this->studentInitialPaymentDetails->update($objRefundAmountToUpdate, $objRefundAmountUpdate['id']);
                }
            }
        }

        return $RefundAmount;
    }

    public function saveTransferPayment($data)
    {

        $studentId = $data['student_id'];
        $student_course_id = $data['student_course_id2'];
        $courseId2 = $this->studentCourse->getWhereVal(['id' => $student_course_id], 'course_id');
        $objAgent = $this->studentCourse->getWhereFirst(['id' => $student_course_id], '*');
        $transferAmount = $this->checkStudentCourseOfTransactionFee($data['amount'], $data['student_course_id2'], $data['student_id']);

        if (count($objAgent) > 0) {
            $agent_id = $objAgent['agent_id'];
            $data['course_id'] = $courseId2;
            $data['invoice_number'] = $data['invoice_number'];
            $data['invoiced_start_date'] = ($data['invoice_start_date'] != '') ? date('Y-m-d', strtotime($data['invoice_start_date'])) : null;
            $due_date = null;
            if ($data['invoice_start_date'] != '') {
                $durations = Config::get('constants.arrCourseDurationType');
                $due_add = $data['paid_duration_text'];
                $due_typ = $durations[$data['paid_duration_day']];
                $due_date = date('Y-m-d', strtotime($data['invoice_start_date'].' +'.$due_add.' '.$due_typ));
            }
            $data['due_date'] = $due_date;
            $data['agent_id'] = $agent_id;
            $data['upfront_fee_to_pay'] = $transferAmount;
            $data['upfront_fee_pay'] = $transferAmount;
            $data['commission_value'] = $data['agent_commission_rate'];
            $commission = ($transferAmount * $data['agent_commission_rate']) / 100;
            $commissionFormat = number_format($commission, 2, '.', '');
            $data['commission'] = $commissionFormat;
            if ($data['GST'] == 'GST') {
                $data['gst_amount'] = number_format($commissionFormat * 10 / 100, 2);
            }
            $data['student_course_id'] = $data['student_course_id2'];
            $data['payment_type'] = 'Schedual';
            $data['payment_status'] = 'paid';
            $data['created_by'] = Auth::user()->id;
            $data['updated_by'] = Auth::user()->id;
            $createId = $this->studentInitialPaymentDetails->create($data);

            $this->updateInvoiceNumber($data['invoice_number']);

            //            $result['selectedInvoice'][]= $createId->id;
            //            $result['upfront_fee_pay'][]= $transferAmount;
            return $createId->id;
        }
    }

    public function checkStudentCourseOfTransactionFee($transferAmountP, $student_course_id2, $student_id)
    {
        $courseId2 = $this->studentCourse->getWhereVal(['id' => $student_course_id2], 'course_id');
        $objCheckStudentCourse = $this->studentCourse->getWhereFirst(['id' => $student_course_id2, 'student_id' => $student_id], '*');
        $objCheckExistingInvoice = $this->studentInitialPaymentDetails->getWhere(['id' => $student_course_id2, 'student_id' => $student_id, 'payment_status' => 'paid'], '*');
        $upFrontFeePay = 0;
        if (! empty($objCheckExistingInvoice)) {
            foreach ($objCheckExistingInvoice as $row) {
                $upFrontFeePay += $row['upfront_fee_to_pay'];
            }
        }
        $totalCourse = $objCheckStudentCourse['course_fee'] - $upFrontFeePay;

        if ($totalCourse > $transferAmountP) {
            $transferAmount = $transferAmountP;
        } elseif ($totalCourse < $transferAmountP) {
            $transferAmount = $totalCourse;
        } else {
            $transferAmount = $transferAmountP;
        }

        return $transferAmount;
    }

    private function updateInvoiceNumber($invoiceExist)
    {
        $invoiceNumber = InvoiceNumber::where('invoice_number', $invoiceExist)->select('*')->first();
        if (! empty($invoiceNumber)) {
            $findInvoiceNumber = $invoiceNumber['invoice_number'];
            $incNumber = sprintf("%'03d", $findInvoiceNumber + 1);
            $objsaveInvoiceNumber = $this->invoiceNumber->find($invoiceNumber['id']);
            $objsaveInvoiceNumberUpdate['invoice_number'] = $incNumber;
            $this->invoiceNumber->update($objsaveInvoiceNumberUpdate, $invoiceNumber['id']);
        }
    }

    public function saveTransferPaymentTransaction($paymentDetailsId, $data)
    {
        $collegeId = Auth::user()->college_id;
        $studentId = $data['student_id'];
        $student_course_id = $data['student_course_id2'];
        $course_id2 = $data['course_id2'] = $this->studentCourse->getWhereVal(['student_id' => $studentId, 'id' => $student_course_id], 'course_id');
        $resceiptNumber = $this->resceiptNumber->getWhereVal(['college_id' => $collegeId], 'resceipt_number');
        $transactionNumber = $this->transactionNumber->getWhereVal(['college_id' => $collegeId], 'transaction_number');

        if (isset($paymentDetailsId['selectedInvoice']) && is_array($paymentDetailsId['selectedInvoice'])) {

            if (! empty($paymentDetailsId['selectedInvoice'])) {
                for ($i = 0; $i < count($paymentDetailsId['selectedInvoice']); $i++) {

                    $data['course_id'] = $course_id2;
                    $data['student_course_id'] = $data['student_course_id2'];
                    $data['initial_payment_detail_id'] = $paymentDetailsId['selectedInvoice'][$i];
                    $data['receipt_no'] = $resceiptNumber;
                    $data['transection_no'] = $transactionNumber;
                    $data['payment_date'] = date('Y-m-d');
                    $data['bank_deposit_date'] = date('Y-m-d');
                    $data['paid_amount'] = $paymentDetailsId['upfront_fee_pay'][$i];
                    $data['deposited_amount'] = $paymentDetailsId['upfront_fee_pay'][$i];
                    $data['agent_bonus'] = 0;
                    $data['bonus_paid_date'] = date('Y-m-d');
                    $data['created_by'] = Auth::user()->id;
                    $data['updated_by'] = Auth::user()->id;
                    $objTransferTransaction = $this->studentInitialPaymentTransaction->create($data);
                    $this->studentPaymentRepository->editTransactionNumber();

                    //        **********************  Student Agent Commission  ******************************
                    $detail_id = $objTransferTransaction->initial_payment_detail_id;
                    $detailData = $this->studentInitialPaymentDetails->getWhereFirst(['id' => $detail_id], ['agent_id', 'invoice_number']);
                    $invoice_number = null;
                    $agent_id = null;

                    if (count($detailData) > 0) {
                        $invoice_number = ($detailData['invoice_number'] != '' && $detailData['invoice_number'] != null) ? $detailData['invoice_number'] : null;
                        $agent_id = ($detailData['agent_id'] != '' && $detailData['agent_id'] != null) ? $detailData['agent_id'] : null;
                    }

                    $data['agent_id'] = $agent_id;
                    $commission_payable = 0;
                    if ($data['agent_commission_rate'] > 0) {
                        $commission_payable = $paymentDetailsId['upfront_fee_pay'][$i] * ($data['agent_commission_rate'] / 100);
                    }

                    $data['commission_payable'] = number_format($commission_payable, 2);
                    if ($data['GST'] == 'GST') {
                        $data['gst_amount'] = number_format(($data['commission_payable'] * 0.10), 2);
                    }

                    $data['CHQ_NO'] = 0;
                    $data['mode'] = $data['payment_mode'];
                    $data['paid_date'] = date('Y-m-d');
                    $data['transaction_no'] = $transactionNumber;

                    return $this->studentAgentCommission->create($data);
                }
            }
        } else {
            //  $objTransferPayment = new StudentInitialPaymentDetails();
            $objCheckStudentCourse = $this->studentCourse->getWhereFirst(['id' => $data['student_course_id2'], 'student_id' => $data['student_id']]);

            $objCheckExistingInvoice = $this->studentInitialPaymentTransaction->getWhere(['course_id' => $course_id2, 'student_course_id' => $student_course_id, 'student_id' => $data['student_id']]);
            $upFrontFeePay = 0;
            if (! empty($objCheckExistingInvoice)) {
                foreach ($objCheckExistingInvoice as $row) {
                    $upFrontFeePay += $row['paid_amount'];
                }
            }
            $totalCourse = $objCheckStudentCourse['course_fee'] - $upFrontFeePay;
            if ($totalCourse > $data['amount']) {
                $transferAmount = $data['amount'];
            } elseif ($totalCourse < $data['amount']) {
                $transferAmount = $totalCourse;
            } else {
                $transferAmount = $data['amount'];
            }

            $data['receipt_no'] = $resceiptNumber;
            $data['transection_no'] = $transactionNumber;
            $data['payment_date'] = date('Y-m-d');
            $data['initial_payment_detail_id'] = $paymentDetailsId;
            $data['student_course_id'] = $data['student_course_id2'];
            $data['paid_amount'] = $transferAmount;
            $data['deposited_amount'] = $transferAmount;
            $data['agent_bonus'] = 0;
            $data['bonus_paid_date'] = date('Y-m-d');
            $objTransferTransaction = $this->studentInitialPaymentTransaction->create($data);
            $this->studentPaymentRepository->editTransactionNumber();

            //        **********************  Student Agent Commission  ******************************
            $detail_id = $objTransferTransaction['initial_payment_detail_id'];
            $detailData = $this->studentInitialPaymentDetails->getWhereFirst(['id' => $detail_id], ['agent_id', 'invoice_number']);
            $invoice_number = null;
            $agent_id = null;
            if (count($detailData) > 0) {
                $invoice_number = ($detailData['invoice_number'] != '' && $detailData['invoice_number'] != null) ? $detailData['invoice_number'] : null;
                $agent_id = ($detailData['agent_id'] != '' && $detailData['agent_id'] != null) ? $detailData['agent_id'] : null;
            }
            $data['transaction_no'] = $transactionNumber;
            $data['invoice_no'] = $invoice_number;
            $data['agent_id'] = $agent_id;

            $commission_payable = 0;
            if ($data['agent_commission_rate'] > 0) {
                $commission_payable = $transferAmount * ($data['agent_commission_rate'] / 100);
            }
            $data['commission_payable'] = number_format($commission_payable, 2);
            if ($data['GST'] == 'GST') {
                $data['gst_amount'] = number_format($data['commission_payable'] * 0.10, 2);
            }
            $data['CHQ_NO'] = 0;
            $data['mode'] = $data['payment_mode'];
            $data['paid_date'] = date('Y-m-d');

            return $this->studentAgentCommission->create($data);
        }
    }

    public function updateTransfered($data)
    {
        $transaction = $this->studentInitialPaymentTransaction->getWhereFirst(['transection_no' => $data['transaction_number'], 'student_id' => $data['student_id'], 'student_course_id' => $data['student_course_id']]);

        if (count($transaction) > 0) {
            $paymentDetailId = $transaction['initial_payment_detail_id'];
            $newAmountRefund = $data['amount'];

            $objRefundAmount = $this->studentInitialPaymentDetails->find($paymentDetailId);
            $objRefundAmountUpdate['upfront_fee_pay'] = $objRefundAmount['upfront_fee_pay'] - $newAmountRefund;
            $objRefundAmountUpdate['payment_status'] = 'unpaid';
            $objRefundAmountUpdate['updated_by'] = Auth::user()->id;

            $this->studentInitialPaymentDetails->update($objRefundAmountUpdate, $paymentDetailId);

            $objSavePaymentRefund = $this->studentInitialPaymentTransaction->find($transaction['id']);
            $objSavePaymentRefundData['amount_refund'] = (($objSavePaymentRefund['amount_refund'] != null) ? $objSavePaymentRefund->amount_refund : 0) + $newAmountRefund;
            $objSavePaymentRefundData['remarks'] = $data['remarks'];

            // Remove transction form old course when amount refund and paid amount is equal

            if ($objSavePaymentRefundData['amount_refund'] == $objSavePaymentRefund['paid_amount']) {
                // StudentInitialPaymentTransaction::where('id',$transaction['id'])->delete();
                $this->studentInitialPaymentTransaction->delete($transaction['id']);
            } else {
                $this->studentInitialPaymentTransaction->update($objSavePaymentRefundData, $transaction['id']);
            }
        }
    }

    public function updateTransferRefundPaymentTransaction($data)
    {

        $transaction = $this->studentPaymentRepository->getInitialPaymentTransactionDetail($data['transaction_number']);
        if ($transaction) {
            $transactionData['agent_commission_refund_amount'] = $transaction[0]['agent_commission_refund_amount'] + number_format((($data['amount'] * $transaction[0]['commission_value']) / 100), 2);
            $transactionData['agent_refunded'] = $transaction[0]['agent_refunded'] + number_format((($data['amount'] * $transaction[0]['commission_value']) / 100), 2);
            $transactionData['agent_refund_date'] = date('Y-m-d');
            $this->studentInitialPaymentTransaction->update($transactionData, $transaction[0]['id']);
        }
        $transactionAgentCommission = $this->studentAgentCommission->getWhere(['transaction_no' => $data['transaction_number']]);
        if ($transactionAgentCommission) {
            $transactionAgentCommissionData['comm_to_refund'] = $transactionAgentCommission[0]['comm_to_refund'] + number_format((($data['amount'] * $transaction[0]['commission_value']) / 100), 2);
            $transactionAgentCommissionData['GST_to_refund'] = 0;
            if ($transactionAgentCommission[0]['GST'] == 'GST') {
                $transactionAgentCommissionData['GST_to_refund'] = number_format(($transactionAgentCommission[0]['comm_to_refund'] * 0.10), 2);
            }
            $transactionAgentCommissionData['remarks'] = 'NEED';
            $transactionAgentCommissionData['refund_amount'] = number_format(($transactionAgentCommission[0]['comm_to_refund'] + $transactionAgentCommissionData['GST_to_refund']), 2);
            $this->studentAgentCommission->update($transactionAgentCommissionData, $transactionAgentCommission[0]['id']);
        }
    }

    public function getPaymentTransferData($request)
    {
        $studentId = $request->student_id;
        $student_course_id = $request->student_course_id;
        $courseId = $this->studentCourse->getWhereVal(['id' => $student_course_id], 'course_id');
        $collegeId = Auth::user()->college_id;

        return [
            'data' => $this->studentPaymentRepository->getInitialPaymentTransactionV2($request, $collegeId, $studentId, $courseId, $student_course_id),
            'total' => $this->studentPaymentRepository->getInitialPaymentTransactionV2($request, $collegeId, $studentId, $courseId, $student_course_id, true),
        ];
    }

    public function addMiscellaneousRequestDetails(AddMiscellaneousPayment $data)
    {
        $postData = $data->toArray();
        $postData['course_id'] = $this->studentCourse->find($postData['student_course_id'])->course_id;
        $postData['created_by'] = Auth::user()->id;
        $postData['updated_by'] = Auth::user()->id;
        $postData['oshc_finish_date'] = (($postData['oshc_duration'] != '' && $postData['oshc_start_date'] != '' && $postData['oshc_start_date'] != null) ? date('Y-m-d', strtotime('+'.$postData['oshc_duration'].' months', strtotime($postData['oshc_start_date']))) : null);
        $postData['transaction_number'] = ($postData['payment_status'] == 'paid') ? $this->transactionNumber->getWhereVal(['college_id' => $postData['college_id']], 'transaction_number') : '';
        $postData['resceipt_number'] = ($postData['payment_status'] == 'paid') ? $this->resceiptNumber->getWhereVal(['college_id' => $postData['college_id']], 'resceipt_number') : '';
        DB::beginTransaction();
        try {
            $insertData = $this->studentMiscellaneousPayment->create($postData);
            if ($insertData) {
                $this->studentPaymentRepository->updateInvoiceNumber($insertData->invoice_number);
                $this->studentPaymentRepository->editTransactionNumber();
                $this->studentPaymentRepository->editResceiptNumber();
            }
            DB::commit();

            return $insertData;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function editMiscellaneousRequestData(AddMiscellaneousPayment $data)
    {
        $postData = $data->toArray();
        $postData['paid_amount'] = ($postData['payment_status'] == 'paid') ? $postData['amount'] : 0;
        $postData['course_id'] = $this->studentCourse->find($postData['student_course_id'])->course_id;
        $postData['updated_by'] = auth()->user()->id;
        unset($postData['resceipt_number']);

        DB::beginTransaction();
        try {
            $res = $this->studentMiscellaneousPayment->update($postData, $postData['id']);
            $objStudentProfileOshc = StudentProfileOshc::updateOrCreate(
                ['student_miscellaneous_primary_id' => $postData['id']],  // Corrected this line
                [
                    'student_miscellaneous_primary_id' => $postData['id'],
                    'college_id' => $postData['college_id'],
                    'remarks' => $postData['remarks'],
                    'card_pickup_date' => $postData['card_pickup_date'],
                    'card_arrival_date' => $postData['card_arrival_date'],
                    'invoice_number' => $postData['invoice_number'],
                ]
            );

            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function generateNewSchedule($postData)
    {
        $request = $postData->toArray();
        $studentCourseId = $request['student_course_id'];
        $studentId = $request['student_id'];

        $studCourseData = StudentCourses::find($studentCourseId);
        if ($request['invoiced_start_date'] > $request['due_date']) {
            return ['status' => 'error', 'message' => 'Invoice start date is greater from due date.'];
        }
        /*if ($request['invoiced_start_date'] < $studCourseData->start_date || $request['due_date'] > $studCourseData->finish_date) {
            return ['status' => 'error', 'message' => 'Invoice date is outside of the course range or invalid.'];
        }*/
        if ($request['due_date'] > $studCourseData->finish_date) {
            return ['status' => 'error', 'message' => 'Invoice due date is greater from the course finish date.'];
        }

        $request['course_id'] = $courseId = $studCourseData->course_id;
        $whereArr = [
            'college_id' => auth()->user()->college_id,
            'student_id' => $studentId,
            'course_id' => $courseId,
            'student_course_id' => $studentCourseId,
        ];
        $arrStudentInitialPayment = StudentInitialPayment::where($whereArr)->first();

        $objStudentsLists = $this->studentPaymentRepository->getAccountPayment($studentCourseId, $studentId);
        $initialPaymentRequestData = $this->setRequestDataForInitialPayment($request, $objStudentsLists[0]);

        DB::beginTransaction();
        try {

            if (! $arrStudentInitialPayment) {
                $arrStudentInitialPayment = $this->studentPaymentRepository->saveStudentInitialPaymentV2($initialPaymentRequestData);
            }

            $arrStudentInitialPayment = StudentInitialPayment::where($whereArr)->first();
            $totalTuitionFee = $arrStudentInitialPayment->tution_fee;

            $existScheduleAmount = StudentInitialPaymentDetails::where($whereArr)->sum('upfront_fee_to_pay');
            $pendingAmount = $totalTuitionFee - $existScheduleAmount;

            if ($request['upfront_fee_to_pay'] <= $pendingAmount) {
                $request['agent_id'] = $initialPaymentRequestData['agent_id'];
                $paymentDetailData = $this->setRequestData($request);
                $this->studentPaymentRepository->generateStudentInitialPaymentDetails($paymentDetailData);
                $returnData = ['status' => 'success', 'message' => 'New Payment Schedule Added'];
            } else {
                $returnData = ['status' => 'error', 'message' => 'Total schedule amount will exceed course actual fee'];
            }

            DB::commit();

            return $returnData;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function setRequestData($request)
    {
        $commissionVal = $request['apllied_commission'];
        $request['invoiced_start_date'] = date('Y-m-d', strtotime($request['invoiced_start_date']));
        $request['due_date'] = date('Y-m-d', strtotime($request['due_date']));
        $request['commission'] = number_format((($request['upfront_fee_to_pay'] * $commissionVal) / 100), 2, '.', '');
        $request['GST'] = isset($request['GST']) ? 'GST' : 'NO GST';
        if ($request['GST'] == 'GST') {
            $request['gst_amount'] = number_format((($request['upfront_fee_to_pay'] * $commissionVal) / 100), 2, '.', '') * 10 / 100;
        }
        $request['commisson_period'] = $commissionVal.'% + '.$request['GST'];

        return $request;
    }

    public function setRequestDataForInitialPayment($request, $objStudentsLists)
    {
        $request['tution_fee'] = $objStudentsLists['course_fee'];
        $request['agent_id'] = $objStudentsLists['agent_id'];
        $request['apllied_commission'] = $request['commission_value'];
        $request['commission_rate'] = $request['commission_value'];
        $request['invoice_due_date'] = date('Y-m-d', strtotime($request['due_date']));
        $request['invoice_start_date'] = date('Y-m-d', strtotime($request['invoiced_start_date']));
        // $request['commisson_period']   = $request['paid_duration_day'];   //TODO::GNG-2874
        $request['GST'] = isset($request['GST']) ? 'GST' : 'NO GST';
        $request['payment_type'] = Config::get('constants.keyForSchedulePaymentType');

        // $request['payment_type']       = '3';
        return $request;
    }

    public function getPaymentDetailsData($request)
    {
        return $this->studentPaymentRepository->getPaymentDetails(Auth::user()->college_id, $request['detailId']);
    }

    public function saveEditStudentSchedule($request)
    {
        $returnData = $this->checkScheduleUpdateValidation($request);
        if ($returnData['status'] == 'error') {
            return $returnData;
        }

        DB::beginTransaction();
        try {
            $returnData = $this->checkScheduleUpdateValidation($request);
            if ($returnData['status'] == 'success') {
                $id = $request['editSchedulePaymentInfoId'];
                $paymentDetailData = $this->setEditRequestData($request);
                //                $payment = StudentInitialPaymentDetails::findOrFail($id);
                //                $payment->fill($paymentDetailData);
                //                /* Add columns not in fillable manually here */
                //                $payment->save();
                // $res = StudentInitialPaymentDetails::where('id', $id)->update($paymentDetailData);
                $res = $this->studentInitialPaymentDetails->update($paymentDetailData, $id);
                $returnData['status'] = 'success';
                $returnData['message'] = 'Payment Schedule Updated SuccessFully';
            }
            DB::commit();

            return $returnData;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function checkScheduleUpdateValidation($data)
    {

        $collageId = Auth::user()->college_id;
        $submitRecoredDetail = $this->studentPaymentRepository->getPaymentDetails($collageId, $data['editSchedulePaymentInfoId']);

        if ($data['upfront_fee_to_pay'] <= 0) {
            return ['status' => 'error', 'message' => 'Amount must be greater than 0.'];
        }

        if ($submitRecoredDetail[0]['upfront_fee_pay'] > $data['upfront_fee_to_pay']) {
            $returnData['status'] = 'error';
            $returnData['message'] = 'Student Already Paid '.$submitRecoredDetail[0]['upfront_fee_pay'];

            return $returnData;
        }

        $studCourseData = StudentCourses::find($data['student_course_id']);
        if ($data['invoiced_start_date'] > $data['due_date']) {
            return ['status' => 'error', 'message' => 'Invoice start date is greater from course finish date.'];
        }
        /*if ($data['invoiced_start_date'] < $studCourseData->start_date || $data['due_date'] > $studCourseData->finish_date) {
            return ['status' => 'error', 'message' => 'Invoice date is outside of the course range or invalid.'];
        }*/
        if ($data['due_date'] > $studCourseData->finish_date) {
            return ['status' => 'error', 'message' => 'Invoice due date is greater from course finish date or invalid.'];
        }

        $invoiceNo = '';
        $student_course_id = $submitRecoredDetail[0]['student_course_id'];
        $otherDetailForThisCourse = $this->studentPaymentRepository->studentPaymentDetailsGetV3($collageId, $student_course_id, $submitRecoredDetail[0]['student_id'], $invoiceNo, true);
        $getCourseDetail = $this->studentPaymentRepository->getAppliedStudentCourseV2($submitRecoredDetail[0]['course_id'], $submitRecoredDetail[0]['student_id'], $student_course_id)->toArray();
        $courseActualFee = ($getCourseDetail) ? $getCourseDetail[0]['course_fee'] : 0;
        /*$courseFinishdate = ($getCourseDetail) ? strtotime($getCourseDetail[0]['finish_date']) : "";
        $invoiceStartDate = strtotime($data['invoiced_start_date']);
        $invoiceDueDate = strtotime($data['due_date']);

        if ($courseFinishdate < $invoiceStartDate || $courseFinishdate < $invoiceDueDate) {
            return ['status' => 'error', 'message' => 'Date is out of course Range.'];
        }*/

        $totalInvoiceAmount = 0;
        $totalAmountWithoutThisTransaction = 0;
        for ($i = 0; $i < count($otherDetailForThisCourse); $i++) {
            $totalInvoiceAmount += $otherDetailForThisCourse[$i]['upfront_fee_to_pay'];
            if ($data['editSchedulePaymentInfoId'] != $otherDetailForThisCourse[$i]['id']) {
                $totalAmountWithoutThisTransaction += $otherDetailForThisCourse[$i]['upfront_fee_to_pay'];
            }
        }
        $currentCourseFees = $totalAmountWithoutThisTransaction + $data['upfront_fee_to_pay'];
        if ($courseActualFee < $currentCourseFees) {
            return ['status' => 'error', 'message' => 'Course Fees is greater than Course actual fees.'];
        }

        return ['status' => 'success', 'message' => 'Course Schedule update successfully.'];
    }

    public function setEditRequestData($request)
    {
        if ($request['payment_status'] == 'unpaid' && $request['upfront_fee_pay'] == 0) {
            $request['agent_id'] = $request['agent_id'] ?? '';
        }
        $request['commission'] = number_format(($request['upfront_fee_to_pay'] * $request['commission_value']) / 100, 2, '.', '');
        $request['GST'] = isset($request['GST']) ? 'GST' : 'NO GST';
        $request['bonus_gst_amount'] = isset($request['GST']) ? ($request['commission_value'] * 10) / 100 : 0;
        $request['gst_amount'] = isset($request['GST']) ? $request['commission'] * 10 / 100 : 0;

        if ($request['upfront_fee_pay'] >= $request['upfront_fee_to_pay']) {
            $paymentStatus = 'paid';
        } elseif ($request['upfront_fee_pay'] > 0) {
            $paymentStatus = 'partially paid';
        } else {
            $paymentStatus = 'unpaid';
        }
        $request['payment_status'] = $paymentStatus;
        unset($request['editSchedulePaymentInfoId']);

        return $request;
    }

    public function revertBackPaymentHistory($request)
    {
        $paymentTransactionId = $request['primaryID'];
        $collegeId = Auth::user()->college_id;
        $pTransData = $this->studentInitialPaymentTransaction->find($paymentTransactionId);

        if ($pTransData) {
            $studentId = $pTransData->student_id;
            $courseId = $pTransData->course_id;
            $studentCourseId = $pTransData->student_course_id;

            if ($pTransData->student_credit > 0) {
                $studInitialPayment = StudentInitialPayment::where(['college_id' => $collegeId, 'student_id' => $studentId])->first();
                if ($studInitialPayment) {
                    $totalCredit = $studInitialPayment->student_credit;
                    if ($totalCredit <= 0 || ($totalCredit - $pTransData->student_credit) < 0) {
                        return ['type' => 'error', 'message' => 'Cannot revert this transaction due to credit amount exceed.'];
                    }
                }
            }

            if ($pTransData->scholarship > 0) {
                $scholarshipResult = StudentScholarship::where(['college_id' => $collegeId, 'student_course_id' => $studentCourseId])
                    ->selectRaw('SUM(used_scholarship_amount) as used_scholarship, SUM(scholarship_amount) as total_scholarship')
                    ->first();

                $availableScholarshipAmount = $scholarshipResult->total_scholarship - $scholarshipResult->used_scholarship;
                if ($availableScholarshipAmount < $pTransData->scholarship) {
                    return ['type' => 'error', 'message' => 'Cannot revert this transaction due to scholarship amount exceed.'];
                }
            }

        } else {
            return ['type' => 'error', 'message' => 'Transaction data not found.'];
        }

        DB::beginTransaction();
        try {
            $pTransData->reversed = 0;
            $pTransData->reverse_comment = null;
            $pTransData->save();

            $this->reverseBackPayment($pTransData->initial_payment_detail_id, $pTransData->paid_amount);

            $this->updatePaidScholarshipAmount($collegeId, $studentId, $courseId, $pTransData->scholarship);

            $this->updateStudentCreditAmount($collegeId, $studentId, $pTransData->student_credit);

            $getPaymentDetail = $this->studentInitialPaymentDetails->find($paymentTransactionId);
            if ($getPaymentDetail && ! empty($getPaymentDetail->invoice_number)) {
                StudentAgentCommission::where([
                    'college_id' => $collegeId,
                    'invoice_no' => $getPaymentDetail->invoice_number,
                ])->update([
                    'is_reversed' => 0,
                    'comm_to_refund' => 0,
                    'GST_to_refund' => 0,
                ]);
            }
            /*$result = StudentAgentCommission::where([
                            'college_id' => $collegeId,
                            'transaction_no' => $pTransData->transection_no
                        ])->update([
                            'is_reversed'       => 0,
                            'comm_to_refund'    => 0,
                            'GST_to_refund'     => 0
                        ]);*/
            DB::commit();

            return ['type' => 'success', 'data' => []];
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);

            return ['type' => 'error', 'message' => $e->getMessage()];
            // throw new ApplicationException($e->getMessage());
        }

        /*$reverseBackTransaction = $this->reverseBackTransaction($paymentTransactionId);
        $this->reverseBackPayment($reverseBackTransaction['initialPaymentDetailId'], $reverseBackTransaction['paidAmount']);
        //reverse Scholarship amount student-payment-summary
        $this->updatePaidScholarshipAmount($collegeId, $reverseBackTransaction['studentId'], $reverseBackTransaction['courseId'], $reverseBackTransaction['scholarship']);
        return StudentAgentCommission::where('college_id', '=', $collegeId)->where('transaction_no', '=', $reverseBackTransaction['transectionNo'])->update(['is_reversed' => 0, 'comm_to_refund' => 0, 'GST_to_refund' => 0]);*/
    }

    public function reverseBackTransaction($paymentTransactionId)
    {

        // $objReverseTransaction = StudentInitialPaymentTransaction::find($paymentTransactionId);
        $objReverseTransaction = $this->studentInitialPaymentTransaction->find($paymentTransactionId);
        $objReverseTransaction->reversed = 0;
        $objReverseTransaction->reverse_comment = null;
        $objReverseTransaction->save();

        return [
            'initialPaymentDetailId' => $objReverseTransaction->initial_payment_detail_id,
            'paidAmount' => $objReverseTransaction->paid_amount,
            'scholarship' => $objReverseTransaction->scholarship,
            'creditAmount' => $objReverseTransaction->student_credit,
            'studentId' => $objReverseTransaction->student_id,
            'courseId' => $objReverseTransaction->course_id,
            'transectionNo' => $objReverseTransaction->transection_no,
        ];
    }

    // reverse Back Transaction from student-payment-summary
    public function reverseBackPayment($initialPaymentDetailId, $paidAmount)
    {
        // $objReversePayment = StudentInitialPaymentDetails::find($initialPaymentDetailId);
        $objReversePayment = $this->studentInitialPaymentDetails->find($initialPaymentDetailId);
        $objReversePayment->upfront_fee_pay = $objReversePayment->upfront_fee_pay + $paidAmount;

        if ($objReversePayment->upfront_fee_pay >= $objReversePayment->upfront_fee_to_pay) {
            $objReversePayment->payment_status = 'paid';
        } elseif ($objReversePayment->upfront_fee_pay > 0) {
            $objReversePayment->payment_status = 'partially paid';
        }

        $objReversePayment->save();
    }

    public function saveRevertBackTransactionAdditionalPaymentService($request)
    {
        DB::beginTransaction();
        try {
            $result = $this->studentServicePayment->update(['is_reversed' => $request['is_reversed'], 'reversed_comment' => $request['reversed_comment']], $request['id']);
            DB::commit();

            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    // TODO:: GNG-2007
    public function deleteAgentCommissionDetail(StudentAgentCommission $agentCommission)
    {
        DB::beginTransaction();
        try {
            // $res = StudentAgentCommission::where(['transaction_no' => $agentCommission['xero_invoice_id']])->delete();
            $res = StudentAgentCommission::where('id', $agentCommission->id)->delete();
            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteStudentPaymentDetail(StudentInitialPaymentDetails $studentPaymentDetail)
    {
        // TODO::GNG-2007
        $transactionDataArr = StudentInitialPaymentTransaction::where('initial_payment_detail_id', $studentPaymentDetail->id)->get();

        DB::beginTransaction();
        try {
            StudentAgentCommission::where([
                'college_id' => $studentPaymentDetail->college_id,
                'student_course_id' => $studentPaymentDetail->student_course_id,
                'invoice_no' => $studentPaymentDetail->invoice_number,
            ])->delete();

            // All transactions
            foreach ($transactionDataArr as $tData) {
                // Delete agent commission data
                /*StudentAgentCommission::where([
                    'college_id'        => $tData->college_id,
                    'student_course_id' => $tData->student_course_id,
                    'transaction_no'    => $tData->transection_no
                ])->delete();*/

                // Delete miscellaneous amount data
                StudentMiscellaneousPayment::where([
                    'college_id' => $tData->college_id,
                    'student_course_id' => $tData->student_course_id,
                    'transaction_number' => $tData->transection_no,
                ])->forceDelete();

                // Manage scholarship amount data
                $this->studentPaymentRepository->deletePaidScholarshipAmount($tData->college_id, $tData->student_id, $tData->course_id, $tData->scholarship);

                // Delete payment transaction data
                // StudentInitialPaymentTransaction::where('id', $tData->id)->delete();
                $this->studentInitialPaymentTransaction->delete($tData->id);
            }

            // Delete payment detail row
            $res = StudentInitialPaymentDetails::where('id', $studentPaymentDetail->id)->forceDelete();
            // $res = StudentInitialPaymentDetails::where('id', $studentPaymentDetail->id)->delete();
            // $res = $this->studentInitialPaymentDetails->delete($studentPaymentDetail->id);
            // XeroInvoice::where('xero_invoice_id', $studentPaymentDetail->xero_invoice_id)->delete();

            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteStudentMiscellaneousPaymentDetail(StudentMiscellaneousPayment $miscellaneousPayment)
    {
        DB::beginTransaction();
        try {
            // $res = StudentMiscellaneousPayment::where('transaction_number', $studentPaymentDetail->xero_invoice_id)->delete();
            $res = StudentMiscellaneousPayment::where('id', $miscellaneousPayment->id)->forceDelete();
            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteStudentServicePaymentDetail(StudentServicePayment $studentServicePayment)
    {
        DB::beginTransaction();
        try {
            // additional_services_id
            $serviceData = StudentServicePayment::find($studentServicePayment->id);
            if ($serviceData) {
                StudentAdditionalServiceRequest::where('id', $serviceData->additional_services_id)->delete();
                // $res = $serviceData->forceDelete();
            }
            $res = StudentServicePayment::where('id', $studentServicePayment->id)->forceDelete();
            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getSelectedCourseInvoiceData($request)
    {
        DB::beginTransaction();
        try {
            /*$result = StudentInitialPaymentDetails::where([
                            'student_course_id' => $request['selectedCourse'],
                            'student_id'        => $request['student_id'],
                            'payment_status'    => "unpaid"
                        ])->get()->toArray();*/
            $result = $this->studentInitialPaymentDetails->getWhere([
                'student_course_id' => $request['selectedCourse'],
                'student_id' => $request['student_id'],
                'payment_status' => 'unpaid',
            ]);

            $result1 = [];
            for ($i = 0; $i < count($result); $i++) {
                $result[$i]['amtToPay'] = $result[$i]['upfront_fee_to_pay'] - $result[$i]['upfront_fee_pay'];
                $date = $result[$i]['due_date'];
                $amt = $result[$i]['upfront_fee_to_pay'];
                $result1[$i]['Name'] = 'Invoice No: '.$result[$i]['formatted_invoice_number'].' - '.'$'.$amt;
                $result1[$i]['Id'] = $result[$i]['id'];
            }
            DB::commit();

            return $result1;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getSelectedTransactionDetailsData($request)
    {
        DB::beginTransaction();
        try {
            // $result = StudentInitialPaymentDetails::where('id',$request['id'])->get()->toArray();
            $result = $this->studentInitialPaymentDetails->findData($request['id']);
            DB::commit();

            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getRecommendedCommissionRateData($request)
    {
        $tmpCourse = StudentCourses::where('id', $request['selectedCourse'])->get()->toArray();
        $data = AgentCommissionModel::where([
            'rto_agent_commission.college_id' => Auth::user()->college_id,
            'rto_agent_commission.agent_id' => $tmpCourse[0]['agent_id'],
            'rto_agent_commission.commission_period' => '1',
        ])
            ->where('rto_agent_commission.rate_valid_from', '<', $tmpCourse[0]['start_date'])
            ->where('rto_agent_commission.rate_valid_to', '>', $tmpCourse[0]['start_date'])
            ->select(DB::raw("CONCAT(commission,' %+',gst) AS Name"), DB::raw("CONCAT(commission,' %+',gst) AS Id"))
            ->groupBy('Name')
            ->get()
            ->toArray();
        if (empty($data)) {
            $data[] = ['Id' => '0% + No GST', 'Name' => '0% + No GST'];
        }

        return $data;
    }

    private function checkValidStatusForDelete($objectData, $isForceDelete = false)
    {

        if ($isForceDelete && ! is_null($objectData->xeroInvoice)) {
            $invoice = $objectData->xeroInvoice;

            return
                ($invoice?->xero_invoice_status == null && $invoice?->xero_invoice_id == null) ||
                ($invoice?->xero_invoice_status === Invoice::STATUS_DRAFT) ||
                ($invoice?->xero_invoice_status === Invoice::STATUS_AUTHORISED);
        }

        return
            (is_null($objectData->xeroInvoice) && is_null($objectData->xeroCreditNote)) ||
            ($objectData->xeroInvoice?->xero_invoice_status === Invoice::STATUS_DRAFT) ||
            ($objectData->xeroCreditNote?->xero_invoice_status === Invoice::STATUS_DRAFT);

        /*if($objectData->xeroInvoice == NULL || $objectData->xeroInvoice->xero_invoice_status == Invoice::STATUS_DRAFT){
            return true;
        }elseif ($objectData->xeroCreditNote == NULL || $objectData->xeroCreditNote->xero_invoice_status == Invoice::STATUS_DRAFT){
            return true;
        }
        return false;*/
    }

    public function convertKendoFormat($resArr, $param1 = 'Id', $param2 = 'Name', $isSameValue = false)
    {
        $data = [];
        if ($resArr) {
            // unset($resArr['']);
            foreach ($resArr as $key => $value) {
                $data[] = [
                    $param1 => ($isSameValue) ? $value : $key,
                    $param2 => $value,
                ];
            }
        }

        return $data;
    }

    public function getGeneratePaymentScheduleFormData($request)
    {
        return [
            'arrCommissionPeriod' => $this->convertKendoFormat(Config::get('constants.arrCommissionPeriod')),
            'arrPaymentType' => $this->convertKendoFormat(Config::get('constants.arrPaymentType')),
            'arrPaidDuration' => $this->convertKendoFormat(Config::get('constants.arrPaidDuration')),
            'invoiceDueDays' => InvoiceSetting::getValueFromKey(InvoiceSetting::DAYS_AFTER_START_DATE)->value ?? 0,
            'oshcProvider' => $this->studentCourseService->getOshcProvider($request),
            'paymentModeList' => $this->paymentMode->getAll(['id as Id', 'name as Name'], 'name'),
        ];
    }

    public function getUpfrontFeeFormData($request)
    {
        return [
            'arrCommissionPeriod' => $this->convertKendoFormat(Config::get('constants.arrCommissionPeriod')),
            'arrPaidDuration' => $this->convertKendoFormat(Config::get('constants.arrPaidDuration')),
            'paymentModeList' => $this->paymentMode->getAll(['id as Id', 'name as Name'], 'name'),

        ];
    }
}
