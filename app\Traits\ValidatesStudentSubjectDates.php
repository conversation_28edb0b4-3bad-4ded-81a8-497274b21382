<?php

namespace App\Traits;

use App\Model\v2\StudentCourses;
use App\Model\v2\StudentSubjectEnrolment;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model as EloquentModel;
use Illuminate\Validation\ValidationException;

trait ValidatesStudentSubjectDates
{
    public static function bootValidatesStudentSubjectDates()
    {

        // Add validation before creating
        static::creating(function (EloquentModel $model) {
            $model->validateDates();
        });

        // Add validation before updating
        static::updating(function (EloquentModel $model) {
            $model->validateDates();
        });
    }

    /**
     * Validate date fields
     *
     * @throws ValidationException
     */
    protected function validateDates()
    {
        // Check if course is HigherEd and skip validation if it is
        if ($this->student_course_id != null) {
            $isHigherEd = StudentCourses::checkCourseIsHigherEd($this->student_course_id);

            if ($isHigherEd) {
                return;
            }
        }

        $errors = [];

        // Validate last_assessment_approved_date
        $this->validateLastAssessmentDate($errors);

        // Validate activity_start_date relationships
        $this->validateActivityStartDate($errors);

        // Validate activity_start_date when outcome is 85 (NYS)
        $this->validateOutcome85WithFutureStartDate($errors);

        // Validate that future Activity Start Date is not allowed for non-85 outcomes
        $this->validateNoFutureStartDateForNon85($errors);

        // Validate new start must be after last attempt's end date
        $this->validateNewStartDateAfterLastAttemptEnd($errors);

        // Validate funding source national
        $this->validateFundingSourceNational($errors);

        // Validate outcome WD requires attended hours
        $this->validateOutcomeWDAttendedHours($errors);

        // Validate outcome 70 cannot be used when RTO has outcome 41 records
        $this->validateOutcome70WithRTOClosure($errors);

        // If there are any errors, throw validation exception
        if (! empty($errors)) {
            throw ValidationException::withMessages($errors);
        }
    }

    /**
     * Validate that last_assessment_approved_date is required and not greater than today
     * when final_outcome is C, NYC, CT, or RPL
     *
     * @param  array  &$errors
     */
    protected function validateLastAssessmentDate(&$errors)
    {

        $finalOutcome = $this->final_outcome;
        $lastAssessmentDate = $this->last_assessment_approved_date;

        // Check if final_outcome is one of the specified values
        if (in_array($finalOutcome, ['C', 'NYC', 'CT', 'RPL'])) {
            // Activity End Date is required
            if (empty($lastAssessmentDate)) {
                $errors['last_assessment_approved_date'] = 'Activity End Date is required when final outcome is C, NYC, CT, or RPL.';

                return;
            }

            // Validate that Activity End Date is not greater than today
            try {
                $dateToCheck = Carbon::parse($lastAssessmentDate);
                $today = Carbon::today();

                if ($dateToCheck->greaterThan($today)) {
                    $errors['last_assessment_approved_date'] = 'Activity End Date cannot be greater than today\'s date when final outcome is C, NYC, CT, or RPL.';
                }
            } catch (\Exception $e) {
                // Invalid date format
                $errors['last_assessment_approved_date'] = 'Invalid date format for Activity End Date.';
            }
        }
    }

    /**
     * When there are previous attempts for the same student/course/subject/unit,
     * the new Activity Start Date must be greater than the last attempt's Activity Finish Date.
     *
     * @param  array  $errors
     */
    protected function validateNewStartDateAfterLastAttemptEnd(&$errors)
    {
        // Require key identifiers and a start date
        if (empty($this->student_id) || empty($this->course_id) || empty($this->subject_id) || empty($this->unit_id)) {
            return;
        }
        if (empty($this->activity_start_date)) {
            return;
        }

        // Parse the new start date
        try {
            $newStartDate = Carbon::parse($this->activity_start_date);
        } catch (\Exception $e) {
            $errors['activity_start_date'] = 'Invalid date format for Activity Start Date.';

            return;
        }

        // Find the most recent prior attempt for this exact combination
        $attemptQuery = StudentSubjectEnrolment::where([
            'college_id' => $this->college_id,
            'student_id' => $this->student_id,
            'course_id' => $this->course_id,
            'subject_id' => $this->subject_id,
            'unit_id' => $this->unit_id,
        ]);
        if (! empty($this->id)) {
            $attemptQuery->where('id', '!=', $this->id);
        }

        $lastAttempt = $attemptQuery->orderBy('subject_attempt', 'DESC')
            ->first(['activity_finish_date']);

        if (! $lastAttempt) {
            return; // No prior attempts
        }

        $lastFinish = $lastAttempt->activity_finish_date;
        if (empty($lastFinish)) {
            return; // Prior attempt has no finish date to compare
        }

        try {
            $lastAttemptEnd = Carbon::parse($lastFinish);
        } catch (\Exception $e) {
            return; // If prior finish date is invalid, skip this check
        }

        if (! $newStartDate->gt($lastAttemptEnd)) {
            $errors['activity_start_date'] = 'New start date is not greater than the end date of the last attempt.';
        }
    }

    /**
     * Validate that activity_start_date is not greater than
     * last_assessment_approved_date and activity_finish_date
     *
     * @param  array  &$errors
     */
    protected function validateActivityStartDate(&$errors)
    {
        $activityStartDate = $this->activity_start_date;
        $activityFinishDate = $this->activity_finish_date;
        $lastAssessmentDate = $this->last_assessment_approved_date;

        if (empty($activityStartDate)) {
            return;
        }

        if (empty($lastAssessmentDate)) {
            return;
        }

        try {
            $startDate = Carbon::parse($activityStartDate);

            // Validate against activity_finish_date
            if (! empty($activityFinishDate)) {
                $finishDate = Carbon::parse($activityFinishDate);

                if ($startDate->greaterThan($finishDate)) {
                    $errors['activity_start_date'] = 'Activity Start Date cannot be greater than Finish Date.';
                }

                // New validation: Duration should not exceed 5 years
                $diffInYears = $startDate->diffInYears($finishDate);
                if ($diffInYears > 5) {
                    $errors['activity_finish_date'] = 'The time between Activity Start Date and Activity End Date must not exceed 5 years.';
                }
            }

            // Validate against last_assessment_approved_date
            if (! empty($lastAssessmentDate)) {
                $assessmentDate = Carbon::parse($lastAssessmentDate);

                if ($startDate->greaterThan($assessmentDate)) {
                    $errors['activity_start_date'] = 'Activity Start Date cannot be greater than Activity End Date.';
                }
            }
        } catch (\Exception $e) {
            // Invalid date format
            $errors['activity_start_date'] = 'Invalid date format for Activity Start Date.';
        }
    }

    /**
     * Validate that when final_outcome is 85 (NYS - Not Yet Started),
     * activity_start_date must be a future date (greater than today)
     *
     * @param  array  &$errors
     */
    protected function validateOutcome85WithFutureStartDate(&$errors)
    {
        $finalOutcome = $this->final_outcome;

        // Accept both literal code 'NYS' and numeric/string '85'
        if (in_array($finalOutcome, ['NYS', '85', 85], true)) {
            $activityStartDate = $this->activity_start_date;

            if (empty($activityStartDate)) {
                return;
            }

            try {
                $startDate = Carbon::parse($activityStartDate)->startOfDay();
                $today = Carbon::today();

                if (! $startDate->greaterThan($today)) {
                    $errors['activity_start_date'] = 'Activity Start Date must be a future date when final outcome is 85 (Not Yet Started).';
                }
            } catch (\Exception $e) {
                $errors['activity_start_date'] = 'Invalid date format for Activity Start Date.';
            }
        }
    }

    /**
     * Validate that Activity Start Date cannot be a future date
     * unless final_outcome is 85 (NYS - Not Yet Started)
     *
     * @param  array  &$errors
     */
    protected function validateNoFutureStartDateForNon85(&$errors)
    {
        $finalOutcome = $this->final_outcome;

        // If outcome is 85 (NYS), this rule does not apply
        if (in_array($finalOutcome, ['NYS', '85', 85], true)) {
            return;
        }

        $activityStartDate = $this->activity_start_date;
        if (empty($activityStartDate)) {
            return;
        }

        try {
            $startDate = Carbon::parse($activityStartDate)->startOfDay();
            $today = Carbon::today();

            if ($startDate->greaterThan($today)) {
                $errors['activity_start_date'] = 'Activity Start Date cannot be a future date unless final outcome is 85 (Not Yet Started).';
            }
        } catch (\Exception $e) {
            $errors['activity_start_date'] = 'Invalid date format for Activity Start Date.';
        }
    }

    /**
     * Validate that if funding_identifier is not blank,
     * funding_source_nat must be 13
     *
     * @param  array  &$errors
     */
    protected function validateFundingSourceNational(&$errors)
    {
        $fundingIdentifier = $this->funding_identifier;
        $fundingSourceNat = $this->funding_source_nat;

        // If specific funding identifier is not blank, funding source national must be 13
        if (! empty($fundingIdentifier) && $fundingSourceNat != 13) {
            $errors['funding_source_nat'] = 'Funding Source - National must be 13 when Specific Funding Identifier is provided.';
        }
    }

    /**
     * Validate that if final_outcome is 'WD',
     * attended_hour in related StudentUnitEnrollment must be greater than 0
     *
     * @param  array  &$errors
     */
    protected function validateOutcomeWDAttendedHours(&$errors)
    {
        // Check if final_outcome is 'WD'
        if ($this->final_outcome == 'WD') {
            // Get related StudentUnitEnrollment record
            $unitEnrollment = \App\Model\v2\StudentUnitEnrollment::where('student_subject_enrollment_id', $this->id)->first();

            // Check if unit enrollment exists and attended_hour is not greater than 0
            if (! $unitEnrollment || empty($unitEnrollment->attended_hour) || $unitEnrollment->attended_hour <= 0) {
                $errors['final_outcome'] = 'Attended hours must be greater than 0 when outcome is WD.';
            }
        }
    }

    /**
     * Validate that a record cannot be reported with outcome identifier 70 (continuing)
     * if the training organisation has other records with outcome identifier 41
     * (Withdrawn/discontinued due to RTO closure)
     *
     * @param  array  &$errors
     */
    protected function validateOutcome70WithRTOClosure(&$errors)
    {
        // Check if final_outcome is 'CE' (70 - Continuing enrolment)
        if ($this->final_outcome == 'CE') {
            // Check if there are any records in the same college with final_outcome 'INC' (41 - Incomplete due to RTO closure)
            $hasRTOClosureRecords = static::where('college_id', $this->college_id)
                ->where('student_id', '=', $this->student_id)
                ->where('final_outcome', 'INC')
                ->exists();

            if ($hasRTOClosureRecords) {
                $errors['final_outcome'] = 'A record cannot be reported with a continuing outcome (Outcome identifier - national = 70) if the training organisation has other records with an outcome \'41 - Withdrawn/discontinued due to RTO closure\'. When the RTO has closed down, all unfinished training should be reported with outcome \'41\'.';
            }
        }
    }
}
