<?php

namespace App\Traits;

use App;
use App\Helpers\Helpers;
use App\Model\v2\SmtpSetup;
use App\Model\v2\Staff;
use App\Model\v2\Student;
use App\Repositories\StudentProfileCommonRepository;
use Auth;
use File;
use Illuminate\Http\Request;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Support\Facades\Config;
use Mail;
use Support\Services\UploadService;

trait SendEmailTrait
{
    use CommonTrait;

    // const EMAIL_TYPE_GENERIC = 'generic';
    // const EMAIL_TYPE_COURSE = 'course';

    protected $studentProfileCommonRepository;

    public function __construct(
        StudentProfileCommonRepository $studentProfileCommonRepository
    ) {
        $this->studentProfileCommonRepository = $studentProfileCommonRepository;
    }

    public function setStudentProfileCommonRepository(StudentProfileCommonRepository $studentProfileCommonRepository)
    {
        $this->studentProfileCommonRepository = $studentProfileCommonRepository;
    }

    public function getCourseType()
    {
        return 'course';
    }

    public function getGenericType()
    {
        return 'generic';
    }

    public function sendMailToStudentTrait($request)
    {
        // Retrieve input data from the request
        $studentIDs = (! is_array($request->student_id)) ? explode(',', $request->student_id) : $request->student_id;
        $courseId = $request->input('course_id');
        $studentCourseId = $request->input('student_course_id');
        $emailType = ($request->input('email_type')) ? $request->input('email_type') : $this->getCourseType();

        // Retrieve student course information based on course or student course ID
        if (! empty($studentCourseId)) {
            $studentArrInfo = $this->studentProfileCommonRepository->getStudentCoursesById($studentCourseId, $studentIDs);
        } else {
            $studentArrInfo = $this->studentProfileCommonRepository->getStudentCoursesByCourse($courseId, $studentIDs);
        }
        [$attachment, $attachmentLogData, $existAttachment, $existAttachmentLogData] = $this->getAttachmentsData($request);
        $sendCount = 0;
        $notSendCount = 0;
        $failReason = '';
        $checkMailListener = false;
        $escapeCourseCheck = true;
        foreach ($studentArrInfo as $studRow) {
            $flag = false;
            $rowStudentId = $studRow->id;
            $rowCourseId = '';
            // Check If selected Course Found or not
            if (((count($studRow->studentCourses) > 0) && ($emailType == $this->getCourseType())) || ($emailType == $this->getGenericType())) {
                // if ($courseId == $studRow->studentCourses[0]->course_id || $studentCourseId == $studRow->studentCourses[0]->id || $escapeCourseCheck) {
                if ($emailType == $this->getCourseType()) {
                    $rowCourseId = $studRow->studentCourses[0]->course_id;
                }

                // if (!$checkMailListener) {
                //     $failReason = $this->checkTestMail();
                //     $checkMailListener = true;
                // }
                // if ($checkMailListener && empty($failReason)) {

                $mailData = $this->prepareStudentMailData($rowStudentId, $rowCourseId, $studRow, $request, $existAttachment, $attachment, $emailType);
                // dd($mailData);
                $mailData['attachmentLogData'] = array_merge($attachmentLogData, $existAttachmentLogData);

                $this->sendEmailEvent($mailData, $request);
                $sendCount++;
                // } else {
                //     $notSendCount++;
                //     $emailStatusData['data']['failData'][] = $this->getFailDataArr($studRow, $failReason);
                // }
                $flag = true;
                // }
            }
            if (! $flag) {
                $notSendCount++;
                $emailStatusData['data']['failData'][] = $this->getFailDataArr($studRow, "Course not found for $studRow->first_name");
            }
        }
        // Prepare response data
        $emailStatusData['data']['success_msg'] = 'Email notification sent successfully for '.$sendCount.(($sendCount > 1) ? ' Student(s)' : ' student');
        $emailStatusData['data']['fail_msg'] = ($notSendCount == 0) ? '' : 'Email notification not sent for '.$notSendCount.(($notSendCount > 1) ? ' Student(s)' : ' student');

        return $emailStatusData;
    }

    public function sendMailForStudentOrientationTrait($request)
    {
        $studentCourseIDs = explode(',', $request->student_course_id);
        $studentCourseInfo = $this->studentCoursesRepository->with(['student', 'course', 'agent'])->whereIn('id', $studentCourseIDs)->get();

        return $this->sendMailForStudentOrientationAndCommunication($request, $studentCourseInfo, 'Student', 'Orientation');
    }

    public function sendMailForStudentCommunicationTrait($request)
    {
        $studentCourseIDs = explode(',', $request->student_course_id);
        $studentCourseInfo = $this->studentCoursesRepository->with(['student'])->whereIn('id', $studentCourseIDs)->get();

        return $this->sendMailForStudentOrientationAndCommunication($request, $studentCourseInfo, 'Student', 'Communication');
    }

    private function sendMailForStudentOrientationAndCommunication($request, $resData, $userRole = 'Student', $module = '')
    {
        [$attachment, $attachmentLogData, $existAttachment, $existAttachmentLogData] = $this->getAttachmentsData($request);

        $contentData['attachment'] = $attachment;
        $contentData['attachmentLog'] = $attachmentLogData;
        $contentData['email_subject'] = ($request->input('email_subject') != '') ? $request->input('email_subject') : "$userRole $module";

        $failReason = $this->checkTestMail();
        if ($failReason) {
            return ['success_msg' => '', 'fail_msg' => $failReason, 'statusData' => []];
        }

        $sendCount = 0;
        $notSendCount = 0;
        $sendStatus = [];
        foreach ($resData as $res) {
            $studRow = $res->student;
            $mailData = $this->prepareStudentMailData($res->student_id, $res->course_id, $studRow, $request, $existAttachment, $attachment);
            $mailData['attachmentLogData'] = array_merge($attachmentLogData, $existAttachmentLogData);

            try {
                $this->sendEmailEvent($mailData, $request);
                $sendCount++;
                $sendStatus[] = $this->getSendDataArr($studRow, 'Success', 'Send Successfully');
                if ($module == 'Orientation') {
                    $this->studentCoursesRepository->update(['is_orientation_email_send' => 1], $res->id);
                }
            } catch (\Exception $e) {
                $notSendCount++;
                $sendStatus[] = $this->getSendDataArr($studRow, 'Fail', $e->getMessage());
            }
        }

        return [
            'success_msg' => 'Email sent successfully for '.$sendCount.(($sendCount > 1) ? ' Student(s)' : ' student'),
            'fail_msg' => ($notSendCount == 0) ? '' : 'Email sent fail for '.$notSendCount.(($notSendCount > 1) ? ' Student(s)' : ' student'),
            'statusData' => $sendStatus,
        ];
    }

    public function sendMailForStaffCommunicationTrait($request)
    {
        $staffIDs = explode(',', $request->staff_id);
        $staffInfo = Staff::whereIn('id', $staffIDs)->get();

        $emailCC = ($request->input('email_cc') != '') ? explode(',', $request->input('email_cc')) : [];
        $emailBCC = ($request->input('email_bcc') != '') ? explode(',', $request->input('email_bcc')) : [];
        $replyToEmail = $request->input('reply_to_email') ?? '';

        [$attachment, $attachmentLogData, $existAttachment, $existAttachmentLogData] = $this->getAttachmentsData($request);

        $contentData['attachment'] = $attachment;
        $contentData['attachmentLog'] = $attachmentLogData;
        $contentData['email_subject'] = ($request->input('email_subject') != '') ? $request->input('email_subject') : 'Student Orientation';

        $sendCount = 0;
        $notSendCount = 0;
        $sendStatus = [];
        $emailStatusData = [];
        $checkMailListener = false;

        foreach ($staffInfo as $staff) {
            $flag = false;
            $failReason = '';
            if (! $checkMailListener) {
                $failReason = $this->checkTestMail();
                $checkMailListener = true;
            }
            if ($checkMailListener && empty($failReason)) {
                [$emailSubject, $emailContent] = $this->getStaffMailContent($staff->id, $request->email_subject, $request->email_content);
                $mailData = [
                    'id' => $staff->id,
                    'to' => $staff->email,
                    'from' => $request->email_from,
                    'cc' => $emailCC,
                    'bcc' => $emailBCC,
                    'replyTo' => $replyToEmail,
                    'subject' => $emailSubject,
                    'attachFile' => array_merge($existAttachment, $attachment),
                    'body' => $emailContent,
                    'attachmentLogData' => array_merge($attachmentLogData, $existAttachmentLogData),
                ];
                $this->sendEmailEvent($mailData, $request);
                $flag = true;
            }

            if ($flag) {
                $sendStatus[] = $this->getSendDataArr($staff, $flag, 'Send Successfully');
                $sendCount++;
            } else {
                $notSendCount++;
                $sendStatus[] = $this->getSendDataArr($staff, $flag, $failReason);
            }
        }

        $emailStatusData['success_msg'] = 'Email sent successfully for '.$sendCount.(($sendCount > 1) ? ' Staff(s)' : ' Staff');
        $emailStatusData['fail_msg'] = ($notSendCount == 0) ? '' : 'Email sent fail for '.$notSendCount.(($notSendCount > 1) ? ' Staff(s)' : ' Staff');
        $emailStatusData['statusData'] = $sendStatus;

        return $emailStatusData;
    }

    private function getSendDataArr($studRow, $status, $description)
    {
        return [
            'id' => $studRow['id'],
            'secure_id' => encryptIt($studRow['id']),
            'name' => $studRow['first_name'].' '.$studRow['family_name'],
            'profile_pic' => $this->getStudentProfilePicPath($studRow['id'], $studRow['profile_picture'], 'small'),
            'status' => ($status) ? 'Success' : 'Fail',
            'description' => $description,
        ];
    }

    private function getAttachmentsData($request)
    {
        // Retrieve existing attachments
        $emailExistAttachmentIdArr = ($request->input('existing_attachment_id') != '') ? explode(',', $request->input('existing_attachment_id')) : [];
        [$existAttachment, $existAttachmentLogData] = $this->retrieveExistingAttachments($emailExistAttachmentIdArr, $request->input('email_template_id'));

        // Retrieve attachment files
        $attachment = $attachmentLogData = [];
        if ($request->file('attachment_file')) {
            [$attachment, $attachmentLogData] = $this->mailAttachment($request, true);
        }

        return [$attachment, $attachmentLogData, $existAttachment, $existAttachmentLogData];
    }

    private function checkLetterTestMail($payload)
    {
        $tempMailData = Config::get('constants.testMailData');
        $tempMailData['college_id'] = $payload->college_id;
        $res = $this->sendLetterSmtpTestMail($tempMailData);
        if ($res['status']) {
            return '';
        } else {
            return $res['message'];
        }
    }

    private function sendLetterSmtpTestMail($mailData)
    {
        $collegeId = $mailData['college_id'];
        $mail = SmtpSetup::getSMTPDetail();
        if (($mail) && ($mail->status)) {
            $this->setSMTP();
        }
        $fromEmail = $mail && $mail->status ? $mail->email : config('mail.from.address');
        $fromEmailName = $mail && $mail->status ? $mail->name : config('mail.from.name');
        $mailData['from'] = $fromEmail;
        $mailData['fromName'] = $fromEmailName;
        $page = $mailData['page'] ?? [];
        $data = $mailData['data'] ?? [];
        try {
            ini_set('memory_limit', '1024M');
            ini_set('max_execution_time', 180); // 3 minutes

            Mail::send($page, $data, function ($message) use ($mailData) {
                $message->from(($mailData['from'] = ! '' ? $mailData['from'] : '<EMAIL>'), $mailData['fromName']);
                $message->to($mailData['to']);
                $message->subject($mailData['subject']);
                $message->html($mailData['body']);
            });
            $result = '';
        } catch (\Exception  $exception) {
            $errorMessage = $exception->getMessage();
            $errorCode = $exception->getCode();
            $contentText = (isset($mailData['data']['content'])) ? $mailData['data']['content'] : (isset($mailData['body']) ? $mailData['body'] : '');
            $arrFailMail = [
                'sender' => ($mailData['from'] != '') ? $mailData['from'] : '<EMAIL>',
                'receiver' => $mailData['to'],
                'subject' => $mailData['subject'],
                'content' => $contentText,
                'error_message' => $errorMessage,
                'error_code' => $errorCode,
            ];
            $this->studentProfileCommonRepository->saveFailedEmails($collegeId, $arrFailMail);

            return ['status' => false, 'message' => $errorMessage];
        }
        if ($result == '') {
            return ['status' => true, 'message' => 'Email sent successfully'];
        } else {
            return ['status' => false, 'message' => 'Email is not send successfully.'];
        }
    }

    private function setSMTP()
    {
        $config = SmtpSetup::where('status', 1)->first(); // Adjust the query to suit your needs
        if ($config) {
            // Set mail configuration
            Config::set('mail.mailers.smtp.transport', 'smtp');
            Config::set('mail.mailers.smtp.host', $config->host);
            Config::set('mail.mailers.smtp.port', $config->port);
            Config::set('mail.mailers.smtp.username', $config->username);
            Config::set('mail.mailers.smtp.password', $config->password);
            Config::set('mail.mailers.smtp.encryption', $config->encryption);
            Config::set('mail.from.address', $config->email);
            Config::set('mail.from.name', $config->name);
        }
    }

    private function checkTestMail()
    {
        $tempMailData = Config::get('constants.testMailData');
        $res = $this->sendSmtpTestMail($tempMailData);
        if ($res['status']) {
            return '';
        } else {
            return $res['message'];
        }
    }

    private function retrieveExistingAttachments($emailExistAttachmentIdArr, $emailTemplateId)
    {
        $existAttachment = [];
        $existAttachmentLogData = [];
        if (count($emailExistAttachmentIdArr) > 0) {
            $existFilePath = Config::get('constants.uploadFilePath.Templates');
            $existDestinationPath = Helpers::changeRootPath($existFilePath, $emailTemplateId);
            $docList = $this->studentProfileCommonRepository->emailTemplateDocModel($emailExistAttachmentIdArr);
            foreach ($docList as $doc) {
                $existAttachment[] = $existDestinationPath['view'].$doc['file'];
                $existAttachmentLogData[$doc['file']] = $existDestinationPath['view'].$doc['file'];
            }
        }

        return [$existAttachment, $existAttachmentLogData];
    }

    private function prepareStudentMailData($studentId, $courseId, $studRow, $request, $existAttachment, $attachment, $emailType = 'course')
    {
        $emailCC = ($request->input('email_cc') != '') ? explode(',', $request->input('email_cc')) : [];
        $emailBCC = ($request->input('email_bcc') != '') ? explode(',', $request->input('email_bcc')) : [];
        $replyToEmail = $request->input('reply_to_email') ?? '';

        [$convertedSubject, $convertedData] = $this->getStudentMailContent($studentId, $courseId, $request->email_content, $request->email_subject, $emailType);

        return [
            'id' => $studentId,
            'to' => $studRow->email,
            'from' => $request->email_from,
            'cc' => $emailCC,
            'bcc' => $emailBCC,
            'replyTo' => $replyToEmail,
            'subject' => $convertedSubject,
            'body' => $convertedData,
            'attachFile' => array_merge($existAttachment, $attachment),
        ];
    }

    private function getStudentMailContent($studentId, $courseId, $content, $subject, $emailType)
    {
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);
        if ($emailType == $this->getCourseType()) {
            $arrStudentCourse = $this->studentProfileCommonRepository->getStudentCoursesEmailContent($studentId, $courseId);
        } else {
            $arrStudentCourse = $this->studentProfileCommonRepository->getStudentEmailContent($studentId);
        }

        $arrStudentEnrolledCourse = $this->studentProfileCommonRepository->getArrayStudentEnrolledCourseName($studentId);
        $arrStudentOfferedCourse = $this->studentProfileCommonRepository->getArrayStudentOfferedCourseName($studentId);
        if (! empty($arrStudentCourse)) {
            $row = $arrStudentCourse[0];
            $domain = url('/');
            $basePath = $destinationPath['view'];
            // $college_logo_url = $domain . str_replace('\\', "/", $basePath) . $row['college_logo'];

            $usePublicImages = $row['allow_public_images'] ?? true;
            $college_logo_url = $this->getUploadedFileUrl($basePath.$row['college_logo'], $usePublicImages, true);
            $college_signature_url = $this->getUploadedFileUrl($basePath.$row['college_signature'], $usePublicImages, true);
            $dean_signature_url = $this->getUploadedFileUrl($basePath.$row['dean_signature'], $usePublicImages, true);
            $admission_manager_signature_url = $this->getUploadedFileUrl($basePath.$row['admission_manager_signature'], $usePublicImages, true);
            $student_support_signature_url = $this->getUploadedFileUrl($basePath.$row['student_support_signature'], $usePublicImages, true);

            $college_logo = '<img src="'.$college_logo_url.'" alt="College Logo" style="height: auto; width: 150px;padding-left: 49px; padding-top: 20px;"  />';
            $college_signature = '<img src="'.$college_signature_url.'" alt="College Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $dean_signature = '<img src="'.$dean_signature_url.'" alt="Dean/CEO Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $admission_manager_signature = '<img src="'.$admission_manager_signature_url.'" alt="Admission Manager Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $student_support_signature = '<img src="'.$student_support_signature_url.'" alt="Student Support Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';

            $enrolledCourseList = '';
            if (! empty($arrStudentEnrolledCourse)) {
                $enrolledCourseList = '<ul>';
                foreach ($arrStudentEnrolledCourse as $value) {
                    $enrolledCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
                }
                $enrolledCourseList .= '</ul>';
            }
            $offeredCourseList = '';
            if (! empty($arrStudentOfferedCourse)) {
                $offeredCourseList = '<ul>';
                foreach ($arrStudentOfferedCourse as $value) {
                    $offeredCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
                }
                $offeredCourseList .= '</ul>';
            }
            $imgArr = [
                'college_logo' => $college_logo,
                'college_signature' => $college_signature,
                'dean_signature' => $dean_signature,
                'admission_manager_signature' => $admission_manager_signature,
                'student_support_signature' => $student_support_signature,
            ];
            // dd($imgArr);
            $dataArr = $this->dataBindForStudent($row, $enrolledCourseList, $offeredCourseList, $imgArr);
            foreach ($dataArr as $key => $value) {
                // TODO::GNG-5159 (Use regex to replace only placeholders that are not inside HTML attributes)
                $pattern = '/(?<!data-mention=")'.preg_quote($key, '/').'(?!")(?![^<]*>)/';
                $content = preg_replace($pattern, $value, $content);
                $subject = str_replace("$key", $value, $subject);
            }

            return [$subject, $content];
        } else {
            return false;
        }
        // return $student;
    }

    private function getStaffMailContent($staffId, $emailSubject, $emailContent)
    {
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);

        $arrStaffData = (new Staff)->getStaffEmailContent($staffId);

        if (! empty($arrStaffData)) {
            $row = $arrStaffData[0];
            // $domain = env('APP_URL');
            $domain = url('/');

            $basePath = $destinationPath['view'];

            $usePublicImages = $row['allow_public_images'] ?? true;
            $college_logo_url = $this->getUploadedFileUrl($basePath.$row['college_logo'], $usePublicImages);
            $college_signature_url = $this->getUploadedFileUrl($basePath.$row['college_signature'], $usePublicImages);
            $dean_signature_url = $this->getUploadedFileUrl($basePath.$row['dean_signature'], $usePublicImages);
            $admission_manager_signature_url = $this->getUploadedFileUrl($basePath.$row['admission_manager_signature'], $usePublicImages);
            $student_support_signature_url = $this->getUploadedFileUrl($basePath.$row['student_support_signature'], $usePublicImages);

            $college_logo = '<img src="'.$college_logo_url.'" alt="College Logo" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $college_signature = '<img src="'.$college_signature_url.'" alt="College Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $dean_signature = '<img src="'.$dean_signature_url.'" alt="Dean/CEO Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $admission_manager_signature = '<img src="'.$admission_manager_signature_url.'" alt="Admission Manager Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $student_support_signature = '<img src="'.$student_support_signature_url.'" alt="Student Support Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';

            $dataArr = [
                '{AlterEmail1}' => '',
                '{AlterEmail2}' => '',
                //  "{CollegeEmail}"        => $row['coe_name'],
                '{CollegeLogo}' => $college_logo,
                '{CollegeEmail}' => $row['college_email'],
                '{Country}' => $row['country_name'],
                '{CountryBirth}' => '',
                '{CurrentDate}' => date('d-m-Y'),
                '{DoB}' => '',
                '{DOB}' => '',
                '{DoB Without Stroke}' => '******************',
                '{Email}' => $row['staff_email'],
                '{ExpDate}' => '',
                '{Fax}' => $row['fax'],
                '{StudentId}' => '',
                '{FirstName}' => $row['first_name'],
                '{MiddleName}' => '',
                '{LastName}' => $row['last_name'],
                '{Gender}' => '',
                '{Mobile}' => $row['mobile'],
                '{Nationality}' => $row['nationality'],
                '{NickName}' => '',
                '{PassportNo}' => '',
                '{Phone}' => $row['phone'],
                '{Postcode}' => $row['postcode'],
                '{State}' => $row['state'],
                '{StreetAddress}' => $row['address'],
                '{StreetNumber}' => '',
                '{UnitDetail}' => '',
                '{BuildingName}' => '',
                '{Suburb}' => $row['city_town'],
                '{Title}' => $row['name_title'],
                '{UserName}' => $row['staff_name'],
                '{VisaType}' => '',
                '{CourseCode}' => '',
                '{CourseName}' => '',
                '{CollegeRtoCode}' => $row['RTO_code'],
                '{CollegeCircosCode}' => $row['CRICOS_code'],
                '{CollegeLegalName}' => $row['legal_name'],
                '{CollegeName}' => $row['entity_name'],
                '{CollegeSignature}' => $college_signature,
                '{DeanName}' => $row['dean_name'],
                '{DeanSignature}' => $dean_signature,
                '{AdmissionManagerSignature}' => $admission_manager_signature,
                '{StudentSupportSignature}' => $student_support_signature,
                '{CollegeContactPerson}' => $row['contact_person'],
                '{CollegeContactPhone}' => $row['contact_phone'],
                '{CollegeURL}' => $row['college_url'],
                '{CollegeABN}' => $row['college_ABN'],
                '{CollegeFax}' => $row['fax'],
                '{CourseType}' => '',
                '{Campus}' => '',
                '{StudentType}' => '',
                '{TeacherFirstName}' => $row['teacher_first_name'],
                '{TeacherLastName}' => $row['teacher_last_name'],
                '{TeacherEmail}' => $row['teacher_email'],
                '{TeacherMobile}' => $row['teacher_mobile'],
                '{AgencyName}' => '',
                '{AgentName}' => '',
                '{AgentEmail}' => '',
                '{AgentTelephone}' => '',
                '{EnrolledCourseList}' => '',
                '{OfferedCourseList}' => '',
                '{CourseStartDate}' => '',
                '{CourseEndDate}' => '',
                '{CourseDuration}' => '',
                '{StudentContactEmail}' => '',
                '{StudentAlternateEmail}' => '',
                '{StudentEmergencyEmail}' => '',
            ];

            foreach ($dataArr as $key => $value) {
                $emailContent = str_replace("$key", $value, $emailContent);
                $emailSubject = str_replace("$key", $value, $emailSubject);
            }

            return [$emailSubject, $emailContent];
        } else {
            return false;
        }
    }

    private function mailAttachment($request, $viewFlag = false)
    {
        $emailAttachments = $request->file('attachment_file');
        $filePath = Config::get('constants.uploadFilePath.TempMailAttachment');
        $destinationPath = Helpers::changeRootPath($filePath);

        $savedFileName = [];
        $mailFileName = [];
        $mailLogFileName = [];
        $counts = 0;
        $imageGet = 0;

        foreach ($emailAttachments as $emailAttachment) {
            $originalName = $emailAttachment->getClientOriginalName();
            $filename = date('YmdHsi').'-'.$originalName;
            $savedFileName[] = $filename;
            if ($imageGet == 0) {
                if (! is_dir($destinationPath['default'])) {
                    File::makeDirectory($destinationPath['default'], 0777, true, true);
                }
                // $emailAttachment->move($destinationPath['default'], $filename);
                $upload_success = UploadService::uploadAs($destinationPath['view'], $emailAttachment, $filename);
                info('file uploaded form email attachment', [$upload_success]);

                $mailFileName[] = $destinationPath['view'].$savedFileName[$counts];
                if ($viewFlag) {
                    $mailLogFileName[$originalName] = $destinationPath['view'].$savedFileName[$counts];
                }
                $counts++;
            }
        }

        if ($viewFlag) {
            return [$mailFileName, $mailLogFileName];
        }

        return $mailFileName;
    }

    private function getFailDataArr($studRow, $reason)
    {
        return [
            'id' => $studRow->id,
            'name' => $studRow->first_name.' '.$studRow->family_name,
            'profile_pic' => $this->getStudentProfilePicPath($studRow->id, $studRow->profile_picture, 'small'),
            'reason' => $reason,
        ];
    }

    private function sendSmtpTestMail($mailData)
    {
        if (! empty(Auth()->guard('student')->user())) {
            $this->loginUser = Auth()->guard('student')->user();
        }
        if (! empty(Auth()->guard('teacher')->user())) {
            $this->loginUser = Auth()->guard('teacher')->user();
        }
        if (! empty(Auth()->guard('agent')->user())) {
            $this->loginUser = Auth()->guard('agent')->user();
        }
        if (! empty(Auth::user())) {
            $this->loginUser = Auth::user();
        }
        $collegeId = (isset($this->loginUser->college_id) ? $this->loginUser->college_id : 0);
        $mail = SmtpSetup::get()->first();
        $mailData['from'] = $mail->email;
        $mailData['fromName'] = $mail->name;
        $page = $mailData['page'] ?? [];
        $data = $mailData['data'] ?? [];
        try {
            ini_set('memory_limit', '1024M');
            ini_set('max_execution_time', 180); // 3 minutes
            Mail::send($page, $data, function ($message) use ($mailData) {
                $message->from(($mailData['from'] = ! '' ? $mailData['from'] : '<EMAIL>'), $mailData['fromName']);
                $message->to($mailData['to']);
                $message->subject($mailData['subject']);
                $message->html($mailData['body']);
            });
            $result = '';
        } catch (\Exception  $exception) {
            $errorMessage = $exception->getMessage();
            $errorCode = $exception->getCode();
            $contentText = (isset($mailData['data']['content'])) ? $mailData['data']['content'] : (isset($mailData['body']) ? $mailData['body'] : '');
            $arrFailMail = [
                'sender' => ($mailData['from'] != '') ? $mailData['from'] : '<EMAIL>',
                'receiver' => $mailData['to'],
                'subject' => $mailData['subject'],
                'content' => $contentText,
                'error_message' => $errorMessage,
                'error_code' => $errorCode,
            ];
            //    $objFailedEmail=new FailedEmails();
            //    $objFailedEmail->saveFailedEmails($collegeId,$arrFailMail);
            $this->studentProfileCommonRepository->saveFailedEmails($collegeId, $arrFailMail);

            return ['status' => false, 'message' => $errorMessage];
        }
        if ($result == '') {
            return ['status' => true, 'message' => 'Email sent successfully'];
        } else {
            return ['status' => false, 'message' => 'Email is not send successfully.'];
        }
    }

    private function dataBindForStudent($row, $enrolledCourseList, $offeredCourseList, $imgArr)
    {
        return [

            '{AlterEmail1}' => $row['emergency_email'],
            '{AlterEmail2}' => $row['emergency_email'],
            '{CollegeLogo}' => $imgArr['college_logo'],
            '{CollegeEmail}' => $row['college_email'],
            '{Country}' => $row['country_name'],
            '{CountryBirth}' => $row['birth_country'],
            // "{CurrentDate}"         => date('d-m-Y'),
            '{CurrentDate}' => $this->getCurrentDateTimeWithTimeZone($row['college_timezone'], 'd-m-Y'), // TODO::GN-2333
            '{DoB}' => date('d-m-Y', strtotime($row['birth_date'])),
            '{DOB}' => date('d-m-Y', strtotime($row['birth_date'])),
            '{DoB Without Stroke}' => '******************',
            '{Email}' => $row['student_email'],
            '{ExpDate}' => date('d-m-Y', strtotime($row['visa_expiry_date'])),
            '{Fax}' => $row['fax'],
            '{Student ID}' => $row['generated_stud_id'],
            '{Student Name}' => $row['first_name'].' '.$row['family_name'],
            '{StudentId}' => $row['generated_stud_id'],
            '{FirstName}' => $row['first_name'],
            '{MiddleName}' => $row['middel_name'],
            '{LastName}' => $row['family_name'],
            '{Gender}' => $row['gender'],
            '{Mobile}' => $row['current_mobile_phone'],
            '{Nationality}' => $row['nationality'],
            '{NickName}' => $row['nickname'],
            '{PassportNo}' => $row['passport_no'],
            '{Phone}' => $row['current_mobile_phone'],
            '{Postcode}' => $row['current_postcode'],
            '{State}' => $row['current_state'],
            '{StreetAddress}' => $row['current_street_name'],
            '{StreetNumber}' => $row['current_street_no'],
            '{UnitDetail}' => $row['current_unit_detail'],
            '{BuildingName}' => $row['current_building_name'],
            '{Suburb}' => $row['current_city'],
            '{Title}' => $row['name_title'],
            '{UserName}' => $row['generated_stud_id'],
            '{VisaType}' => $row['visa_type'],
            '{CourseCode}' => $row['course_code'] ?? '',
            '{CourseName}' => $row['course_name'] ?? '',
            '{CollegeRtoCode}' => $row['RTO_code'],
            '{CollegeCircosCode}' => $row['CRICOS_code'],
            '{CollegeLegalName}' => $row['legal_name'],
            '{CollegeName}' => $row['entity_name'],
            '{CollegeSignature}' => $imgArr['college_signature'],
            '{DeanName}' => $row['dean_name'],
            '{DeanSignature}' => $imgArr['dean_signature'],
            '{AdmissionManagerSignature}' => $imgArr['admission_manager_signature'],
            '{StudentSupportSignature}' => $imgArr['student_support_signature'],
            '{CollegeContactPerson}' => $row['contact_person'],
            '{CollegeContactPhone}' => $row['contact_phone'],
            '{CollegeURL}' => $row['college_url'],
            '{CollegeABN}' => $row['college_ABN'],
            '{CollegeFax}' => $row['fax'],
            '{CourseType}' => $row['course_type'] ?? '',
            '{Campus}' => $row['campus_name'] ?? '',
            '{StudentType}' => $row['student_type'],
            '{TeacherFirstName}' => $row['teacher_first_name'],
            '{TeacherLastName}' => $row['teacher_last_name'],
            '{TeacherEmail}' => $row['teacher_email'],
            '{TeacherMobile}' => $row['teacher_mobile'],
            '{AgencyName}' => $row['agency_name'] ?? '',
            '{AgentName}' => $row['agent_name'] ?? '',
            '{AgentEmail}' => $row['agent_email'] ?? '',
            '{AgentTelephone}' => $row['agent_telephone'] ?? '',
            '{EnrolledCourseList}' => $enrolledCourseList,
            '{OfferedCourseList}' => $offeredCourseList,
            '{CourseStartDate}' => (isset($row['start_date'])) ? date('d-m-Y', strtotime($row['start_date'])) : '',
            '{CourseEndDate}' => (isset($row['finish_date'])) ? date('d-m-Y', strtotime($row['finish_date'])) : '',
            '{CourseDuration}' => (isset($row['total_weeks'])) ? $row['total_weeks'].' Weeks' : '',
            '{StudentContactEmail}' => $row['personalEmail'],
            '{StudentAlternateEmail}' => $row['AlternateEmail'],
            '{StudentEmergencyEmail}' => $row['emergency_email'],
        ];
    }

    private function sendEmailEvent($mailData, $request)
    {
        return event(new \App\Events\SendStudentMailEvent($mailData, $request));
        /*$event = event(new \App\Events\SendStudentMailEvent($mailData, $request));
        return optional($event[0])->success ?? false;*/
    }
}
