<template>
    <UserViewComponent
        :store="store"
        :tabs="tabs"
        :show-arrows="true"
        :has-profile-header-actions="true"
    >
        <template #tab-panel-assign_units="{ tab, index, isActive }">
            <AssignUnitsTabContent :store="store" />
        </template>
        <!--        <template #tab-panel-timesheet="{ tab, index, isActive }">-->
        <!--            <TimesheetTabContent :store="store" />-->
        <!--        </template>-->
        <template #tab-panel-leave_info="{ tab, index, isActive }">
            <LeaveInfoTabContent :store="store" />
        </template>
        <template #tab-panel-document="{ tab, index, isActive }">
            <DocumentsTabContent :store="store" />
        </template>
        <template #tab-panel-timetable="{ tab, index, isActive }">
            <TimetableTabContent :store="store" />
        </template>
        <template #profile-header-actions>
            <StaffEmailForm :data="store.formData" />
        </template>
    </UserViewComponent>
</template>

<script setup>
import UserViewComponent from '@spa/modules/users/UserViewComponent.vue';
import { usePage } from '@inertiajs/vue3';
import { computed, onMounted } from 'vue';
import { useTeacherStore } from '@spa/stores/modules/teacher/useTeacherStore.js';
import AssignUnitsTabContent from '@spa/modules/users/trainers/partials/AssignUnitsTabContent.vue';
import TimesheetTabContent from '@spa/modules/users/trainers/partials/TimetableTabContent.vue';
import LeaveInfoTabContent from '@spa/modules/users/trainers/partials/LeaveInfoTabContent.vue';
import DocumentsTabContent from '@spa/modules/users/trainers/partials/DocumentsTabContent.vue';
import TimetableTabContent from '@spa/modules/users/trainers/partials/TimetableTabContent.vue';
import Button from '@spa/components/Buttons/Button.vue';
import StaffEmailForm from '@spa/modules/staffemail/StaffEmailForm.vue';

const page = usePage();
const store = useTeacherStore();

const staffId = computed(() => page.props.params.id);

const tabs = [
    {
        title: 'Overview',
        name: 'overview',
    },
    {
        title: 'Login & Access',
        name: 'login_access',
    },
    {
        title: 'Roles & Permissions',
        name: 'roles_permissions',
    },
    {
        title: 'Activity Log',
        name: 'activity_log',
    },
    {
        title: 'Profile Details',
        name: 'profile_details',
    },
    {
        title: 'Assign Units',
        name: 'assign_units',
    },
    // {
    //     title: 'Timesheet',
    //     name: 'timesheet',
    // },
    {
        title: 'Timetable',
        name: 'timetable',
    },
    {
        title: 'Document',
        name: 'document',
    },
    {
        title: 'Leave Info',
        name: 'leave_info',
    },
];

onMounted(() => {
    store.fetchDataById(staffId.value);
});
</script>
<style scoped></style>
