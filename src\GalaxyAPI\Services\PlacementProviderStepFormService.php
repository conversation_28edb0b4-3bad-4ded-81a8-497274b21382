<?php

namespace GalaxyAPI\Services;

use App\Model\v2\VpmsPlacementProvider;
use App\Users;
use GalaxyAPI\Enums\UserStatusEnum;
use GalaxyAPI\Interfaces\UserTypeStepFormInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class PlacementProviderStepFormService implements UserTypeStepFormInterface
{
    public function __construct(protected UserService $userService) {}

    public function map(string $step, array $data, Model $model): Model
    {
        if ($step === 'review') {
            return $model;
        }

        /** @var VpmsPlacementProvider $model */
        return match ($step) {
            'user_info' => $this->saveUserInfoStep($model, $data),
            'address_postal' => $this->saveAddressAndPostalStep($model, $data),
            default => throw new \InvalidArgumentException("Unsupported step: $step for Staff"),
        };
    }

    private function saveUserInfoStep(VpmsPlacementProvider $model, array $data)
    {
        $user = null;
        $collegeId = Auth::user()->college_id;
        if (! $model->user_id && isset($data['email'])) {
            $user = Users::where('email', $data['email'])->first();
            if (! $user) {
                $userData = [
                    'college_id' => $collegeId,
                    'name' => trim(($data['name'] ?? '')),
                    'username' => $data['email'],
                    'email' => $data['email2'],
                    'phone' => $data['phone'] ?? null,
                    'mobile' => $data['mobile'] ?? null,
                    'role_id' => 18,
                    'password' => Hash::make(Str::random(12)),
                    'status' => UserStatusEnum::PENDING->value,
                ];
                $user = Users::create($userData);
            }
            $model->user_id = $user->id;

        }
        $file = $data['user_image'] ?? null;
        if ($file) {
            $this->userService->uploadProfile($user->id, $file);
        }

        $model->fill([
            'provider_name' => $data['name'] ?? null,
            'provider_code' => $data['agent_code'] ?? null,
            'first_name' => $data['first_name'] ?? null,
            'last_name' => $data['last_name'] ?? null,
            'email' => $data['email'] ?? null,
            'contact_number' => $data['mobile'] ?? null,
            'web_url' => $data['website'] ?? null,
            'categories' => $data['category'] ?? null,
        ]);

        if (! $model->id) {
            $model->created_by = auth()->id();
        }
        if (! $model->exists) {
            $model->created_by = auth()->id();
        }
        $model->updated_by = auth()->id();

        return $model;
    }

    private function saveAddressAndPostalStep(VpmsPlacementProvider $model, array $data)
    {
        $model->fill([
            'address_line_1' => $data['residential_address'] ?? null,
            'city' => $data['residential_city'] ?? null,
            'state' => $data['residential_state'] ?? null,
            'postcode' => $data['residential_postcode'] ?? null,
        ]);

        $model->updated_by = auth()->id();

        return $model;
    }
}
