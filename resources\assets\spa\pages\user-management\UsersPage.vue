<script setup>
import Layout from '@spa/pages/Layouts/Layout';
import { Head, usePage } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import { computed } from 'vue';
import StaffsListComponent from '@spa/modules/users/staffs/StaffsListComponent.vue';
import AgentsListComponent from '@spa/modules/users/agents/AgentsListComponent.vue';
import EmployersListComponent from '@spa/modules/users/employers/EmployersListComponent.vue';
import ServiceProvidersListComponent from '@spa/modules/users/service-providers/ServiceProvidersListComponent.vue';
import PlacementProvidersListComponent from '@spa/modules/users/placement-providers/PlacementProvidersListComponent.vue';
import TrainersListComponent from '@spa/modules/users/trainers/TrainersListComponent.vue';
import AgentStaffsListComponent from '@spa/modules/users/agents-staff/AgentStaffsListComponent.vue';
import StudentsListComponent from '@spa/modules/users/students/StudentsListComponent.vue';
import { useUserAuth } from '@spa/stores/modules/useUserAuth.js';

const $page = usePage();
const userAuth = useUserAuth();

const activeTab = computed(() => {
    const path = $page.url;
    const slug = path.split('/').pop();
    if (!isNaN(Number(slug))) {
        return 'team-members';
    }
    return slug || 'team-members';
});

const tabs = [
    {
        name: 'Team Members(Staffs)',
        slug: 'team-members',
        route: route('spa.manage-users.team-members'),
        hidden: !userAuth.can(userAuth.Access.STAFF_LIST_ACCESS),
    },
    {
        name: 'Trainers(Teachers)',
        slug: 'trainers',
        route: route('spa.manage-users.trainers'),
        hidden: !userAuth.can(userAuth.Access.TRAINERS_LIST_ACCESS),
    },
    {
        name: 'Agent Staffs',
        slug: 'agent-staffs',
        route: route('spa.manage-users.agent-staffs'),
        hidden: !userAuth.can(userAuth.Access.AGENT_STAFFS_LIST_ACCESS),
    },
    {
        name: 'Education Agency(Agents)',
        slug: 'agents',
        route: route('spa.manage-users.agents'),
        hidden: !userAuth.can(userAuth.Access.AGENT_LIST_ACCESS),
    },
    {
        name: 'Employers',
        slug: 'employers',
        route: route('spa.manage-users.employers'),
        hidden: !userAuth.can([userAuth.Access.EMPLOYERS_LIST_ACCESS]),
    },
    {
        name: 'Service Providers',
        slug: 'service-providers',
        route: route('spa.manage-users.service-providers'),
        hidden: !userAuth.can(userAuth.Access.SERVICE_PROVIDERS_ACCESS),
    },
    {
        name: 'Placement Providers',
        slug: 'placement-providers',
        route: route('spa.manage-users.placement-providers'),
        hidden: !userAuth.can(userAuth.Access.PLACEMENT_PROVIDER_ACCESS),
    },
    {
        name: 'Students',
        slug: 'students',
        route: route('spa.manage-users.students'),
        hidden: !userAuth.can(userAuth.Access.STUDENTS_LIST_ACCESS),
    },
];
const getUser = computed(() => {
    return window.authUser;
});
</script>
<template>
    <Layout
        :no-spacing="true"
        :loading="false"
        :title="'Manage Users'"
        :show-header="true"
        :show-tabs="true"
        :tabs="tabs"
        :active-tab="activeTab"
    >
        <Head title="Manage Users" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Manage Users" :back="false" />
        </template>
        <div class="flex h-full flex-col px-8 py-6">
            <StaffsListComponent
                v-if="
                    activeTab === 'team-members' && userAuth.can(userAuth.Access.STAFF_LIST_ACCESS)
                "
            />
            <TrainersListComponent
                v-if="
                    activeTab === 'trainers' && userAuth.can(userAuth.Access.TRAINERS_LIST_ACCESS)
                "
            />
            <AgentsListComponent
                v-if="activeTab === 'agents' && userAuth.can(userAuth.Access.AGENT_LIST_ACCESS)"
            />
            <AgentStaffsListComponent
                v-if="
                    activeTab === 'agent-staffs' &&
                    userAuth.can(userAuth.Access.AGENT_STAFFS_LIST_ACCESS)
                "
            />
            <EmployersListComponent
                v-if="
                    activeTab === 'employers' && userAuth.can(userAuth.Access.EMPLOYERS_LIST_ACCESS)
                "
            />
            <ServiceProvidersListComponent
                v-if="
                    activeTab === 'service-providers' &&
                    userAuth.can(userAuth.Access.SERVICE_PROVIDERS_ACCESS)
                "
            />
            <PlacementProvidersListComponent
                v-if="
                    activeTab === 'placement-providers' &&
                    userAuth.can(userAuth.Access.PLACEMENT_PROVIDER_ACCESS)
                "
            />
            <StudentsListComponent
                v-if="
                    activeTab === 'students' && userAuth.can(userAuth.Access.STUDENTS_LIST_ACCESS)
                "
            />
        </div>
    </Layout>
</template>

<style scoped></style>
