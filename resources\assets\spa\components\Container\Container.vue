<template>
    <div :class="containerClasses">
        <div class="space-y-4" v-if="showHeader || showTabs">
            <!-- Header slot -->
            <div class="px-8 pt-8" v-if="$slots.header || showHeader">
                <header :class="headerClasses">
                    <slot name="header">
                        <h1 v-if="title" :class="titleClasses">{{ title }}</h1>
                    </slot>
                    <!-- Button Group Slot -->
                    <slot name="buttonGroup" />
                </header>
                <!-- Breadcrumb slot -->
                <slot name="breadcrumb"> </slot>
            </div>
            <!-- Tabs slot -->
            <template v-if="$slots.tabs && showTabs" :class="tabsClasses">
                <slot name="tabs">
                    <HeaderTabs :tabs="tabs" :current="activeTab" />
                </slot>
            </template>
        </div>
        <!-- Main content area -->
        <main :class="mainClasses" :style="mainStyles">
            <!-- Primary content -->
            <slot name="default"></slot>
        </main>
        <template v-if="$slots.footer || showFooter">
            <footer :class="footerClasses">
                <slot name="footer"></slot>
            </footer>
        </template>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { twMerge } from 'tailwind-merge';
import HeaderTabs from '@spa/modules/common/HeaderTabs.vue';
import Breadcrumb from '@spa/components/Breadcrumb/Breadcrumb.vue';

const props = defineProps({
    // Layout configuration
    layout: {
        type: String,
        default: 'default',
        validator: (value) => ['default', 'full-width'].includes(value),
    },

    // Container settings
    maxWidth: {
        type: String,
        default: '1920px',
    },
    padding: {
        type: String,
        default: '0',
    },
    gap: {
        type: String,
        default: '1.5rem',
    },

    // Content settings
    title: {
        type: String,
        default: '',
    },

    // Visibility controls
    showHeader: {
        type: Boolean,
        default: false,
    },
    showTabs: {
        type: Boolean,
        default: false,
    },
    showFooter: {
        type: Boolean,
        default: false,
    },

    // Responsive settings
    responsive: {
        type: Boolean,
        default: true,
    },
    pt: {
        type: Object,
        default: {},
    },
    tabs: {
        type: Array,
        default: () => [],
    },
    activeTab: {
        type: String,
        default: '',
    },
});

const containerClasses = computed(() => {
    return twMerge('mx-auto', props.pt.root);
});

const headerClasses = computed(() => {
    return twMerge('flex items-center gap-4 w-full justify-between', props.pt.header);
});

const mainClasses = computed(() => {
    return twMerge('flex-1 overflow-y-auto', props.pt.main);
});

const tabsClasses = computed(() => {
    return twMerge('', props.pt.tabs);
});

const contentClasses = computed(() => {
    return twMerge('flex-1', props.pt.content);
});

const secondaryClasses = computed(() => {
    return twMerge('w-1/4', props.pt.secondary);
});

const footerClasses = computed(() => {
    return twMerge('flex justify-between items-center', props.pt.footer);
});

const containerStyles = computed(() => {
    return {
        gap: props.gap,
    };
});

const mainStyles = computed(() => {
    return {
        maxWidth: props.responsive ? props.maxWidth : '100%',
        padding: props.padding,
        gap: props.gap,
    };
});
</script>

<style></style>
