<?php

namespace App\Http\Controllers\Frontend;

use App;
use App\Exports\AvetMissDataNat00010AExport;
use App\Exports\AvetMissDataNat00010Export;
use App\Exports\AvetMissDataNat00020Export;
use App\Exports\AvetMissDataNat00030Export;
use App\Exports\AvetMissDataNat00060Export;
use App\Exports\AvetMissDataNat00080Export;
use App\Exports\AvetMissDataNat00085Export;
use App\Exports\AvetMissDataNat00090Export;
use App\Exports\AvetMissDataNat00100Export;
use App\Exports\AvetMissDataNat00120Export;
use App\Exports\AvetMissDataNat00130Export;
use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Model\CampusVenue;
use App\Model\CollegeDetails;
use App\Model\Courses;
use App\Model\Language;
use App\Model\StudentCourse;
use App\Model\Students;
use App\Model\StudentSubjectEnrolment;
use App\Model\UnitModule;
use App\Traits\AvetMissTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;
use Maatwebsite\Excel\Facades\Excel;
use ZipArchive;

class AvetMissDataExportController extends Controller
{
    use AvetMissTrait;

    public function studentAvetDataExports(Request $request)
    {

        $arrReportType = Config::get('constants.arrReportType');
        $arrAvetMissExportsType = Config::get('constants.arrAvetMissExportsType');

        $arrState = Config::get('constants.arrState');
        unset($arrState['00'], $arrState['09']);
        $arrMonthName = Config::get('constants.arrMonthName');
        $arrIntakeYear = Config::get('constants.arrYearData');
        $arrMonth = Config::get('constants.arrMonth');

        $data['pagetitle'] = 'Student Avet Miss Reports';
        $data['plugincss'] = [];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'datepicker/bootstrap-datepicker.js', 'ckeditor/ckeditor.js'];
        $data['js'] = ['studentAvetMissReports.js'];
        $data['funinit'] = ['studentAvetMissReports.initAvetMissReports()'];
        $data['activateValue'] = 'Offers';
        $data['header'] = [
            'title' => 'Export NAT txt files for AVETMISS reporting',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Export NAT txt files for AVETMISS reporting' => '',
            ]];

        $data['arrReportType'] = $arrReportType;
        $data['arrAvetMissExportsType'] = $arrAvetMissExportsType;
        $data['arrState'] = $arrState;
        $data['arrYear'] = $arrIntakeYear;
        $data['arrMonthName'] = $arrMonthName;
        $data['arrMonth'] = $arrMonth;
        $data['mainmenu'] = 'administration';

        return view('frontend.users.student-avet-miss', $data);
    }

    /*  generate Xls files */

    public function generate_excel(Request $request)
    {

        $arrAvetMissFileName = Config::get('constants.arrAvetMissFileName');
        $filePath = Config::get('constants.uploadFilePath.AVETMISS');
        $destinationPath = Helpers::changeRootPath($filePath);
        $collegeId = Auth::user()->college_id;

        if (! is_dir($destinationPath['default'])) {
            mkdir($destinationPath['default'], 0777);
        }
        ob_end_clean();
        $expType = $request->input('export_type');

        if ($expType == 2) {
            $zipFileName = 'AVETMISS-for-National.zip';
            $typeFolderName = 'AVETMISS-for-National';
        } else {
            $zipFileName = 'AVETMISS-for-State.zip';
            $typeFolderName = 'AVETMISS-for-State';
        }
        if (is_file(public_path('uploads/'.$collegeId.'/AVETMISS/'.$zipFileName))) {
            unlink(public_path('uploads/'.$collegeId.'/AVETMISS/'.$zipFileName));
        }

        $directoryPath = $destinationPath['default'].date('Y-m-d').$typeFolderName.'/';
        $directoryViewPath = $destinationPath['view'].date('Y-m-d').$typeFolderName.'/';
        $timtStamp = $destinationPath['default'].date('Y-m-d').$typeFolderName.'/';
        $files = glob("$timtStamp/*"); // get all file names
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            } // delete file
        }

        if (! is_dir($directoryPath)) {
            mkdir($directoryPath, 0777);
        }
        $objReports = [];
        $objReports['export_type'] = $request->input('export_type');
        $objReports['reportType'] = $request->input('reportType');
        $objReports['month'] = $request->input('month');
        $objReports['state'] = $request->input('state');
        $objReports['year'] = $request->input('year_collection');
        $objReports['from_date'] = $request->input('from_date');
        $objReports['to_date'] = $request->input('to_date');
        $objReports['traning_location'] = $request->input('traning_location');
        $objReports['claim_only'] = $request->input('claim_only');
        $objReports['non_claim'] = $request->input('non_claim');
        $objReports['selectedState'] = $request->input('selectedState');
        $objReports['stateName'] = $request->input('state_name');
        $objReports['claim_only'] = $request->input('claim_only');
        $objReports['smart_and_skill'] = $request->input('smart_and_skill');

        if ($request->input('submitBtn') == 'Excel') {
            for ($j = 1; $j <= count($arrAvetMissFileName); $j++) {
                ini_set('memory_limit', '-1');
                $this->_setXlsxFileContains($arrAvetMissFileName[$j], $timtStamp, $directoryViewPath, $objReports);
                $this->moveFile($arrAvetMissFileName[$j], $directoryViewPath);
            }

            $zip = new ZipArchive;
            $fileName = $zipFileName;
            if ($zip->open(public_path('uploads/'.$collegeId.'/AVETMISS/'.$fileName), ZipArchive::CREATE) === true) {
                $files = File::files(public_path($directoryViewPath));
                foreach ($files as $key => $value) {
                    $relativeNameInZipFile = basename($value);
                    $zip->addFile($value, $relativeNameInZipFile);
                }
                $zip->close();
            }
            $files = glob("$timtStamp/*"); // get all file names
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                } // delete file
            }
            rmdir($timtStamp);

            return response()->download(public_path('uploads/'.$collegeId.'/AVETMISS/'.$fileName))->deleteFileAfterSend(true);
        } else {
            // AVETMISS Validation: Generate files in specific order for cross-file validation

            // Step 1: Generate NAT00010 FIRST to collect training org identifier
            ini_set('memory_limit', '-1');
            $this->_setTxtFileContains('nat00010', $timtStamp, $objReports);

            // Step 2: Generate NAT00120 to collect student IDs, program IDs, and subject IDs
            ini_set('memory_limit', '-1');
            $this->_setTxtFileContains('nat00120', $timtStamp, $objReports);

            // Step 3: Generate NAT00130 to collect program IDs from completions
            ini_set('memory_limit', '-1');
            $this->_setTxtFileContains('nat00130', $timtStamp, $objReports);

            // Step 4: Merge program identifiers and student IDs from NAT00120 and NAT00130
            // This ensures NAT00030 includes programs from both training activities AND completions
            // This ensures NAT00080, NAT00085, NAT00090, NAT00100 include students from both training activities AND completions
            $this->validProgramIdentifiersForNat00030 = array_unique(array_merge(
                $this->validProgramIdentifiersForNat00030,
                $this->validProgramIdentifiersFromNat00130
            ));

            $this->validStudentIdsForNat00080 = array_unique(array_merge(
                $this->validStudentIdsForNat00080,
                $this->validStudentIdsFromNat00130
            ));

            // Step 5: Generate all other files (they will use the collected identifiers)
            for ($j = 1; $j <= count($arrAvetMissFileName); $j++) {
                // Skip files already generated
                if (in_array($arrAvetMissFileName[$j], ['nat00010', 'nat00120', 'nat00130'])) {
                    continue;
                }
                ini_set('memory_limit', '-1');
                $this->_setTxtFileContains($arrAvetMissFileName[$j], $timtStamp, $objReports);
            }

            $zip = new ZipArchive;
            $fileName = $zipFileName;
            if ($zip->open(public_path('uploads/'.$collegeId.'/AVETMISS/'.$fileName), ZipArchive::CREATE) === true) {
                $files = File::files(public_path($directoryViewPath));
                foreach ($files as $key => $value) {
                    $relativeNameInZipFile = basename($value);
                    $zip->addFile($value, $relativeNameInZipFile);
                }
                $zip->close();
            }
            // delete file first
            $files = glob("$timtStamp/*"); // get all file names
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                } // delete file
            }
            // delete dir
            rmdir($timtStamp);

            return response()->download(public_path('uploads/'.$collegeId.'/AVETMISS/'.$fileName))->deleteFileAfterSend(true);
        }
    }

    public function moveFile($filename, $directoryPath)
    {
        $filename = "{$filename}.xlsx";
        $sourcePath = public_path("uploads/avetmiss-xls/{$filename}");
        $destinationPath = public_path("$directoryPath{$filename}");

        // Check if the source file exists
        if (File::exists($sourcePath)) {
            // Check if the destination directory exists, create it if not
            $destinationDirectory = pathinfo($destinationPath, PATHINFO_DIRNAME);
            if (! File::isDirectory($destinationDirectory)) {
                File::makeDirectory($destinationDirectory, 0755, true, true);
            }

            // Move the file
            File::move($sourcePath, $destinationPath);
        }
    }

    public function _setXlsxFileContains($filename, $timtStamp, $directoryPath, $objReports)
    {

        if ($filename == 'nat00010') {
            $this->_createNat00010File($filename, $timtStamp, $directoryPath, $objReports);
        } elseif ($filename == 'nat00010A') {
            $this->_createNat00010AFile($filename, $timtStamp, $directoryPath, $objReports);
        } elseif ($filename == 'nat00020') {
            $this->_createNat00020File($filename, $timtStamp, $directoryPath, $objReports);
        } elseif ($filename == 'nat00030') {
            $this->_createNat00030File($filename, $timtStamp, $directoryPath, $objReports);
        } elseif ($filename == 'nat00030A') {
            //  $this->_createNat00030AFile($filename, $timtStamp, $directoryPath, $objReports);
        } elseif ($filename == 'nat00060') {
            $this->_createNat00060File($filename, $timtStamp, $directoryPath, $objReports);
        } elseif ($filename == 'nat00080') {
            $this->_createNat00080File($filename, $timtStamp, $directoryPath, $objReports);
        } elseif ($filename == 'nat00085') {
            $this->_createNat00085File($filename, $timtStamp, $directoryPath, $objReports);
        } elseif ($filename == 'nat00090') {
            $this->_createNat00090File($filename, $timtStamp, $directoryPath, $objReports);
        } elseif ($filename == 'nat00100') {
            $this->_createNat00100File($filename, $timtStamp, $directoryPath, $objReports);
        } elseif ($filename == 'nat00120') {
            $this->_createNat00120File($filename, $timtStamp, $directoryPath, $objReports);
        } elseif ($filename == 'nat00130') {
            $this->_createNat00130File($filename, $timtStamp, $directoryPath, $objReports);
        }

        return true;
    }

    public function _createNat00010File($filename, $timtStamp, $directoryPath, $objReportsArr)
    {

        $dataArray = json_encode($objReportsArr);

        return Excel::store(new AvetMissDataNat00010Export($dataArray), $filename.'.xlsx', 'avetmiss');
    }

    public function _createNat00010AFile($filename, $timtStamp, $directoryPath, $objReportsArr)
    {

        $dataArray = json_encode($objReportsArr);

        return Excel::store(new AvetMissDataNat00010AExport($dataArray), $filename.'.xlsx', 'avetmiss');
    }

    public function _createNat00020File($filename, $timtStamp, $directoryPath, $objReportsArr)
    {

        $dataArray = json_encode($objReportsArr);

        return Excel::store(new AvetMissDataNat00020Export($dataArray), $filename.'.xlsx', 'avetmiss');
    }

    public function _createNat00030File($filename, $timtStamp, $directoryPath, $objReportsArr)
    {

        $dataArray = json_encode($objReportsArr);

        return Excel::store(new AvetMissDataNat00030Export($dataArray), $filename.'.xlsx', 'avetmiss');
    }

    public function _createNat00030AFile($filename, $timtStamp, $directoryPath, $objReportsArr)
    {

        Excel::create($filename, function ($excel) use ($objReportsArr) {
            $headers = ['cID', 'cName', 'cRecID', 'cLevelEdID', 'cFieldEdID', 'anzscoID', 'vetFlag'];
            $excel->setTitle('Exports in xls');
            $excel->setCreator('testCreator')->setCompany('testCompany');
            $excel->setDescription('A demonstration to change the file properties');
            $excel->sheet('Sheetname', function ($sheet) use ($headers, $objReportsArr) {
                $collegeId = Auth::user()->college_id;
                $objCourses = new Courses;
                $courses = $objCourses->getCourseListByCollageId($collegeId, $objReportsArr);

                foreach ($courses as $val => $row) {
                    $sheet->fromArray([
                        [$row->course_code, $row->course_name, $row->course_recognition_id, $row->level_of_education_id, $row->field_of_education_id, $row->ANZSCO_code, ($row->vet_flag == 1 || $row->vet_flag == 'Yes') ? 'Y' : 'N'],
                        //          array($row->course_recognition_id, $row->level_of_education, $row->field_of_education, $row->ANZSCO_code, ($row->AVETMISS_Report == '1') ? 'Y' : 'N'),
                    ], null, null, false, false);
                }
                $sheet->setFontSize(11);
                $sheet->setHeight(0, 20);
                $sheet->freezeFirstRow(1);
                $sheet->prependRow($headers);
            });
        })->store('xlsx', public_path().$directoryPath);
        $excel = App::make('excel');

        return true;
    }

    public function _createNat00060File($filename, $timtStamp, $directoryPath, $objReportsArr)
    {

        $dataArray = json_encode($objReportsArr);

        return Excel::store(new AvetMissDataNat00060Export($dataArray), $filename.'.xlsx', 'avetmiss');
    }

    public function _createNat00080File($filename, $timtStamp, $directoryPath, $objReportsArr)
    {

        $dataArray = json_encode($objReportsArr);

        return Excel::store(new AvetMissDataNat00080Export($dataArray), $filename.'.xlsx', 'avetmiss');
    }

    public function _createNat00085File($filename, $timtStamp, $directoryPath, $objReportsArr)
    {

        $dataArray = json_encode($objReportsArr);

        return Excel::store(new AvetMissDataNat00085Export($dataArray), $filename.'.xlsx', 'avetmiss');
    }

    public function _createNat00090File($filename, $timtStamp, $directoryPath, $objReportsArr)
    {

        $dataArray = json_encode($objReportsArr);

        return Excel::store(new AvetMissDataNat00090Export($dataArray), $filename.'.xlsx', 'avetmiss');
    }

    public function _createNat00100File($filename, $timtStamp, $directoryPath, $objReportsArr)
    {

        $dataArray = json_encode($objReportsArr);

        return Excel::store(new AvetMissDataNat00100Export($dataArray), $filename.'.xlsx', 'avetmiss');
    }

    public function _createNat00120File($filename, $timtStamp, $directoryPath, $objReportsArr)
    {

        $dataArray = json_encode($objReportsArr);

        return Excel::store(new AvetMissDataNat00120Export($dataArray), $filename.'.xlsx', 'avetmiss');
    }

    public function _createNat00130File($filename, $timtStamp, $directoryPath, $objReportsArr)
    {

        $dataArray = json_encode($objReportsArr);

        return Excel::store(new AvetMissDataNat00130Export($dataArray), $filename.'.xlsx', 'avetmiss');
    }

    public function ajaxAction(Request $request)
    {
        $action = $request->input('action');
        switch ($action) {
            case 'getStateDetails':
                $stateName = $request->input('data.state');
                $this->_getSingleStateDetails($stateName);
                break;
        }
        exit;
    }

    public function _getSingleStateDetails($stateName)
    {

        $collegeId = Auth::user()->college_id;
        $objVenue = new CampusVenue;
        $arrVenueList = $objVenue->getVenueTraningList($collegeId, $stateName);
        echo json_encode($arrVenueList);
        exit;
    }

    // Property to store valid student IDs from NAT00120 for NAT00080 validation
    private $validStudentIdsForNat00080 = [];

    // Property to store valid program identifiers from NAT00120 for NAT00030 validation
    private $validProgramIdentifiersForNat00030 = [];

    // Property to store valid program identifiers from NAT00130 for NAT00030 validation
    private $validProgramIdentifiersFromNat00130 = [];

    // Property to store valid student IDs from NAT00130 for NAT00080, NAT00085, NAT00090, NAT00100 validation
    private $validStudentIdsFromNat00130 = [];

    // Property to store valid subject identifiers from NAT00120 for NAT00060 validation
    private $validSubjectIdentifiersForNat00060 = [];

    // Property to store valid venue codes from NAT00120 for NAT00020 validation
    private $validVenueCodesForNat00020 = [];

    // Property to store valid training org identifier from NAT00010 for NAT00130 validation
    private $validTrainingOrgIdentifierForNat00130 = '';

    public function _setTxtFileContains($filename, $generateTxtPath, $objReports)
    {

        if ($filename == 'nat00010') {
            // NAT00010 is generated and returns the training org identifier for NAT00130 validation
            $this->validTrainingOrgIdentifierForNat00130 = $this->_createTxtNat00010File($filename, $generateTxtPath, $objReports);
        } elseif ($filename == 'nat00010A') {
            // $this->_createTxtNat00010AFile($filename, $generateTxtPath, $objReports);
        } elseif ($filename == 'nat00020') {
            // NAT00020 uses valid venue codes collected from NAT00120
            // Only venues with training activities in NAT00120 will be included
            $this->_createTxtNat00020File($filename, $generateTxtPath, $objReports, $this->validVenueCodesForNat00020);
        } elseif ($filename == 'nat00030') {
            // NAT00030 uses valid program identifiers collected from NAT00120
            // Only programs with training activities in NAT00120 will be included
            $this->_createTxtNat00030File($filename, $generateTxtPath, $objReports, $this->validProgramIdentifiersForNat00030);
        } elseif ($filename == 'nat00030A') {
            //  $this->_createTxtNat00030AFile($filename, $generateTxtPath, $objReports);
        } elseif ($filename == 'nat00060') {
            // NAT00060 uses valid subject identifiers collected from NAT00120
            // Only subjects with training activities in NAT00120 will be included
            $this->_createTxtNat00060File($filename, $generateTxtPath, $objReports, $this->validSubjectIdentifiersForNat00060);
        } elseif ($filename == 'nat00080') {
            // NAT00080 uses valid student IDs collected from NAT00120
            // Only students with valid training activities in NAT00120 will be included
            $this->_createTxtNat00080File($filename, $generateTxtPath, $objReports, $this->validStudentIdsForNat00080);
        } elseif ($filename == 'nat00085') {
            // NAT00085 uses valid student IDs collected from NAT00120
            // Only students with valid training activities in NAT00120 will be included
            $this->_createTxtNat00085File($filename, $generateTxtPath, $objReports, $this->validStudentIdsForNat00080);
        } elseif ($filename == 'nat00090') {
            // NAT00090 uses valid student IDs collected from NAT00120
            // Only students with valid training activities in NAT00120 will be included
            $this->_createTxtNat00090File($filename, $generateTxtPath, $objReports, $this->validStudentIdsForNat00080);
        } elseif ($filename == 'nat00100') {
            // NAT00100 uses valid student IDs collected from NAT00120
            // Only students with valid training activities in NAT00120 will be included
            $this->_createTxtNat00100File($filename, $generateTxtPath, $objReports, $this->validStudentIdsForNat00080);
        } elseif ($filename == 'nat00120') {
            // NAT00120 is generated and returns valid student IDs, program identifiers, subject identifiers, and venue codes
            // This ensures bidirectional validation between NAT00080/NAT00120, NAT00030/NAT00120, NAT00060/NAT00120, and NAT00020/NAT00120
            $result = $this->_createTxtNat00120File($filename, $generateTxtPath, $objReports);
            $this->validStudentIdsForNat00080 = $result['studentIds'];
            $this->validProgramIdentifiersForNat00030 = $result['programIdentifiers'];
            $this->validSubjectIdentifiersForNat00060 = $result['subjectIdentifiers'];
            $this->validVenueCodesForNat00020 = $result['venueCodes'];
        } elseif ($filename == 'nat00130') {
            // NAT00130 is generated and returns program identifiers and student IDs from completions
            // This ensures NAT00030 includes programs from both NAT00120 and NAT00130
            // This ensures NAT00080, NAT00085, NAT00090, NAT00100 include students from both NAT00120 and NAT00130
            $result = $this->_createTxtNat00130File($filename, $generateTxtPath, $objReports, $this->validProgramIdentifiersForNat00030, $this->validTrainingOrgIdentifierForNat00130);
            $this->validProgramIdentifiersFromNat00130 = $result['programIdentifiers'];
            $this->validStudentIdsFromNat00130 = $result['studentIds'];
        }

        return true;
    }

    public function _createTxtNat00010File($filename, $generateTxtPath, $objReportsArr)
    {

        $college_id = Auth::user()->college_id;
        $objRtoCollegeDetails = new CollegeDetails;
        $collageDetails = $objRtoCollegeDetails->getCollegeDetails($college_id);
        $collageDetails = $collageDetails[0];

        $softwareProductName = Config::get('constants.arrSoftwareProductName');
        $softwareVendorEmail = Config::get('constants.arrSoftwareVendorEmail');

        $code = $this->getRtoCodeByState($collageDetails, $objReportsArr);
        $content = '';
        if ($objReportsArr['export_type'] == 2) {
            $content .= $this->getSpecificNumberStr($collageDetails->RTO_code, 10).$this->getSpecificNumberStr($collageDetails->legal_name, 440);
        } else {
            $content .= $this->getSpecificNumberStr($code, 10)
                    .$this->getSpecificNumberStr($collageDetails->legal_name, 100)
                    .$this->getSpecificNumberStr('', 158)
                    .$this->getSpecificNumberStr($collageDetails->contact_person, 60)
                    .$this->getSpecificNumberStr($collageDetails->contact_phone, 20)
                    .$this->getSpecificNumberStr($collageDetails->fax, 20)
                    .$this->getSpecificNumberStr($collageDetails->contact_email, 80)
                    .$this->getSpecificNumberStr($softwareProductName, 20)
                    .$this->getSpecificNumberStr($softwareVendorEmail, 80)
                    .$this->getSpecificNumberStr('', 2);
        }
        File::put($generateTxtPath.$filename.'.txt', $content);
        //     File::put($generateTxtPath . $filename, $content);

        // Return the training org identifier for NAT00130 validation
        return $code;
    }

    public function _createTxtNat00010AFile($filename, $generateTxtPath, $objReportsArr)
    {

        $college_id = Auth::user()->college_id;
        $objRtoCollegeDetails = new CollegeDetails;
        $collageDetail = $objRtoCollegeDetails->getCollegeDetails($college_id);
        $collageDetails = $collageDetail[0];

        $arrTrainingOrg = Config::get('constants.arrTraningOrgazinationType');
        $arrTraningOrgazinationState = Config::get('constants.arrTraningOrgazinationState');

        unset($arrTraningOrgazinationState['99'], $arrTraningOrgazinationState['09']);
        if (in_array($collageDetails->postal_state, $arrTraningOrgazinationState)) {
            $stateCode = array_search($collageDetails->current_state, $arrTraningOrgazinationState);
        } else {
            $stateCode = $collageDetails->postal_state;
        }
        $content = '';
        if ($objReportsArr['export_type'] == 1) {
            $content .= $this->getSpecificNumberStr($arrTrainingOrg[$collageDetails->training_organisation_type_id], 2)
                     .$this->getSpecificNumberStr($collageDetails->legal_name, 100)
                    .$this->getSpecificNumberStr($collageDetails->contact_person, 60)
                    .$this->getSpecificNumberStr($collageDetails->contact_phone, 20)
                    .$this->getSpecificNumberStr($collageDetails->fax, 20)
                    .$this->getSpecificNumberStr($collageDetails->contact_email, 80);
        } else {
            $content .= $this->getSpecificNumberStr($collageDetails->RTO_code, 10)
                    .$this->getSpecificNumberStr($collageDetails->legal_name, 100)
                    .$this->getSpecificNumberStr($arrTrainingOrg[$collageDetails->training_organisation_type_id], 2)
                    .$this->getSpecificNumberStr($collageDetails->street_address, 50)
                    .$this->getSpecificNumberStr($collageDetails->street_address2, 50)
                    .$this->getSpecificNumberStr($collageDetails->street_suburb, 50)
                    .$this->getSpecificNumberStr($collageDetails->street_postcode, 4)
                    .$this->getSpecificNumberStr($stateCode, 2)
                    .$this->getSpecificNumberStr($collageDetails->contact_person, 60)
                    .$this->getSpecificNumberStr($collageDetails->contact_phone, 20)
                    .$this->getSpecificNumberStr($collageDetails->fax, 20)
                    .$this->getSpecificNumberStr($collageDetails->contact_email, 80);
        }
        File::put($generateTxtPath.$filename.'.txt', $content);
        //       File::put($generateTxtPath . $filename, $content);

        return true;
    }

    public function _createTxtNat00020File($filename, $generateTxtPath, $objReportsArr, $validVenueCodes = [])
    {

        $collegeId = Auth::user()->college_id;
        $objRtoCampusVenue = new CampusVenue;

        // AVETMISS Validation: Only fetch venues that exist in NAT00120
        // Query-level filtering is more efficient than loop-level filtering
        $venueData = $objRtoCampusVenue->getVenueListForXlsFilesFilter($collegeId, $objReportsArr, $validVenueCodes);

        $arrState = Config::get('constants.arrState');
        unset($arrState['00']);
        $content = '';
        foreach ($venueData as $val => $row) {
            $code = $this->getRtoCodeByState($row, $objReportsArr);

            if (in_array($row->state, $arrState)) {
                $stateCode = array_search($row->state, $arrState);
            } else {
                $stateCode = $row->state;
            }
            if (! in_array($row->state, $arrState)) {
                $postCode = 'OSPC';
                $stateCode = '09';
            } else {
                $postCode = $row->postcode;
            }
            if ($postCode == 'OSPC') {
                $stateCode = '99';
                $ccode = ['1100', '1101', '1102', '1199'];
                if (in_array($row->country_absvalue, $ccode)) {
                    $countryCode = '';
                } else {
                    $countryCode = (! empty($row->country_absvalue) ? $this->getSpecificNumberStr($row->country_absvalue, 4) : '@@@@');
                }
            } else {
                $countryCode = (! empty($row->country_absvalue) ? $this->getSpecificNumberStr($row->country_absvalue, 4) : '@@@@');
            }

            $content .= $this->getSpecificNumberStr($code, 10)
                    .$this->getSpecificNumberStr($row->venue_code, 10)
                    .$this->getSpecificNumberStr($row->venue_name, 100)
                    .(! empty($postCode) ? $this->getSpecificNumberStr($postCode, 4) : '@@@@');
            $content .= $this->getSpecificNumberStr($stateCode, 2).
                    $this->getSpecificNumberStr($row->sub_urb, 50).
                     (! empty($countryCode) ? $this->getSpecificNumberStr($countryCode, 4) : '@@@@').PHP_EOL;
        }

        File::put($generateTxtPath.$filename.'.txt', $content);

        return true;
    }

    public function _createTxtNat00030File($filename, $generateTxtPath, $objReportsArr, $validProgramIdentifiers = [])
    {

        $content = '';
        $collegeId = Auth::user()->college_id;
        $objCourses = new Courses;

        // AVETMISS Validation: Only fetch programs that exist in NAT00120 or NAT00130
        // Query-level filtering is more efficient than loop-level filtering
        $courses = $objCourses->getCourseListByCollageId($collegeId, $objReportsArr, $validProgramIdentifiers);

        foreach ($courses as $val => $row) {
            if ($row->module_delivery == 0) {
                $content .= $this->getSpecificNumberStr($row->national_code, 10)
                        .$this->getSpecificNumberStr($row->course_name, 100)
                        .$this->getSpecificNumberStr(str_pad($row->total_nominal_hours, 4, '0', STR_PAD_LEFT), 4)
                        .$this->getSpecificNumberStr('', 15)
                        .$this->getSpecificNumberStr(($row->AVETMISS_Report == '1') ? 'Y' : '', 1)
                        .PHP_EOL;
            }
        }
        File::put($generateTxtPath.$filename.'.txt', $content);

        return true;
    }

    public function _createTxtNat00030AFile($filename, $generateTxtPath, $objReportsArr)
    {
        $content = '';

        $collegeId = Auth::user()->college_id;
        $objCourses = new Courses;
        $courses = $objCourses->getCourseListByCollageId($collegeId, $objReportsArr);

        foreach ($courses as $val => $row) {

            $totalNominalHours = '0000';
            if ($row->total_nominal_hours != '') {
                $totalNominalHours = $row->total_nominal_hours;
            }
            $content .= $this->getSpecificNumberStr($row->course_code, 10)
                    .$this->getSpecificNumberStr($row->course_name, 100)
                    .$this->getSpecificNumberStr($totalNominalHours, 4)
                    .$this->getSpecificNumberStr($row->course_recognition_id, 2)
                    .$this->getSpecificNumberStr($row->level_of_education_id, 3)
                    .$this->getSpecificNumberStr($row->field_of_education_id, 6)
                    .$this->getSpecificNumberStr($row->ANZSCO_code, 4)
                    .($row->vet_flag == 'Yes' ? 'Y' : 'N')
                    .PHP_EOL;
        }

        File::put($generateTxtPath.$filename.'.txt', $content);

        return true;
    }

    public function _createTxtNat00060File($filename, $generateTxtPath, $objReportsArr, $validSubjectIdentifiers = [])
    {

        $content = '';
        $collegeId = Auth::user()->college_id;
        $objUnitModules = new UnitModule;

        // AVETMISS Validation: Only fetch subjects that exist in NAT00120
        // Query-level filtering is more efficient than loop-level filtering
        $courses = $objUnitModules->getUnitModuleByColageIdForXlsFile($collegeId, $objReportsArr, $validSubjectIdentifiers);

        foreach ($courses as $val => $row) {
            $content .= $this->getSpecificNumberStr($row->vet_unit_code, 12)
                    .$this->getSpecificNumberStr($row->unit_name, 100)
                    .$this->getSpecificNumberStr($row->field_education, 6)
                    .($row->vet_flag == 'Yes' ? 'Y' : 'N')
                    .$this->getSpecificNumberStr($row->nominal_hours, 4)
                    .PHP_EOL;
        }
        File::put($generateTxtPath.$filename.'.txt', $content);

        return true;
    }

    public function _createTxtNat00080File($filename, $generateTxtPath, $objReportsArr, $validStudentIds = [])
    {

        $content = '';
        $collegeId = Auth::user()->college_id;
        $objStudent = new Students;

        // AVETMISS Validation: Only fetch students that exist in NAT00120
        // Query-level filtering is more efficient than loop-level filtering
        $arrStudent = $objStudent->getStudentDetailForXls($collegeId, $objReportsArr, $validStudentIds);

        $arrSchoolLevel = Config::get('constants.arrSchoolLevel');
        $arrState = Config::get('constants.arrState');

        unset($arrState['00'], $arrState['09']);
        $objRtoLanguage = new Language;

        foreach ($arrStudent as $val => $row) {

            if ($row->survey_contact_status == '') {
                $surveyContactStatus = 'A';
            } else {
                $surveyContactStatus = $row->survey_contact_status;
            }
            if (! array_key_exists($row->highest_completed_school_level, $arrSchoolLevel) || empty($row->highest_completed_school_level)) {
                $level = '@@';
            } else {
                $level = $row->highest_completed_school_level;
            }

            if ($row->student_type != 'Domestic') {
                $postCode = 'OSPC';
                $stateCode = '99';
                $city = '';
            } else {
                $postCode = (! empty($row->current_postcode) ? $row->current_postcode : '@@@@');
                $city = $row->current_city;
                if (in_array($row->current_state, $arrState)) {
                    $stateCode = array_search($row->current_state, $arrState);
                } else {
                    $stateCode = '99';
                }
            }

            if ($row->current_employment_status == '' || $row->current_employment_status == '00' || $row->current_employment_status == '0') {
                $row->current_employment_status = '@@';
            } else {
                $row->current_employment_status = '0'.$row->current_employment_status;
            }
            $language_abs = $row->language_abs_value;

            if ($objReportsArr['export_type'] == 1 && $objReportsArr['state'] == 03) {
                if ($row->gender == 'Male') {
                    $gender = 'M';
                } elseif ($row->gender == 'Female') {
                    $gender = 'F';
                } elseif ($row->gender == 'Not Specified') {
                    $gender = 'X';
                } else {
                    $gender = '';
                }
            } else {
                $gender = ($row->gender == 'Male' ? 'M' : 'F');
            }

            // if ($objReportsArr['export_type'] == 1) { // enter when export type is state
            $fullname = $row->family_name.', '.$row->first_name.' '.$row->middel_name;
            $content .= $this->getSpecificNumberStr($row->generated_stud_id, 10)
                    .$this->getSpecificNumberStr($fullname, 60)
                    .$this->getSpecificNumberStr($level, 2)
                    .$this->getSpecificNumberStr($gender, 1)
                    .$this->getSpecificNumberStr(date('dmY', strtotime($row->DOB)), 8)
                    .$this->getSpecificNumberStr($postCode, 4)
                    .$this->getSpecificNumberStr($row->is_aus_aboriginal_or_islander, 1)
                    .$this->getSpecificNumberStr($language_abs, 4)
                    .$this->getSpecificNumberStr($row->current_employment_status, 2)
                    .$this->getSpecificNumberStr($row->country_name, 4)
                    .(($row->disability == 0) ? 'N' : 'Y')
                    .(($row->qualification_level_id == '') ? 'N' : 'Y')
                    .(($row->attending_secondary_school == 1) ? 'Y' : 'N')
                    .$this->getSpecificNumberStr($city, 50)
                    .$this->getSpecificNumberStr($row->USI, 10)
                    .$this->getSpecificNumberStr($stateCode, 2)
                    .$this->getSpecificNumberStr($row->current_building_name, 50)
                    .$this->getSpecificNumberStr($row->current_unit_detail, 30)
                    .$this->getSpecificNumberStr($row->current_street_no, 15)
                    .$this->getSpecificNumberStr($row->current_street_name, 70)
                    .$this->getSpecificNumberStr($surveyContactStatus, 1) // Daynemic new field
                    .$this->getSpecificNumberStr('', 11)
                    .$this->getSpecificNumberStr('', 9)
                    .PHP_EOL;
        }

        File::put($generateTxtPath.$filename.'.txt', $content);

        return true;
    }

    public function _createTxtNat00085File($filename, $generateTxtPath, $objReportsArr, $validStudentIds = [])
    {

        $collegeId = Auth::user()->college_id;
        $objStudent = new Students;

        // AVETMISS Validation: Only fetch students that exist in NAT00120
        // Query-level filtering is more efficient than loop-level filtering
        $arrStudent = $objStudent->getStudentDetailForXls($collegeId, $objReportsArr, $validStudentIds);

        $arrState = Config::get('constants.arrState');
        unset($arrState['00'], $arrState['09']);
        $content = '';

        foreach ($arrStudent as $val => $row) {

            if ($row->student_type != 'Domestic') {
                $postCode = 'OSPC';
                $stateCode = '99';
                $city = $row->current_city;
            } else {
                $postCode = (! empty($row->current_postcode) ? $row->current_postcode : '@@@@');
                $city = $row->current_city;
                if (in_array($row->current_state, $arrState)) {
                    $stateCode = array_search($row->current_state, $arrState);
                } else {
                    $stateCode = '99';
                }
            }

            $content .= $this->getSpecificNumberStr($row->generated_stud_id, 10)
                    .$this->getSpecificNumberStr($row->name_title, 4)
                    .$this->getSpecificNumberStr($row->first_name, 40)
                    .$this->getSpecificNumberStr($row->family_name, 40)
                    .$this->getSpecificNumberStr($row->current_building_name, 50)
                    .$this->getSpecificNumberStr($row->current_unit_detail, 30)
                    .$this->getSpecificNumberStr($row->current_street_no, 15)
                    .$this->getSpecificNumberStr($row->current_street_name, 70)
                    .$this->getSpecificNumberStr($row->postal_po_box, 22)
                    .$this->getSpecificNumberStr($city, 50)
                    .$this->getSpecificNumberStr($postCode, 4)
                    .$this->getSpecificNumberStr($stateCode, 2)
                    .$this->getSpecificNumberStr($row->current_home_phone, 20)
                    .$this->getSpecificNumberStr($row->current_work_phone, 20)
                    .$this->getSpecificNumberStr($row->current_mobile_phone, 20)
                    .$this->getSpecificNumberStr($row->email, 80)
                    .$this->getSpecificNumberStr($row->alertnet_email, 80)
                    .PHP_EOL;
        }

        File::put($generateTxtPath.$filename.'.txt', $content);

        return true;
    }

    public function _createTxtNat00090File($filename, $generateTxtPath, $objReportsArr, $validStudentIds = [])
    {

        $collegeId = Auth::user()->college_id;
        $objStudent = new Students;

        // AVETMISS Validation: Only fetch students that exist in NAT00120
        // Query-level filtering is more efficient than loop-level filtering
        $arrStudent = $objStudent->getStudentDetailForXls($collegeId, $objReportsArr, $validStudentIds);
        $content = '';

        foreach ($arrStudent as $val => $row) {
            if ($row->disability == 1) {
                $content .= $this->getSpecificNumberStr($row->generated_stud_id, 10)
                        .$this->getSpecificNumberStr($row->area_of_disability, 2).PHP_EOL;
            }
        }

        File::put($generateTxtPath.$filename.'.txt', $content);

        return true;
    }

    public function _createTxtNat00100File($filename, $generateTxtPath, $objReportsArr, $validStudentIds = [])
    {

        $content = '';
        $collegeId = Auth::user()->college_id;
        $objStudent = new Students;

        // AVETMISS Validation: Only fetch students that exist in NAT00120
        // Query-level filtering is more efficient than loop-level filtering
        $arrStudent = $objStudent->getStudentDetailForXls100($collegeId, $objReportsArr, $validStudentIds);

        foreach ($arrStudent as $val => $row) {
            if ($row->generated_stud_id != '' && $row->qualification_level_id != '' && $row->previous_qualifications == '1') {
                if ($row->qualification_level_id == '8') {
                    $row->qualification_level_id = '008';
                }
                $content .= $this->getSpecificNumberStr($row->generated_stud_id, 10).$this->getSpecificNumberStr($row->qualification_level_id, 3).PHP_EOL;
            }
        }

        File::put($generateTxtPath.$filename.'.txt', $content);

        return true;
    }

    public function _createTxtNat00120File($filename, $generateTxtPath, $objReportsArr)
    {

        $content = '';
        $objSubjectEnroll = new StudentSubjectEnrolment;
        $subjectEnroll = $objSubjectEnroll->getStudentSubjectEnrolmentDetailForXls($objReportsArr);

        $arrTrainingOrg = Config::get('constants.arrTraningOrgazinationType');
        $arrTraningOrgazinationState = Config::get('constants.arrTraningOrgazinationState');

        // PASS 1: Validate all records and collect valid ones with their student IDs, program identifiers, subject identifiers, and venue codes
        $validRecords = [];
        $validStudentIds = [];
        $validProgramIdentifiers = [];
        $validSubjectIdentifiers = [];
        $validVenueCodes = [];

        foreach ($subjectEnroll as $val => $row) {
            $arrSelectFinalOutcomeXlsx = Config::get('constants.arrSelectFinalOutcomeXlsx');

            // Commencing program identifier start
            if ($row->national_code == '' || $row->national_code == null) {
                $row->course_commencing_id = 8;
            } else {
                if ($row->course_commencing_id == '') {
                    $row->course_commencing_id = 3;
                }
            }
            // Commencing program identifier end
            // VET IN School flag start
            if ($row->vet_in_school == '') {
                $row->vet_in_school = 'N';
            }
            // VET IN School flag end
            // $row->delivery_mode
            if ($row->delivery_mode == '') {
                $row->delivery_mode = 10;
            }

            // Final Out Come Condition start
            if ($row->final_outcome != '') {
                // Check if outcome code exists in the export array (some codes like CANCEL are not exported to AVETMISS)
                if (isset($arrSelectFinalOutcomeXlsx[$row->final_outcome])) {
                    $outcomeIDTra = ($arrSelectFinalOutcomeXlsx[$row->final_outcome] == '') ? 70 : $arrSelectFinalOutcomeXlsx[$row->final_outcome];
                    $outcomeIDNat = ($arrSelectFinalOutcomeXlsx[$row->final_outcome] == '') ? 70 : $arrSelectFinalOutcomeXlsx[$row->final_outcome];
                } else {
                    // Outcome code not in export array (e.g., CANCEL, INC, SS, D, TNC, RW) - skip this record
                    // CANCEL means "@@ - DO NOT REPORT IN AVETMISS" - these records should not be included in the export
                    continue;
                }
            } else {
                $outcomeIDTra = 70;
                $outcomeIDNat = 70;
            }

            // Activity Start Date Validation Rule 1: Activity Start Date must not be after the Collection Period End Date
            // Skip records where Activity Start Date > Collection Period End Date
            if (! empty($objReportsArr['year'])) {
                if (strtotime($row->activity_start_date) > strtotime($objReportsArr['year'].'-12-31')) {
                    // Skip this record - activity start date is after collection year end date
                    continue;
                }
            } elseif (! empty($objReportsArr['month']) && $objReportsArr['reportType'] == 2) {
                if (strtotime($row->activity_start_date) > strtotime(date('Y').'-'.$objReportsArr['month'].'-31')) {
                    // Skip this record - activity start date is after collection month end date
                    continue;
                }
            } elseif (! empty($objReportsArr['from_date']) && ! empty($objReportsArr['to_date'])) {
                if (strtotime($row->activity_start_date) > strtotime($objReportsArr['to_date'])) {
                    // Skip this record - activity start date is after collection end date
                    continue;
                }
            }

            // Activity Start Date Validation Rule 2: Activity Start Date must not be after date of validation
            // unless the Outcome identifier - national is 85 - Not yet started
            // Date of validation = current date when export is generated
            $dateOfValidation = strtotime(date('Y-m-d'));
            $activityStartDate = strtotime($row->activity_start_date);

            if ($activityStartDate > $dateOfValidation) {
                // Activity Start Date is in the future
                // Only allow if Outcome is 85 (Not Yet Started)
                if ($outcomeIDNat != 85) {
                    // Skip this record - activity start date is after validation date but outcome is not 85
                    continue;
                }
            }

            // Activity End Date Validation:
            // Rule 1: If Activity End Date > Collection Year End Date, then Outcome Identifier must be 70 or 85
            // Rule 2: If Activity End Date ≤ Collection Year End Date, then Outcome Identifier 70 should NOT be used (skip record)
            if (! empty($objReportsArr['year'])) {
                $collectionEndDate = strtotime($objReportsArr['year'].'-12-31');
                $activityEndDate = strtotime($row->last_assessment_approved_date);

                if ($activityEndDate > $collectionEndDate) {
                    // Only allow outcome 70 (Continuing Enrolment) or 85 (Not Yet Started) when activity end date is after collection year
                    if (! in_array($outcomeIDNat, [70, 85])) {
                        $outcomeIDNat = 70;
                    }
                } else {
                    // Activity End Date is on or before collection year end date
                    // Outcome 70 should NOT be used - skip this record
                    if ($outcomeIDNat == 70) {
                        continue;
                    }
                }
            } elseif (! empty($objReportsArr['month']) && $objReportsArr['reportType'] == 2) {
                $collectionEndDate = strtotime(date('Y').'-'.$objReportsArr['month'].'-31');
                $activityEndDate = strtotime($row->last_assessment_approved_date);

                if ($activityEndDate > $collectionEndDate) {
                    // Only allow outcome 70 or 85 when activity end date is after collection month
                    if (! in_array($outcomeIDNat, [70, 85])) {
                        $outcomeIDNat = 70;
                    }
                } else {
                    // Activity End Date is on or before collection month end date
                    // Outcome 70 should NOT be used - skip this record
                    if ($outcomeIDNat == 70) {
                        continue;
                    }
                }
            } elseif (! empty($objReportsArr['from_date']) && ! empty($objReportsArr['to_date'])) {
                $collectionEndDate = strtotime($objReportsArr['to_date']);
                $activityEndDate = strtotime($row->last_assessment_approved_date);

                if ($activityEndDate > $collectionEndDate) {
                    // Only allow outcome 70 or 85 when activity end date is after collection end date
                    if (! in_array($outcomeIDNat, [70, 85])) {
                        $outcomeIDNat = 70;
                    }
                } else {
                    // Activity End Date is on or before collection end date
                    // Outcome 70 should NOT be used - skip this record
                    if ($outcomeIDNat == 70) {
                        continue;
                    }
                }
            }

            if ($outcomeIDNat == 70) {

                // If result is CE course enddate should be the activity end date
                // But If student have multipal enrollment for same course only `enroll` and `current student` status condiser for activityend date
                $studentCourse = StudentCourse::where('student_id', $row->student_id)
                    ->where('course_id', $row->course_id)
                    ->get();

                if ($studentCourse->count() > 1) {
                    $getResult = $studentCourse->whereIn('status', ['Enrolled', 'Current Student'])->first();
                    $row->last_assessment_approved_date = $getResult ? $getResult->finish_date : null;
                } else {
                    $row->last_assessment_approved_date = $row->coursefinishdate;
                }

            }

            if ($row->last_assessment_approved_date == '') {
                $row->last_assessment_approved_date = ($row->competency_date != '') ? $row->competency_date : (($row->activity_finish_date != '') ? $row->activity_finish_date : '');
            }
            // ‘NNN’ is only to be used where the :• Outcome identifier - National is recognition of prior learning (‘51’ or ‘52’) or a credit transfer (‘60’)
            // Delivery Mode Identifier Validation
            // If Delivery Mode Identifier = "NNN" (Not Applicable), then Outcome Identifier must be 51, 52, 60, 70, or 85
            // This applies to Recognition of Prior Learning, Credit Transfer, Continuing Enrolment, and Not Yet Started scenarios
            $os = [51, 52, 60, 70, 85];
            if (in_array($outcomeIDNat, $os)) {
                $row->deliveryModetxtfile = 'NNN';
            } elseif ($row->deliveryModetxtfile == '') {
                $row->deliveryModetxtfile = 'YNN';
            }

            if ($row->funding_source_nat == '') {
                $row->funding_source_nat = '20';
            }

            if ($row->module_delivery == 1) {
                if ($objReportsArr['export_type'] = 2) {
                    $unit_code = $row->national_code;
                    $national_code = '';
                } else {
                    $unit_code = '';
                    $national_code = $row->national_code;
                }
            } else {
                $unit_code = $row->unit_code;
                $national_code = $row->national_code;
            }

            $code = $this->getRtoCodeByState($row, $objReportsArr);
            // Final Out Come condtion Start
            if (! empty($row->generated_stud_id)) {

                $school_type_identifier = '';
                if ($row->attending_secondary_school == 1) {
                    $school_type_identifier = $row->school_type;
                }
                $subjectTutionFee = '';
                $feeExemptionId = '';
                $purchasingContractIdentifier = '';
                $purchasingContractScheduleIdentifier = '';
                $attendedHour = '';
                $associatedCourseIdentifier = '';
                $scheduleHours = '';
                $predominantDeliveryMode = '';
                $isFulltimelearing = '';
                $softwareProductName = '';
                $softwareVendorEmail = '';
                $fundingSourceState = '';
                // QLD and SA
                if ($objReportsArr['export_type'] == 1) {
                    // enter where exports type AVAITMISS for State and selecte any state name

                    $isFulltimelearing = 'N';

                    if ($row->is_claim == 1) {
                        $purchasingContractIdentifier = $row->purchasing_contract_identifier;
                        $associatedCourseIdentifier = $row->associated_course_identifier;
                        $isFulltimelearing = ($row->is_fulltimelearing == 0 ? 'N' : 'Y');
                    }
                    $schoolLocationId = '';
                    // SA state
                    if ($objReportsArr['state'] == '04') {
                        $subjectTutionFee = '';
                        $feeExemptionId = '';
                        $attendedHour = $row->school_location_id; // For SA state school_location_id should be submitted in attendedHours field
                        $purchasingContractScheduleIdentifier = ''; // not used in SA - leave blank
                    } else {
                        $subjectTutionFee = $row->tution_fee;
                        $feeExemptionId = $row->fee_exemption_id;
                        $attendedHour = $row->attended_hour;
                        $purchasingContractScheduleIdentifier = $row->purchasing_contract_schedule_identifier;
                    }

                    $scheduleHours = $row->schedule_hours;
                    $predominantDeliveryMode = $row->predominant_delivery_mode;
                    $fundingSourceState = $row->funding_source_state;
                    $softwareProductName = Config::get('constants.arrSoftwareProductName');
                    $softwareVendorEmail = Config::get('constants.arrSoftwareVendorEmail');
                }
                // Include records based on Activity End Date and Outcome Identifier validation
                // If Activity End Date > Collection Year End Date: only include outcome 70 or 85
                // If Activity End Date ≤ Collection Year End Date: include any valid outcome
                if (
                    (in_array($outcomeIDNat, [70, 85]) && strtotime($row->last_assessment_approved_date) > strtotime($objReportsArr['year'].'-12-31')) ||
                    (strtotime($row->last_assessment_approved_date) <= strtotime($objReportsArr['year'].'-12-31'))
                ) {
                    // Record passed all validations - store it for PASS 2
                    $validRecords[] = [
                        'row' => $row,
                        'code' => $code,
                        'unit_code' => $unit_code,
                        'national_code' => $national_code,
                        'outcomeIDNat' => $outcomeIDNat,
                        'school_type_identifier' => $school_type_identifier,
                        'fundingSourceState' => $fundingSourceState,
                        'subjectTutionFee' => $subjectTutionFee,
                        'feeExemptionId' => $feeExemptionId,
                        'purchasingContractIdentifier' => $purchasingContractIdentifier,
                        'purchasingContractScheduleIdentifier' => $purchasingContractScheduleIdentifier,
                        'attendedHour' => $attendedHour,
                        'associatedCourseIdentifier' => $associatedCourseIdentifier,
                        'scheduleHours' => $scheduleHours,
                        'predominantDeliveryMode' => $predominantDeliveryMode,
                        'softwareProductName' => $softwareProductName,
                        'softwareVendorEmail' => $softwareVendorEmail,
                    ];

                    // Collect student ID for NAT00080 validation
                    $validStudentIds[] = $row->generated_stud_id;

                    // Collect program identifier for NAT00030 validation (only if not empty)
                    if (! empty($national_code)) {
                        $validProgramIdentifiers[] = $national_code;
                    }

                    // Collect subject identifier for NAT00060 validation (only if not empty)
                    if (! empty($unit_code)) {
                        $validSubjectIdentifiers[] = $unit_code;
                    }

                    // Collect venue code for NAT00020 validation (only if not empty)
                    if (! empty($row->venue_code)) {
                        $validVenueCodes[] = $row->venue_code;
                    }
                }

            }
        }

        // Get unique student IDs, program identifiers, subject identifiers, and venue codes
        $validStudentIds = array_unique($validStudentIds);
        $validProgramIdentifiers = array_unique($validProgramIdentifiers);
        $validSubjectIdentifiers = array_unique($validSubjectIdentifiers);
        $validVenueCodes = array_unique($validVenueCodes);

        // PASS 2: Generate content for all valid records
        foreach ($validRecords as $record) {
            $row = $record['row'];
            $code = $record['code'];
            $unit_code = $record['unit_code'];
            $national_code = $record['national_code'];
            $outcomeIDNat = $record['outcomeIDNat'];
            $school_type_identifier = $record['school_type_identifier'];
            $fundingSourceState = $record['fundingSourceState'];
            $subjectTutionFee = $record['subjectTutionFee'];
            $feeExemptionId = $record['feeExemptionId'];
            $purchasingContractIdentifier = $record['purchasingContractIdentifier'];
            $purchasingContractScheduleIdentifier = $record['purchasingContractScheduleIdentifier'];
            $attendedHour = $record['attendedHour'];
            $associatedCourseIdentifier = $record['associatedCourseIdentifier'];
            $scheduleHours = $record['scheduleHours'];
            $predominantDeliveryMode = $record['predominantDeliveryMode'];
            $softwareProductName = $record['softwareProductName'];
            $softwareVendorEmail = $record['softwareVendorEmail'];

            $content .= $this->getSpecificNumberStr($code, 10)
                .$this->getSpecificNumberStr($row->venue_code, 10)
                .$this->getSpecificNumberStr($row->generated_stud_id, 10)
                .$this->getSpecificNumberStr($unit_code, 12)
                .$this->getSpecificNumberStr($national_code, 10)
                .$this->getSpecificNumberStr(date('dmY', strtotime($row->activity_start_date)), 8)
                .$this->getSpecificNumberStr(date('dmY', strtotime($row->last_assessment_approved_date)), 8)
                .$this->getSpecificNumberStr($row->deliveryModetxtfile, 3)
                .$this->getSpecificNumberStr($outcomeIDNat, 2)
                .$this->getSpecificNumberStr($row->funding_source_nat, 2)
                .$this->getSpecificNumberStr($row->course_commencing_id, 1)
                .$this->getSpecificNumberStr($row->training_contract_id, 10)
                .$this->getSpecificNumberStr($row->apprenticeship_client_id, 10)
                .$this->getStudyResonString($row->study_reason)
                .$this->getSpecificNumberStr($row->vet_in_school, 1)
                .$this->getSpecificNumberStr($row->funding_identifier, 10)
                .$this->getSpecificNumberStr($school_type_identifier, 2)
                .$this->getSpecificNumberStr($row->outcome_identifier, 3)
                .$this->getSpecificNumberStr($fundingSourceState, 3)
                .$this->getSpecificNumberStr($subjectTutionFee, 5)
                .$this->getSpecificNumberStr($feeExemptionId, 2) // Fee exemption/concession type identifier //used space 2 charater for 1 charater used
                .$this->getSpecificNumberStr($purchasingContractIdentifier, 12) // Purchasing contract identifier
                .$this->getSpecificNumberStr($purchasingContractScheduleIdentifier, 3) // Purchasing contract schedule identifier
                .$this->getSpecificNumberStr($attendedHour, 4)
                .$this->getSpecificNumberStr($associatedCourseIdentifier, 10)  // Associated course identifier
                .$this->getSpecificNumberStr($scheduleHours, 4)  // Scheduled hours
                .$this->getSpecificNumberStr($predominantDeliveryMode, 1)  // Predominant delivery mode
                .$this->getSpecificNumberStr(($row->is_fulltimelearing == 0 ? 'N' : 'Y'), 1)
                .$this->getSpecificNumberStr($softwareProductName, 20)  // Predominant delivery mode
                .$this->getSpecificNumberStr($softwareVendorEmail, 80)
                .PHP_EOL;
        }

        File::put($generateTxtPath.$filename.'.txt', $content);

        // Return array with valid student IDs, program identifiers, subject identifiers, and venue codes for cross-file validation
        return [
            'studentIds' => $validStudentIds,
            'programIdentifiers' => $validProgramIdentifiers,
            'subjectIdentifiers' => $validSubjectIdentifiers,
            'venueCodes' => $validVenueCodes,
        ];
    }

    public function _createTxtNat00120FileOLD($filename, $generateTxtPath, $objReportsArr)
    {

        $content = '';
        $objSubjectEnroll = new StudentSubjectEnrolment;
        $subjectEnroll = $objSubjectEnroll->getStudentSubjectEnrolmentDetailForXlsOld($objReportsArr);

        $arrTrainingOrg = Config::get('constants.arrTraningOrgazinationType');
        $arrTraningOrgazinationState = Config::get('constants.arrTraningOrgazinationState');

        // dd($subjectEnroll);
        foreach ($subjectEnroll as $val => $row) {
            $arrSelectFinalOutcomeXlsx = Config::get('constants.arrSelectFinalOutcomeXlsx');

            // Commencing program identifier start
            if ($row->national_code == '' || $row->national_code == null) {
                $row->course_commencing_id = 8;
            } else {
                if ($row->course_commencing_id == '') {
                    $row->course_commencing_id = 3;
                }
            }
            // Commencing program identifier end
            // VET IN School flag start
            if ($row->vet_in_school == '') {
                $row->vet_in_school = 'N';
            }
            // VET IN School flag end
            // $row->delivery_mode
            if ($row->delivery_mode == '') {
                $row->delivery_mode = 10;
            }

            // Final Out Come Condition start
            if ($row->final_outcome != '') {
                // Check if outcome code exists in the export array (some codes like CANCEL are not exported to AVETMISS)
                if (isset($arrSelectFinalOutcomeXlsx[$row->final_outcome])) {
                    $outcomeIDTra = ($arrSelectFinalOutcomeXlsx[$row->final_outcome] == '') ? 70 : $arrSelectFinalOutcomeXlsx[$row->final_outcome];
                    $outcomeIDNat = ($arrSelectFinalOutcomeXlsx[$row->final_outcome] == '') ? 70 : $arrSelectFinalOutcomeXlsx[$row->final_outcome];
                } else {
                    // Outcome code not in export array (e.g., CANCEL, INC, SS, D, TNC, RW) - skip this record
                    // CANCEL means "@@ - DO NOT REPORT IN AVETMISS" - these records should not be included in the export
                    continue;
                }
            } else {
                $outcomeIDTra = 70;
                $outcomeIDNat = 70;
            }

            // Activity Start Date Validation: Activity Start Date must not be after the Collection Period End Date
            // Skip records where Activity Start Date > Collection Period End Date
            if (! empty($objReportsArr['year'])) {
                if (strtotime($row->activity_start_date) > strtotime($objReportsArr['year'].'-12-31')) {
                    // Skip this record - activity start date is after collection year end date
                    continue;
                }
            } elseif (! empty($objReportsArr['month']) && $objReportsArr['reportType'] == 2) {
                if (strtotime($row->activity_start_date) > strtotime(date('Y').'-'.$objReportsArr['month'].'-31')) {
                    // Skip this record - activity start date is after collection month end date
                    continue;
                }
            } elseif (! empty($objReportsArr['from_date']) && ! empty($objReportsArr['to_date'])) {
                if (strtotime($row->activity_start_date) > strtotime($objReportsArr['to_date'])) {
                    // Skip this record - activity start date is after collection end date
                    continue;
                }
            }

            // Activity End Date Validation: If Activity End Date > Collection Year End Date, then Outcome Identifier must be 70 or 85
            // If Activity End Date ≤ Collection Year End Date, then Outcome Identifier can be any valid value
            if (! empty($objReportsArr['year'])) {
                if (strtotime($row->last_assessment_approved_date) > strtotime($objReportsArr['year'].'-12-31')) {
                    // Only allow outcome 70 (Continuing Enrolment) or 85 (Not Yet Started) when activity end date is after collection year
                    if (! in_array($outcomeIDNat, [70, 85])) {
                        $outcomeIDNat = 70;
                    }
                }
            } elseif (! empty($objReportsArr['month']) && $objReportsArr['reportType'] == 2) {
                if (strtotime($row->last_assessment_approved_date) > strtotime(date('Y').'-'.$objReportsArr['month'].'-31')) {
                    // Only allow outcome 70 or 85 when activity end date is after collection month
                    if (! in_array($outcomeIDNat, [70, 85])) {
                        $outcomeIDNat = 70;
                    }
                }
            } elseif (! empty($objReportsArr['from_date']) && ! empty($objReportsArr['to_date'])) {
                if (strtotime($row->last_assessment_approved_date) > strtotime($objReportsArr['to_date'])) {
                    // Only allow outcome 70 or 85 when activity end date is after collection end date
                    if (! in_array($outcomeIDNat, [70, 85])) {
                        $outcomeIDNat = 70;
                    }
                }
            }

            if ($outcomeIDNat == 70) {

                // If result is CE course enddate should be the activity end date
                // But If student have multipal enrollment for same course only `enroll` and `current student` status condiser for activityend date
                $studentCourse = StudentCourse::where('student_id', $row->student_id)
                    ->where('course_id', $row->course_id)
                    ->get();

                if ($studentCourse->count() > 1) {
                    $getResult = $studentCourse->whereIn('status', ['Enrolled', 'Current Student'])->first();
                    $row->last_assessment_approved_date = $getResult ? $getResult->finish_date : null;
                } else {
                    $row->last_assessment_approved_date = $row->coursefinishdate;
                }

            }

            if ($row->last_assessment_approved_date == '') {
                $row->last_assessment_approved_date = ($row->competency_date != '') ? $row->competency_date : (($row->activity_finish_date != '') ? $row->activity_finish_date : '');
            }
            // ‘NNN’ is only to be used where the :• Outcome identifier - National is recognition of prior learning (‘51’ or ‘52’) or a credit transfer (‘60’)
            // Delivery Mode Identifier Validation
            // If Delivery Mode Identifier = "NNN" (Not Applicable), then Outcome Identifier must be 51, 52, 60, 70, or 85
            // This applies to Recognition of Prior Learning, Credit Transfer, Continuing Enrolment, and Not Yet Started scenarios
            $os = [51, 52, 60, 70, 85];
            if (in_array($outcomeIDNat, $os)) {
                $row->deliveryModetxtfile = 'NNN';
            } elseif ($row->deliveryModetxtfile == '') {
                $row->deliveryModetxtfile = 'YNN';
            }

            if ($row->funding_source_nat == '') {
                $row->funding_source_nat = '20';
            }

            if ($row->module_delivery == 1) {
                if ($objReportsArr['export_type'] = 2) {
                    $unit_code = $row->national_code;
                    $national_code = '';
                } else {
                    $unit_code = '';
                    $national_code = $row->national_code;
                }
            } else {
                $unit_code = $row->unit_code;
                $national_code = $row->national_code;
            }

            $code = $this->getRtoCodeByState($row, $objReportsArr);
            // Final Out Come condtion Start
            if (! empty($row->generated_stud_id)) {

                $school_type_identifier = '';
                if ($row->attending_secondary_school == 1) {
                    $school_type_identifier = $row->school_type;
                }
                $subjectTutionFee = '';
                $feeExemptionId = '';
                $purchasingContractIdentifier = '';
                $purchasingContractScheduleIdentifier = '';
                $attendedHour = '';
                $associatedCourseIdentifier = '';
                $scheduleHours = '';
                $predominantDeliveryMode = '';
                $isFulltimelearing = '';
                $softwareProductName = '';
                $softwareVendorEmail = '';
                $fundingSourceState = '';
                // QLD and SA
                if ($objReportsArr['export_type'] == 1) {
                    // enter where exports type AVAITMISS for State and selecte any state name

                    $isFulltimelearing = 'N';

                    if ($row->is_claim == 1) {
                        $purchasingContractIdentifier = $row->purchasing_contract_identifier;
                        $associatedCourseIdentifier = $row->associated_course_identifier;
                        $isFulltimelearing = ($row->is_fulltimelearing == 0 ? 'N' : 'Y');
                    }
                    $schoolLocationId = '';
                    // SA state
                    if ($objReportsArr['state'] == '04') {
                        $subjectTutionFee = '';
                        $feeExemptionId = '';
                        $attendedHour = $row->school_location_id; // For SA state school_location_id should be submitted in attendedHours field
                        $purchasingContractScheduleIdentifier = ''; // not used in SA - leave blank
                    } else {
                        $subjectTutionFee = $row->tution_fee;
                        $feeExemptionId = $row->fee_exemption_id;
                        $attendedHour = $row->attended_hour;
                        $purchasingContractScheduleIdentifier = $row->purchasing_contract_schedule_identifier;
                    }

                    $scheduleHours = $row->schedule_hours;
                    $predominantDeliveryMode = $row->predominant_delivery_mode;
                    $fundingSourceState = $row->funding_source_state;
                    $softwareProductName = Config::get('constants.arrSoftwareProductName');
                    $softwareVendorEmail = Config::get('constants.arrSoftwareVendorEmail');
                }
                // Include records based on Activity End Date and Outcome Identifier validation
                // If Activity End Date > Collection Year End Date: only include outcome 70 or 85
                // If Activity End Date ≤ Collection Year End Date: include any valid outcome
                if (
                    (in_array($outcomeIDNat, [70, 85]) && strtotime($row->last_assessment_approved_date) > strtotime($objReportsArr['year'].'-12-31')) ||
                    (strtotime($row->last_assessment_approved_date) <= strtotime($objReportsArr['year'].'-12-31'))
                ) {
                    $content .= $this->getSpecificNumberStr($code, 10)
                    .$this->getSpecificNumberStr($row->venue_code, 10)
                    .$this->getSpecificNumberStr($row->generated_stud_id, 10)
                    .$this->getSpecificNumberStr($unit_code, 12)
                    .$this->getSpecificNumberStr($national_code, 10)
                    .$this->getSpecificNumberStr(date('dmY', strtotime($row->activity_start_date)), 8)
                    .$this->getSpecificNumberStr(date('dmY', strtotime($row->last_assessment_approved_date)), 8)
                    .$this->getSpecificNumberStr($row->deliveryModetxtfile, 3)
                    .$this->getSpecificNumberStr($outcomeIDNat, 2)
                    .$this->getSpecificNumberStr($row->funding_source_nat, 2)
                    .$this->getSpecificNumberStr($row->course_commencing_id, 1)
                    .$this->getSpecificNumberStr($row->training_contract_id, 10)
                    .$this->getSpecificNumberStr($row->apprenticeship_client_id, 10)
                    .$this->getStudyResonString($row->study_reason)
                    .$this->getSpecificNumberStr($row->vet_in_school, 1)
                    .$this->getSpecificNumberStr($row->funding_identifier, 10)
                    .$this->getSpecificNumberStr($school_type_identifier, 2)
                    .$this->getSpecificNumberStr($row->outcome_identifier, 3)
                    .$this->getSpecificNumberStr($fundingSourceState, 3)
                    .$this->getSpecificNumberStr($subjectTutionFee, 5)
                    .$this->getSpecificNumberStr($feeExemptionId, 2) // Fee exemption/concession type identifier //used space 2 charater for 1 charater used
                    .$this->getSpecificNumberStr($purchasingContractIdentifier, 12) // Purchasing contract identifier
                    .$this->getSpecificNumberStr($purchasingContractScheduleIdentifier, 3) // Purchasing contract schedule identifier
                    .$this->getSpecificNumberStr($attendedHour, 4)
                    .$this->getSpecificNumberStr($associatedCourseIdentifier, 10)  // Associated course identifier
                    .$this->getSpecificNumberStr($scheduleHours, 4)  // Scheduled hours
                    .$this->getSpecificNumberStr($predominantDeliveryMode, 1)  // Predominant delivery mode
                    .$this->getSpecificNumberStr(($row->is_fulltimelearing == 0 ? 'N' : 'Y'), 1)
                    .$this->getSpecificNumberStr($softwareProductName, 20)  // Predominant delivery mode
                    .$this->getSpecificNumberStr($softwareVendorEmail, 80)
                    .PHP_EOL;
                }

            }
        }

        File::put($generateTxtPath.$filename.'.txt', $content);

        return true;
    }

    public function _createTxtNat00130File($filename, $generateTxtPath, $objReportsArr, $validProgramIdentifiers = [], $validTrainingOrgIdentifier = '')
    {

        $objStudentCourse = new StudentCourse;
        $studentCourseDetails = $objStudentCourse->getStudentDetailsForXlsx130($objReportsArr);

        // PASS 1: Validate all records and collect valid ones with their program identifiers and student IDs
        $validRecords = [];
        $validProgramIdentifiersFromNat00130 = [];
        $validStudentIdsFromNat00130 = [];

        foreach ($studentCourseDetails as $row) {
            $code = $this->getRtoCodeByState($row, $objReportsArr);

            // AVETMISS Validation: Training Organisation Identifier in NAT00130 must exist in NAT00010
            // Skip records where the training org identifier doesn't match NAT00010
            // if (!empty($validTrainingOrgIdentifier) && $code !== $validTrainingOrgIdentifier) {
            //     continue;
            // }

            // Check if finish date is within collection period
            if ((strtotime($row->finish_date) <= strtotime($objReportsArr['year'].'-12-31'))) {
                // Store valid record
                $validRecords[] = [
                    'code' => $code,
                    'national_code' => $row->national_code,
                    'generated_stud_id' => $row->generated_stud_id,
                    'finish_date' => $row->finish_date,
                    'certificate_no' => $row->certificate_no,
                    'issueDate' => $row->issueDate,
                ];

                // Collect program identifier for NAT00030 validation (only if not empty)
                if (! empty($row->national_code)) {
                    $validProgramIdentifiersFromNat00130[] = $row->national_code;
                }

                // Collect student ID for NAT00080, NAT00085, NAT00090, NAT00100 validation (only if not empty)
                if (! empty($row->generated_stud_id)) {
                    $validStudentIdsFromNat00130[] = $row->generated_stud_id;
                }
            }
        }

        // Get unique program identifiers and student IDs from NAT00130
        $validProgramIdentifiersFromNat00130 = array_unique($validProgramIdentifiersFromNat00130);
        $validStudentIdsFromNat00130 = array_unique($validStudentIdsFromNat00130);

        // PASS 2: Generate content from valid records only
        $content = '';
        foreach ($validRecords as $record) {
            $certificateIssueYesNo = (! empty($record['certificate_no'])) ? 'Y' : 'N';
            $content .= $this->getSpecificNumberStr($record['code'], 10)
                    .$this->getSpecificNumberStr($record['national_code'], 10)
                    .$this->getSpecificNumberStr($record['generated_stud_id'], 10)
                    .$this->getSpecificNumberStr(date('dmY', strtotime($record['finish_date'])), 8)
                    .$certificateIssueYesNo
                    .$this->getSpecificNumberStr(date('dmY', strtotime($record['issueDate'])), 8)
                    .$this->getSpecificNumberStr($record['certificate_no'], 25)
                    .PHP_EOL;
        }

        File::put($generateTxtPath.$filename.'.txt', $content);

        // Return array with valid program identifiers and student IDs for cross-file validation
        return [
            'programIdentifiers' => $validProgramIdentifiersFromNat00130,
            'studentIds' => $validStudentIdsFromNat00130,
        ];
    }

    public function spaceStringFunction($numberOfString)
    {
        $spaceString = '';
        for ($i = 0; $i < $numberOfString; $i++) {
            $spaceString = ' '.$spaceString;
        }

        return $spaceString;
    }

    public function getSpecificNumberStr($str, $totalLenth)
    {
        $str = trim($str);
        $returnStr = '';
        $spaceString = '';
        if ($str == '') {
            for ($i = 0; $i < $totalLenth; $i++) {
                $returnStr .= ' '.$spaceString;
            }
        } else {
            $strLenth = strlen(trim($str));
            $returnStr = trim($str);
            if ($totalLenth > $strLenth) {
                for ($i = 0; $i < ($totalLenth - $strLenth); $i++) {
                    $returnStr .= ' '.$spaceString;
                }
            } elseif ($totalLenth == $strLenth) {
                $returnStr = $str;
            } else {
                $returnStr = substr($str, 0, $totalLenth);
            }
        }

        return $returnStr;
    }

    public function getStudyResonString($studyReason)
    {
        return ($studyReason == '') ? '@@' : ((strlen($studyReason) == 1) ? '0'.$studyReason : $studyReason);
    }
}
