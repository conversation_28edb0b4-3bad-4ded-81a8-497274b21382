<script setup>
import { computed, inject, ref } from 'vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormPhoneInput from '@spa/components/KendoInputs/FormPhoneInput2.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormUploader from '@spa/components/KendoInputs/FormUploader.vue';
import CountrySelect from '@spa/modules/country/CountrySelect.vue';
import FormRadioGroup from '@spa/components/KendoInputs/FormRadioGroup.vue';
import IndustriesSelect from '@spa/modules/industries/IndustriesSelect.vue';
import StaffSelect from '@spa/modules/timesheet-submission/StaffSelect.vue';
import FormNumericInput from '@spa/components/KendoInputs/FormNumericInput.vue';
import StaffsSelect from '@spa/modules/users/staffs/StaffsSelect.vue';
import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
import { getValidationMessage } from '@spa/composables/formComposables.js';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
import AgentStatusSelect from '@spa/modules/agentstatus/AgentStatusSelect.vue';
import { hasFieldAccess } from '@spa/composables/formComposables.js';
import AgentSelect from '@spa/modules/agent/AgentSelect.vue';

const props = defineProps({
    userType: {},
    modelValue: {},
});
const userType = computed(() => {
    return props.userType ?? route().params.user_type;
});
const isEdit = inject('isEdit');
const emit = defineEmits(['update:modelValue']);
const formData = computed({
    get() {
        return props.modelValue || {};
    },
    set(value) {
        emit('update:modelValue', value);
    },
});

const nameLabel = computed(() => {
    if (hasFieldAccess(['staff', 'teacher'], userType)) {
        return 'Name';
    } else if (hasFieldAccess(['agent', 'agent-staff'], userType)) {
        return 'Agency Name';
    } else if (hasFieldAccess(['employer', 'serviceprovider'], userType)) {
        return 'Company Name';
    } else if (hasFieldAccess(['placementprovider'], userType)) {
        return 'Provider Name';
    }
    return 'Name';
});
</script>

<template>
    <div class="space-y-6 border-gray-200" :class="{ 'border-r pr-8': !isEdit }">
        <h3 class="text-base font-semibold text-gray-800" v-if="!isEdit">Personal Details:</h3>
        <div class="grid grid-cols-2 gap-x-4 gap-y-6">
            <div
                class="col-span-2 xl:col-span-1"
                v-if="
                    hasFieldAccess(
                        ['agent', 'placementprovider', 'serviceprovider', 'employer'],
                        userType
                    )
                "
            >
                <FormInput
                    v-model="formData.name"
                    name="name"
                    :label="nameLabel"
                    :placeholder="nameLabel"
                    :required="true"
                    :validation-message="formData?.errors?.name?.[0]"
                    :valid="formData.errors?.name?.length"
                    :touched="formData.errors?.name?.length"
                />
            </div>
            <div
                class="col-span-2 xl:col-span-1"
                v-if="hasFieldAccess(['serviceprovider'], userType)"
            >
                <FormInput
                    v-model="formData.emergency_contact_name"
                    name="contact_name"
                    label="Contact Name"
                    placeholder="Contact Name"
                    :required="true"
                    :validation-message="formData?.errors?.emergency_contact_name?.[0]"
                    :valid="formData.errors?.emergency_contact_name?.length"
                    :touched="formData.errors?.emergency_contact_name?.length"
                />
            </div>
            <div class="col-span-2 xl:col-span-1" v-if="hasFieldAccess(['employer'], userType)">
                <FormInput
                    v-model="formData.trading_name"
                    name="trading_name"
                    label="Trading Name"
                    placeholder="Trading Name"
                    :required="true"
                    :validation-message="formData?.errors?.trading_name?.[0]"
                    :valid="formData.errors?.trading_name?.length"
                    :touched="formData.errors?.trading_name?.length"
                />
            </div>
            <div
                class="col-span-2 xl:col-span-1"
                v-if="hasFieldAccess(['agent', 'placementprovider'], userType)"
            >
                <FormInput
                    v-model="formData.agent_code"
                    name="agent_code"
                    :label="
                        hasFieldAccess(['placementprovider'], userType)
                            ? 'Provider Code'
                            : 'Agent Code'
                    "
                    :placeholder="
                        hasFieldAccess(['placementprovider'], userType)
                            ? 'Provider Code'
                            : 'Agent Code'
                    "
                    :required="true"
                    :validation-message="formData?.errors?.agent_code?.[0]"
                    :valid="formData.errors?.agent_code?.length"
                    :touched="formData.errors?.agent_code?.length"
                />
            </div>
            <div
                class="col-span-2 grid grid-cols-3 gap-x-4 gap-y-6"
                :class="
                    hasFieldAccess(['staff', 'teacher'], userType) ? 'grid-cols-3' : 'grid-cols-2'
                "
                v-if="
                    hasFieldAccess(
                        ['staff', 'teacher', 'placementprovider', 'agent-staff'],
                        userType
                    )
                "
            >
                <div
                    class="col-span-3 grid grid-cols-3 gap-x-4 gap-y-6 xl:col-span-3"
                    v-if="hasFieldAccess(['staff', 'teacher'], userType)"
                >
                    <FormDropDown
                        v-model="formData.name_title"
                        name="name_title"
                        label="Title"
                        :data-items="[
                            { value: 'Mr', text: 'Mr' },
                            { value: 'Mrs', text: 'Mrs' },
                            { value: 'Ms', text: 'Ms' },
                            { value: 'Dr', text: 'Dr' },
                            { value: 'Prof', text: 'Prof' },
                        ]"
                        :default-item="{ value: '-', text: 'Select' }"
                        :value-primitive="true"
                        text-field="text"
                        value-field="value"
                        placeholder="Select"
                        :required="true"
                        v-bind="getValidationMessage(formData, 'name_title')"
                    />
                </div>
                <div class="col-span-3 xl:col-span-1">
                    <FormInput
                        v-model="formData.first_name"
                        name="first_name"
                        label="First Name"
                        placeholder="First Name"
                        :required="true"
                        v-bind="getValidationMessage(formData, 'first_name')"
                    />
                </div>
                <div
                    class="col-span-3 xl:col-span-1"
                    v-if="hasFieldAccess(['staff', 'teacher'], userType)"
                >
                    <FormInput
                        v-model="formData.middle_name"
                        name="middle_name"
                        label="Middle Name"
                        placeholder="Middle Name"
                        v-bind="getValidationMessage(formData, 'middle_name')"
                    />
                </div>
                <div class="col-span-3 xl:col-span-1">
                    <FormInput
                        v-model="formData.last_name"
                        name="last_name"
                        label="Last Name"
                        placeholder="Last Name"
                        :required="true"
                        v-bind="getValidationMessage(formData, 'last_name')"
                    />
                </div>
            </div>
            <!--            Email -->
            <div class="col-span-2 xl:col-span-1">
                <FormInput
                    v-model="formData.email"
                    name="email"
                    label="Personal Email"
                    placeholder="<EMAIL>"
                    type="email"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'email')"
                />
            </div>
            <div
                class="col-span-2 xl:col-span-1"
                v-if="hasFieldAccess(['staff', 'teacher', 'agent'], userType)"
            >
                <FormInput
                    v-model="formData.email2"
                    name="email2"
                    label="Work Email"
                    placeholder="<EMAIL>"
                    type="email"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'email2')"
                />
            </div>
            <!--            Phone Fields -->
            <div
                class="col-span-2 xl:col-span-1"
                v-if="hasFieldAccess(['staff', 'teacher', 'agent', 'employer'], userType)"
            >
                <FormPhoneInput
                    v-model="formData.phone"
                    name="phone"
                    :label="
                        hasFieldAccess(['staff', 'teacher', 'agent'], userType)
                            ? 'Phone Number (Work)'
                            : 'Phone Number'
                    "
                    :required="true"
                    v-bind="getValidationMessage(formData, 'phone')"
                />
            </div>
            <div class="col-span-2 xl:col-span-1">
                <FormPhoneInput
                    v-model="formData.mobile"
                    name="phone"
                    :label="
                        hasFieldAccess(['staff', 'teacher', 'agent', 'agent-staff'], userType)
                            ? 'Phone Number (Personal)'
                            : 'Mobile Number'
                    "
                    :required="true"
                    v-bind="getValidationMessage(formData, 'mobile')"
                />
            </div>
            <div
                class="col-span-2 xl:col-span-1"
                v-if="hasFieldAccess(['agent', 'agent-staff'], userType)"
            >
                <FormRadioGroup
                    v-model="formData.is_agency"
                    name="is_agency"
                    label="Is part of an agency?"
                    layout="horizontal"
                    :data-items="[
                        { value: 1, label: 'Yes' },
                        { value: 0, label: 'No' },
                    ]"
                    :value-primitive="true"
                    text-field="label"
                    value-field="value"
                    :required="true"
                />
            </div>
            <div
                class="col-span-2 xl:col-span-1"
                v-if="formData.is_agency && hasFieldAccess(['agent', 'agent-staff'], userType)"
            >
                <AgentSelect
                    v-model="formData.super_agent_id"
                    label="Parent Agency"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'super_agent_id')"
                />
            </div>
            <div class="col-span-2 xl:col-span-1" v-if="hasFieldAccess(['agent'], userType)">
                <FormNumericInput
                    v-model="formData.total_employes"
                    name="total_employes"
                    label="Number of Employees"
                    placeholder="Number of Employees"
                    :required="true"
                    :validation-message="formData?.errors?.total_employes?.[0]"
                    :valid="formData.errors?.total_employes?.length"
                    :touched="formData.errors?.total_employes?.length"
                />
            </div>
            <div class="col-span-2 xl:col-span-1" v-if="hasFieldAccess(['agent'], userType)">
                <AgentStatusSelect
                    v-model="formData.status"
                    label="Agent Status"
                    v-bind="getValidationMessage(formData, 'status')"
                    :required="true"
                    :indicaterequired="true"
                />
            </div>
            <div
                class="col-span-2 xl:col-span-1"
                v-if="hasFieldAccess(['agent', 'employer'], userType)"
            >
                <IndustriesSelect
                    v-model="formData.industry_id"
                    label="Type of Industry"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'industry_id')"
                />
            </div>
            <div class="col-span-2 xl:col-span-1" v-if="hasFieldAccess(['agent'], userType)">
                <StaffsSelect
                    v-model="formData.account_manager_id"
                    label="Account Manager"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'account_manager_id')"
                />
            </div>
            <div class="col-span-2" v-if="hasFieldAccess(['agent', 'serviceprovider'], userType)">
                <FormTextArea
                    v-model="formData.notes"
                    name="notes"
                    label="Notes"
                    placeholder="Notes"
                    v-bind="getValidationMessage(formData, 'notes')"
                />
            </div>
            <div
                class="col-span-2 xl:col-span-1"
                v-if="hasFieldAccess(['agent', 'serviceprovider', 'placementprovider'], userType)"
            >
                <FormInput
                    v-model="formData.website"
                    name="website"
                    label="Website"
                    placeholder="Website"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'website')"
                />
            </div>
            <div
                class="col-span-2 xl:col-span-1"
                v-if="hasFieldAccess(['agent', 'serviceprovider', 'employer'], userType)"
            >
                <FormInput
                    v-model="formData.fax"
                    name="fax"
                    label="Fax"
                    placeholder="Fax"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'fax')"
                />
            </div>
            <div
                class="col-span-2 xl:col-span-1"
                v-if="hasFieldAccess(['placementprovider'], userType)"
            >
                <FormInput
                    v-model="formData.category"
                    name="category"
                    label="Category"
                    placeholder="Category"
                    :required="true"
                    :validation-message="formData?.errors?.category?.[0]"
                    :valid="formData.errors?.category?.length"
                    :touched="formData.errors?.category?.length"
                />
            </div>
            <!--            Gender -->
            <div
                class="col-span-2 xl:col-span-1"
                v-if="hasFieldAccess(['staff', 'teacher'], userType)"
            >
                <FormDropDown
                    v-model="formData.gender"
                    label="Gender"
                    :data-items="[
                        { value: 'M', text: 'Male' },
                        { value: 'F', text: 'Female' },
                        { value: 'O', text: 'Other' },
                    ]"
                    :default-item="{ value: '-', text: 'Select Gender' }"
                    :value-primitive="true"
                    text-field="text"
                    value-field="value"
                    placeholder="Select"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'gender')"
                />
            </div>
            <div
                class="col-span-2 xl:col-span-1"
                v-if="hasFieldAccess(['staff', 'teacher'], userType)"
            >
                <FormDatePicker
                    name="birth_date"
                    label="Date of Birth"
                    v-model="formData.birth_date"
                    emit-format="yyyy-MM-dd"
                    v-bind="getValidationMessage(formData, 'birth_date')"
                />
            </div>
            <!-- Nationality -->
            <div
                class="col-span-2 xl:col-span-1"
                v-if="hasFieldAccess(['staff', 'teacher'], userType)"
            >
                <CountrySelect
                    v-model="formData.country"
                    label="Country of Birth"
                    :required="true"
                    v-bind="getValidationMessage(formData, 'country')"
                    :async="false"
                />
            </div>
            <div class="col-span-2">
                <FormUploader
                    v-model="formData.user_image"
                    label="Upload User Image"
                    accept="image/*"
                    :max-file-size="30000000"
                    v-bind="getValidationMessage(formData, 'user_image')"
                />
            </div>
        </div>
    </div>
</template>

<style scoped></style>
