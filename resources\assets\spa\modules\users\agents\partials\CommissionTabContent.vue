<template>
    <Card :variant="'compact'" :pt="{ root: 'bg-gray-100' }">
        <template #header>
            <div class="flex items-center gap-2">
                <h2 class="text-lg font-medium">Commission</h2>
            </div>
        </template>
        <template #content>
            <div>
                <AgentCommissionListComponent
                    :filters="{
                        agent_id: store.formData?.id,
                    }"
                />
            </div>
        </template>
    </Card>
</template>
<script setup>
import Card from '@spa/components/Card/Card.vue';
import AgentCommissionListComponent from '@spa/modules/agent-commission/AgentCommissionListComponent.vue';

const props = defineProps({
    store: Object,
});
</script>
