<script setup>
import { computed, onMounted, ref } from 'vue';
import KDropdown from '@spa/components/DropdownMenu/KDropdown.vue';
const props = defineProps({
    modelValue: {},
    style: {
        type: String,
        default: { width: 'fit-content', minWidth: '200px' },
    },
    options: {
        type: Array,
        default: () => [],
    },
    placeholder: {
        type: String,
        default: '',
    },
    valueField: {
        type: String,
        default: 'value',
    },
    textField: {
        type: String,
        default: 'text',
    },
});
const emit = defineEmits(['update:modelValue']);
const vModel = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
});
const defaultItem = computed(() => ({
    [props.textField]: props.placeholder,
    [props.valueField]: null,
}));
</script>

<template>
    <KDropdown
        :text-field="textField"
        :value-field="valueField"
        :dataItems="options"
        :style="style"
        :popup-settings="{
            animate: false,
        }"
        tag-render="tagRender"
        :autoClose="true"
        :default-item="defaultItem"
        v-model="vModel"
        :valuePrimitive="true"
    />
</template>

<style scoped></style>
