<template>
    <grid-wrapper
        :fullWidth="false"
        :rounded="true"
        :actionSticky="true"
        class="tw-attendance-import-grid"
    >
        <k-grid
            ref="attendanceImportGrid"
            :height="'100vh'"
            :columns="columns"
            :data-items="gridData"
            :loading="loaderStore.contextLoaders['grid-loader']"
            :loader="'loaderTemplate'"
            :resizable="true"
            :sortable="{ allowUnsort: false }"
            :selected-field="'selected'"
            :sort="sort ? sort : []"
            :pageable="{
                buttonCount: 5,
                info: true,
                type: 'numeric',
                pageSizes: pagination.pageSizes || [10, 20, 50, 100],
                previousNext: true,
            }"
            :skip="getSkip"
            :take="getPerpageSize"
            :total="getTotalRecordsCount"
            @sortchange="sortChangeHandler"
            @pagechange="pageChangeHandler"
            @selectionchange="onSelectionChange"
            @headerselectionchange="onHeaderSelectionChange"
            :row-render="rowRender"
        >
            <k-grid-no-records>
                <empty-state />
            </k-grid-no-records>
            <template #selectedHeaderTemplate="{ props }">
                <Checkbox :binary="true" v-bind="props" :checked="selectAll" />
            </template>
            <template #loaderTemplate>
                <table-loader v-if="loaderStore.contextLoaders['grid-loader']" />
            </template>
            <template #sortingHeaderCell="{ props }">
                <header-cell v-bind:props="props" />
            </template>
            <template #nonSortingHeaderCell="{ props }">
                <div class="flex items-center gap-2">
                    <span class="text-13 font-normal">{{ props.title }}</span>
                </div>
            </template>
            <template #defaultCell="{ props }">
                <default-cell v-bind:props="props" />
            </template>
            <template #badgeCell="{ props }">
                <badge-cell
                    v-bind:props="props"
                    :badge="{ variant: getBadgeVariant(props.dataItem[props.field]) }"
                />
            </template>
            <template #dateCell="{ props }">
                <date-cell v-bind:props="props" :format="dateFormat" />
            </template>

            <template #actionCell="{ props }">
                <td :class="props.className">
                    <div class="flex items-center space-x-1">
                        <GridActionButton
                            @click="viewDetails(props.dataItem)"
                            tooltip-title="View Details"
                        >
                            <icon
                                :name="'eye'"
                                :fill="'currentColor'"
                                :height="'20'"
                                :width="'20'"
                            />
                        </GridActionButton>
                        <GridActionButton
                            @click="viewError(props.dataItem)"
                            tooltip-title="View Error"
                            v-if="
                                props.dataItem.error_message && props.dataItem.status === 'failed'
                            "
                        >
                            <icon
                                :name="'warning'"
                                :height="'20'"
                                :width="'20'"
                                :fill="'currentColor'"
                            />
                        </GridActionButton>
                        <GridActionButton
                            @click="editRecord(props.dataItem)"
                            v-if="props.dataItem.status === 'failed'"
                            tooltip-title="Edit Entry"
                        >
                            <icon
                                :name="'edit'"
                                :fill="'currentColor'"
                                :height="'18'"
                                :width="'18'"
                            />
                        </GridActionButton>
                        <GridActionButton
                            v-if="
                                props.dataItem.status === 'failed' ||
                                props.dataItem.status === 'pending'
                            "
                            @click="resyncImport(props.dataItem)"
                            tooltip-title="Retry Import"
                        >
                            <icon
                                :name="'sync-time'"
                                :fill="'currentColor'"
                                :height="'20'"
                                :width="'20'"
                            />
                        </GridActionButton>
                        <GridActionButton
                            v-if="props.dataItem.status !== 'completed'"
                            @click="confirmDelete(props.dataItem)"
                            tooltip-title="Delete Entry"
                            class="text-red-500"
                        >
                            <icon
                                :name="'delete'"
                                :fill="'currentColor'"
                                :height="'20'"
                                :width="'20'"
                            />
                        </GridActionButton>
                    </div>
                </td>
            </template>
        </k-grid>
    </grid-wrapper>
</template>

<script>
import GridWrapperVue from '@spa/components/KendoGrid/GridWrapper.vue';
import { Grid, GridNoRecords } from '@progress/kendo-vue-grid';
import EmptyState from '@spa/components/KendoGrid/EmptyState';
import TableLoader from '@spa/components/KendoGrid/TableLoader';
import DefaultCellTemplateVue from '@spa/components/KendoGrid/templates/DefaultCellTemplate.vue';
import BadgeCellTemplateVue from '@spa/components/KendoGrid/templates/BadgeCellTemplate.vue';
import DateCellTemplate from '@spa/components/KendoGrid/templates/DateCellTemplate.vue';

import { useLoaderStore } from '@spa/stores/modules/global-loader';
import HeaderTemplate from '@spa/components/KendoGrid/templates/HeaderTemplate.vue';
import Button from '@spa/components/Buttons/Button.vue';
import { DEFAULT_DATE_FORMAT } from '@spa/helpers/constants.js';
import Checkbox from '@spa/components/Checkbox.vue';
import GridActionButton from '@spa/components/AsyncComponents/Grid/Partials/GridActionButton.vue';

export default {
    props: {
        data: { type: Array, default: () => [] },
        pagination: { type: Object, default: () => ({}) },
        sort: { type: Array, default: () => [] },
        selectedIds: { type: Array, default: () => [] },
        selectAll: { type: Boolean, default: false },
    },
    setup() {
        const loaderStore = useLoaderStore();
        return { loaderStore };
    },
    components: {
        GridActionButton,
        'grid-wrapper': GridWrapperVue,
        'k-grid': Grid,
        'k-grid-no-records': GridNoRecords,
        'empty-state': EmptyState,
        'table-loader': TableLoader,
        'default-cell': DefaultCellTemplateVue,
        'badge-cell': BadgeCellTemplateVue,
        'date-cell': DateCellTemplate,
        'header-cell': HeaderTemplate,
        Button,
        Checkbox,
    },
    data() {
        return {
            columns: [
                {
                    field: 'selected',
                    width: '50px',
                    headerSelectionValue: this.selectAll,
                    headerCell: 'selectedHeaderTemplate',
                },
                {
                    field: 'studentId',
                    title: 'Student ID',
                    cell: 'defaultCell',
                    headerCell: 'nonSortingHeaderCell',
                    sortable: false,
                    minResizableWidth: 120,
                    width: 120,
                },
                {
                    field: 'unitCode',
                    title: 'Unit Code',
                    cell: 'defaultCell',
                    headerCell: 'nonSortingHeaderCell',
                    sortable: false,
                    minResizableWidth: 120,
                    width: 120,
                },
                {
                    field: 'attendanceDate',
                    title: 'Attendance Date',
                    cell: 'defaultCell',
                    headerCell: 'nonSortingHeaderCell',
                    sortable: false,
                    minResizableWidth: 150,
                    width: 150,
                },
                {
                    field: 'attendanceStatus',
                    title: 'Status (CSV)',
                    cell: 'defaultCell',
                    headerCell: 'nonSortingHeaderCell',
                    sortable: false,
                    minResizableWidth: 120,
                    width: 120,
                },
                {
                    field: 'status',
                    title: 'Import Status',
                    cell: 'badgeCell',
                    headerCell: 'sortingHeaderCell',
                    sortable: true,
                    minResizableWidth: 120,
                    width: 120,
                },
                {
                    field: 'imported_by',
                    title: 'Imported By',
                    cell: 'defaultCell',
                    headerCell: 'nonSortingHeaderCell',
                    sortable: false,
                    minResizableWidth: 120,
                    width: 120,
                },
                {
                    field: 'created_at',
                    title: 'Imported At',
                    cell: 'dateCell',
                    headerCell: 'sortingHeaderCell',
                    sortable: true,
                    minResizableWidth: 150,
                    width: 150,
                },
                {
                    field: 'actions',
                    title: 'Actions',
                    cell: 'actionCell',
                    sortable: false,
                    width: 180,
                },
                {},
            ],
            dateFormat: DEFAULT_DATE_FORMAT,
        };
    },
    computed: {
        gridData() {
            return this.prepareTableData(this.data);
        },
        getSkip() {
            return this.pagination.skip || 0;
        },
        getPerpageSize() {
            return this.pagination.take || 10;
        },
        getTotalRecordsCount() {
            return this.pagination.total || 0;
        },
    },
    methods: {
        sortChangeHandler(e) {
            this.$emit('sort', e.sort);
        },
        pageChangeHandler(e) {
            this.$emit('changepage', e);
        },
        viewDetails(item) {
            this.$emit('view-details', item);
        },
        resyncImport(item) {
            this.$emit('resync', item);
        },
        confirmDelete(item) {
            this.$emit('delete', item);
        },
        editRecord(item) {
            this.$emit('edit', item);
        },
        viewError(item) {
            this.$emit('view-error', item.error_message);
        },
        toggleSelectAll() {
            this.$emit('toggle-select-all');
        },
        toggleSelectItem(id) {
            this.$emit('toggle-select-item', id);
        },
        isSelected(id) {
            return this.selectedIds.includes(id);
        },
        onHeaderSelectionChange(event) {
            let checked = event.event.target.checked;
            this.gridData.forEach((item) => {
                if (item.status !== 'completed') {
                    item.selected = checked;
                }
            });
            this.$emit('headerselectionchange', event, this.gridData);
        },
        onSelectionChange(event) {
            if (event.dataItem.status === 'completed') {
                event.dataItem.selected = false;
                return false;
            }
            event.dataItem.selected = !event.dataItem.selected;
            this.$emit('selectionchange', event);
        },
        prepareTableData(data) {
            if (!Array.isArray(data)) return [];
            return data.map((item) => ({
                id: item.id,
                selected: this.isSelected(item.id),
                studentId: item.import_data?.StudentId || 'N/A',
                unitCode: item.import_data?.UnitCode || 'N/A',
                attendanceDate: item.import_data?.AttendanceDate || 'N/A',
                attendanceStatus: item.import_data?.AttendanceStatus || 'N/A',
                status: item.status,
                imported_by: item.creator?.name || 'N/A',
                created_at: item.created_at,
                import_data: item.import_data,
                error_message: item.error_message,
            }));
        },
        rowRender(h, defaultRendering, defaultSlots, props) {
            return h(
                'tr',
                {
                    ...defaultRendering.props,
                    'data-status': props.dataItem.status,
                    class: `${defaultRendering.props.class} ${props.dataItem.status === 'completed' ? 'completed-row' : ''}`,
                },
                defaultRendering.children
            );
        },
        getBadgeVariant(status) {
            switch (status) {
                case 'completed':
                    return 'success';
                case 'pending':
                    return 'warning';
                case 'failed':
                    return 'error';
                default:
                    return 'secondary';
            }
        },
    },
};
</script>

<style lang="scss">
.tw-attendance-import-grid {
    .k-grid-header {
        background-color: #f9fafb;
    }
    .k-grid-content {
        overflow-y: auto;
    }
    .k-grid-table {
        .k-grid-content-sticky {
            background-color: white;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
        }
        tr[data-status='completed'] {
            .k-checkbox,
            .k-checkbox-wrapper,
            input[type='checkbox'] {
                opacity: 0.5;
                cursor: not-allowed;
                pointer-events: none;
            }
            opacity: 0.8;
            background-color: #f9fafb;
            &:hover {
                background-color: #f3f4f6;
            }
        }
        .completed-row {
            .k-checkbox {
                opacity: 0.5 !important;
                cursor: not-allowed !important;
                pointer-events: none !important;
            }
        }
    }
}
</style>
