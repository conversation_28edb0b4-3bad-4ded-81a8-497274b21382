<template>
    <SidebarDrawer
        :visibleDialog="visible"
        :hideOnOverlayClick="false"
        :fixedActionBar="true"
        :width="'50%'"
        @drawerclose="cancelProcess"
        @drawersaved="handleEditIntake"
        :primaryBtnLabel="'Edit This Intake'"
        :existTertiary="false"
        :isDisabled="false"
        :isSubmitting="isSaving"
    >
        <template #title>
            <div class="text-lg font-medium">{{ intakeData.intake_name }}</div>
        </template>
        <template #content>
            <courseinfo :coursedata="course" :courseClass="'mb-4'" />
            <div class="grid grid-cols-1 gap-4 p-4 md:grid-cols-2">
                <div>
                    <span class="font-semibold text-gray-600">Intake Name:</span>
                    <div class="text-gray-800">{{ intakeData.intake_name }}</div>
                </div>
                <div>
                    <span class="font-semibold text-gray-600">Duration (days):</span>
                    <div class="text-gray-800">{{ intakeData.intake_duration }}</div>
                </div>
                <div>
                    <span class="font-semibold text-gray-600">Receiver:</span>
                    <div class="text-gray-800">{{ intakeData.intake_receiver }}</div>
                </div>
                <div>
                    <span class="font-semibold text-gray-600">Seats:</span>
                    <div class="text-gray-800">
                        {{ intakeData.intake_seats }} ({{
                            intakeData.restrict_enrollments_to_seats ? 'Fixed' : 'Flexible'
                        }})
                    </div>
                </div>
                <div>
                    <span class="font-semibold text-gray-600">Intake Start:</span>
                    <div class="text-gray-800">
                        <FormatDate :date="intakeData.intake_start" />
                        <div class="italic text-primary-blue-500">({{ getIntakeStartInfo }})</div>
                    </div>
                </div>
                <div>
                    <span class="font-semibold text-gray-600">Intake End:</span>
                    <div class="text-gray-800">
                        <FormatDate :date="intakeData.intake_end" />
                        <div class="flex space-x-2 italic text-primary-blue-500">
                            <div v-if="isIntakeExpired" class="font-medium text-red-500">
                                [Already Expired]
                            </div>
                            <div>(This Intake spans for {{ getIntakeDays }} days)</div>
                        </div>
                    </div>
                </div>

                <div>
                    <span class="font-semibold text-gray-600">Class Start:</span>
                    <div class="text-gray-800">
                        <FormatDateTime :date="getClassStartDateTime" />
                        <div class="italic text-primary-blue-500">
                            (Class starts {{ Math.abs(getClassStartDays) }} days
                            {{ getClassStartDays < 0 ? 'before' : 'after' }} intake ends)
                        </div>
                    </div>
                </div>
                <div>
                    <span class="font-semibold text-gray-600">Class End:</span>
                    <div class="text-gray-800">
                        <FormatDateTime :date="getClassEndDateTime" />
                        <div class="italic text-primary-blue-500">
                            (Class runs for {{ getClassDays }})
                        </div>
                    </div>
                </div>
                <div>
                    <span class="font-semibold text-gray-600">Course Fee:</span>
                    <div class="flex space-x-2 text-gray-800">
                        <div
                            class="text-gray-800"
                            v-if="intakeData.intake_delivery_mode == 'facetoface'"
                        >
                            ${{ intakeData.facetoface_fee }} (Face-To-Face fee)
                        </div>
                        <div
                            class="text-gray-800"
                            v-else-if="intakeData.intake_delivery_mode == 'online'"
                        >
                            ${{ intakeData.online_fee }} (Online fee)
                        </div>
                        <div class="text-gray-800" v-else>
                            ${{
                                intakeData.facetoface_fee > intakeData.online_fee
                                    ? intakeData.facetoface_fee
                                    : intakeData.online_fee
                            }}
                            (Blended Mode fee)
                        </div>
                    </div>
                </div>
                <div>
                    <span class="font-semibold text-gray-600">Status:</span>
                    <div class="text-gray-800">
                        <span
                            :class="
                                intakeData.intake_status === 1
                                    ? 'font-bold text-green-600'
                                    : 'font-bold text-red-600'
                            "
                        >
                            {{ intakeData.intake_status === 1 ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                </div>
            </div>
        </template>
    </SidebarDrawer>
</template>

<script>
import { router } from '@inertiajs/vue3';
import { useForm } from '@inertiajs/vue3';
import axios from 'axios';
import { Form } from '@progress/kendo-vue-form';
import { mapState } from 'pinia';
import { useCoursesStore } from '@spa/stores/modules/courses';
import { courseIntakes } from '@spa/services/courseFormResource';
import FormContent from '@spa/pages/courses/includes/forms/CourseIntakeFormContent.vue';
import SidebarDrawer from '@spa/components/KendoModals/SidebarDrawer.vue';
import SelectedCourseInfo from '@spa/pages/courses/includes/SelectedCourseInfo.vue';
import FormatDate from '@spa/components/FormatDate.vue';
import FormatDateTime from '@spa/components/FormatDateTime.vue';
import Button from '@spa/components/Buttons/Button';

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        intakeData: {
            type: Object,
            default: () => ({}),
        },
        course: {
            type: Object,
            default: () => ({}),
        },
        filters: {
            type: Object,
            default: () => ({}),
        },
    },
    data: function () {
        return {
            cancelSource: axios.CancelToken.source(),
            isSaving: false,
        };
    },
    components: {
        'k-form': Form,
        formcontent: FormContent,
        Button,
        SidebarDrawer,
        courseinfo: SelectedCourseInfo,
        FormatDate,
        FormatDateTime,
    },
    computed: {
        ...mapState(useCoursesStore, ['course', 'getIntakes', 'setIntakes']),
        getCourseData: function () {
            //format the data to provide it to the form
            return this.courseGeneral(this.course);
        },
        getIntakeStartDays() {
            const today = new Date();
            today.setHours(0, 0, 0, 0); // normalize to midnight

            const target = new Date(this.intakeData.intake_start);
            target.setHours(0, 0, 0, 0); // normalize to midnight

            const diffMs = target - today;
            return Math.round(diffMs / (1000 * 60 * 60 * 24));
        },
        getIntakeDays() {
            const startDate = new Date(this.intakeData.intake_start);
            const endDate = new Date(this.intakeData.intake_end);
            const diffMs = endDate - startDate; // difference in milliseconds
            const days = diffMs / (1000 * 60 * 60 * 24);
            return days;
        },
        getIntakeStartInfo() {
            let diffDays = this.getIntakeStartDays;
            if (diffDays === 0) return 'Intake Started From Today';
            if (diffDays > 0)
                return `Will start in ${diffDays} day${diffDays > 1 ? 's' : ''} from today`;
            return `Intake started ${Math.abs(diffDays)} day${Math.abs(diffDays) > 1 ? 's' : ''} earlier from today`;
        },
        isIntakeExpired() {
            if (
                this.getIntakeStartDays < 0 &&
                Math.abs(this.getIntakeStartDays) - this.getIntakeDays > 0
            ) {
                return true;
            }
            return false;
        },
        getClassStartDateTime() {
            const date = new Date(this.intakeData.class_start_date); // base date
            const [hours, minutes, seconds] = this.intakeData.class_start_time
                .split(':')
                .map(Number);
            return date.setHours(hours, minutes, seconds);
        },
        getClassEndDateTime() {
            const date = new Date(this.intakeData.class_end_date); // base date
            const [hours, minutes, seconds] = this.intakeData.class_end_time.split(':').map(Number);
            return date.setHours(hours, minutes, seconds);
        },
        getClassStartDays() {
            const startDate = new Date(this.intakeData.intake_end);
            const endDate = new Date(this.intakeData.class_start_date);
            const diffMs = endDate - startDate; // difference in milliseconds
            const days = diffMs / (1000 * 60 * 60 * 24);
            return days;
        },
        getClassDays() {
            let diffMs = this.getClassEndDateTime - this.getClassStartDateTime;
            const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
            diffMs -= days * 1000 * 60 * 60 * 24;

            const hours = Math.floor(diffMs / (1000 * 60 * 60));
            diffMs -= hours * 1000 * 60 * 60;

            const minutes = Math.floor(diffMs / (1000 * 60));

            // Build parts dynamically
            const parts = [];
            if (days) parts.push(`${days} day${days > 1 ? 's' : ''}`);
            if (hours) parts.push(`${hours} hour${hours > 1 ? 's' : ''}`);
            if (minutes) parts.push(`${minutes} minute${minutes > 1 ? 's' : ''}`);

            return `${parts.join(' ')}`;
        },
    },
    methods: {
        courseIntakes,
        handleEditIntake() {
            this.$emit('edit');
        },
        cancelProcess() {
            this.isSaving = false;
            this.$emit('cancel');
        },
    },
    watch: {},
};
</script>
