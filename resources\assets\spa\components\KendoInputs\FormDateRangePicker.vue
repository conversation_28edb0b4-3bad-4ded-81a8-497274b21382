<template>
    <div id="customDateRangePicker" ref="popup">
        <DateRangePicker
            :show="show"
            :start-date-input="'CustomStartDateInput'"
            :end-date-input="'CustomEndDateInput'"
            :calendar="'CustomCalendar'"
            :format="'dd-MM-yyyy'"
            :formatPlaceholder="{
                day: 'dd',
                month: 'mm',
                year: 'yyyy',
            }"
            :value="internalValue"
            @change="handleChange"
            @focus="handleFocus"
            @reset="reset"
            :class="hideField ? 'h-0' : 'h-9'"
            :popup-settings="{
                appendTo: 'customDateRangePicker',
                animate: false,
                offset: { top: '4px' },
                popupAlign: { horizontal: 'right', vertical: 'top' },
                anchorAlign: { horizontal: 'right', vertical: 'bottom' },
            }"
            @update:filter="handleFilter"
            @cancel="show = false"
        >
            <template v-slot:CustomCalendar="{ props }">
                <CustomCalendar
                    v-bind="props"
                    :is-valid="isValid"
                    @change="(e) => props.onChange(e)"
                    @focus="props.onFocus"
                    @blur="props.onBlur"
                    @keydown="props.onKeydown"
                />
            </template>
            <template v-slot:CustomStartDateInput="{ props }">
                <div class="flex items-center gap-1 md:gap-2" v-if="!hideField">
                    <CustomDateInput v-bind="props" />
                    <span>~</span>
                </div>
            </template>
            <template v-slot:CustomEndDateInput="{ props }">
                <CustomDateInput v-bind="props" v-if="!hideField" />
            </template>
        </DateRangePicker>
    </div>
</template>

<script>
import { DateRangePicker } from '@progress/kendo-vue-dateinputs';
import CustomDateInput from '@spa/components/KendoInputs/datepickers/CustomDateRangeItem.vue';
import CustomCalendar from '@spa/components/KendoInputs/datepickers/CustomCalendar.vue';

export default {
    name: 'CustomDateRangePicker',
    components: {
        DateRangePicker,
        CustomDateInput,
        CustomCalendar,
    },
    emits: ['update:modelValue'],
    props: {
        modelValue: {
            type: Object,
            default: () => ({ start: null, end: null }),
        },
        hideField: {
            type: Boolean,
            default: false,
        },
        defaultValue: {
            type: Object,
            default: () => ({ start: null, end: null }),
        },
    },
    data() {
        return {
            show: false,
            internalValue: { start: null, end: null },
            isValid: true,
        };
    },
    mounted() {
        this.initializeValue();
        document.addEventListener('click', this.handleClickOutside);
    },
    beforeUnmount() {
        document.removeEventListener('click', this.handleClickOutside);
    },
    methods: {
        initializeValue() {
            // Use modelValue if provided, otherwise use defaultValue
            const initialValue =
                this.modelValue || this.modelValue?.start || this.modelValue?.end
                    ? this.modelValue
                    : this.defaultValue;

            this.internalValue = {
                start: this.parseDate(initialValue.start),
                end: this.parseDate(initialValue.end),
            };
        },
        parseDate(dateString) {
            if (!dateString) return null;
            try {
                const date = new Date(dateString);
                return isNaN(date.getTime()) ? null : date;
            } catch (error) {
                console.warn('Invalid date string:', dateString);
                return null;
            }
        },
        formatDate(date) {
            if (!date || !(date instanceof Date) || isNaN(date.getTime())) return null;
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },
        handleChange(event) {
            const startDate = event?.value?.start instanceof Date ? event.value.start : null;
            const endDate = event?.value?.end instanceof Date ? event.value.end : null;

            let adjustedEnd = endDate;

            if (startDate && endDate && startDate > endDate) {
                adjustedEnd = null;
                this.isValid = false;
            } else {
                this.isValid = true;
            }

            this.internalValue = {
                start: startDate,
                end: adjustedEnd,
            };

            if (startDate && adjustedEnd) {
                this.emitUpdate();
            }
        },
        handleFocus() {
            this.show = true;
        },
        handleFilter() {
            this.show = false;
            this.emitUpdate();
        },
        handleClickOutside(event) {
            if (this.$refs.popup && this.show) {
                const popupElement = this.$refs.popup;
                if (!popupElement.contains(event.target) && event.target.className !== 'k-link') {
                    this.show = false;
                }
            }
        },
        emitUpdate() {
            const formattedValue = {
                start: this.formatDate(this.internalValue.start),
                end: this.formatDate(this.internalValue.end),
            };
            // Only emit if we have valid dates or both are null
            if (formattedValue.start !== undefined && formattedValue.end !== undefined) {
                this.$emit('update:modelValue', formattedValue);
            }
        },
        reset() {
            this.internalValue = { start: null, end: null };
            this.emitUpdate();
        },
    },
    watch: {
        modelValue: {
            handler(newVal) {
                if (newVal) {
                    this.internalValue = {
                        start: this.parseDate(newVal.start),
                        end: this.parseDate(newVal.end),
                    };
                } else {
                    this.internalValue = { start: null, end: null };
                }
            },
            deep: true,
        },
    },
};
</script>
