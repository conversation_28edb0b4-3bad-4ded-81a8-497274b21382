<script setup>
import { computed } from 'vue';
import Card from '@spa/components/Card/Card.vue';
import TimetableListComponent from '@spa/modules/teacher-profile/timetable/TimetableListComponent.vue';

const props = defineProps({
    store: Object,
});
</script>

<template>
    <Card :variant="'compact'" :pt="{ root: 'bg-gray-100' }">
        <template #header>
            <div class="flex items-center gap-2">
                <h2 class="text-lg font-medium">Timesheet</h2>
            </div>
        </template>
        <template #content>
            <div class="space-y-4" v-if="store.formData?.id">
                <TimetableListComponent :teacherId="store.formData?.id" />
            </div>
        </template>
    </Card>
</template>

<style scoped></style>
