<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { DropDownList, MultiSelect } from '@progress/kendo-vue-dropdowns';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { FieldWrapper } from '@progress/kendo-vue-form';
import Button from '@spa/components/Buttons/Button.vue';
import { useStudentInterventionStore } from '@spa/stores/modules/studentintervention/useStudentInterventionStore.js';
const popupOptions = {
    animate: false,
    popupClass: 'tw-popup',
};

const props = defineProps({
    required: {
        type: Boolean,
        default: false,
    },
    indicaterequired: {
        type: Boolean,
        default: false,
    },
    validationMessage: {
        type: String,
        default: '',
    },
    valid: {
        type: Boolean,
        default: true,
    },
    label: String,
    labelClass: String,
    editorId: String,
    hint: String,
    optional: Boolean,
    className: String,
    optionValue: {
        type: String,
        default: 'id',
    },
    optionLabel: {
        type: String,
        default: 'semester_name',
    },
    disabled: Boolean,
    store: Object,
    modelValue: [String, Number, Array, Object],
    clearable: {
        type: Boolean,
        default: false,
    },
    multiple: {
        type: Boolean,
        default: true,
    },
    readonly: Boolean,
    useChips: {
        type: Boolean,
        default: true,
    },
    placeholder: {
        type: String,
        default: '',
    },
    hasCreateAction: {
        type: Boolean,
        default: false,
    },
    filters: {
        type: Object,
        default: () => ({}),
    },
    initFormData: {
        type: Object,
        default: () => ({}),
    },
    forceReload: {
        type: Boolean,
        default: false,
    },
    style: {
        type: Object,
        default: () => ({}),
    },
});

const store = useStudentInterventionStore();

const emit = defineEmits(['update:modelValue']);
const allOptions = ref([]);
const loading = ref(false);
const searchValue = ref('');
const dropDownRef = ref(null);
const dropdownKey = ref(0);

const filteredOptions = computed(() => {
    if (!searchValue.value) return allOptions.value;
    const needle = searchValue.value.toLowerCase();
    return allOptions.value.filter((item) =>
        item[props.optionLabel]?.toLowerCase().includes(needle)
    );
});

const defaultItem = computed(() => ({
    [props.optionLabel]: props.placeholder,
    [props.optionValue]: null,
}));

const resetDropdown = () => {
    computedValue.value = null;
    dropdownKey.value++;
};

watch(
    () => props.filters?.courseId,
    async (newVal) => {
        console.log('newvalue', newVal);
        allOptions.value = await store?.fetchSemesterByCourse(newVal);
    }
);

onMounted(async () => {
    console.log('props.filters?.courseId', props.filters?.courseId);
    allOptions.value = await store?.fetchSemesterByCourse(props.filters?.courseId);
});

const computedValue = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});

function onChange(event) {
    emit('update:modelValue', event.value);
}

function onFilterChange(event) {
    if (!event || !event.filter) return;

    const value = event.filter.value || '';
    searchValue.value = value;

    if (!store || !store.filters) return;

    store.filters.query = value;

    // For empty searches, refresh from server
    if (value === '') {
        loading.value = true;
        try {
            store.fetchPaged();
        } catch (e) {
            console.error('Error fetching options:', e);
        } finally {
            loading.value = false;
        }
    }
}
const handleFooterClick = () => {
    console.log('dropDownRef', dropDownRef.value);
    dropDownRef.value?.handleBlur();
    store.formData = {
        ...props.initFormData,
    };
    store.formDialog = true;
};

watch(
    () => props.modelValue,
    (newVal) => {
        if (!newVal) {
            resetDropdown();
        }
    },
    { deep: true }
);
</script>

<template>
    <FieldWrapper class="flex-1">
        <Label
            :class="labelClass"
            :editor-id="editorId"
            :editor-valid="valid"
            :disabled="disabled"
            :optional="optional"
            v-if="label && label !== ''"
        >
            {{ label }}
            <span v-if="required || indicaterequired" :class="'ml-1 text-red-500'">*</span>
        </Label>
        <div class="k-form-field-wrap flex-1">
            <div :class="`async-select ${className}`">
                <DropDownList
                    :class="'async' + label"
                    ref="dropDownRef"
                    :data-items="filteredOptions"
                    :text-field="optionLabel"
                    :value-field="optionValue"
                    :data-item-key="optionValue"
                    v-model="computedValue"
                    :filterable="true"
                    :clear-button="clearable"
                    :disabled="disabled"
                    :readonly="readonly"
                    :loading="loading"
                    :value-primitive="true"
                    :popup-settings="popupOptions"
                    :default-item="defaultItem"
                    @filterchange="onFilterChange"
                    :footer="hasCreateAction ? 'myFooter' : ''"
                    :style="{
                        width: '100%',
                        minWidth: '150px',
                        maxWidth: '100%',
                        ...style,
                    }"
                >
                    <template v-if="hasCreateAction" #myFooter>
                        <div class="p-[1rem]">
                            <Button variant="primary" size="xs" @click="handleFooterClick">
                                <span>Add New</span>
                                <icon name="add" width="16" height="16" fill="#fff" />
                            </Button>
                        </div>
                    </template>
                </DropDownList>
            </div>
            <Error v-if="!valid">
                {{ validationMessage }}
            </Error>
            <Hint v-else-if="hint">{{ hint }}</Hint>
        </div>
    </FieldWrapper>
    <slot name="createDialog"></slot>
</template>
<style lang="scss">
.async-select {
    .k-multiselect {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
}
</style>
