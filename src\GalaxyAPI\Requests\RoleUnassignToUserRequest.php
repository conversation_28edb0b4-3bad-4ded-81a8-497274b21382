<?php

namespace GalaxyAPI\Requests;

use App\Users;
use Dotenv\Exception\ValidationException;
use Illuminate\Foundation\Http\FormRequest;

class RoleUnassignToUserRequest extends FormRequest
{
    public function prepareForValidation()
    {
        $user_id = $role_id = null;
        $user = $this->user_id ?? '';
        $role = $this->role_id ?? '';
        try {
            $user_id = decryptIt($user);
            $role_id = decryptIt($role);
        } catch (\Exception $e) {
            $user_id = $role_id = 0;
        }
        // get user and role information here and get the allowed permissions
        $user = Users::with(['roles' => function ($query) use ($role_id) {
            $query->where('id', $role_id);
        }])->find($user_id);

        if (! $user) {
            throw (new ValidationException('User information provided is not valid.'));
        }
        $role = $user->roles[0] ?? null;

        if (! $role) {
            throw (new ValidationException('Role information provided is not valid.'));
        }

        $this->merge([
            'user_id' => $user_id,
            'role_id' => $role_id,
        ]);
    }

    public function rules(): array
    {
        return [
            'user_id' => ['required'],
            'role_id' => ['required'],
        ];
    }
}
