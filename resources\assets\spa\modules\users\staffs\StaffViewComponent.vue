<template>
    <UserViewComponent
        :store="store"
        :tabs="tabs"
        :show-arrows="false"
        :has-profile-header-actions="true"
    >
        <template #tab-panel-leave_info="{ tab, index, isActive }">
            <LeaveInfoTabContent :store="store" />
        </template>
        <template #tab-panel-document="{ tab, index, isActive }">
            <DocumentsTabContent :store="store" />
        </template>
        <template #profile-header-actions>
            <StaffEmailForm :data="store.formData" />
        </template>
    </UserViewComponent>
</template>

<script setup>
import UserViewComponent from '@spa/modules/users/UserViewComponent.vue';
import { usePage } from '@inertiajs/vue3';
import { computed, onMounted } from 'vue';
import { useStaffStore } from '@spa/stores/modules/staff/useStaffStore.js';
import LeaveInfoTabContent from '@spa/modules/users/trainers/partials/LeaveInfoTabContent.vue';
import DocumentsTabContent from '@spa/modules/users/trainers/partials/DocumentsTabContent.vue';
import StaffEmailForm from '@spa/modules/staffemail/StaffEmailForm.vue';

const page = usePage();
// const store = useTeamMemberStore();
const store = useStaffStore();
const staffId = computed(() => page.props.params.id);

const tabs = [
    {
        title: 'Overview',
        name: 'overview',
    },
    {
        title: 'Login & Access',
        name: 'login_access',
    },
    {
        title: 'Roles & Permissions',
        name: 'roles_permissions',
    },
    {
        title: 'Activity Log',
        name: 'activity_log',
    },
    {
        title: 'Profile Details',
        name: 'profile_details',
    },
    {
        title: 'Document',
        name: 'document',
    },
    {
        title: 'Leave Info',
        name: 'leave_info',
    },
];

onMounted(() => {
    store.fetchDataById(staffId.value);
});
</script>
<style scoped></style>
