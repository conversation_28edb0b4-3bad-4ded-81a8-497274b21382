<template>
    <FormWrapper
        :visibleDialog="store.actionDialog"
        :hideOnOverlayClick="false"
        :fixedActionBar="false"
        :width="'50%'"
        :maxWidth="'400px'"
        :style="{
            maxWidth: '400px',
        }"
        :primaryBtnLabel="submitText"
        :secondaryBtnLabel="'Cancel'"
        :isDisabled="store.loading"
        :isSubmitting="store.loading"
        @drawerclose="store.actionDialog = false"
        :position="dialogPosition"
        :pt="{ content: 'p-0' }"
    >
        <template #title>
            <div class="text-lg font-medium">Change Teacher Code</div>
        </template>
        <template #content>
            <KendoForm :initial-values="initialValues" @submitclick="handleSubmit">
                <form-element class="h-full" :id="uniqueId">
                    <fieldset class="flex h-full flex-col space-y-6 px-5 pb-5 pt-4">
                        <div class="flex-1 overflow-y-auto">
                            <Field
                                name="code"
                                label="Trainer Code"
                                v-model="formData.code"
                                :validation-message="store.errors?.code"
                                :valid="!store.errors?.code"
                                :touched="true"
                                :indicaterequired="true"
                                :component="FormInput"
                            />
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <Button
                                size="base"
                                variant="secondary"
                                @click="store.actionDialog = false"
                            >
                                <span>Cancel</span>
                            </Button>
                            <Button
                                size="base"
                                type="button"
                                variant="primary"
                                class="min-w-[100px]"
                                @click="triggerSubmit"
                            >
                                <span>{{ submitText || 'Save' }}</span>
                            </Button>
                        </div>
                    </fieldset>
                </form-element>
            </KendoForm>
        </template>
    </FormWrapper>
</template>
<script setup>
import { ref, watch, computed, onUnmounted } from 'vue';
import { Form as KendoForm, Field, FormElement } from '@progress/kendo-vue-form';
import Button from '@spa/components/Buttons/Button.vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import FormWrapper from '@spa/components/KendoModals/SidebarDrawer.vue';
import { uuid } from '@spa/helpers/index.js';
import AgentStatusSelect from '@spa/modules/agentstatus/AgentStatusSelect.vue';
import { useAgentStore } from '@spa/stores/modules/agent/useAgentStore.js';
import { storeToRefs } from 'pinia';
import { useStaffStore } from '@spa/stores/modules/staff/useStaffStore.js';
import { useTeacherStore } from '@spa/stores/modules/teacher/useTeacherStore.js';

const props = defineProps({
    dialogTitle: String,
    dialogWidth: {
        type: [String, Number],
        default: '50%',
    },
    dialogPosition: {
        type: String,
        default: 'center',
    },
    initialValues: {
        type: Object,
        default: () => ({}),
    },
    validator: {
        type: Function,
        default: () => () => ({}),
    },
    submitText: {
        type: String,
        default: 'Save',
    },
    cancelText: {
        type: String,
        default: 'Cancel',
    },
    hasCustomSubmit: {
        type: Boolean,
        default: false,
    },
    layout: {
        type: Object,
        default: () => ({
            cols: 1,
            gap: 20,
        }),
    },
    orientation: {
        type: String,
        default: 'vertical', // vertical or horizontal
    },
    maxWidth: {
        default: '600px',
    },
    action: String,
    selectedRow: Object,
});

const emit = defineEmits(['submit', 'cancel', 'success']);

const store = useTeacherStore();

const { formData } = storeToRefs(store);

const dialogTitle = computed(() => {
    return 'Change Teacher Code';
});

const uniqueId = computed(() => {
    return 'form_id_' + uuid() + Date.now();
});

const submitting = ref(false);

const triggerSubmit = () => {
    // document.getElementById(uniqueId.value).dispatchEvent(new Event('submit'));
    document.getElementById(uniqueId.value).requestSubmit();
};

const handleSubmit = async (e) => {
    if (!e.isValid) return;
    let result;
    const data = {
        code: formData.value?.code,
        secure_id: props.selectedRow.secure_id,
    };
    result = await store.updateTeacherCode(data);
    emit('success', result);
};

onUnmounted(() => {
    store.actionDialog = false;
});

watch(
    () => store.actionDialog,
    (val) => {
        if (val) {
            formData.value = {
                ...props.selectedRow,
                code: props.selectedRow.code,
            };
        }
    }
);
</script>
<style lang=""></style>
