<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="false"
        :has-actions="false"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '400px',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :has-header-actions="true"
        :enable-selection="true"
        :search-placeholder="'Search by student name, course'"
    >
        <template #bulk-actions>
            <Button
                variant="secondary"
                class="!h-9 shadow-none"
                size="xs"
                @click="exportData"
                :disabled="store.loading.exportData || store.selected.length === 0"
            >
                <file-icon name="csv" fill="currentColor" />
                Export
            </Button>
        </template>
        <template #body-cell-application_id="{ props }">
            <CopyToClipboard :text="props.dataItem.application_reference_id" />
        </template>
        <template #body-cell-student_id="{ props }">
            <CopyToClipboard :text="props.dataItem.generated_stud_id" />
        </template>
        <template #body-cell-student_name="{ props }">
            <div class="flex items-center gap-2">
                <a
                    :href="route('student-profile-view', [props.dataItem?.secure_id])"
                    target="_blank"
                >
                    <Avatar :label="getInitialName(props.dataItem.student_name)" class="shrink-0" />
                </a>
                <div class="space-y-0">
                    <a
                        :href="route('student-profile-view', [props.dataItem?.secure_id])"
                        class="text-left font-medium text-gray-900"
                    >
                        <span class="text-left font-medium text-gray-900">{{
                            props.dataItem.student_name
                        }}</span>
                    </a>
                    <CopyToClipboard :text="props.dataItem?.generated_stud_id" />
                </div>
            </div>
        </template>
        <template #body-cell-current_course="{ props }">
            <div class="flex gap-2">
                <span class="truncate">{{ props.dataItem?.current_course?.course_name }}</span>
                <!--                <span-->
                <!--                    class="rounded-sm bg-gray-100 px-2 py-1 text-xs"-->
                <!--                    v-if="props.dataItem?.current_course?.length > 1"-->
                <!--                >-->
                <!--                    +{{ props.dataItem?.current_course?.length - 1 }}</span-->
                <!--                >-->
            </div>
        </template>
        <template #body-cell-status="{ props }">
            <Badge :variant="'default'">{{ props.dataItem?.current_course?.status }}</Badge>
        </template>
        <template #body-cell-campus="{ props }">
            <div class="flex gap-2">
                <span class="truncate">{{ props.dataItem?.campus }}</span>
                <!--                <span-->
                <!--                    class="rounded-sm bg-gray-100 px-2 py-1 text-xs"-->
                <!--                    v-if="props.dataItem?.campus?.length > 1"-->
                <!--                >-->
                <!--                    +{{ props.dataItem.campus.length - 1 }}</span-->
                <!--                >-->
            </div>
        </template>
    </AsyncGrid>
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted, watch } from 'vue';
import { useAgentStudentsStore } from '@spa/stores/modules/agentstudents/useAgentStudentsStore.js';
import Badge from '@spa/components/badges/Badge.vue';
import Avatar from '@spa/components/Avatar/Avatar.vue';
import CopyToClipboard from '@spa/components/CopyAction/CopyToClipboard.vue';
import { getInitialName } from '@spa/composables/strComposables.js';
import Button from '@spa/components/Buttons/Button.vue';
const store = useAgentStudentsStore();

const props = defineProps({
    agentId: Number,
});

const columns = [
    {
        field: 'application_reference_id',
        name: 'application_id',
        title: 'Application ID',
        replace: true,
        sortable: true,
        width: '200px',
    },
    {
        field: 'student_name',
        name: 'student_name',
        title: 'Student Name',
        replace: true,
        sortable: true,
        width: '200px',
    },
    {
        field: 'campus',
        name: 'campus',
        title: 'Campus',
        replace: true,
        sortable: false,
        width: '200px',
    },
    {
        field: 'current_course_name',
        name: 'current_course',
        title: 'Current Course',
        replace: true,
        sortable: false,
    },
    {
        field: 'status',
        name: 'status',
        title: 'Status',
        replace: true,
        sortable: false,
    },
    // Add more columns as needed
];

const getBadgeVariant = (value) => {
    let badgeMapping = {
        'Current Student': 'primary-blue',
        Cancelled: 'red',
        Transitioned: 'yellow',
        Completed: 'green',
        Finished: 'green',
        Withdrawn: 'pink',
        Suspended: 'red',
        Enrolled: 'primary-blue',
        'New Application Request': 'green',
    };
    return badgeMapping[value] || 'default';
};

const exportData = () => {
    store.exportData({
        agent_id: props.agentId,
    });
};

const initFilters = () => {
    store.filters = {
        agentId: props.agentId,
    };
};

watch(
    () => props.agentId,
    (newVal) => {
        store.filters = {
            agentId: newVal,
        };
    }
);

onMounted(() => {
    initFilters();
});
</script>
