<template>
    <Form :initialValues="roleData" @submit="handleSubmit">
        <FormContent
            @cancel="handleClose"
            :multilevel="multilevel"
            v-if="checkAccess"
            :title="title"
        />
        <Unauthorized v-else />
    </Form>
</template>
<script>
import { mapState } from 'pinia';
import { usersStore } from '@spa/stores/modules/userManagement';
import { Form, FormElement, Field } from '@progress/kendo-vue-form';
import FormContent from '@spa/modules/usermanagement/forms/RoleForm.vue';
import { DialogActionsBar } from '@progress/kendo-vue-dialogs';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import { useUserAuth } from '@spa/stores/modules/useUserAuth.js';

export default {
    setup() {
        const store = useLoaderStore();
        const authUser = useUserAuth();
        return {
            store,
            authUser,
        };
    },
    props: {
        visible: { type: Boolean, default: false },
        isSaving: { type: Boolean, default: false },
        isLoading: { type: Boolean, default: false },
        multilevel: { type: Boolean, default: false },
        title: { type: String, default: 'Add New User' },
        roleData: { type: Object, default: {} },
    },
    components: {
        Form,
        FormContent,
        DialogActionsBar,
    },
    computed: {
        ...mapState(usersStore, ['setPermissions', 'setUserRoles']),
        checkAccess() {
            return this.authUser.can(['ROLES_ADD', 'ROLES_EDIT']);
        },
        isSaving() {
            return this.store.isContextLoading(this.contextName);
        },
    },
    data() {
        return {
            viewRoleInformation: true,
            viewUserRoles: false,
            contextName: 'savingRole',
            errors: {},
        };
    },
    mounted() {
        window.addEventListener('keydown', this.handleKey);
    },
    beforeUnmount() {
        window.removeEventListener('keydown', this.handleKey);
    },
    methods: {
        handleClose() {
            this.$emit('close');
        },
        handleSubmit(formData) {
            if (this.isSaving) {
                return false;
            }
            this.store.startContextLoading(this.contextName);
            try {
                $http.post(route('spa.roles.save'), formData).then((resp) => {
                    console.log('resp', resp);
                    if (resp.success === 1) {
                        this.handleClose();
                        this.setPermissions(resp.data.permissions);
                        this.setUserRoles(resp.data.roles);
                    } else {
                        this.errors = resp.errors;
                    }
                    this.store.stopContextLoading(this.contextName);
                });
            } catch (error) {
                console.log(error);
                this.store.stopContextLoading(this.contextName);
            }
        },
        handleKey(event) {
            if (event.key === 'Escape') {
                this.handleClose();
            }
        },
    },
};
</script>
