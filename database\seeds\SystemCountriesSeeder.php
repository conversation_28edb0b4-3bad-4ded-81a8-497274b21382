<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class SystemCountriesSeeder extends Seeder
{
    private const SOURCE = 'json';

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $path = base_path('storage/app/countries-full.json');

        if (! File::exists($path)) {
            $this->command->error("File not found: {$path}");

            return;
        }

        $json = json_decode(File::get($path), true);
        if (! $json) {
            $this->command->error('Invalid JSON format.');

            return;
        }
        /* first updated all the status to non-standard ie 0 */
        DB::table('rto_country')->update(['updated_standard' => 0]);
        $cnt = 0;
        foreach ($json as $country) {
            $existing = DB::table('rto_country')->where('name', $country['country'])->first();
            $data = [
                'name' => $country['country'] ?? null,
                'official_name' => $country['official_name'] ?? null,
                'countrycode' => $country['short_name'] ?? null,
                'iso3' => $country['iso3'] ?? null,
                'nationality' => $country['nationality'] ?? null,
                'capital' => $country['capital'] ?? null,
                'latitude' => $country['latitude'] ?? null,
                'longitude' => $country['longitude'] ?? null,
                'continent' => $country['continent'] ?? null,
                'phone_code' => $country['phone_code'] ?? null,
                'phone_mask' => $country['phone_mask'] ?? null,
                'flag' => $country['flag'] ?? null,
                'currency' => $country['currency'] ?? null,
                'currency_name' => $country['currency_name'] ?? null,
                'currency_symbol' => $country['currency_symbol'] ?? null,
                'official_language' => $country['official_language'] ?? null,
                'tld' => $country['tld'] ?? null,
                'region' => $country['region'] ?? null,
                'region_detail' => $country['region'] ?? null,
                'updated_standard' => 1,
                'updated_at' => now(),
            ];

            if ($existing) {
                // Keep same ID, update data
                DB::table('rto_country')->where('id', $existing->id)->update($data);
            } else {
                // Insert new country
                DB::table('rto_country')->insert(array_merge($data, [
                    'created_at' => now(),
                    'created_by' => 1,
                    'updated_by' => 1,
                ]));
            }
            $cnt++;
        }

        $this->command->info("{$cnt} Countries data successfully updated from JSON!");

    }
}
