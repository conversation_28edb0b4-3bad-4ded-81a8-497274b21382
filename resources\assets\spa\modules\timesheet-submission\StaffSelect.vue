<script setup>
import AsyncSelect from '@spa/components/AsyncComponents/Select/AsyncSelect.vue';
import { useTimesheetStaffStore } from '@spa/stores/modules/timesheet-submission/useTimesheetStaffStore.js';
import { computed, onMounted, watch } from 'vue';

const props = defineProps({
    modelValue: [String, Number, Array, Object],
    label: String,
    className: String,
    optionValue: {
        type: String,
        default: 'id',
    },
    optionLabel: {
        type: String,
        default: 'name',
    },
    disabled: Boolean,
    clearable: {
        type: Boolean,
        default: true,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    readonly: Boolean,
    useChips: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: 'Select Staff',
    },
    filters: {
        type: Object,
        default: () => ({}),
    },
    sortBy: {
        type: String,
        default: 'first_name',
    },
    hasCreateAction: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['update:modelValue']);
const vModel = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});
const store = useTimesheetStaffStore();

// watch(
//     () => props.filters,
//     (newFilter) => {
//         store.filters = newFilter;
//     }
// );
</script>

<template>
    <AsyncSelect
        :label="label"
        :className="className"
        :optionValue="optionValue"
        :optionLabel="optionLabel"
        :disabled="disabled"
        :store="store"
        v-model="vModel"
        :clearable="clearable"
        :multiple="multiple"
        :readonly="readonly"
        :useChips="useChips"
        :placeholder="placeholder"
        :filters="filters"
        :sortBy="sortBy"
        :hasCreateAction="hasCreateAction"
        :enable-virtual="false"
    />
</template>

<style scoped></style>
