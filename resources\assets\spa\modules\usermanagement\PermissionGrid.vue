<template>
    <div class="relative h-full flex-1 overflow-auto">
        <button
            @click="scrollTabs('prev')"
            :disabled="!canScrollLeft"
            :class="'absolute left-[180px] top-1.5 z-50 flex-shrink-0 rounded bg-white p-1 text-gray-500 transition-colors hover:text-primary-blue-500 focus:outline-none focus:ring-2 focus:ring-primary-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:text-gray-300 disabled:hover:text-gray-300'"
            aria-label="Previous tabs"
            v-if="showNavigation"
        >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z" />
            </svg>
        </button>
        <button
            v-if="showNavigation"
            @click="scrollTabs('next')"
            :disabled="!canScrollRight"
            :class="[
                'flex-shrink-0 bg-white p-1 text-gray-500 transition-colors hover:text-primary-blue-500',
                'disabled:cursor-not-allowed disabled:text-gray-300 disabled:hover:text-gray-300',
                'rounded focus:outline-none focus:ring-2 focus:ring-primary-blue-500 focus:ring-offset-2',
                'absolute right-1 top-1.5 z-50',
            ]"
            aria-label="Next tabs"
        >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z" />
            </svg>
        </button>
        <GlobalContextLoader context="fetch-roles" :overlay="true">
            <div
                class="tw-permission-grid relative h-full max-h-[calc(100vh-4rem)] w-full flex-1 overflow-auto rounded-lg"
                ref="tabsContainer"
            >
                <div v-if="Object.keys(getPermissions).length > 0">
                    <table class="relative w-full table-fixed bg-white">
                        <thead class="sticky top-0 z-20">
                            <tr v-if="!isInFormMode" class="h-10 bg-white shadow-inner-line">
                                <template
                                    v-for="(role, index) in roles.filter(
                                        (role) => isInFormMode || role.visible
                                    )"
                                    :key="role.field"
                                >
                                    <th
                                        :style="{
                                            width: role.width ? role.width + 'px' : 'auto',
                                            left: role.locked ? role.lockat + 'px' : 'auto',
                                        }"
                                        class="z-20 px-4 py-3 text-left text-sm font-normal text-gray-600"
                                        :class="[
                                            role.locked === true
                                                ? 'sticky left-0 top-0 min-w-52 bg-white shadow-inner-line'
                                                : 'min-w-24',
                                        ]"
                                    >
                                        {{ getRoleTitle(index, role.title) }}
                                    </th>
                                </template>
                            </tr>
                        </thead>
                        <tbody>
                            <template
                                v-for="(permissionsList, moduleName, index) in getPermissions"
                                :key="moduleName"
                            >
                                <tr
                                    class="h-12 border-b bg-primary-blue-25 p-2 text-sm font-medium"
                                >
                                    <td
                                        :style="{
                                            width: '200px',
                                        }"
                                        class="sticky left-0 z-10 border-b bg-primary-blue-25 p-2 font-medium"
                                        :class="[
                                            openModules[moduleName] || defaultOpen
                                                ? 'permissions-list-visible'
                                                : 'permissions-list-hidden',
                                        ]"
                                        @click="toggleModule(moduleName)"
                                    >
                                        <div
                                            class="flex cursor-pointer items-center justify-start gap-2"
                                        >
                                            <span
                                                :class="
                                                    openModules[moduleName] || defaultOpen
                                                        ? 'rotate-180'
                                                        : ''
                                                "
                                                class="transition-transform duration-300 ease-in-out"
                                            >
                                                <icon name="arrow-down" />
                                            </span>
                                            <div>
                                                {{ moduleName }} ({{ permissionsList.length }})
                                            </div>
                                        </div>
                                    </td>
                                    <template
                                        v-for="(role, index) in roles
                                            .slice(1)
                                            .filter((role) => isInFormMode || role.visible)"
                                        :key="role.field"
                                    >
                                        <td
                                            v-if="!isInFormMode"
                                            class="border-b px-4 py-3"
                                            :style="{
                                                width: role.width ? role.width + 'px' : 'auto',
                                                left: role.locked ? role.lockat + 'px' : 'auto',
                                            }"
                                            :class="[
                                                hasChangedPermissions(role, permissionsList) &&
                                                highlightChanges &&
                                                !openModules[moduleName] &&
                                                !defaultOpen
                                                    ? highlightCellClass
                                                    : '',
                                                role.locked === true
                                                    ? 'sticky z-10  bg-primary-blue-25'
                                                    : '',
                                            ]"
                                        >
                                            <div class="flex items-center gap-2">
                                                <input
                                                    id="all-permissions"
                                                    type="checkbox"
                                                    class="k-checkbox k-checkbox-lg tw-checkbox custom-checkbox"
                                                    :checked="
                                                        isAllPermissionsGiven(role, permissionsList)
                                                    "
                                                    :disabled="!hasEditAccess"
                                                    @change="
                                                        handleGroupPermission(
                                                            $event,
                                                            role,
                                                            permissionsList
                                                        )
                                                    "
                                                    :data-number="
                                                        getPermissionsCount(role, permissionsList)
                                                    "
                                                />
                                                <label>All</label>
                                            </div>
                                        </td>
                                    </template>
                                </tr>
                                <template v-if="permissionsList.length > 0">
                                    <tr
                                        v-for="permission in permissionsList"
                                        :key="permission.id"
                                        class="accordion-row"
                                        :class="{
                                            'accordion-expanded':
                                                openModules[moduleName] || defaultOpen,
                                            'accordion-collapsed': !(
                                                openModules[moduleName] || defaultOpen
                                            ),
                                        }"
                                    >
                                        <td
                                            class="sticky left-0 z-10 border-b bg-white px-4 py-3 text-sm"
                                        >
                                            <div class="accordion-content" :title="permission.name">
                                                {{ permission.label }}
                                            </div>
                                        </td>
                                        <template
                                            v-for="(role, index) in roles
                                                .slice(1)
                                                .filter((role) => role.visible)"
                                            :key="role.field"
                                        >
                                            <td
                                                class="border-b px-4 py-3"
                                                :style="{
                                                    width: role.width ? role.width + 'px' : 'auto',
                                                    left: role.locked ? role.lockat + 'px' : 'auto',
                                                }"
                                                :class="[
                                                    role.group_permission[permission.name].c
                                                        ? highlightCellClass
                                                        : '',
                                                    role.locked === true
                                                        ? 'sticky  left-52 z-10 bg-white'
                                                        : '',
                                                ]"
                                            >
                                                <div class="accordion-content">
                                                    <input
                                                        type="checkbox"
                                                        v-model="
                                                            role.group_permission[permission.name].v
                                                        "
                                                        @change="
                                                            handlePermissionChange(
                                                                $event,
                                                                permission,
                                                                role
                                                            )
                                                        "
                                                        :disabled="!hasEditAccess"
                                                        class="k-checkbox k-checkbox-lg tw-checkbox"
                                                    />
                                                </div>
                                            </td>
                                        </template>
                                    </tr>
                                </template>
                            </template>
                        </tbody>
                    </table>
                    <PermissionChangesConfirmPopup
                        v-if="confirmDialog && hasEditAccess"
                        @close="closePermissionConfirmation"
                        @submit="savePermissionChanges"
                    />
                    <div
                        v-if="hasUpdatedPermissions && hasEditAccess"
                        class="fixed bottom-4 left-1/2 z-50 -translate-x-1/2 transform rounded-lg bg-gray-200 px-5 py-3 shadow-lg"
                    >
                        <div class="mx-auto flex w-auto justify-center space-x-4">
                            <Button
                                variant="secondary"
                                class="flex min-w-32 cursor-pointer"
                                @click="resetPermissionChanges"
                            >
                                <span> Cancel </span>
                            </Button>
                            <Button
                                variant="primary"
                                class="flex min-w-32 cursor-pointer"
                                @click="confirmPermissionChanges"
                            >
                                <span> Save Changes </span>
                            </Button>
                        </div>
                    </div>
                </div>
                <NoData :message="getNoDataFoundMessage" v-else>
                    <template #action>
                        <!-- <PrimaryButton @click="openAddCourseModal">
                            <icon :name="'add'" :width="16" :height="16" :fill="'#FFF'" />
                            <div class="ml-2 capitalize">Add Permissions</div>
                        </PrimaryButton> -->
                    </template>
                </NoData>
            </div>
        </GlobalContextLoader>
    </div>
</template>
<script>
import { mapState } from 'pinia';
import { usersStore } from '@spa/stores/modules/userManagement';
import Button from '@spa/components/Buttons/Button.vue';
import PermissionChangesConfirmPopup from '@spa/modules/usermanagement/PermissionChangesConfirmPopup.vue';
import GlobalContextLoader from '@spa/components/Loader/GlobalContextLoader.vue';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import globalHelper from '@spa/plugins/global-helper';
import NoData from '@spa/modules/usermanagement/NoData.vue';
import PrimaryButton from '@spa/components/Buttons/PrimaryButton.vue';
import { useUserAuth } from '@spa/stores/modules/useUserAuth.js';
import { router } from '@inertiajs/vue3';

export default {
    setup() {
        const store = useLoaderStore();
        const authUser = useUserAuth();
        const loaderStore = useLoaderStore();
        const handleStart = () => loaderStore.startContextLoading('fetch-roles');
        const handleFinish = () => loaderStore.stopContextLoading('fetch-roles');

        router.on('start', handleStart);
        router.on('finish', handleFinish);
        return {
            store,
            authUser,
        };
    },
    props: {
        mode: { type: String, default: 'grid' },
        save: { type: String, default: 'onchange' },
        openstate: { type: Boolean, default: false },
        newrolename: { type: String, default: '[Role_Name]' },
        filters: { type: [Object, Array], default: {} },
    },
    components: {
        GlobalContextLoader,
        Button,
        PermissionChangesConfirmPopup,
        NoData,
        PrimaryButton,
    },
    data() {
        return {
            openModules: {},
            newRole: [],
            highlightChanges: true,
            confirmDialog: false,
            highlightCellClass: 'border-primary-blue-100',
            canScrollLeft: false,
            canScrollRight: false,
            showNavigation: false,
        };
    },
    mounted() {
        const permissionsModules = Object.keys(this.permissions);
        if (!this.openstate) {
            const firstModule = permissionsModules[0];
            if (firstModule) this.openModules[firstModule] = true;
        } else if (this.openstate) {
            permissionsModules.forEach((item) => {
                this.openModules[item] = true;
            });
        }
        if (this.isInFormMode) {
            this.newRole = this.getDummyRole || [];
        }
        this.showNavigation = this.roles.length > 5;
        this.$nextTick(() => {
            this.updateScrollButtons();
        });
        window.addEventListener('resize', this.updateScrollButtons);
    },
    beforeUnmount() {
        window.removeEventListener('resize', this.updateScrollButtons);
    },
    computed: {
        ...mapState(usersStore, [
            'getUserRoles',
            'getSelectedRoles',
            'getDummyRole',
            'getEditableRoles',
            'permissions',
            'updatePermissionsForRole',
            'updatePermissionsInBulk',
            'trackPermissionsForRole',
        ]),
        isInFormMode() {
            return this.mode == 'edit';
        },
        roles() {
            if (this.mode === 'grid') {
                return this.getUserRoles || [];
                // }else if(this.mode == "user"){
                //     this.getSelectedRoles || []
            } else {
                return this.newRole || [];
            }
        },
        getPermissions() {
            if (
                this.filters.search != undefined &&
                this.filters.search != '' &&
                Object.keys(this.permissions).length
            ) {
                const result = {};
                for (const moduleName in this.permissions) {
                    const permissions = this.permissions[moduleName];
                    const matched = permissions.filter((permission) =>
                        permission.label.toLowerCase().includes(this.filters.search.toLowerCase())
                    );
                    if (matched.length > 0) {
                        result[moduleName] = matched;
                    }
                }
                return result;
            } else {
                return this.permissions;
            }
        },
        defaultOpen() {
            return this.openstate == true && this.firstLoad == true;
        },
        hasUpdatedPermissions() {
            let changedPermissions = 0;
            for (const role of this.getUserRoles) {
                if (!role.visible || !role.group_permission) continue;
                for (const key in role.group_permission) {
                    if (role.group_permission[key].o !== role.group_permission[key].v) {
                        changedPermissions++; // Permission changed
                    }
                }
            }
            return changedPermissions; // No permission changes found
        },
        hasEditAccess() {
            return this.authUser.can('ROLES_EDIT');
        },
        getNoDataFoundMessage() {
            if (this.filters.search != undefined && this.filters.search != '') {
                return 'There are no permissions set for this user group. Use different search term.';
            } else {
                return 'There are no permissions set for this user group. Please contact your Administrator.';
            }
        },
    },
    methods: {
        getLockedOffset(index) {
            // Sum widths of all locked columns before this one
            let offset = 0;
            for (let i = 0; i < index; i++) {
                const prevRole = this.roles.filter((role) => this.isInFormMode || role.visible)[i];
                if (prevRole && prevRole.locked) {
                    offset += prevRole.width;
                }
            }
            return offset;
        },
        toggleModule(moduleName) {
            this.openModules[moduleName] = !this.openModules[moduleName];
        },
        handleGroupPermission(e, role, permissionsList) {
            if (role.is_default) {
                e.target.checked = true;
                globalHelper.methods.showPopupError('This default role can not be modified.');
                return true;
            }
            if (!role.group_permission) return;
            permissionsList.forEach(function (permission) {
                role.group_permission[permission.name].v = e.target.checked || false;
                role.group_permission[permission.name].c =
                    role.group_permission[permission.name].v !=
                    role.group_permission[permission.name].o;
            });
        },
        getPermissionsCount(role, permissionsList) {
            const permissionNames = permissionsList.map((permission) => permission.name);
            const allowed = Object.entries(role.group_permission).filter(
                ([key, value]) => permissionNames.includes(key) && value.v === true
            ).length;
            if (allowed > 0 && permissionNames.length != allowed) {
                return allowed;
            }
            return '';
        },
        isAllPermissionsGiven(role, permissionsList) {
            const permissionNames = permissionsList.map((permission) => permission.name);
            return (
                Object.entries(role.group_permission).filter(
                    ([key, value]) => permissionNames.includes(key) && value.v === false
                ).length == 0
            );
        },
        hasChangedPermissions(role, permissionsList) {
            const permissionNames = permissionsList.map((permission) => permission.name);
            return Object.entries(role.group_permission).some(([key, value]) => {
                return permissionNames.includes(key) && value?.c === true;
            });
        },
        handlePermissionChange(e, permission, role) {
            if (role.is_default) {
                globalHelper.methods.showPopupError('This default role can not be modified.');
                role.group_permission[permission.name].v = role.group_permission[permission.name].o;
                role.group_permission[permission.name].c = false;
                return;
            }
            role.group_permission[permission.name].c =
                role.group_permission[permission.name].v !=
                role.group_permission[permission.name].o;
        },
        getRoleTitle(index, roletitle) {
            if (index > 0 && this.mode == 'edit') {
                return this.newrolename || 'ROLE_NAME';
            }
            return roletitle;
        },
        resetPermissionChanges() {
            for (const role of this.getUserRoles) {
                for (const key in role.group_permission) {
                    role.group_permission[key].v = role.group_permission[key].o;
                    role.group_permission[key].c = false;
                }
            }
        },
        confirmPermissionChanges() {
            this.confirmDialog = true;
        },
        closePermissionConfirmation() {
            this.confirmDialog = false;
        },
        async savePermissionChanges() {
            const saved = await this.updatePermissionsInBulk();
            if (saved) {
                this.closePermissionConfirmation();
            }
        },
        scrollTabs(direction) {
            const container = this.$refs.tabsContainer;
            console.log('direction', container);
            const scrollAmount = 162 * 2;
            if (direction === 'prev') {
                container.scrollLeft -= scrollAmount;
            } else if (direction === 'next') {
                container.scrollLeft += scrollAmount;
            }
            this.updateScrollButtons();
        },
        updateScrollButtons() {
            const container = this.$refs.tabsContainer;
            this.canScrollLeft = container.scrollLeft > 0;
            this.canScrollRight =
                container.scrollLeft < container.scrollWidth - container.clientWidth;
        },
    },
};
</script>
<style>
.custom-checkbox {
    @apply flex cursor-pointer items-center justify-center;
}

.custom-checkbox::after {
    content: attr(data-number);
    @apply justify-between text-xs font-medium text-blue-500;
}

.accordion-row {
    transition: all 450ms ease-in-out;
    overflow: hidden;
}

.accordion-collapsed {
    max-height: 0;
    opacity: 0;
    visibility: hidden;
    border: none;
}

.accordion-collapsed td {
    padding-top: 0;
    padding-bottom: 0;
    border: none;
}

.accordion-expanded {
    max-height: 500px;
    opacity: 1;
    visibility: visible;
}

.accordion-collapsed .accordion-content {
    display: none;
}

.accordion-expanded .accordion-content {
    display: block;
}
</style>
