<script setup>
import { computed } from 'vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormUploader from '@spa/components/KendoInputs/FormUploader.vue';
import Button from '@spa/components/Buttons/Button.vue';
import { getValidationMessage } from '@spa/composables/formComposables.js';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';

const props = defineProps({
    modelValue: {},
});
const emit = defineEmits(['update:modelValue']);
const formData = computed({
    get() {
        const data = props.modelValue || {};
        // Initialize arrays if they don't exist
        if (!data.training_qualifications) {
            data.training_qualifications = [];
        }
        return data;
    },
    set(value) {
        emit('update:modelValue', value);
    },
});
const removeTrainingQualification = (index) => {
    if (
        formData.value.training_qualifications &&
        formData.value.training_qualifications.length > 0
    ) {
        formData.value.training_qualifications.splice(index, 1);
    }
};
const addTrainingQualification = () => {
    if (!formData.value.training_qualifications) {
        formData.value.training_qualifications = [];
    }
    formData.value.training_qualifications.push({
        name: '',
        code: '',
        provider: '',
        awarded_date: null,
        document: null,
    });
};
</script>
<template>
    <div>
        <h3 class="mb-6 text-lg font-semibold">Training Qualifications</h3>

        <div
            class="hidden grid-cols-1 items-end gap-4 border-b border-gray-200 p-4 md:grid-cols-6 xl:grid"
        >
            <div class="md:col-span-1">
                <h4 class="text-sm font-medium leading-5 text-gray-500">
                    Credentials Name<span class="text-red-500">*</span>
                </h4>
            </div>
            <div class="md:col-span-1">
                <h4 class="text-sm font-medium leading-5 text-gray-500">Code</h4>
            </div>
            <div class="md:col-span-1">
                <h4 class="text-sm font-medium leading-5 text-gray-500">
                    Provider Name<span class="text-red-500">*</span>
                </h4>
            </div>
            <div class="md:col-span-1">
                <h4 class="text-sm font-medium leading-5 text-gray-500">Awarded Date</h4>
            </div>
            <div class="md:col-span-2">
                <h4 class="text-sm font-medium leading-5 text-gray-500">Related Document</h4>
            </div>
        </div>
        <div class="border-t border-gray-200 bg-white">
            <div
                v-for="(training, index) in formData.training_qualifications"
                :key="`training-${index}`"
                class="grid grid-cols-1 items-start gap-4 border-t border-gray-200 p-4 xl:grid-cols-6"
            >
                <div class="md:col-span-1">
                    <FormInput
                        v-model="training.name"
                        placeholder="Add Name"
                        :required="true"
                        v-bind="
                            getValidationMessage(formData, `training_qualifications.${index}.name`)
                        "
                    />
                </div>
                <div class="md:col-span-1">
                    <FormInput v-model="training.code" placeholder="Code Name" />
                </div>
                <div class="md:col-span-1">
                    <FormInput
                        v-model="training.provider"
                        placeholder="Add provider"
                        :required="true"
                        v-bind="
                            getValidationMessage(
                                formData,
                                `training_qualifications.${index}.provider`
                            )
                        "
                    />
                </div>
                <div class="md:col-span-1">
                    <!--                    <FormInput-->
                    <!--                        v-model="training.start_date"-->
                    <!--                        name="awarded_date"-->
                    <!--                        label="Awarded Date"-->
                    <!--                        type="date"-->
                    <!--                    />-->
                    <FormDatePicker
                        v-model="training.start_date"
                        name="awarded_date"
                        type="date"
                        :required="true"
                        v-bind="
                            getValidationMessage(
                                formData,
                                `training_qualifications.${index}.end_date`
                            )
                        "
                        emit-format="yyyy-MM-dd"
                    />
                </div>
                <div class="flex items-end gap-2 md:col-span-2">
                    <FormUploader
                        v-model="training.document"
                        accept=".pdf,.doc,.docx"
                        mode="simple"
                        class="flex-1"
                    />
                    <button
                        type="button"
                        @click="removeTrainingQualification(index)"
                        class="flex-shrink-0 rounded-full p-2 text-red-500 transition-colors duration-150 hover:bg-red-50 hover:text-red-700"
                        :aria-label="`Remove training qualification ${index + 1}`"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="18"
                            height="18"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            class="pointer-events-none"
                        >
                            <circle cx="12" cy="12" r="10" />
                            <path d="m15 9-6 6" />
                            <path d="m9 9 6 6" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <div class="flex justify-end pt-3">
            <Button
                @click="addTrainingQualification"
                variant="primary"
                :outline="true"
                class="flex items-center gap-2"
            >
                <svg
                    class="h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                >
                    <path d="M12 5v14M5 12h14" />
                </svg>
                Add More
            </Button>
        </div>
    </div>
</template>

<style scoped></style>
