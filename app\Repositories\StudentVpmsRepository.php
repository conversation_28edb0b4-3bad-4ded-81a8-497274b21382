<?php

namespace App\Repositories;

use App\Model\v2\Student;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentsVpms;
use App\Model\v2\VpmsPlacementProvider;
use App\Traits\CommonTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class StudentVpmsRepository
{
    use CommonTrait;

    protected $model;

    // Constructor to bind model to repo
    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    public function getModel()
    {
        return $this->model;
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function modify(array $data, array $where)
    {
        return $this->model->where($where)->update($data);
    }

    public function update(array $data, $id)
    {
        $record = $this->model->find($id);

        return $record->update($data);
    }

    public function selectdata($fields = '*')
    {
        $selectdata = $this->model->select($fields);

        return $selectdata;
    }

    public function getWhere($whereArr, $fields = '*')
    {
        return $this->model->where($whereArr)->select($fields)->get();
    }

    public function Where($whereArr)
    {
        return $this->model->where($whereArr)->get();
    }

    public function delete($id)
    {
        return $this->model->destroy($id);
    }

    public function getPlacementProviderData($request, $countOnly = false)
    {

        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'id',
            'provider_name',
            'provider_code',
            'web_url',
            DB::raw("CONCAT(first_name,'  ',last_name) as name"),
            'email',
            'contact_number',
            'categories',
            DB::raw("CONCAT(address_line_1,'  ',address_line_2) as address"),
            'city',
            'state',
            'postcode',
        ];

        $columns = [
            'provider_name' => 'provider_name',
            'provider_code' => 'provider_code',
            'web_url' => 'web_url',
            'name' => DB::raw("CONCAT(first_name,'  ',last_name) as name"),
            'email' => 'email',
            'contact_number' => 'contact_number',
            'categories' => 'categories',
            'address' => DB::raw("CONCAT(address_line_1,'  ',address_line_2) as address"),

        ];

        $query = VpmsPlacementProvider::from('rto_vpms_placement_provider')
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function getStudentsVpmsData($request, $countOnly = false)
    {

        $studentId = $request->student_id;
        $student_course_id = $request->student_course_id;
        $post = ($request->input()) ? $request->input() : [];
        $columnArr = [
            'rsv.id',
            'rvpp.provider_name',
            'rsv.start_date',
            'rsv.end_date',
            DB::raw("CONCAT(rc.course_code,' - ',rc.course_name) as course_name"),
            'rc.work_placement_hour',
            DB::raw("CONCAT(rvpp.address_line_1,', ',rvpp.address_line_2,', ',rvpp.city,', ',rvpp.state) as provider_address"),
        ];

        $query = StudentsVpms::from('rto_students_vpms as rsv')
            ->leftjoin('rto_vpms_placement_provider as rvpp', 'rvpp.id', '=', 'rsv.provider_id')
            ->leftjoin('rto_student_courses as rsc', 'rsc.id', '=', 'rsv.student_course_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where('rsv.student_id', '=', $studentId)
            ->where('rsv.student_course_id', '=', $student_course_id)

            ->select($columnArr);

        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function getProviderStudentsData($request, $countOnly = false)
    {

        $post = ($request->input()) ? $request->input() : [];
        $providerId = $request->provider_id;
        // print_r($request->input());

        $columnArr = [
            'rsv.id',
            'rs.generated_stud_id as student_id as student_id',
            'provider_code',
            DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as full_name"),
            DB::raw("CONCAT(rc.course_code,' - ',rc.course_name) as course"),
            'rsv.start_date as vpms_start_date',
            'rsv.end_date as vpms_end_date',
            'rsv.status',
        ];

        $columns = [
            'student_id' => 'rs.generated_stud_id as student_id as student_id',
            'full_name' => DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as full_name"),
            'course' => DB::raw("CONCAT(rc.course_code,' - ',rc.course_name) as course"),
            'vpms_start_date' => 'rsv.start_date as vpms_start_date',
            'vpms_end_date' => 'rsv.end_date as vpms_end_date',
            'status' => 'rsv.status',
        ];

        $query = StudentsVpms::from('rto_students_vpms as rsv')
            ->leftjoin('rto_vpms_placement_provider as rvpp', 'rvpp.id', '=', 'rsv.provider_id')
            ->leftjoin('rto_student_courses as rsc', 'rsc.id', '=', 'rsv.student_course_id')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsv.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where('rsv.provider_id', '=', $providerId)
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function getStudentPlacementData($request, $countOnly = false)
    {

        $collegeId = $request->user()->college_id;
        $post = ($request->input()) ? $request->input() : [];
        $customFilterParts = (isset($post['filter']) && isset($post['filter']['filters'])) ? $post['filter']['filters'] : [];

        $columnArr = [
            'rs.id',
            'rsc.id as student_course_id',
            'rs.profile_picture',
            DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as student_name"),
            DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as placement_officer_name"),
            'rs.application_reference_id as application_id',
            DB::raw("GROUP_CONCAT(CONCAT_WS(' - ', rc.course_code, rc.course_name) SEPARATOR ',@@,') as course_list"),
            'generated_stud_id as student_id',
            'campus.name as campus',
            'rsc.start_date',
            'rsc.finish_date',
            'rsv.start_date as vpms_start_date',
            'rsv.end_date as vpms_finish_date',
            'rsv.status',
            'rc.work_placement_hour',
            'rvpp.provider_name as provider_name',
        ];

        /* This array use for filterable only */
        $columns = [
            'student_name' => DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as student_name"),
            'course_list' => DB::raw("CONCAT(rc.course_code,' - ',rc.course_name) as course_list"),
            'student_type' => 'rs.student_type',
            'student_id' => 'generated_stud_id as student_id',
            'campus' => 'campus.name as campus',
            'placement_officer_name' => DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as placement_officer_name"),
            'application_id' => 'rs.application_reference_id as application_id',
            'vpms_start_date' => 'rsv.start_date as vpms_start_date',
            'vpms_finish_date' => 'rsv.end_date as vpms_finish_date',
            'start_date' => 'rsc.start_date as start_date',
            'finish_date' => 'rsc.finish_date as finish_date',
            'work_placement_hour' => 'rc.work_placement_hour as work_placement_hour',
            'provider_name' => 'rvpp.provider_name as provider_name',
        ];

        $query = Student::alias('rto_students as rs')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->leftjoin('rto_campus as campus', 'campus.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_staff_and_teacher as rst', 'rc.placement_officer_id', '=', 'rst.id')
            ->leftjoin('rto_students_vpms as rsv', 'rsv.student_course_id', '=', 'rsc.id')
            ->leftjoin('rto_vpms_placement_provider as rvpp', 'rvpp.id', '=', 'rsv.provider_id')
            ->where(['rs.college_id' => $collegeId, 'is_student' => 1, 'rc.work_placement' => 1, 'rsc.status' => 'Current Student'])
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);

        foreach ($customFilterParts as $filter) {
            if ($filter['field'] == 'extra' && isset($filter['value'])) {
                /* grid wise custom filter apply here */
                $query->where(function ($childQuery) use ($filter) {
                    foreach ($filter['value'] as $fieldName => $fieldvalue) {
                        if (count($fieldvalue) > 0) {
                            if ($fieldName == 'course') {
                                $childQuery->whereIn('rc.id', $fieldvalue);
                            } elseif ($fieldName == 'provider') {
                                $childQuery->whereIn('rsv.provider_id', $fieldvalue);
                            }
                        }
                    }
                });
            }
        }

        $this->gridDataSorting($query, $post);

        $query->groupBy('rsc.id');

        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function getVocationalStartDate($request)
    {

        $studentCourseId = $request->student_course_id;
        // $studentCourseId = $request->id;
        $courseStartDate = StudentCourses::from('rto_student_courses as rsc')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where('rsc.id', $studentCourseId)
            ->select('rsc.start_date', 'rsc.finish_date', 'rc.id', 'rc.vocational_duration', 'rc.vocational_startweek', 'rc.work_placement_hour')
            ->get()->toArray();
        $vocationalStart = (isset($courseStartDate[0]['vocational_startweek']) && $courseStartDate[0]['vocational_startweek'] != null) ? $courseStartDate[0]['vocational_startweek'] : 0;
        $vocationalEnd = (isset($courseStartDate[0]['vocational_duration']) && $courseStartDate[0]['vocational_duration'] != null) ? $courseStartDate[0]['vocational_duration'] : 0;
        $work_placement_hour = (isset($courseStartDate[0]['work_placement_hour']) && $courseStartDate[0]['work_placement_hour'] != null) ? $courseStartDate[0]['work_placement_hour'] : 0;
        $finish_date = (isset($courseStartDate[0]['finish_date']) && $courseStartDate[0]['finish_date'] != null) ? $courseStartDate[0]['finish_date'] : '';

        $vpmsStartDate = date('d-m-Y', strtotime($courseStartDate[0]['start_date'].$vocationalStart.' weeks'));
        $vpmsStartDatetemp = date('d M Y', strtotime($courseStartDate[0]['start_date'].$vocationalStart.' weeks'));
        $vpmsEndDate = date('d-m-Y', strtotime($vpmsStartDatetemp.$vocationalEnd.' weeks'));
        $finish_date = date('d-m-Y', strtotime($finish_date));

        $vpmsDates = ['vocational_duration' => (isset($courseStartDate[0]['vocational_duration']) && $courseStartDate[0]['vocational_duration'] != null) ? $courseStartDate[0]['vocational_duration'] : 0,
            'vpms_start_date' => $vpmsStartDate,
            'vpms_end_date' => $vpmsEndDate, 'finish_date' => $finish_date, 'work_placement_hour' => $work_placement_hour];

        return $vpmsDates;
    }

    public function getSidebarResult($request)
    {

        $collegeId = $request->user()->college_id;
        $categoryId = $request->input('id');

        $query = Student::alias('rto_students as rs')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->leftjoin('rto_campus as campus', 'campus.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_students_vpms as rsv', 'rsv.student_course_id', '=', 'rsc.id')
            ->where(['rs.college_id' => $collegeId, 'is_student' => 1, 'rc.work_placement' => 1, 'rsc.status' => 'Current Student']);

        if ($categoryId == 1) {
            $query->select(['rc.id', DB::raw('count(rc.id) as total'), 'rc.course_code', 'rc.course_name'])
                ->orderBy('total', 'desc')
                ->groupBy('rc.course_code');

            $resultArr = $query->get();
            $result = [];
            foreach ($resultArr as $row) {
                $fullText = $row->course_code.': '.$row->course_name;
                $subText = (strlen($fullText) > 20) ? (substr($fullText, 0, 20).'...') : $fullText;
                $result[] = [
                    'id' => $row->id,
                    'category_id' => $categoryId,
                    'type' => 'checkbox',
                    'hasChild' => false,
                    'field' => 'course',
                    'value' => $row->id,
                    'original' => $fullText,
                    'subtext' => $subText." ($row->total)",
                ];
            }

            return $result;
        }
        if ($categoryId == 2) {
            $ProviderList = VpmsPlacementProvider::from('rto_vpms_placement_provider as rvpp')
                ->select(['rvpp.id', DB::raw('count(rvpp.id) as total'), 'rvpp.provider_name'])
                ->orderBy('total', 'desc')
                ->groupBy('rvpp.id');
            $resultArr = $ProviderList->get();
            $result = [];
            foreach ($resultArr as $row) {
                $result[] = [
                    'id' => $row->id,
                    'category_id' => $categoryId,
                    'type' => 'checkbox',
                    'hasChild' => false,
                    'field' => 'provider',
                    'value' => $row->id,
                    'original' => $row->provider_name,
                    'subtext' => $row->provider_name,
                ];
            }

            return $result;
        }

    }
}
