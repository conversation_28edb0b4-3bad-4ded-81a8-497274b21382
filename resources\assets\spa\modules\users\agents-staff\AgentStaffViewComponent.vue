<template>
    <UserViewComponent :store="store" :show-arrows="false"> </UserViewComponent>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import { useAgentStaffsStore } from '@spa/stores/modules/agent-staffs/useAgentStaffsStore.js';
import UserViewComponent from '@spa/modules/users/UserViewComponent.vue';
import { usePage } from '@inertiajs/vue3';
import ProfileDetailsTabContent from '@spa/modules/users/partials/ProfileDetailsTabContent.vue';
import CommunicationTabContent from '@spa/modules/users/agents/partials/CommunicationTabContent.vue';
import DocumentsTabContent from '@spa/modules/users/agents/partials/DocumentsTabContent.vue';
import PaymentTabContent from '@spa/modules/users/agents/partials/PaymentTabContent.vue';
import CommissionTabContent from '@spa/modules/users/agents/partials/CommissionTabContent.vue';
import AgentStudentsTabContent from '@spa/modules/users/agents/partials/AgentStudentsTabContent.vue';

const page = usePage();
const store = useAgentStaffsStore();
const agentId = computed(() => page.props.params.id);
const tabs = [
    {
        title: 'Overview',
        name: 'overview',
    },
    {
        title: 'Login & Access',
        name: 'login_access',
    },
    {
        title: 'Roles & Permissions',
        name: 'roles_permissions',
    },
    {
        title: 'Activity Log',
        name: 'activity_log',
    },
    {
        title: 'Profile Details',
        name: 'profile_details',
    },
];

onMounted(() => {
    store.fetchDataById(agentId.value);
});
</script>
<style scoped></style>
