<template>
    <h3 class="text-base font-medium text-gray-900">Choose the file format to export:</h3>
    <ul class="list-none text-gray-600">
        <FormRadioGroup
            v-model="vModel"
            name="file_format"
            :data-items="[
                { label: 'CSV', value: 'csv' },
                { label: 'Excel (.xlsx)', value: 'xlsx' },
            ]"
            :layout="'vertical'"
        />
    </ul>
</template>

<script setup>
import { computed } from 'vue';
import FormRadioGroup from '@spa/components/KendoInputs/FormRadioGroup.vue';
const props = defineProps({
    store: Object,
    modelValue: String,
});

const vModel = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});
</script>
