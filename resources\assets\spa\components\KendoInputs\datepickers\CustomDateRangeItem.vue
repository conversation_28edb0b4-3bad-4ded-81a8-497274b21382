<template>
    <div :class="rootClass">
        <!-- <label v-if="hasLabel" :class="labelClass">{{ this.label }}</label> -->
        <DateInput
            :value="value || null"
            :format="format"
            :id="id"
            @change="handleChange"
            :width="width"
            :formatPlaceholder="{
                day: 'dd',
                month: 'mm',
                year: 'yyyy',
            }"
            :class="active ? '!text-gray-700' : '!text-gray-400'"
            :disabled="disabled"
        />
        <span class="absolute right-2 top-2" v-if="icon">
            <icon name="calendar" fill="#6B7280" />
        </span>
    </div>
</template>

<script>
import { DateInput } from '@progress/kendo-vue-dateinputs';
import { twMerge } from 'tailwind-merge';
export default {
    components: {
        DateInput,
    },
    inheritAttrs: false,
    props: {
        value: { type: Date, default: null },
        format: { type: String, default: null },
        id: { type: String, default: null },
        min: { type: Date, default: null },
        max: { type: Date, default: null },
        disabled: { type: Boolean, default: false },
        label: { type: String, default: null },
        active: { type: Boolean, default: true },
        width: { type: Number, default: 200 },
        icon: { type: Boolean, default: false },
        pt: { type: Object, default: () => ({}) },
    },
    computed: {
        labelClass() {
            return 'text-gray-500';
        },
        hasLabel() {
            if (this.$props.label != null && this.$props.label != '') return true;
            return false;
        },
        getProps() {
            return this.$props;
        },
        rootClass() {
            return twMerge('relative', this.pt.root);
        },
    },
    methods: {
        handleChange(e) {
            this.$emit('change', e);
        },
    },
};
</script>
