<?php

namespace GalaxyAPI\Controllers;

use App\Exports\StaffExport;
use App\Model\v2\Staff;
use App\Model\v2\SystemRoles;
use Carbon\Carbon;
use GalaxyAPI\Controllers\Traits\HasStaffStepActionTrait;
use GalaxyAPI\DTO\UserInfoDTO;
use GalaxyAPI\Enums\OldRolesEnums;
use GalaxyAPI\Imports\StaffImport;
use GalaxyAPI\Requests\EmptyRequest;
use GalaxyAPI\Requests\StaffRequest;
use GalaxyAPI\Requests\UserStepRequest;
use GalaxyAPI\Resources\StaffResource;
use GalaxyAPI\Resources\StaffStepsResource;
use GalaxyAPI\Resources\UserResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use Support\Auth\UserGroup;

class StaffController extends CrudBaseController
{
    use HasStaffStepActionTrait;

    public function init()
    {
        $filters = json_decode(request()->query('filters', '{}'), true);
        /*
        $newRole = OldRolesEnums::getNewRoleFromOld((int) auth()->user()->role_id);
        $roleArray = getRolesMatched($newRole);
        //dd($roleArray);
        $roleTypes = SystemRoles::whereIn('role_alias', $roleArray)->pluck('id');
        //dd($roleTypes);
        $rolesToSync = UserGroup::whereIn('system_role_type', $roleTypes)->whereIn('id', $roles)->get();
        //dd($rolesToSync);
        */
        // throw(new \Exception("Not valid"));
        $this->scopeWithValue = [
            'collegeId' => Auth::user()?->college_id,
            'withTeacher' => $filters['withTeacher'] ?? false,
        ];

        $commonLoads = [
            'user',
            'createdBy',
            'updatedBy',
            'birthCountry',
            'formProgress',
            'position',
            'staffPosition',
        ];
        $this->withAll = [
            ...$commonLoads,
        ];
        $this->loadAll = [
            ...$commonLoads,
        ];

    }

    public function __construct()
    {
        parent::__construct(
            model: Staff::class,
            storeRequest: StaffRequest::class,
            updateRequest: EmptyRequest::class,
            resource: UserResource::class,
            mapperClass: UserInfoDTO::class
        );
    }

    // public function afterSave(Model $model){
    //     $model->status = Status::INACTIVE;
    // }

    public function importStaff(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimetypes:text/csv|max:30720',
            'send_invites' => 'nullable|in:0,1',
        ]);
        $import = new StaffImport;
        Excel::import($import, $request->file('file'));

        return $this->success('Staff import completed');
    }

    public function downloadSampleTemplate()
    {
        $data = [
            ['full_name', 'email', 'roles', 'department', 'status'],
            ['Alice Johnson', '<EMAIL>', 'Staff,Teacher', 'Admin', 'Active'],
            ['Bob Smith', '<EMAIL>', 'Staff', 'Academic', 'Inactive'],
        ];
        $csv = implode("\n", array_map(fn ($row) => '"'.implode('","', $row).'"', $data));

        return response($csv, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="staff-import-sample.csv"',
        ]);
    }

    public function saveStep(UserStepRequest $request)
    {
        $result = $this->saveStaffStep($request);

        return $this->success('Staff step saved successfully', $result);
    }

    public function getSteps(
        $id
    ) {
        $staff = $this->getStaffSteps($id);

        return $this->success('Staff steps retrieved successfully', StaffStepsResource::make($staff));
    }

    public function bulkCreate(Request $request)
    {
        $request->validate([
            'staff_members' => 'required|array|min:1',
            'staff_members.*.name' => 'required|string|max:255',
            'staff_members.*.email' => 'required|email|unique:users,email',
            'staff_members.*.roles' => 'required',
            'staff_members.*.department_id' => 'nullable',
            'staff_members.*.status' => 'required|string|in:active,inactive,pending',
        ]);

        $createdStaff = [];
        $errors = [];

        foreach ($request->staff_members as $index => $staffData) {
            try {
                // Create user first
                $user = \App\Users::create([
                    'name' => $staffData['name'],
                    'username' => $staffData['email'],
                    'email' => $staffData['email'],
                    'password' => \Hash::make('temp_password_'.rand(10000, 99999)),
                    'college_id' => Auth::user()->college_id,
                    'user_type' => 'Staff',
                    'is_active' => $staffData['status'] === 'active',
                ]);

                $nameArr = explode(' ', $staffData['name']);
                $nameArr = array_filter($nameArr);
                $fistName = @$nameArr[0];
                $lastName = @$nameArr[1] ?? '-';
                // Create staff record
                $staff = Staff::create([
                    'first_name' => $fistName,
                    'last_name' => $lastName,
                    'email' => $staffData['email'],
                    'user_id' => $user->id,
                    'college_id' => Auth::user()->college_id,
                    'department_id' => @$staffData['department_id'],
                    'status' => $staffData['status'],
                    'created_by' => Auth::id(),
                    'updated_by' => Auth::id(),
                ]);
                $createdStaff[] = StaffResource::make($staff);
            } catch (\Exception $e) {
                $errors[] = [
                    'index' => $index + 1,
                    'email' => $staffData['email'],
                    'error' => $e->getMessage(),
                ];
            }
        }

        if (empty($createdStaff) && ! empty($errors)) {
            return $this->error('Failed to create any staff members', $errors, 422);
        }

        $response = ['created' => $createdStaff];
        if (! empty($errors)) {
            $response['errors'] = $errors;
        }

        return $this->success(
            count($createdStaff).' staff member(s) created successfully'.
                (count($errors) > 0 ? ' with '.count($errors).' error(s)' : ''),
            $response
        );
    }

    public function updateStaffCode($id)
    {
        try {
            $id = decrypt($id);
        } catch (\Exception $e) {
            return $this->error('Invalid staff member id', [], 422);
        }
        $staff = Staff::findOrFail($id);
        request()->validate([
            'code' => ['required', 'string', 'max:50', 'unique:rto_staff_and_teacher,staff_number,'.$staff->id],
        ]);
        try {
            $staff->staff_number = request()->input('code');
            $staff->save();

            return ajaxSuccess(['data' => $staff], 'Staff code updated successfully');
        } catch (\Exception $e) {
            return ajaxError($e->getMessage(), 500);
        }

    }

    public function export()
    {
        request()->validate([
            'ids' => 'array',
            'ids.*' => 'exists:rto_staff_and_teacher,id',
            'format' => 'required|in:csv,xlsx',
        ]);
        $ids = request()->input('ids', []);
        $format = request()->input('format', 'csv');
        $current_date = Carbon::now()->format('Y-m-d H:i:s.u');
        $fileName = "Staff_Export-$current_date";
        if ($format === 'csv') {
            return Excel::download(
                new StaffExport($ids),
                $fileName.'.csv',
                \Maatwebsite\Excel\Excel::CSV
            );
        }

        // Default to Excel
        return Excel::download(
            new StaffExport($ids),
            $fileName.'.xlsx',
            \Maatwebsite\Excel\Excel::XLSX
        );
    }
}
