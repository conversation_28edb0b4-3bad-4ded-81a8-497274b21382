{{ Form::hidden('_token', csrf_token(), array()) }}
@if(count($users) > 0) 
@foreach($users as $usr)
<tr>
    <td>
        {{ $usr->id }}
        <div class="action-overlay">
            <ul class="icon-actions-set">
                <li>
                    <a href="{{ route('spa.manage-users.agents.profile', array('id'=>encryptIt($usr->id))) }}#payment" class="link-black text-sm" data-toggle="tooltip" data-original-title="View Payment"> <i class="fa fa-search"></i> </a>
                </li>
            </ul>
        </div>
    </td>
    <td>{{ $usr->agency_name }}</td>
    <td>{{ $usr->contact_person }}</td>
    <td>{{ $usr->primary_email }}</td>
    <td>{{ $usr->agency_name }}</td>
    <td>{{ (!array_key_exists($usr->status, $arrStatus)) ? '' : $arrStatus[$usr->status] }}</td>
</tr>
@endforeach
@else
<tr>
    <td colspan="6" style="text-align: center">
        <p style="color:red;">No Record Found</p>
    </td>
</tr>
@endif