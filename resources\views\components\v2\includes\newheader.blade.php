@php
    $currentRole = Session::get('currentRole');
    $userRole = Session::get('userRole', []);
    $roleCount = $userRole && count($userRole) > 0 ? count($userRole) : '0';
    $currRoute = Route::current()->getName();
    $menuTitle = isset($mainmenu) && !empty($mainmenu) ? $mainmenu : '';
    $currentUrl = url()->current();
    $previousUrl = null;
    // if (Cookie::has('UrlWithFilter')) {
    // $previousUrl = Cookie::get('UrlWithFilter');
    // } else

    if (isset($back)) {
        $previousUrl = $back;
    }
    // else {
    // $previousUrl = url()->previous();
    // }
    $isTransparent = isset($layout) && $layout === 'transparent';
@endphp
<div
    {{ $attributes->twMerge(
        'header relative flex items-center justify-between gap-4 max-md:gap-2 h-16 flex-shrink-0 p-3 lg:pr-6 lg:pl-8
                    lg:py-3',
        $isTransparent ? 'bg-transparent' : 'gradientbackground shadow',
    ) }}>
    @if (!$isTransparent)
        <div
            class="flex items-center gap-2"
            x-data="{ isOpen: false }"
        >
            <button
                class="p-1 rounded-full hover:shadow-2xl focus:ring-2 hover:bg-primary-blue-500 focus:bg-primary-blue-500/90 focus:ring-offset-1 focus:ring-offset-transparent transition-all duration-75 !text-white lg:hidden"
                x-on:click="$dispatch('toggle-sidebar')"
            >
                <span class="sr-only">Open sidebar</span>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                >
                    <path
                        fill="currentColor"
                        d="M2.753 18h18.5a.75.75 0 0 1 .101 1.493l-.101.007h-18.5a.75.75 0 0 1-.102-1.494zh18.5zm0-6.497h18.5a.75.75 0 0 1 .101 1.493l-.101.007h-18.5a.75.75 0 0 1-.102-1.494zh18.5zm-.001-6.5h18.5a.75.75 0 0 1 .102 1.493l-.102.007h-18.5A.75.75 0 0 1 2.65 5.01zh18.5z"
                    />
                </svg>
            </button>
            <div
                id="pageNavigationsBar"
                class="flex items-center gap-2  max-md:hidden"
            >
                @if ($previousUrl /* && $previousUrl !== $currentUrl */)
                    <div class="back">
                        <a href="{{ $previousUrl }}">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="none"
                            >
                                <path
                                    d="M10.7327 19.7907C11.0326 20.0764 11.5074 20.0649 11.7931 19.765C12.0787 19.465 12.0672 18.9903 11.7673 18.7046L5.51587 12.75L20.25 12.75C20.6642 12.75 21 12.4142 21 12C21 11.5858 20.6642 11.25 20.25 11.25L5.51577 11.25L11.7673 5.29529C12.0672 5.00961 12.0787 4.53487 11.7931 4.23495C11.5074 3.93502 11.0326 3.92348 10.7327 4.20916L3.31379 11.2759C3.14486 11.4368 3.04491 11.642 3.01393 11.8554C3.00479 11.9022 3 11.9505 3 12C3 12.0496 3.00481 12.098 3.01398 12.1449C3.04502 12.3581 3.14496 12.5632 3.31379 12.724L10.7327 19.7907Z"
                                    fill="white"
                                />
                            </svg>
                        </a>
                    </div>
                @endif
                <h1 class="page-title text-base md:text-2xl font-semibold leading-none text-white my-0">
                    @yield('title')
                </h1>
            </div>
        </div>
    @endif
    <div class="flex flex-1 items-center justify-end space-x-2">
        @if ((!isset($hideGlobalSearch) || @$hideGlobalSearch == false) && !$isTransparent)
            <form
                class="m-0 flex md:ml-0"
                action="#"
                method="GET"
            >
                <div class="flex flex-1 justify-center lg:justify-end">
                    <div class="px-2">
                        <label
                            for="search"
                            class="sr-only"
                        > Search </label>

                        @if (config('features.scout') && config('features.scout_global_search'))
                            <livewire:global-search layout="beta" />
                        @else
                            <div class="relative text-gray-400 focus-within:text-gray-400">
                                <div class="pointer-events-none absolute inset-y-0 left-0 z-[1] flex items-center pl-3">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                    >
                                        <path
                                            d="M10.5 5C13.5376 5 16 7.46243 16 10.5C16 11.8388 15.5217 13.0659 14.7266 14.0196L18.8536 18.1464C19.0488 18.3417 19.0488 18.6583 18.8536 18.8536C18.68 19.0271 18.4106 19.0464 18.2157 18.9114L18.1464 18.8536L14.0196 14.7266C13.0659 15.5217 11.8388 16 10.5 16C7.46243 16 5 13.5376 5 10.5C5 7.46243 7.46243 5 10.5 5ZM10.5 6C8.01472 6 6 8.01472 6 10.5C6 12.9853 8.01472 15 10.5 15C12.9853 15 15 12.9853 15 10.5C15 8.01472 12.9853 6 10.5 6Z"
                                            fill="#9CA3AF"
                                        />
                                    </svg>
                                </div>
                                <!-- <input id="findStudent" type="text" class="form-control search-icon-nav findStudent"> -->
                                <input
                                    id="globalSearchInput"
                                    name="search"
                                    class="findStudent findStudent block w-52 rounded-md border border-transparent bg-white py-2 pl-10 pr-3 leading-5 text-gray-400 transition-all duration-500 focus:bg-white focus:text-gray-900 focus:placeholder-gray-400 focus:outline-none focus:ring-0 sm:text-sm"
                                    placeholder="Search or Ctrl + J"
                                    type="text"
                                />
                                <div
                                    id="globalSearchResult"
                                    class="search-result animate__animated animate__fadeIn absolute right-0 top-full z-50 mt-1 max-h-96 w-[600px] origin-top-right overflow-auto rounded-md bg-white p-4 pt-0 text-base shadow-lg ring-1 ring-black ring-opacity-5 transition duration-500 ease-in-out focus:outline-none sm:text-sm"
                                    style="display: none"
                                >
                                    <div
                                        id="searchTabStrip"
                                        style="min-height: 130px;"
                                    >
                                        <ul
                                            style="display: flex;"
                                            class="border-b-2"
                                        >
                                            <li class="k-state-active">
                                                <span
                                                    class="block text-sm font-medium leading-4 text-gray-500">All</span>
                                            </li>
                                            <li>
                                                <span
                                                    class="block text-sm font-medium leading-4 text-gray-500">Students</span>
                                            </li>
                                            <li>
                                                <span
                                                    class="block text-sm font-medium leading-4 text-gray-500">Tasks</span>
                                            </li>
                                            <li>
                                                <span
                                                    class="block text-sm font-medium leading-4 text-gray-500">Document</span>
                                            </li>
                                        </ul>
                                        <div class="all-li-detail">
                                            <div class="h-full w-full">
                                                <div id="searchallList"></div>
                                            </div>
                                        </div>
                                        <div class="students-li-detail">
                                            <div class="h-full w-full">
                                                <div id="searchstudentsList"></div>
                                            </div>
                                        </div>
                                        <div class="tasks-li-detail">
                                            <div class="h-full w-full">
                                                <div id="searchtasksList"></div>
                                            </div>
                                        </div>
                                        <div class="document-li-detail">
                                            <div class="h-full w-full">
                                                <div id="searchdocumentList"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </form>
        @endif
        {{-- @dd($hideGlobalSearch) --}}
        <div class="relative inline-flex items-center gap-2 text-left">
            @if (galaxy_feature('release_notes') && (!isset($hideGlobalSearch) || @$hideGlobalSearch == false))
                @livewire('release-notes')
            @endif
            @if (!isset($hideGlobalSearch) || @$hideGlobalSearch == false)
                <button
                    id="feedback-button"
                    title="Give your feedback"
                    class="updateNotificationCount glob-tooltip tw-header-button {{ $isTransparent ? 'text-gray-700 hover:!bg-primary-blue-100 hover:!text-gray-700' : 'text-white' }}"
                >
                    <svg
                        width="24"
                        height="24"
                        fill="none"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M10.75 14A2.25 2.25 0 0 1 13 16.25v1.502l-.008.108c-.31 2.127-2.22 3.149-5.425 3.149-3.193 0-5.134-1.01-5.553-3.112L2 17.75v-1.5A2.25 2.25 0 0 1 4.25 14h6.5Zm0 1.5h-6.5a.75.75 0 0 0-.75.75v1.42c.28 1.2 1.55 1.839 4.067 1.839 2.516 0 3.73-.631 3.933-1.816V16.25a.75.75 0 0 0-.75-.75ZM7.5 6a3.5 3.5 0 1 1 0 7 3.5 3.5 0 0 1 0-7Zm12.25-4A2.25 2.25 0 0 1 22 4.25v3.5A2.25 2.25 0 0 1 19.75 10h-1.455l-2.166 2.141A1.25 1.25 0 0 1 14 11.253V9.986a2.25 2.25 0 0 1-2-2.236v-3.5A2.25 2.25 0 0 1 14.25 2h5.5ZM7.5 7.5a2 2 0 1 0 0 4 2 2 0 0 0 0-4Zm12.25-4h-5.5a.75.75 0 0 0-.75.75v3.5c0 .414.336.75.75.75h1.249v2.154L17.68 8.5h2.071a.75.75 0 0 0 .75-.75v-3.5a.75.75 0 0 0-.75-.75Z"
                            stroke-width="2"
                            fill="currentColor"
                        />
                    </svg>
                </button>
                @include('frontend.newlayout.include.newnotification', [
                    'isTransparent' => $isTransparent,
                ])
            @endif
        </div>
        @if ($currentRole == '9' || $currentRole == '14' || $currentRole == '10')
            @if (Session::get('agentStaffRole') != 'standard')
                <div class="relative mr-2 inline-block text-left">
                    <button
                        aria-haspopup="true"
                        aria-expanded="true"
                        data-toggle="dropdown"
                        title="Settings"
                        class="tw-dropdown-trigger glob-tooltip tw-header-button  {{ $isTransparent ? 'text-gray-700 hover:!bg-primary-blue-100 hover:!text-gray-700' : 'text-white' }}"
                    >
                        <span class="sr-only">Settings</span>
                        <svg
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M10.0124 0.25C10.7464 0.25846 11.4775 0.343262 12.1939 0.503038C12.5067 0.572785 12.7406 0.833511 12.7761 1.15196L12.9463 2.67881C13.0233 3.37986 13.6152 3.91084 14.3209 3.91158C14.5105 3.91188 14.6982 3.87238 14.8734 3.79483L16.2741 3.17956C16.5654 3.05159 16.9057 3.12136 17.1232 3.35362C18.1354 4.43464 18.8892 5.73115 19.3279 7.14558C19.4225 7.45058 19.3137 7.78203 19.0566 7.9715L17.8151 8.88659C17.461 9.14679 17.2518 9.56001 17.2518 9.99946C17.2518 10.4389 17.461 10.8521 17.8159 11.1129L19.0585 12.0283C19.3156 12.2177 19.4246 12.5492 19.3299 12.8543C18.8914 14.2685 18.138 15.5649 17.1264 16.6461C16.9091 16.8783 16.569 16.9483 16.2777 16.8206L14.8714 16.2045C14.4691 16.0284 14.007 16.0542 13.6268 16.274C13.2466 16.4937 12.9935 16.8812 12.9452 17.3177L12.7761 18.8444C12.7413 19.1592 12.5124 19.4182 12.2043 19.4915C10.7558 19.8361 9.24673 19.8361 7.79828 19.4915C7.49015 19.4182 7.26129 19.1592 7.22643 18.8444L7.0576 17.32C7.00802 16.8843 6.75459 16.498 6.37467 16.279C5.99475 16.06 5.53345 16.0343 5.13244 16.2094L3.72582 16.8256C3.43446 16.9533 3.09428 16.8833 2.87703 16.6509C1.86487 15.5685 1.11144 14.2705 0.673445 12.8548C0.579106 12.5499 0.688106 12.2186 0.94509 12.0293L2.18842 11.1133C2.54256 10.8531 2.75172 10.4399 2.75172 10.0005C2.75172 9.56101 2.54256 9.14779 2.18796 8.88725L0.945406 7.97285C0.68804 7.78345 0.57894 7.45178 0.673612 7.14658C1.11236 5.73215 1.86619 4.43564 2.87837 3.35462C3.09584 3.12236 3.43618 3.05259 3.72749 3.18056L5.12786 3.79572C5.53081 3.97256 5.99404 3.94585 6.37601 3.72269C6.75633 3.50209 7.00953 3.11422 7.05841 2.67764L7.22849 1.15196C7.26401 0.83335 7.49811 0.572541 7.81105 0.502942C8.52832 0.34342 9.2602 0.258654 10.0124 0.25ZM10.0126 1.7499C9.55857 1.75524 9.1058 1.79443 8.65807 1.86702L8.54914 2.84418C8.44731 3.75368 7.92028 4.56102 7.13066 5.01903C6.33622 5.48317 5.36761 5.53903 4.52483 5.16917L3.62654 4.77456C3.0546 5.46873 2.59938 6.25135 2.27877 7.09168L3.07656 7.67879C3.81537 8.22162 4.25172 9.08367 4.25172 10.0005C4.25172 10.9172 3.81537 11.7793 3.07734 12.3215L2.27829 12.9102C2.59863 13.752 3.05392 14.5361 3.62625 15.2316L4.53138 14.8351C5.36947 14.4692 6.33149 14.5227 7.12377 14.9794C7.91606 15.4361 8.44457 16.2417 8.54824 17.1526L8.6572 18.1365C9.54684 18.2878 10.4557 18.2878 11.3453 18.1365L11.4543 17.1527C11.5551 16.2421 12.083 15.4337 12.8762 14.9753C13.6695 14.5168 14.6334 14.463 15.473 14.8305L16.3775 15.2267C16.9493 14.5323 17.4044 13.7495 17.7249 12.909L16.927 12.3211C16.1882 11.7783 15.7518 10.9162 15.7518 9.99946C15.7518 9.08267 16.1882 8.22062 16.9261 7.67847L17.7229 7.09109C17.4023 6.25061 16.947 5.46784 16.375 4.77356L15.4785 5.16737C15.1132 5.32901 14.718 5.4122 14.3189 5.41158C12.8492 5.41004 11.6158 4.30355 11.4554 2.84383L11.3464 1.8667C10.9009 1.7942 10.4529 1.75512 10.0126 1.7499ZM9.99994 6.24995C12.071 6.24995 13.7499 7.92888 13.7499 9.99995C13.7499 12.071 12.071 13.75 9.99994 13.75C7.92887 13.75 6.24994 12.071 6.24994 9.99995C6.24994 7.92888 7.92887 6.24995 9.99994 6.24995ZM9.99994 7.74995C8.7573 7.74995 7.74994 8.75731 7.74994 9.99995C7.74994 11.2426 8.7573 12.25 9.99994 12.25C11.2426 12.25 12.2499 11.2426 12.2499 9.99995C12.2499 8.75731 11.2426 7.74995 9.99994 7.74995Z"
                                fill="currentColor"
                            />
                        </svg>
                    </button>

                    <div
                        class="tw-dropdown-popup absolute right-0 top-full z-50 mt-2 w-44 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 transition duration-500 ease-in-out focus:outline-none hidden"
                        role="menu"
                        aria-orientation="vertical"
                        aria-labelledby="menu-button"
                        tabindex="-1"
                    >
                        <div>
                            <ul class="p-1">
                                @if ($currentRole == '9' && !config('features.users_beta'))
                                    <!--<li><a href="#" class="btn btn-flat text-left">College setup</a></li>-->
                                    <!--<li><a href="#" class="btn btn-flat text-left">User settings</a></li>-->
                                    <!--<li class="{{ $menuTitle == 'setting' ? 'active' : '' }}"><a href="{{ route('college-info') }}" class="btn btn-flat text-left">Settings</a></li>-->
                                    <li class="{{ $menuTitle == 'organisation_setup' ? 'active bg-gray-100' : '' }}">
                                        <a
                                            href="{{ route('general-info') }}"
                                            class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                        >Organisation
                                            setup</a>
                                    </li>
                                    <li class="{{ $menuTitle == 'staff_setting' ? 'active bg-gray-100' : '' }}">
                                        <a
                                            href="{{ route('staff-list') }}"
                                            class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                        >Staff</a>
                                    </li>
                                    <li class="{{ $menuTitle == 'users_setting' ? 'active bg-gray-100' : '' }}">
                                        <a
                                            href="{{ route('manage-user-account') }}"
                                            class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                        >Users</a>
                                    </li>
                                    <li class="{{ $menuTitle == 'administration' ? 'active bg-gray-100' : '' }}">
                                        <a
                                            href="{{ route('user_profile') }}"
                                            class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                        >Administration</a>
                                    </li>

                                    <li class="{{ $menuTitle == 'trainers' ? 'active bg-gray-100' : '' }}">
                                        <a
                                            href="{{ route('view-teacher') }}"
                                            class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                        >
                                            Trainers
                                        </a>
                                    </li>

                                    <li class="{{ $menuTitle == 'partner_agent' ? 'active bg-gray-100' : '' }}">
                                        <a
                                            href="{{ route('view-agent-list') }}"
                                            class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                        >Agent</a>
                                    </li>
                                    <li class="{{ $menuTitle == 'partner_employee' ? 'active bg-gray-100' : '' }}">
                                        <a
                                            href="{{ route('manage-employer') }}"
                                            class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                        >Employers</a>
                                    </li>
                                    <li class="{{ $menuTitle == 'partners' ? 'active bg-gray-100' : '' }}">
                                        <a
                                            href="{{ route('services-setup') }}"
                                            class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                        >Service
                                            Providers</a>
                                    </li>
                                    <!--<li><a href="{{ route('user_logout') }}" class="btn btn-flat text-left">Sign out</a></li>-->
                                @elseif($currentRole == '14' && !config('features.users_beta'))
                                    <li class="{{ $menuTitle == 'partners' ? 'active bg-gray-100' : '' }}">
                                        @php $manageStaffRoute = galaxy_feature('agent_portal') ? route('spa.agent.staffList') : route('agent-staff-list'); @endphp
                                        <a
                                            href="{{ $manageStaffRoute }}"
                                            class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                        >Manage Staffs</a>
                                    </li>
                                @endif
                                @if(config('features.users_beta') && auth()->check())
                                    @php
                                        $currentActiveRole = auth()->user()->currentActiveRole();
                                    @endphp

                                    @if($currentActiveRole && $currentActiveRole->hasAnyDirectPermission([\Support\Auth\Access::ORGANISATION_PROFILE_ACCESS->value, \Support\Auth\Access::GENERAL_INFO_ACCESS->value]))
                                        <li class="{{ $menuTitle == 'organisation_setup' ? 'active bg-gray-100' : '' }}">
                                            <a
                                                href="{{ route('general-info') }}"
                                                class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                            >Organisation
                                                setup</a>
                                        </li>
                                    @endif

                                    @if($currentActiveRole && $currentActiveRole->hasAnyDirectPermission([\Support\Auth\Access::STAFF_LIST_ACCESS->value]))
                                        <li class="{{ $menuTitle == 'staff_setting' ? 'active bg-gray-100' : '' }}">
                                            <a
                                                href="{{ route('spa.manage-users.team-members') }}"
                                                class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                            >Staff</a>
                                        </li>
                                    @endif

                                    @if($currentActiveRole && $currentActiveRole->hasAnyDirectPermission([\Support\Auth\Access::SETTINGS_ACCESS->value, \Support\Auth\Access::ROLES_MANAGE->value]))
                                        <li class="{{ $menuTitle == 'administration' ? 'active bg-gray-100' : '' }}">
                                            <a
                                                href="{{ route('user_profile') }}"
                                                class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                            >Administration</a>
                                        </li>
                                    @endif

                                    @if($currentActiveRole && $currentActiveRole->hasAnyDirectPermission([\Support\Auth\Access::TRAINERS_LIST_ACCESS->value]))
                                        <li class="{{ $menuTitle == 'trainers' ? 'active bg-gray-100' : '' }}">
                                            <a
                                                href="{{ route('spa.manage-users.trainers') }}"
                                                class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                            >
                                                Trainers
                                            </a>
                                        </li>
                                    @endif
                                    @if($currentActiveRole && $currentActiveRole->hasAnyDirectPermission([\Support\Auth\Access::AGENT_LIST_ACCESS->value]))
                                        <li class="{{ $menuTitle == 'partner_agent' ? 'active bg-gray-100' : '' }}">
                                            <a
                                                href="{{ route('spa.manage-users.agents') }}"
                                                class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                            >Agent</a>
                                        </li>
                                    @endif

                                    @if($currentActiveRole && $currentActiveRole->hasAnyDirectPermission([\Support\Auth\Access::EMPLOYER_LIST_ACCESS->value, \Support\Auth\Access::EMPLOYER_BETA_ACCESS->value]))
                                        <li class="{{ $menuTitle == 'partner_employee' ? 'active bg-gray-100' : '' }}">
                                            <a
                                                href="{{ route('spa.manage-users.employers') }}"
                                                class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                            >Employers</a>
                                        </li>
                                    @endif

                                    @if($currentActiveRole && $currentActiveRole->hasAnyDirectPermission([\Support\Auth\Access::SERVICE_PROVIDERS_ACCESS->value]))
                                        <li class="{{ $menuTitle == 'partners' ? 'active bg-gray-100' : '' }}">
                                            <a
                                                href="{{ route('spa.manage-users.service-providers') }}"
                                                class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                            >Service
                                                Providers</a>
                                        </li>
                                    @endif

                                    {{-- Agent role specific menu --}}
                                    @if($currentActiveRole && $currentActiveRole->hasAnyDirectPermission([\Support\Auth\Access::STAFF_LIST_ACCESS->value]))
                                        <li class="{{ $menuTitle == 'partners' ? 'active bg-gray-100' : '' }}">
                                            <a
                                                href="{{ route('spa.manage-users.agent-staffs') }}"
                                                class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                            >Manage
                                                Staffs</a>
                                        </li>
                                    @endif
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
            @endif
        @endif
        <div class="relative inline-flex items-center pl-1 text-left">
            <button
                id="user-menu"
                class="tw-dropdown-trigger group flex max-w-xs items-center rounded-full text-sm [>svg:text-gray-400]"
                aria-haspopup="true"
                aria-expanded="true"
                data-toggle="dropdown"
            >
                <span class="sr-only">Open user menu</span>
                @if (!authUser('central') && authUser()->isImpersonating())
                    <div class=" bg-primary-blue-500 text-white rounded-full flex items-center gap-1 p-0.5 ps-1">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            class="lucide lucide-venetian-mask-icon lucide-venetian-mask"
                        >
                            <path d="M18 11c-1.5 0-2.5.5-3 2" />
                            <path
                                d="M4 6a2 2 0 0 0-2 2v4a5 5 0 0 0 5 5 8 8 0 0 1 5 2 8 8 0 0 1 5-2 5 5 0 0 0 5-5V8a2 2 0 0 0-2-2h-3a8 8 0 0 0-5 2 8 8 0 0 0-5-2z"
                            />
                            <path d="M6 11c1.5 0 2.5.5 3 2" />
                        </svg>
                        <span class="text-xs">Impersonating</span>
                        <img
                            class="h-8 w-8 rounded-full group-hover:bg-white/20 group-focus:outline-none group-focus:outline-primary-blue-100 group-focus:ring-2"
                            src="{{ url('dist/img/user2-160x160.png') }}"
                            alt="Image"
                        >
                    </div>
                @else
                    <img
                        class="h-8 w-8 rounded-full group-hover:bg-white/20 group-focus:outline-none group-focus:outline-primary-blue-100 group-focus:ring-2"
                        src="{{ url('dist/img/user2-160x160.png') }}"
                        alt="Image"
                    >
                @endif
                <svg
                    viewBox="0 0 512 512"
                    class="ml-1 h-4 w-4 fill-current {{ $isTransparent ? 'text-gray-700' : 'text-white' }}"
                >
                    <path d="m382.059 158.059-126.06 126.06-126.061-126.06L96 192l159.999 160L416 192l-33.941-33.941z">
                    </path>
                </svg>
            </button>
            <div
                class="tw-dropdown-popup absolute right-0 top-full z-50 mt-2 w-56 origin-top-right rounded-md overflow-hidden bg-white shadow-lg ring-1 ring-black ring-opacity-5 transition duration-500 ease-in-out focus:outline-none hidden"
                role="menu"
                aria-orientation="vertical"
                aria-labelledby="menu-button"
                tabindex="-1"
            >
                <div>
                    <div class="p-3 bg-blue-50">
                        <p class="text-xs leading-5 text-gray-500">
                            {{ !authUser('central') && authUser()->isImpersonating() ? 'Acting as' : 'Signed in as' }}
                        </p>
                        <p class="text-sm font-medium leading-5 text-gray-900">
                            @if (Auth::user())
                                {{ Auth::user()->name }}
                            @endif
                        </p>
                        <span class="text-xs leading-5 text-gray-700">
                            <span class="text-gray-500">Last Login</span>
                            <strong>
                                {{ isset(auth()->user()->last_login) && !empty(auth()->user()->last_login)
                                    ? date('d F Y', strtotime(auth()->user()->last_login))
                                    : '15 November 2016' }}
                            </strong>
                            <span class="text-sm leading-5 text-gray-900">
                    </div>
                    <ul class="user-dropdown-menus p-1">

                        @if (!authUser('central'))
                            <!--<li><a href="{{ route('college-info') }}" class="btn btn-flat text-left">College setup</a></li>-->
                            @php
                                $user = authUser();
                                if ($user->isAgent()):
                                    if (Session::get('agentStaffRole') == 'standard'):
                                        $profileRoute = galaxy_feature('agent_staff_portal')
                                            ? route('spa.agent.agentstaffprofile')
                                            : route('profile-manage');
                                    else:
                                        $profileRoute = galaxy_feature('agent_portal')
                                            ? route('spa.agent.agentprofile')
                                            : route('agent_profile');
                                    endif;
                                elseif ($user->isAgentStaff()):
                                    $profileRoute = galaxy_feature('agent_staff_portal')
                                        ? route('spa.agentstaff.profile')
                                        : route('profile-manage');
                                elseif ($user->isStudent()):
                                    $profileRoute = route('student-edit-profile');
                                elseif ($user->isSadmin()):
                                    $profileRoute = route('profile-manage');
                                else:
                                    $profileRoute = route('profile-manage');
                                endif;
                            @endphp
                            <li>
                                <a
                                    href="{{ $profileRoute }}"
                                    class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                >Profile</a>
                            </li>
                            @if (Auth::user()->isSadmin())
                                <li>
                                    <a
                                        href="{{ route('user-settings') }}"
                                        class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                                    >Settings</a>
                                </li>
                            @endif

                            <!--<li><a href="{{ route('create-permission-group') }}" class="btn btn-flat text-left">Settings</a></li>-->
                            @if ($roleCount > 1 && !config('features.users_beta'))
                                <li class="switch-role p-2 tw-dropdown-trigger-prevent">
                                    <div class="rounded-lg bg-gray-100 p-2">
                                        <span
                                            class="mb-1 block text-xs text-gray-700 hover:bg-gray-100 "
                                            title="(You have {{ $roleCount }} Permission)"
                                        >Switch
                                            Role
                                        </span>
                                        <ul class="mb-0">
                                            {{-- @php //print_r($currentRole); @endphp --}}
                                            {{-- <li class="header text-center"><small> ( You have {{ $roleCount }} Permission
                                            )</small></li> --}}
                                            <li>
                                                <select
                                                    class="tw-select tw-dropdown-trigger-prevent permission w-full p-1 text-sm bg-white"
                                                    title="Select Role"
                                                    style="margin-bottom:0px !important;"
                                                >
                                                    @if (isset($userRole))
                                                        @foreach ($userRole as $key => $value)
                                                            @if (!in_array($value, ['DOS', 'DOS-ELICOS', 'DOS-HS', 'DOS-VET', 'IT', 'Admin']))
                                                                <option
                                                                    data-id="{{ $key }}"
                                                                    value="{{ $key }}"
                                                                    {{ $key == $currentRole ? 'selected' : '' }}
                                                                >
                                                                    {{ $value }}
                                                                </option>
                                                            @endif
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </li>
                    </ul>
                </div>
                </li>
                            @endif

                @endif

                @if(config('features.users_beta') && auth()->check())
                    @php
                        $currentActiveRole = auth()->user()->currentActiveRole();
                        $userRoles = auth()->user()->userRoles;
                    @endphp
                    @if($userRoles && $userRoles->count() > 1)
                        <li class="switch-role p-2 tw-dropdown-trigger-prevent">
                            <div class="rounded-lg bg-gray-100 p-2">
                                <span class="mb-1 block text-xs text-gray-700">
                                    Switch Role
                                </span>
                                <ul class="mb-0 space-y-1">
                                    @foreach($userRoles as $role)
                                        <li>
                                            <a
                                                href="{{ route('galaxy.switch-roles', ['roleId' => $role->secure_id]) }}"
                                                class="block w-full text-left px-2 py-1.5 text-sm rounded-md transition-colors
                                                    {{ $currentActiveRole && $currentActiveRole->id == $role->id
                                                        ? 'bg-green-100 text-green-700 font-medium'
                                                        : 'text-gray-700 hover:bg-white hover:shadow-sm' }}"
                                            >
                                                <div class="flex items-center justify-between">
                                                    <span>{{ $role->parentRole ? $role->parentRole->name : $role->name }}</span>
                                                    @if($role->pivot->is_default)
                                                        <span class="ml-2 h-2 w-2 rounded-full bg-blue-500" title="Default Role"></span>
                                                    @endif
                                                </div>
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        </li>
                    @endif
                @endif

                <li
                    class="sign-out-link"
                    x-data
                ><a
                        href="{{ authUser('central') ? route('central.logout') : route('user_logout') }}"
                        x-on:click="localStorage.removeItem('courseId')"
                        class="block px-2 py-1.5 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                    >Sign
                        out</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>
</div>
@if (galaxy_feature('site_notice'))
    @livewire('site-notice')
@endif

@push('script')
    <script
        type="text/javascript"
        src="https://gapp01.atlassian.net/s/d41d8cd98f00b204e9800998ecf8427e-T/-3o5b4z/b/4/c95134bc67d3a521bb3f4331beb9b804/_/download/batch/com.atlassian.jira.collector.plugin.jira-issue-collector-plugin:issuecollector/com.atlassian.jira.collector.plugin.jira-issue-collector-plugin:issuecollector.js?locale=en-US&collectorId=115c2917"
    ></script>
    <script type="text/javascript">
        window.ATL_JQ_PAGE_PROPS = {
            "triggerFunction": function(showCollectorDialog) {
                //Requires that jQuery is available!
                jQuery("#feedback-button").click(function(e) {
                    console.log("Feedback clicked");
                    e.preventDefault();
                    showCollectorDialog();
                });
            }
        };
    </script>
@endpush
