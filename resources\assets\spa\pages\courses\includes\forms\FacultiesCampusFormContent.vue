<template>
    <form-element>
        <div class="space-y-6">
            <fieldset class="k-form-fieldset space-y-4" :style="{ maxWidth: '650px' }">
                <div>
                    <div class="mb-4 text-base font-medium text-gray-700">
                        This course is provided in Campus
                    </div>
                    <div
                        class="mb-4 cursor-pointer text-primary-blue-500"
                        @click="toggleAllCampuses"
                    >
                        {{ allCampusSelected ? 'Clear All' : 'Select All' }}
                    </div>
                    <div class="space-y-4">
                        <div
                            class="course-checkbox flex justify-start"
                            v-for="(campus, index) in allCampusesData"
                            :key="index"
                        >
                            <checkbox
                                v-model="campus.selected"
                                :label="campus.text"
                                :label-placement="'after'"
                                @change="handleChange"
                            />
                        </div>
                    </div>
                </div>
            </fieldset>
            <div class="tw-divider"></div>
            <fieldset class="k-form-fieldset space-y-4" :style="{ maxWidth: '650px' }">
                <div class="grid grid-cols-2 gap-x-6 gap-y-4">
                    <field
                        :id="'faculty_id'"
                        :name="'faculty_id'"
                        :label="'Faculty'"
                        :component="'courseFaculty'"
                        :data-items="courseFaculties"
                        :list-no-data-render="false"
                        :suggest="true"
                        :value="getCurrentFaculty"
                        :allowCustom="true"
                        :text-field="'text'"
                        @change="changeFaculty"
                        :validator="requiredtrue"
                    >
                        <template v-slot:courseFaculty="{ props }">
                            <formautocomplete
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                    <field
                        :id="'department_id'"
                        :name="'department_id'"
                        :label="'Department'"
                        :component="'courseFaculty'"
                        :value="getCurrentDepartment"
                        :data-items="courseDepartments"
                        :allowCustom="true"
                        :text-field="'text'"
                        @change="changeDepartment"
                        :validator="requiredtrue"
                    >
                        <template v-slot:courseFaculty="{ props }">
                            <formautocomplete
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
            </fieldset>
            <div
                class="tw-divider"
                v-if="showCourseCompletionType || showDeliveryType || showResultCalculationMethods"
            ></div>
            <fieldset class="k-form-fieldset space-y-4" :style="{ maxWidth: '650px' }">
                <div class="grid grid-cols-2 gap-x-6 gap-y-4" v-if="showCourseCompletionType">
                    <field
                        :id="'course_completion_type'"
                        :name="'course_completion_type'"
                        :label="'Course Completion Type'"
                        :component="'courseFaculty'"
                        :text-field="'text'"
                        :data-item-key="'id'"
                        :valueField="'id'"
                        :valuePrimitive="true"
                        :default-item="defaultCourseCompletionType"
                        :data-items="courseCompletionTypes"
                        :validator="checkCompletiontype"
                    >
                        <template v-slot:courseFaculty="{ props }">
                            <formdropdown
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                    <field
                        :id="'course_credit_point'"
                        :name="'course_credit_point'"
                        :label="'Total Course Credit Point'"
                        :component="'courseFaculty'"
                        :min="0"
                        :validator="checkCreditpoint"
                    >
                        <template v-slot:courseFaculty="{ props }">
                            <formnumericinput
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <template v-if="showDeliveryType">
                    <field
                        :id="'course_delivery_type'"
                        :name="'course_delivery_type'"
                        :component="'myTemplate'"
                        :label="'Course Delivery'"
                        :placeholder="'Course Delivery'"
                        :text-field="'text'"
                        :data-item-key="'id'"
                        :valueField="'id'"
                        :valuePrimitive="true"
                        :default-item="defaultCourseDelivery"
                        :data-items="courseDeliveryTypes"
                        :validator="checkDeliveryType"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <formdropdown
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </template>
                <template v-if="showResultCalculationMethods">
                    <field
                        :id="'results_calculation_methods'"
                        :name="'results_calculation_methods'"
                        :label="'Results Calculation Method'"
                        :data-items="resultMethods"
                        :layout="'horizontal'"
                        :component="'courseTypeTemplate'"
                        :validator="requiredtrue"
                        :pt="{
                            field: 'grid !grid-cols-1 md:!grid-cols-2',
                        }"
                    >
                        <template v-slot:courseTypeTemplate="{ props }">
                            <formradiogroup
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </template>
            </fieldset>
            <div class="tw-divider"></div>
            <div class="k-form-fieldset space-y-6" :style="{ maxWidth: '650px' }">
                <div class="text-base font-medium text-gray-700">Others</div>
                <div class="flex items-center justify-between">
                    <div class="w-auto">
                        <label for="work_placement" class="font-medium text-gray-700">
                            Does this qualification have a vocational placement component?</label
                        >
                    </div>
                    <div class="w-auto">
                        <field
                            :id="'work_placement'"
                            :name="'work_placement'"
                            :component="'switchTemplate'"
                        >
                            <template v-slot:switchTemplate="{ props }">
                                <formswitch
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                </div>
                <div v-if="showPlacementHours" class="space-y-4">
                    <div class="grid grid-cols-2 gap-x-6 gap-y-4">
                        <field
                            :id="'work_placement_component_type'"
                            :name="'work_placement_component_type'"
                            :component="'myTemplate'"
                            :label="'Work Placement Component Type'"
                            :placeholder="'Work Placement Component Type'"
                            :text-field="'text'"
                            :data-item-key="'id'"
                            :valueField="'id'"
                            :valuePrimitive="true"
                            :default-item="defaultWorkPlacementComponentType"
                            :data-items="workPlacementComponentTypes"
                            :validator="requiredtrue"
                        >
                            <template v-slot:myTemplate="{ props }">
                                <formdropdown
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                        <field
                            :id="'work_placement_hour'"
                            :name="'work_placement_hour'"
                            :component="'myTemplate'"
                            :label="getPlacementHoursLabel"
                            :placeholder="getPlacementHoursLabel"
                            :validator="requiredforplacement"
                        >
                            <template v-slot:myTemplate="{ props }">
                                <formnumericinput
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                    <div class="grid grid-cols-2 gap-x-6 gap-y-4">
                        <field
                            :id="'placement_officer_id'"
                            :name="'placement_officer_id'"
                            :component="'myTemplate'"
                            :label="'Work Placement Officer'"
                            :placeholder="'Work Placement Officer'"
                            :text-field="'text'"
                            :data-item-key="'id'"
                            :valueField="'id'"
                            :valuePrimitive="true"
                            :default-item="defaultPlacementOfficer"
                            :data-items="placementOfficers"
                            :validator="requiredofficerforplacement"
                        >
                            <template v-slot:myTemplate="{ props }">
                                <formdropdown
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                        <field
                            :id="'vocational_duration'"
                            :name="'vocational_duration'"
                            :component="'myTemplate'"
                            :label="'Duration of Vocational Placement'"
                            :placeholder="'Enter number of weeks'"
                            :validator="requiredforplacement"
                        >
                            <template v-slot:myTemplate="{ props }">
                                <formnumericinput
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                    <div class="grid grid-cols-2 gap-x-6 gap-y-4">
                        <field
                            :id="'vocational_startweek'"
                            :name="'vocational_startweek'"
                            :component="'myTemplate'"
                            :label="'Weeks After Course Start Date for Vocational Placement'"
                            :placeholder="'Enter number of weeks'"
                            :validator="requiredforplacement"
                            class="col-span-2"
                        >
                            <template v-slot:myTemplate="{ props }">
                                <formnumericinput
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                </div>
                <div class="mb-3 flex items-center justify-between">
                    <div class="w-auto">
                        <label for="couse_level" class="font-medium text-gray-700"
                            >Course Level</label
                        >
                    </div>
                    <div class="w-auto">
                        <field
                            :id="'couse_level'"
                            :name="'couse_level'"
                            :component="'switchTemplate'"
                        >
                            <template v-slot:switchTemplate="{ props }">
                                <formswitch
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                </div>
                <div class="mb-3 flex items-center justify-between">
                    <div class="w-auto">
                        <label for="fee_help" class="font-medium text-gray-700"
                            >Fee Help Study Load</label
                        >
                    </div>
                    <div class="w-auto">
                        <field :id="'fee_help'" :name="'fee_help'" :component="'switchTemplate'">
                            <template v-slot:switchTemplate="{ props }">
                                <formswitch
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                </div>
                <transition name="fade">
                    <div class="grid grid-cols-2 gap-x-6 gap-y-4" v-if="showFeeHelp">
                        <field
                            :id="'free_help_study_load'"
                            :name="'free_help_study_load'"
                            :label="''"
                            :component="'courseFaculty'"
                            :placeholder="'Enter Fee Help Study Load'"
                            :validator="requiredforfeehelp"
                        >
                            <template v-slot:courseFaculty="{ props }">
                                <forminput
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                        <field
                            :id="'free_help_study_type'"
                            :name="'free_help_study_type'"
                            :label="''"
                            :component="'courseFaculty'"
                            :text-field="'text'"
                            :data-item-key="'id'"
                            :valueField="'id'"
                            :valuePrimitive="true"
                            :default-item="defaultFeehelp"
                            :data-items="feeHelpStudyType"
                            :validator="requiredtypeforfeehelp"
                        >
                            <template v-slot:courseFaculty="{ props }">
                                <formdropdown
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                </transition>

                <transition name="fade">
                    <div class="flex items-center justify-between" v-if="showTcsi">
                        <div class="w-auto">
                            <label for="is_tcsi" class="font-medium text-gray-700"
                                >TCSI Reportable
                            </label>
                        </div>
                        <div class="w-auto">
                            <field :id="'is_tcsi'" :name="'is_tcsi'" :component="'switchTemplate'">
                                <template v-slot:switchTemplate="{ props }">
                                    <formswitch
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                        </div>
                    </div>
                </transition>
            </div>
        </div>
        <navigationbuttons :allowsave="kendoForm.allowSubmit" />
    </form-element>
</template>
<script>
import { Field, FormElement } from '@progress/kendo-vue-form';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormNumericInput from '@spa/components/KendoInputs/FormNumericInput.vue';
import FormCheckbox from '@spa/components/KendoInputs/FormCheckboxInline.vue';
import { Checkbox } from '@progress/kendo-vue-inputs';
import FormRadioGroup from '@spa/components/KendoInputs/FormRadioGroup.vue';
import FormRadioButton from '@spa/components/KendoInputs/FormRadioButton.vue';
import FormSwitch from '@spa/components/KendoInputs/FormSwitch.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormComboBox from '@spa/components/KendoInputs/FormComboBox.vue';
import FormAutoComplete from '@spa/components/KendoInputs/FormAutoComplete.vue';
import { Button } from '@progress/kendo-vue-buttons';
import { mapState } from 'pinia';
import { useCoursesStore } from '@spa/stores/modules/courses';
import NavigationButtons from '@spa/pages/courses/includes/NavigationButtons.vue';
import {
    requiredtrue,
    requiredpositivenumber,
    requiredpositiveinteger,
} from '@spa/services/validators/kendoCommonValidator.js';

export default {
    props: {
        coursedata: Object,
        campuses: Object,
    },
    components: {
        field: Field,
        'form-element': FormElement,
        forminput: FormInput,
        formnumericinput: FormNumericInput,
        formcheckbox: FormCheckbox,
        checkbox: Checkbox,
        formradiogroup: FormRadioGroup,
        formradiobutton: FormRadioButton,
        formswitch: FormSwitch,
        formdropdown: FormDropDown,
        formcombobox: FormComboBox,
        formautocomplete: FormAutoComplete,
        kbutton: Button,
        navigationbuttons: NavigationButtons,
    },
    inject: {
        kendoForm: { default: {} },
    },
    data: function () {
        return {
            dataitem: [],
            allCampusesData: [],
            facultyName: '',
            departmentName: '',
            defaultTarget: {
                text: 'Select work placement officer',
                id: '',
            },
            defaultCourseFaculty: {
                text: 'Select Faculty',
                id: '',
            },
            defaultCourseDepartment: {
                text: 'Select Department',
                id: '',
            },
            defaultCourseDelivery: {
                text: 'Select Course Delivery Type',
                id: '',
            },
            defaultPlacementOfficer: {
                text: 'Select work placement officer',
                id: '',
            },
            defaultFeehelp: {
                text: 'Select fee help study type',
                id: '',
            },
            defaultCourseCompletionType: {
                text: 'Select course completion type',
                id: '',
            },
            defaultWorkPlacementComponentType: {
                text: 'Select Work Placement Component Type',
                id: null,
            },
        };
    },
    mounted() {
        //set up campuses value
        let campusesData = this.formInits?.campuses;
        const campusesDataObj = campusesData.map((item) => ({
            ...item,
            selected: this.kendoForm.valueGetter(`campus_list_${item.id}`),
        }));
        this.allCampusesData = campusesDataObj;
        //set up faculty value
        const selectedFaculty = this.kendoForm.valueGetter('faculty');
        const selectedFacultyId = selectedFaculty.label || '';
        this.facultyName = selectedFacultyId;
        //set up department value
        const selectedDepartment = this.kendoForm.valueGetter('department');
        const selectedDepartmentId = selectedDepartment.label || '';
        this.departmentName = selectedDepartmentId;
        this.kendoForm.onChange('faculty_id', {
            value: selectedFacultyId,
        });
        this.kendoForm.onChange('department_id', {
            value: selectedDepartmentId,
        });
    },
    computed: {
        ...mapState(useCoursesStore, ['formInits']),
        campusesData: function () {
            return this.allCampusesData;
        },
        allCampusSelected: function () {
            return this.allCampusesData.every((campus) => campus.selected);
        },
        feeHelpStudyType: function () {
            return this.formInits?.fee_help_study_types;
        },
        resultMethods: function () {
            return this.formInits?.result_calculation_methods;
        },
        placementOfficers: function () {
            return this.formInits?.placement_officers;
        },
        courseCompletionTypes: function () {
            return this.formInits?.course_completion_types;
        },
        courseDeliveryTypes: function () {
            return this.formInits?.course_delivery_types;
        },
        courseFaculties: function () {
            return this.formInits?.faculties || [];
        },
        courseDepartments: function () {
            const facultyName = this.kendoForm.valueGetter('faculty_id');
            const currentFacultyId =
                this.courseFaculties.find((item) => item.text === facultyName)?.id || null;
            let currentDepartments = [];
            if (currentFacultyId) {
                currentDepartments = this.formInits?.departments[currentFacultyId] || [];
            }
            return currentDepartments;
        },
        workPlacementComponentTypes: function () {
            return this.formInits?.work_placement_component_types;
        },
        showPlacementHours: function () {
            return this.kendoForm.valueGetter('work_placement');
        },
        showFeeHelp: function () {
            return this.kendoForm.valueGetter('fee_help');
        },
        isHigherEd: function () {
            const courseType = this.coursedata?.course_type_id;
            return this.isCourseHigherEd(courseType);
        },
        isVet: function () {
            const courseType = this.coursedata?.course_type_id;
            return this.isCourseVet(courseType);
        },
        isShortCourse: function () {
            const courseType = this.coursedata?.course_type_id;
            return this.isCourseShortCourse(courseType);
        },
        showTcsi: function () {
            return this.isHigherEd;
        },
        showDeliveryType: function () {
            return this.isHigherEd;
        },
        isAccrediatedCourse() {
            return this.coursedata?.course_accrediation_type != 'unaccrediated';
        },
        showResultCalculationMethods() {
            if (this.isCourseShortCourse) {
                return this.isAccrediatedCourse;
            }
            return true;
        },
        showCourseCompletionType: function () {
            return this.isHigherEd || this.isVet;
        },
        showCreditPoint: function () {
            return (
                this.showCourseCompletionType &&
                this.kendoForm.valueGetter('course_completion_type') == 2
            );
        },
        getCurrentFaculty() {
            return this.facultyName;
        },
        getCurrentDepartment() {
            return this.departmentName;
        },
        getPlacementHoursLabel() {
            const type = this.kendoForm.valueGetter('work_placement_component_type');
            const label =
                this.formInits?.work_placement_component_types.find((item) => item.id === type)
                    ?.text || '';
            return `Work Placement ${label}`;
        },
    },
    methods: {
        requiredtrue,
        requiredpositivenumber,
        requiredpositiveinteger,
        handleSubmit(dataItem) {
            alert(JSON.stringify(dataItem, null, 2));
        },
        clear() {
            this.kendoForm.onFormReset();
        },
        handleChange() {
            this.$emit('campusupdated', this.allCampusesData);
        },
        toggleAllCampuses() {
            const allSelected = this.allCampusSelected;
            this.allCampusesData.forEach((campus) => (campus.selected = !allSelected));
            this.$emit('campusupdated', this.allCampusesData);
        },
        changeFaculty(faculty) {
            this.facultyName = faculty.target.value;
            this.kendoForm.onChange('faculty_id', {
                value: faculty.target.value,
            });
            //change the department after the faculty is changes ad department are dependent to faculties
            const selectedDepartment = this.kendoForm.valueGetter('department');
            const selectedDepartmentId = selectedDepartment.label || '';
            const currentDepartmentName = this.getCurrentDepartment;
            //if current department is in the selected faculty then get the value
            let matchingDepartment =
                this.courseDepartments?.find((item) => item.text === currentDepartmentName)?.text ||
                '';
            if (!matchingDepartment) {
                //if not the selected department is in the fuaculty the continue with that
                matchingDepartment =
                    this.courseDepartments?.find((item) => item.text === selectedDepartmentId)
                        ?.text || '';
            }
            this.departmentName = matchingDepartment;
            this.kendoForm.onChange('department_id', {
                value: matchingDepartment,
            });
        },
        changeDepartment(department) {
            this.departmentName = department.target.value;
            this.kendoForm.onChange('department_id', {
                value: department.target.value,
            });
        },
        requiredforplacement(value) {
            const placementOn = this.showPlacementHours;
            if (placementOn) {
                return this.requiredpositiveinteger(value);
            }
            return '';
        },
        requiredofficerforplacement(value) {
            const placementOn = this.showPlacementHours;
            const selected = this.requiredpositiveinteger(value);
            if (placementOn && selected) {
                return 'Select a placement officers.';
            }
            return '';
        },
        requiredforfeehelp(value) {
            const feehelpon = this.showFeeHelp;
            if (feehelpon) {
                return this.requiredpositiveinteger(value);
            }
            return '';
        },
        requiredtypeforfeehelp(value) {
            const feehelpon = this.showFeeHelp;
            const selected = this.requiredpositiveinteger(value);
            if (feehelpon && selected) {
                return 'Select fee help type.';
            }
            return '';
        },
        checkCompletiontype(value) {
            const completiontypeon = this.showCourseCompletionType;
            if (completiontypeon) {
                return this.requiredtrue(value);
            }
            return '';
        },
        checkCreditpoint(value) {
            const creditpointon = this.showCreditPoint;
            if (creditpointon) {
                return this.requiredpositiveinteger(value);
            }
            return '';
        },
        checkDeliveryType(value) {
            const deliverytypeon = this.showDeliveryType;
            if (deliverytypeon) {
                return this.requiredtrue(value);
            }
            return '';
        },
    },
};
</script>
