<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rto_employer', function (Blueprint $table) {
            $table->string('contact_person_phone_1')->nullable()->after('contact_person');
            $table->string('contact_person_phone_2')->nullable()->after('contact_person_phone_1');
            $table->string('contact_person_email')->nullable()->after('contact_person_phone_2');
            $table->text('contact_person_address')->nullable()->after('contact_person_email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rto_employer', function (Blueprint $table) {
            $table->dropColumn('contact_person_phone_1');
            $table->dropColumn('contact_person_phone_2');
            $table->dropColumn('contact_person_email');
            $table->dropColumn('contact_person_address');
        });
    }
};
