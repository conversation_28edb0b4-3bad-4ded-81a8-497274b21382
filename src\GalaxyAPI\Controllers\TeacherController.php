<?php

namespace GalaxyAPI\Controllers;

use App\Exports\TeacherExport;
use App\Http\Requests\BookmarkDocumentRequest;
use App\Http\Requests\CreateFolderRequest;
use App\Http\Requests\DeleteDocumentRequest;
use App\Http\Requests\MoveDocumentRequest;
use App\Http\Requests\RenameFileFolderRequest;
use App\Http\Requests\UplaodFileRequest;
use App\Model\Teacher;
use App\Model\v2\CollegeMaterials;
use App\Services\DocumentService;
use Carbon\Carbon;
use GalaxyAPI\DTO\UserInfoDTO;
use GalaxyAPI\Requests\TeacherRequest;
use GalaxyAPI\Resources\UserResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Support\Services\UploadService;

class TeacherController extends CrudBaseController
{
    protected DocumentService $documentService;

    public function init()
    {
        $this->scopeWithValue = [
            'collegeId' => Auth::user()?->college_id,
        ];

        $this->scopes = ['isTeacher'];

        $commonLoads = [
            'user',
            'createdBy',
            'updatedBy',
            'birthCountry',
            'teacherMatrix',
            'staffPosition',
        ];

        $this->loadAll = [
            ...$commonLoads,
        ];

        $this->withAll = [
            ...$commonLoads,
        ];
    }

    public function __construct(DocumentService $documentService)
    {
        $this->documentService = $documentService;
        parent::__construct(
            model: Teacher::class,
            storeRequest: TeacherRequest::class,
            updateRequest: TeacherRequest::class,
            resource: UserResource::class,
            mapperClass: UserInfoDTO::class
        );
    }

    public function updateUserId(TeacherRequest $request, $id)
    {
        $teacher = Teacher::find($id);
        $teacher->user_id = $request->input('user_id');
        $teacher->save();

        return ajaxSuccess([], '');
    }

    public function getDocument(Request $request)
    {

        if ($request->input('p') == 0) {

            $parentId = $this->documentService->findStaffParentId($request->id);
        } else {
            $parentId = $request->input('p');
        }

        // Safely decrypt the 'p' query parameter
        try {
            $parentId = decryptIt($parentId);
        } catch (\Exception $e) {
            // If decryption fails, set the parentId to null or handle it gracefully
            $parentId = null;
        }

        $data = $this->documentService->getStaffDocumentData($parentId, $request->query('b'), $request->id);

        $data['parentId'] = encryptIt($parentId);
        $data['teacherId'] = $request->id;
        $data['decryptItParentId'] = $parentId;
        $data['isShowBookMarkSwich'] = request()->query('b');
        $data['mainmenu'] = 'document';
        $data['tabDisplayFlag'] = false;
        $data['apibasepath'] = 'spa/teacher';

        return response()->json($data);
    }

    public function createFolder(CreateFolderRequest $request)
    {

        DB::beginTransaction();
        try {
            $data = $request->DTO();
            $createFolder = $this->documentService->createFolder($data->toArray());
            if ($createFolder) {
                DB::commit();

                return ajaxSuccess([], 'Folder Created Successfully');
            }
            throw new \Exception('Folder is not Created Successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function uploadDocuments(UplaodFileRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->DTO()->toArray();

            if ($data['folderId'] == 0 && $data['newFolderName'] == '') {
                return ajaxSuccessWithWarning([], 'Please select folder.');
            }
            $createFolder = $this->documentService->uploadDocuments($data, $request->file('file'), $request->id);
            if ($createFolder) {
                DB::commit();

                return ajaxSuccess([], 'File Upload Successfully');
            }
            throw new \Exception('File is not Upload Successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function renameFileFolder(RenameFileFolderRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->DTO();

            $createFolder = $this->documentService->renameFileFolder($data->toArray());
            if ($createFolder) {
                DB::commit();

                return ajaxSuccess([], 'File rename successfully.');
            }
            throw new \Exception('File is not rename Successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteDocument(DeleteDocumentRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->DTO();

            $createFolder = $this->documentService->deleteDocument($data->toArray());
            if ($createFolder) {
                DB::commit();

                return ajaxSuccess([], 'File delete successfully.');
            }
            throw new \Exception('File is not delete Successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function moveDocument(MoveDocumentRequest $request)
    {
        DB::beginTransaction();
        try {

            $data = $request->DTO();

            $createFolder = $this->documentService->moveDocument($data->toArray());
            if ($createFolder) {
                DB::commit();

                return ajaxSuccess([], 'File moved successfully.');
            }
            throw new \Exception('File is not moved Successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function bookmarkDocument(BookmarkDocumentRequest $request)
    {
        DB::beginTransaction();
        try {

            $data = $request->DTO()->toArray();

            $createFolder = $this->documentService->bookmarkDocument($data);
            if ($createFolder) {
                DB::commit();
                if ($data['type'] == 'add') {
                    return ajaxSuccess([], 'File bookmark successfully.');
                } else {
                    return ajaxSuccess([], 'File removed from bookmark successfully.');
                }
            }
            throw new \Exception('File is not bookmark Successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ajaxError($e->getMessage(), 500);
            // throw new ApplicationException($e->getMessage());
        }
    }

    public function searchDocument(Request $request)
    {
        $data['searchDocument'] = $this->documentService->searchDocument($request);

        return ajaxSuccess($data, '');
    }

    public function downloadFile(Request $request)
    {

        $primaryId = decryptIt($request->fileId);
        if (empty($primaryId)) {
            $request->session()->flash('session_error', 'File does not exist.');

            return redirect()->back();
        }
        $objCollegeMaterials = CollegeMaterials::find($primaryId);

        $folder_path = $objCollegeMaterials->file_path;
        $file_name = $objCollegeMaterials->folder_or_file;
        $original_name = $objCollegeMaterials->original_name;

        $path = $folder_path.$file_name;

        return redirect()->away(UploadService::download($path, $original_name));
    }

    public function updateTeacherCode($id)
    {
        try {
            $id = decrypt($id);
        } catch (\Exception $e) {
            return $this->error('Invalid staff member id', [], 422);
        }

        $teacher = Teacher::findOrFail($id);
        request()->validate([
            'code' => ['required', 'string', 'max:50', 'unique:rto_staff_and_teacher,staff_number,'.$teacher->id],
        ]);
        try {
            $teacher->staff_number = request()->input('code');
            $teacher->save();

            return ajaxSuccess(['data' => $teacher], 'Teacher code updated successfully');
        } catch (\Exception $e) {
            return ajaxError($e->getMessage(), 500);
        }
    }

    public function export()
    {
        request()->validate([
            'ids' => 'array',
            'ids.*' => 'exists:rto_staff_and_teacher,id',
            'format' => 'required|in:csv,xlsx',
        ]);
        $ids = request()->input('ids', []);
        $format = request()->input('format', 'csv');
        $current_date = Carbon::now()->format('Y-m-d H:i:s.u');
        $fileName = "Teacher_Export-$current_date";
        if ($format === 'csv') {
            return Excel::download(
                new TeacherExport($ids),
                $fileName.'.csv',
                \Maatwebsite\Excel\Excel::CSV
            );
        }

        // Default to Excel
        return Excel::download(
            new TeacherExport($ids),
            $fileName.'.xlsx',
            \Maatwebsite\Excel\Excel::XLSX
        );
    }
}
