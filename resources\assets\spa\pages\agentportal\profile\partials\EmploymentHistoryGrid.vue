<template>
    <grid-wrapper :borderlessHeader="true" :rounded="false" class="tw-employmentHistory-grid">
        <k-grid
            ref="employmentHistoryGrid"
            :columns="columns"
            :data-items="gridData"
            :loading="loaderStore.contextLoaders['grid-loader']"
            :loader="'loaderTemplate'"
            :resizable="true"
            :sortable="{
                allowUnsort: false,
            }"
            :sort="sort"
            @sortchange="sortChangeHandler"
        >
            <k-grid-no-records>
                <empty-state />
            </k-grid-no-records>
            <template #loaderTemplate>
                <table-loader v-if="loaderStore.contextLoaders['grid-loader']" />
            </template>
            <template #dateCell="{ props }">
                <date-cell v-bind:props="props" :format="dateFormat" />
            </template>
            <template #sortingHeaderCell="{ props }">
                <header-cell v-bind:props="props" />
            </template>
            <template #defaultCell="{ props }">
                <default-cell v-bind:props="props" />
            </template>
        </k-grid>
    </grid-wrapper>
</template>
<script>
import GridWrapperVue from '@spa/components/KendoGrid/GridWrapper.vue';
import { Grid, GridNoRecords } from '@progress/kendo-vue-grid';
import EmptyState from '@spa/components/KendoGrid/EmptyState';
import TableLoader from '@spa/components/KendoGrid/TableLoader';
import DefaultCellTemplateVue from '@spa/components/KendoGrid/templates/DefaultCellTemplate.vue';
import { useStudentPaymentStore } from '@spa/stores/modules/studentprotal/payment.store';
import GlobalContextLoader from '@spa/components/Loader/GlobalContextLoader.vue';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import { orderBy } from '@progress/kendo-data-query';
import HeaderTemplate from '@spa/components/KendoGrid/templates/HeaderTemplate.vue';
import DateCellTemplate from '@spa/components/KendoGrid/templates/DateCellTemplate.vue';

export default {
    setup(props) {
        const loaderStore = useLoaderStore();
        const store = useStudentPaymentStore();

        return {
            store,
            loaderStore,
        };
    },
    props: {
        data: { type: Object, default: {} },
    },
    components: {
        'grid-wrapper': GridWrapperVue,
        'k-grid': Grid,
        'k-grid-no-records': GridNoRecords,
        'empty-state': EmptyState,
        'table-loader': TableLoader,
        'date-cell': DateCellTemplate,
        'default-cell': DefaultCellTemplateVue,
        'header-cell': HeaderTemplate,
    },
    data() {
        return {
            columns: [
                {
                    field: 'id',
                    title: 'ID',
                    cell: 'defaultCell',
                    minResizableWidth: 100,
                },
                {
                    field: 'employer',
                    title: 'Employer',
                    cell: 'defaultCell',
                    minResizableWidth: 100,
                },
                {
                    field: 'occupation',
                    title: 'Occupation',
                    cell: 'defaultCell',
                    minResizableWidth: 100,
                },
                {
                    field: 'duration_from',
                    title: 'Duration From',
                    cell: 'dateCell',
                    minResizableWidth: 100,
                },
                {
                    field: 'duties',
                    title: 'Duties',
                    cell: 'defaultCell',
                    minResizableWidth: 100,
                },
                {},
            ],
            filter: null,
            searchWord: null,
            sort: [
                { field: 'id', dir: 'asc' },
                { field: 'employer', dir: 'asc' },
                { field: 'occupation', dir: 'asc' },
                { field: 'duration_from', dir: 'asc' },
                { field: 'duties', dir: 'asc' },
            ],
        };
    },
    computed: {
        gridData() {
            return this.prepareTableData(this.data);
        },
    },
    methods: {
        prepareTableData(data) {
            console.log(data);
            return data.map((item) => ({
                id: item.id || 0,
                employer: item.employer,
                occupation: item.occupation,
                duration_from: item.duration_from,
                duties: item.duties,
            }));
        },
        sortChangeHandler: function (e) {
            this.sort = e.sort;
            this.$emit('sort', this.sort);
        },
    },
};
</script>
<style lang=""></style>
