<script setup>
import RegisterImprovementsListComponent from '@spa/modules/register-improvements/RegisterImprovementsListComponent.vue';
import Layout from '@spa/pages/Layouts/Layout';
import { Head } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import ImprovementCategoryListComponent from '@spa/modules/improvementcategory/ImprovementCategoryListComponent.vue';
</script>
<template>
    <Layout :no-spacing="true">
        <Head title="Improvement Category" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Improvement Category" :back="false" />
        </template>
        <div class="h-screen-header flex flex-col px-8 py-6">
            <ImprovementCategoryListComponent />
        </div>
    </Layout>
</template>

<style scoped></style>
