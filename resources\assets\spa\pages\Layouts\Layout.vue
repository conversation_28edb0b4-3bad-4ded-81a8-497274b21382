<script setup>
import { twMerge } from 'tailwind-merge';

import { computed, onMounted } from 'vue';
import GlobalLoader from '@spa/components/Loader/GlobalLoader.vue';
import Container from '@spa/components/Container/Container.vue';
import GlobalDialog from '@spa/pages/Layouts/GlobalDialog.vue';

const props = defineProps({
    noSpacing: {
        type: Boolean,
        default: false,
    },
    fullViewPort: {
        type: Boolean,
        default: true,
    },
    pt: {
        type: Object,
        default: {},
    },
    loading: {
        type: Boolean,
        default: true,
    },
    layout: {
        type: String,
        default: 'default',
        validator: (value) => ['default', 'sidebar-right', 'full-width'].includes(value),
    },

    // Container settings
    maxWidth: {
        type: String,
        default: '1920px',
    },
    padding: {
        type: String,
        default: '0',
    },
    gap: {
        type: String,
        default: '1.5rem',
    },

    // Content settings
    title: {
        type: String,
        default: '',
    },

    // Visibility controls
    showHeader: {
        type: Boolean,
        default: false,
    },
    showTabs: {
        type: Boolean,
        default: false,
    },
    showFooter: {
        type: Boolean,
        default: false,
    },

    // Responsive settings
    responsive: {
        type: Boolean,
        default: true,
    },
    tabs: {
        type: Array,
        default: () => [],
    },
    activeTab: {
        type: String,
        default: '',
    },
});
onMounted(() => {});
const rootClass = computed(() => {
    return twMerge(
        'new-header__fixed w-full overflow-y-auto',
        props.fullViewPort ? ' flex flex-col' : '',
        props.noSpacing ? 'px-0' : 'px-6',
        props.pt.root
    );
});

const titleClasses = computed(() => {
    return twMerge('text-xl font-medium leading-8 text-gray-900', props.pt.title);
});

const wrapperClass = computed(() => {
    return twMerge(props.noSpacing ? 'mt-0' : 'mt-4', props.pt.wrapper);
});
</script>

<template>
    <slot name="pageTitleContent">
        <!-- <div class="justify-start font-semibold text-white">SPA</div> -->
    </slot>
    <GlobalLoader :loading="loading" loadingText="">
        <Container
            :layout="layout"
            :max-width="maxWidth"
            :padding="padding"
            :gap="gap"
            :pt="{
                root: rootClass,
                content: wrapperClass,
            }"
            :responsive="responsive"
            :show-tabs="showTabs"
            :show-header="showHeader"
            :show-footer="showFooter"
            :tabs="tabs"
            :active-tab="activeTab"
        >
            <template #tabs>
                <slot name="tabs" />
            </template>
            <template #header>
                <div v-if="showHeader && !$slots.header">
                    <h1 v-if="title" :class="titleClasses">{{ title }}</h1>
                </div>
                <slot name="header" v-if="$slots.header" />
            </template>
            <template #footer>
                <slot name="footer" />
            </template>
            <template #buttonGroup>
                <slot name="buttonGroup" />
            </template>
            <template #breadcrumb>
                <slot name="breadcrumb" />
            </template>
            <template #default>
                <slot />
            </template>
        </Container>
        <!--        <main :class="rootClass">-->
        <!--            <article :class="wrapperClass">-->
        <!--                <slot />-->
        <!--            </article>-->
        <!--        </main>-->
    </GlobalLoader>
    <slot name="modalContent"></slot>
    <GlobalDialog />
</template>
<style></style>
