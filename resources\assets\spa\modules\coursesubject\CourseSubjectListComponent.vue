<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="true"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :actions="['delete']"
    >
    </AsyncGrid>
    <CourseSubjectForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useCourseSubjectStore } from '@spa/stores/modules/coursesubject/useCourseSubjectStore.js';
import CourseSubjectForm from '@spa/modules/coursesubject/CourseSubjectForm.vue';

const store = useCourseSubjectStore();

const columns = [
    {
        field: 'name',
        title: 'Name',
        width: '200px',
    },
    // Add more columns as needed
];

const initFilters = () => {
    store.filters = {};
};

onMounted(() => {
    initFilters();
});
</script>
