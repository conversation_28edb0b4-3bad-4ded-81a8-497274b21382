import { ref, reactive, watch } from 'vue';
import _ from 'lodash';
import apiClient from '@spa/services/api.client.js';
import useConfirm from '@spa/services/useConfirm';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import globalHelper from '@spa/plugins/global-helper';
import { usePage } from '@inertiajs/vue3';

const api = apiClient;

export default function useCommonStore(apiUrl) {
    const $page = usePage();
    const confirm = useConfirm();
    const loaderStore = useLoaderStore();

    const serverPagination = ref({
        page: 1,
        rowsNumber: 0,
        sortBy: 'id',
        descending: true,
        rowsPerPage: 25,
        column_filters: {},
    });
    const filters = ref({});
    const loading = ref(false);
    const ctxLoading = ref({});
    const enableLoader = ref(true);
    const all = ref([]);
    const form_rows = ref([]);
    const selected = ref([]);
    const formRef = ref(null);
    const formDialog = ref(false);
    const showDialog = ref(false);
    const formData = ref({});
    const errors = ref({});
    const statusOptions = [
        { label: 'Active', value: 1 },
        { label: 'Inactive', value: 0 },
    ];
    const progresses = ref({});
    const bulkActions = ref([]);
    const allSelected = ref(false);
    const excluded = ref([]);
    const showPagination = ref(true);
    const notifySuccess = (message) => {
        window['Fire'].emit('axiosResponseSuccess', {
            message: message,
            time: 1000,
        });
    };
    const notifyError = (message) => {
        window['Fire'].emit('axiosResponseError', {
            message: message,
            time: 1000,
        });
    };

    const buildFormData = (formData, data, parentKey) => {
        if (
            data &&
            typeof data === 'object' &&
            !(data instanceof Date) &&
            !(data instanceof File) &&
            !Array.isArray(data)
        ) {
            Object.keys(data).forEach((key) => {
                buildFormData(formData, data[key], parentKey ? `${parentKey}[${key}]` : key);
            });
        } else if (Array.isArray(data)) {
            // Handle arrays, especially for file uploads
            data.forEach((item, index) => {
                if (item instanceof File) {
                    // For file arrays, append each file with array notation
                    formData.append(`${parentKey}[${index}]`, item);
                } else if (item && typeof item === 'object' && item.rawFile instanceof File) {
                    // Handle Kendo Upload file format
                    formData.append(`${parentKey}[${index}]`, item.rawFile);
                } else {
                    buildFormData(formData, item, `${parentKey}[${index}]`);
                }
            });
        } else if (data instanceof Date) {
            // Format Date objects for Laravel (Y-m-d H:i:s format)
            const formatDateForLaravel = (date) => {
                console.log('datae', date);
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');

                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            };
            formData.append(parentKey, formatDateForLaravel(data));
        } else {
            const value = data == null ? '' : data;
            formData.append(parentKey, value);
        }
    };
    const jsonToFormData = (data) => {
        const formData = new FormData();
        buildFormData(formData, data);
        return formData;
    };
    const store = () => {
        if (enableLoader.value) {
            loading.value = true;
        }
        return new Promise((resolve, reject) => {
            api.post(`/api/` + apiUrl, jsonToFormData(formData.value))
                .then((response) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    resolve(response);
                })
                .catch((error) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    reject(error);
                    const storeUrl = ref('medias');
                    const image = ref('');
                    const saveFile = function (file) {
                        image.value = file[0];
                        const modelData = new FormData();
                        modelData.append('uploaded-file', image.value);
                        modelData.append('upload-for', 'media');
                        api.post('api/app/storage/upload', modelData, {
                            headers: {
                                'Content-Type': 'multipart/form-data',
                            },
                        })
                            .then((response) => {
                                all.value = response.data;
                                form_rows.value = _.cloneDeep(response.data);
                                globalHelper.methods.showPopupSuccess(
                                    'Media Uploaded Successfully',
                                    'Success'
                                );
                                store.fetchPaged();
                            })
                            .catch((err) => {
                                errors.value = err.response.errors;
                            });
                    };
                })
                .finally(() => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                });
        });
    };

    const update = () => {
        if (enableLoader.value) {
            loading.value = true;
        }
        contextLoading('update', true);
        return new Promise((resolve, reject) => {
            const form = jsonToFormData(formData.value);
            // form.append('_method','PUT')
            api.post(`/api/${apiUrl}/${formData.value.id}`, form)
                .then((response) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    resolve(response);
                })
                .catch((error) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    reject(error);
                })
                .finally(() => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    contextLoading('update', false);
                });
        });
    };

    const changeStatusOtherColumn = (column = 'status') => {
        if (enableLoader.value) {
            loading.value = true;
        }
        contextLoading('change-status', true);
        return new Promise((resolve, reject) => {
            const form = jsonToFormData(formData.value);
            form.append('_method', 'PUT');
            api.post(`/api/${apiUrl}/${formData.value.id}/status-change/${column}`, form)
                .then((response) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    resolve(response);
                })
                .catch((error) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    reject(error);
                })
                .finally(() => {
                    contextLoading('change-status', false);
                });
        });
    };
    const updateDropDown = (id, payload) => {
        if (enableLoader.value) {
            loading.value = true;
        }
        contextLoading('update-dropdown', true);
        return new Promise((resolve, reject) => {
            const form = jsonToFormData(formData.value);
            // form.append('_method','PUT')
            api.post(`/api/${apiUrl}/${id}/update-dropdown`, payload)
                .then((response) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    resolve(response);
                })
                .catch((error) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    reject(error);
                })
                .finally(() => {
                    contextLoading('update-dropdown', false);
                });
        });
    };

    const destroy = (id) => {
        contextLoading('destroy', true);
        loaderStore.startContextLoading('remove');
        return new Promise((resolve, reject) => {
            api.delete(`/api/${apiUrl}/${id}`)
                .then((response) => {
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                })
                .finally(() => {
                    contextLoading('destroy', false);
                    loaderStore.stopContextLoading('remove');
                });
        });
    };
    const remove = () => {
        if (enableLoader.value) {
            loading.value = true;
        }
        contextLoading('remove', true);
        loaderStore.startContextLoading('remove');
        return new Promise((resolve, reject) => {
            const items = _.map(selected.value, (item) => {
                return item?.id;
            });
            api.post(`/api/${apiUrl}/delete`, {
                delete_rows: items,
            })
                .then((response) => {
                    selected.value = [];
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                })
                .finally(() => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    contextLoading('remove', false);
                    loaderStore.stopContextLoading('remove');
                });
        });
    };
    const fetchPaged = (callback = () => {}) => {
        if (enableLoader.value) {
            loading.value = true;
        }
        contextLoading('fetch-paged', true);
        api.get(`/api/` + apiUrl, {
            params: {
                ...serverPagination.value,
                filters: JSON.stringify(filters.value),
            },
        })
            .then((response) => {
                if (enableLoader.value) {
                    loading.value = false;
                }
                all.value = response.data;

                if (allSelected.value) {
                    const newItemsToAdd = response.data.filter((item) => {
                        const isExcluded = excluded.value.some(
                            (excludedItem) => excludedItem.id === item.id
                        );
                        const alreadySelected = selected.value.some(
                            (selectedItem) => selectedItem.id === item.id
                        );
                        return !isExcluded && !alreadySelected;
                    });

                    selected.value = [...selected.value, ...newItemsToAdd];
                }

                form_rows.value = _.cloneDeep(response.data);
                if (response.meta && response.meta.total) {
                    serverPagination.value.rowsNumber = response.meta.total;
                    showPagination.value =
                        response.meta.total > response.meta.per_page ||
                        response.meta.current_page > 1;
                }
                callback();
            })
            .catch((error) => {
                all.value = [];
                form_rows.value = [];
                serverPagination.value.rowsNumber = 0;
            })
            .finally(() => {
                if (enableLoader.value) {
                    loading.value = false;
                }
                contextLoading('fetch-paged', false);
            });
    };
    const toggleStatus = async (id) => {
        const res = await api.put(`/api/${apiUrl}/${id}/change-status`);
        if (res.status === 200) {
            globalHelper.methods.showPopupSuccess('Status changed successfully.', 'Success');
            fetchPaged();
            return true;
        } else {
            globalHelper.methods.showPopupError('Oops! Something went wrong.', 'Error');
            return false;
        }
    };
    const onRequest = ({ pagination }) => {
        serverPagination.value = pagination;
        fetchPaged();
    };
    const getAll = () => {
        if (enableLoader.value) {
            loading.value = true;
        }
        contextLoading('get-all', true);
        api.get(`/api/${apiUrl}/all`, {
            params: {
                ...filters.value,
            },
        })
            .then((response) => {
                let res = response.data;
                all.value = res.data;
                form_rows.value = _.cloneDeep(res.data);
            })
            .finally(() => {
                if (enableLoader.value) {
                    loading.value = false;
                }
                contextLoading('get-all', false);
            });
    };
    const fetchDataById = async (id) => {
        if (enableLoader.value) {
            loading.value = true;
        }
        contextLoading('fetch-by-id', true);
        const res = await api.get(`/api/${apiUrl}/${id}`);
        formData.value = Object.assign({}, res.data);
        if (enableLoader.value) {
            loading.value = false;
        }
        contextLoading('fetch-by-id', false);
        return res;
        // return  api.get(`/api/${apiUrl}/${id}`)
        //   .then(response => {
        //     let res = response.data;
        //     formData.value = Object.assign({}, res.data);
        //   })
        //   .finally(() => {
        //     loading.value = false;
        //   });
    };
    const fetchDataBySlug = async (slug) => {
        if (enableLoader.value) {
            loading.value = true;
        }
        const res = await api.get(`/api/${apiUrl}/with-slug/${slug}`);
        formData.value = Object.assign({}, res.data);
        if (enableLoader.value) {
            loading.value = false;
        }
        contextLoading('fetch-by-id', false);
        return res;
    };

    function createFunction() {
        formDialog.value = true;
        formData.value = {};
        errors.value = {};
    }

    function clearFunction() {
        filters.value = {};
        fetchPaged();
    }

    function clearSelection() {
        selected.value = [];
    }

    function closeFunction() {
        const redirect_page = formData.value?.redirect_page;
        const redirect = formData.value?.redirect;
        formData.value = {};
        errors.value = {};
        formDialog.value = false;
        if (redirect) {
            // router.push({
            //   name: redirect_page
            // })
        }
    }

    const edit = (item) => {
        formData.value = Object.assign({}, item);
        formDialog.value = true;
    };
    const processErrors = (resErrors) => {
        const keys = Object.keys(resErrors);
        for (const key of keys) {
            try {
                if (resErrors[key][0]) {
                    errors.value[key] = resErrors[key][0];
                }
            } catch (e) {
                console.log('error', e);
            }
        }
    };
    const submitFormData = () => {
        if (formData.value.id) {
            return update()
                .then((res) => {
                    closeFunction();
                    globalHelper.methods.showPopupSuccess('Updated successfully', 'Success');
                    fetchPaged();
                    return res;
                })
                .catch(({ data }) => {
                    processErrors(data.data);
                });
        } else {
            return store()
                .then((res) => {
                    closeFunction();
                    globalHelper.methods.showPopupSuccess('Created successfully', 'Success');
                    fetchPaged();
                    return res;
                })
                .catch(({ data }) => {
                    processErrors(data.data);
                });
        }
    };
    const objToFormData = (obj) => {
        const newFormData = new FormData();
        Object.keys(obj).forEach((key) => {
            newFormData.append(key, obj[key]);
        });
    };
    const confirmDelete = (item, options = {}) => {
        const {
            message = 'Are you sure you want to delete this item? This action cannot be undone.',
            header = 'Delete Item?',
            icon = 'pi pi-exclamation-triangle',
            variant = 'danger',
            acceptLabel = 'Delete',
            rejectLabel = 'Cancel',
            width = 500,
            onAccept = null,
            onReject = null,
            onHide = null,
        } = options;
        confirm.require({
            message,
            header,
            icon,
            variant,
            acceptLabel,
            rejectLabel,
            width,
            accept: async () => {
                // selected.value.push(item);
                // remove()
                //     .then((res) => {
                //         selected.value = [];
                //         // globalHelper.methods.showPopupSuccess('Deleted successfully', 'Success');
                //         fetchPaged();
                //     })
                //     .catch((err) => {
                //         errors.value = err.response.errors;
                //     });
                await destroy(item.id)
                    .then((res) => {
                        fetchPaged();
                    })
                    .catch((err) => {
                        errors.value = err.response.errors;
                    });
                if (typeof onAccept === 'function') {
                    onAccept();
                }
            },
            reject: () => {
                if (typeof onReject === 'function') {
                    onReject();
                }
                return false;
            },
            onHide: () => {
                if (typeof onHide === 'function') {
                    onHide();
                }
                return false;
            },
        });
    };

    const show = () => {
        if (enableLoader.value) {
            loading.value = true;
        }
        contextLoading('show', true);
        return new Promise((resolve, reject) => {
            api.get(`/api/${apiUrl}/${formData.value.id}`)
                .then((response) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    resolve(response);
                })
                .catch((error) => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    reject(error);
                })
                .finally(() => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    contextLoading('show', false);
                });
        });
    };

    const viewFormData = (item) => {
        formData.value = Object.assign({}, item);
        showDialog.value = true;

        // show()
        //     .then((res) => {
        //         console.log("response333", res)
        //         formData.value = res.data;
        //     })
        //     .catch(({ data }) => {
        //         processErrors(data.data);
        //     });
    };

    const confirmBulkDelete = (options = {}) => {
        const {
            message = `Are you sure you want to delete ${selected.value.length} items? This action cannot be undone.`,
            header = 'Delete Items?',
            icon = 'pi pi-exclamation-triangle',
            variant = 'danger',
            acceptLabel = 'Delete All',
            rejectLabel = 'Cancel',
            width = 500,
            onAccept = null,
            onReject = null,
            onHide = null,
        } = options;
        confirm.require({
            message,
            header,
            icon,
            variant,
            acceptLabel,
            rejectLabel,
            width,
            accept: () => {
                if (typeof onAccept === 'function') {
                    onAccept();
                }
                remove()
                    .then((res) => {
                        selected.value = [];
                        // globalHelper.methods.showPopupSuccess('Deleted successfully', 'Success');
                        fetchPaged();
                    })
                    .catch((err) => {
                        errors.value = err.response.errors;
                    });
            },
            reject: () => {
                if (typeof onReject === 'function') {
                    onReject();
                }
                return false;
            },
            onHide: () => {
                if (typeof onHide === 'function') {
                    onHide();
                }
                return false;
            },
        });
    };

    const resetForm = () => {
        formData.value = {};
        errors.value = {};
    };

    const contextLoading = (context, val) => {
        if (typeof val !== 'undefined') {
            ctxLoading.value[context] = val;
            return;
        }
        return ctxLoading[context];
    };
    let lastFetchedFilters = JSON.stringify({});
    watch(
        () => filters.value,
        (newFilters) => {
            const newFiltersStr = JSON.stringify(newFilters);
            if (lastFetchedFilters === null) {
                lastFetchedFilters = newFiltersStr;
            }
            // Only fetch if filters actually changed from the last fetch
            if (lastFetchedFilters !== newFiltersStr) {
                lastFetchedFilters = newFiltersStr;
                fetchPaged();
            }
        },
        { deep: true }
    );

    const onSelectAll = (val) => {
        allSelected.value = val;
        selected.value = val ? all.value : [];
        excluded.value = val ? [] : excluded.value;
    };

    const getBulkActions = () => {
        try {
            api.get(`/api/${apiUrl}/bulk-actions`)
                .then((response) => {
                    bulkActions.value = response.data;
                })
                .catch((error) => {
                    console.error('Error fetching bulk actions:', error);
                });
        } catch (error) {
            console.error('Error fetching bulk actions:', error);
        }
    };

    const restore = (id) => {
        if (enableLoader.value) {
            loading.value = true;
        }
        contextLoading('restore', true);
        return new Promise((resolve, reject) => {
            api.put(`/api/${apiUrl}/${id}/restore`)
                .then((response) => {
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                })
                .finally(() => {
                    if (enableLoader.value) {
                        loading.value = false;
                    }
                    contextLoading('restore', false);
                });
        });
    };

    const confirmRestore = (item, options = {}) => {
        const {
            message = 'Are you sure you want to restore this item?',
            header = 'Restore Item?',
            icon = 'pi pi-exclamation-triangle',
            variant = 'danger',
            acceptLabel = 'Restore',
            rejectLabel = 'Cancel',
            width = 500,
            onAccept = null,
            onReject = null,
            onHide = null,
        } = options;
        confirm.require({
            message,
            header,
            icon,
            variant,
            acceptLabel,
            rejectLabel,
            width,
            accept: () => {
                restore(item.id)
                    .then((res) => {
                        fetchPaged();
                    })
                    .catch((err) => {
                        errors.value = err.response.errors;
                    });
                if (typeof onAccept === 'function') {
                    onAccept();
                }
            },
            reject: () => {
                if (typeof onReject === 'function') {
                    onReject();
                }
                return false;
            },
            onHide: () => {
                if (typeof onHide === 'function') {
                    onHide();
                }
                return false;
            },
        });
    };
    return {
        serverPagination,
        filters,
        loading,
        enableLoader,
        all,
        form_rows,
        formRef,
        formDialog,
        formData,
        errors,
        progresses,
        statusOptions,
        selected,
        ctxLoading,
        showDialog,
        bulkActions,
        allSelected,
        excluded,
        jsonToFormData,
        getAll,
        fetchDataById,
        onRequest,
        store,
        fetchPaged,
        update,
        remove,
        createFunction,
        closeFunction,
        edit,
        submitFormData,
        confirmDelete,
        clearFunction,
        toggleStatus,
        fetchDataBySlug,
        updateDropDown,
        notifySuccess,
        notifyError,
        changeStatusOtherColumn,
        contextLoading,
        show,
        viewFormData,
        clearSelection,
        confirmBulkDelete,
        resetForm,
        onSelectAll,
        getBulkActions,
        loaderStore,
        processErrors,
        showPagination,
        restore,
        confirmRestore,
    };
}
