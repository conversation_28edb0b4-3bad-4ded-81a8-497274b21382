<template>
    <div>
        <Card v-if="hasFilters" :pt="{ root: 'relative p-4 lg:p-6 rounded-lg mb-4' }">
            <template #content>
                <div v-if="hasRestFilters" class="items-cneter flex justify-between">
                    <div
                        class="flex cursor-pointer items-center gap-2"
                        @click="showFilters = !showFilters"
                        v-tooltip.right="'Show Filters'"
                    >
                        <h2 class="text-base font-semibold">Filter</h2>
                        <Button variant="icon">
                            <icon
                                :name="'filter'"
                                :width="'16'"
                                :height="'16'"
                                :fill="'currentColor'"
                            />
                        </Button>
                    </div>
                    <Button
                        v-if="hasRestFilters"
                        variant="ghost"
                        size="xs"
                        class="flex min-w-32 cursor-pointer justify-start px-0"
                        @click="emit('handelReset')"
                    >
                        <icon
                            :name="'sync'"
                            :fill="isAllFilterValuesNull ? '#69c0ff' : '#1890FF'"
                            :className="reset ? 'animate-spin' : ''"
                        />
                        <span> Reset Filters </span>
                    </Button>
                </div>
                <Transition name="slide-fade">
                    <div
                        class="flex flex-col items-start justify-between gap-x-24 md:flex-row md:items-center"
                        v-if="showFilters"
                    >
                        <div :class="filterWrapperClass">
                            <slot name="filters" />
                        </div>
                    </div>
                </Transition>
            </template>
        </Card>
        <div
            class="flex flex-wrap items-center justify-between gap-4 rounded-t-lg bg-white p-4 max-md:flex-col"
        >
            <div class="flex items-center gap-4" v-if="hasBulkActions || hasAllSelect">
                <DropdownButton
                    variant="primary"
                    :outline="true"
                    :autoHide="true"
                    :buttonLabel="'Bulk Actions'"
                    :disabled="store.selected?.length === 0"
                    v-if="hasBulkActions"
                >
                    <template #dropdown-content>
                        <button
                            v-if="hasExport"
                            class="flex w-full items-center px-4 py-2 text-sm font-medium hover:bg-gray-100"
                        >
                            <file-icon name="xlsx" class="h-6 w-6 text-gray-500" />
                            Export (xls)
                        </button>
                        <slot name="header-actions" />
                    </template>
                </DropdownButton>
                <div
                    class="flex items-center gap-2"
                    v-if="store.selected?.length > 0 && hasAllSelect"
                >
                    <p>{{ selectedCountText }}</p>
                    <Button
                        variant="link"
                        @click="store.onSelectAll(true)"
                        v-if="
                            !store.allSelected &&
                            store.selected?.length < store.serverPagination.rowsNumber
                        "
                        >Select all {{ store.serverPagination.rowsNumber }} records.</Button
                    >
                    <Button variant="link" @click="store.onSelectAll(false)" v-else
                        >Deselect all records.</Button
                    >
                </div>
            </div>

            <div class="item-center flex gap-4" :class="{ 'ml-auto': hasBulkActions }">
                <div v-if="hasSearch" class="coursesSearchInputField relative">
                    <span class="absolute left-3 top-1/2 -translate-y-1/2">
                        <icon :name="'lens'" width="16" height="16"
                    /></span>
                    <input
                        type="text"
                        id="offscreen"
                        :value="store.filters.query"
                        @input="debouncedSearch"
                        class="tw-input-text h-10 w-80 pl-8"
                        :placeholder="searchPlaceholder"
                    />
                </div>
            </div>
            <div class="flex w-full flex-wrap items-center justify-end gap-3 md:w-auto">
                <slot name="bulk-actions" />
                <Button
                    v-if="hasCreateAction"
                    @click="() => store.createFunction()"
                    class="tw-btn-primary !h-9 shadow-none"
                    size="xs"
                    :disabled="store.loading"
                >
                    <icon :name="'plus'" :fill="'currentColor'" :width="'16'" :height="'16'" />
                    {{ createBtnLabel }}
                </Button>

                <Button
                    v-if="hasRefreshAction"
                    @click="
                        () => {
                            store.fetchPaged();
                        }
                    "
                    class="!size-10 rounded-md border bg-white p-2 shadow-none"
                    size="xs"
                    variant="icon"
                    :loading="store.loading"
                    loading-text=""
                >
                    <svg
                        width="25"
                        height="25"
                        class="size-5 fill-gray-600 text-gray-600"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M12 4.75a7.25 7.25 0 1 0 7.201 6.406c-.068-.588.358-1.156.95-1.156.515 0 .968.358 1.03.87a9.25 9.25 0 1 1-3.432-6.116V4.25a1 1 0 1 1 2.001 0v2.698l.034.052h-.034v.25a1 1 0 0 1-1 1h-3a1 1 0 1 1 0-2h.666A7.219 7.219 0 0 0 12 4.75Z"
                        />
                    </svg>
                </Button>
                <slot name="manage-column" />
            </div>
        </div>
    </div>
</template>
<script setup>
import Button from '@spa/components/Buttons/Button.vue';
import Card from '@spa/components/Card/Card.vue';
import { ref, computed, watch, onMounted } from 'vue';
import { twMerge } from 'tailwind-merge';
import DropdownButton from '@spa/components/Buttons/DropdownButton.vue';
import { debounce } from 'lodash';
import { useMediaQuery } from '@spa/services/useMediaQuery.js';

const reset = ref(false);
const search = ref('');
const isAllFilterValuesNull = ref(false);
const showFilters = ref(true);
const isDesktop = useMediaQuery('(min-width: 1024px)');

onMounted(() => {
    showFilters.value = isDesktop.value;
});

const props = defineProps({
    store: { type: Object, required: true },
    hasSearch: { type: Boolean, default: true },
    hasFilters: { type: Boolean, default: true },
    hasBulkActions: { type: Boolean, default: true },
    hasExport: { type: Boolean, default: true },
    hasRestFilters: { type: Boolean, default: true },
    hasRefreshAction: { type: Boolean, default: true },
    hasCreateAction: { type: Boolean, default: true },
    createBtnLabel: { type: String, default: 'Add New' },
    filterColumns: { type: Number, default: 3 },
    hasManageColumns: { type: Boolean, default: true },
    columns: { type: Array, default: () => [] },
    storageKey: { type: String, default: 'grid-columns' },
    hasAllSelect: { type: Boolean, default: false },
    searchPlaceholder: { type: String, default: 'Enter Keyword' },
});

const selectedCountText = computed(() => {
    let selectedCount = props.store.selected.length;
    if (props.store.allSelected) {
        selectedCount =
            props.store.serverPagination.rowsNumber - (props.store.excluded?.length || 0);
    }
    return `${selectedCount} record${selectedCount !== 1 ? 's' : ''} selected`;
});

const filterWrapperClass = computed(() => {
    const columnsMap = {
        1: 'md:grid-cols-1',
        2: 'md:grid-cols-2',
        3: 'md:grid-cols-3',
        4: 'md:grid-cols-4',
        6: 'md:grid-cols-6',
        12: 'md:grid-cols-12',
    };
    return twMerge(
        'grid gap-4 gap-x-8 flex-1',
        columnsMap[props.filterColumns] || 'md:grid-cols-3'
    );
});

const handleSearch = (event) => {
    props.store.filters.query = event.target?.value;
};
const debouncedSearch = debounce(handleSearch, 450);

const emit = defineEmits(['handelReset', 'handleExport']);
</script>
<style scoped>
.slide-fade-enter-active {
    transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
    transition: all 0.1s ease-out;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
    transform: translateY(20px);
    opacity: 0;
}
</style>
