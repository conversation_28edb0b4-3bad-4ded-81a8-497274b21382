<?php

namespace App\Http\Controllers\v2\api;

use App\Exceptions\ApplicationException;
use App\Http\Controllers\Controller;
use App\Http\Requests\FormValidation\StudentProfile\AddPaymentAdditionalServiceInformationPaymentData;
use App\Http\Requests\FormValidation\StudentProfile\AgentCommissionRequest;
use App\Http\Requests\FormValidation\StudentProfile\EditStudentPaymentSchedule;
use App\Http\Requests\FormValidation\StudentProfile\InvoiceCreditRequest;
use App\Http\Requests\FormValidation\StudentProfile\MiscellaneousPaymentRequest;
use App\Http\Requests\FormValidation\StudentProfile\PayUpfrontFeeRequest;
use App\Http\Requests\FormValidation\StudentProfile\RecordMiscellaneousPaymentRequest;
use App\Http\Requests\FormValidation\StudentProfile\RecordPaymentRequest;
use App\Http\Requests\FormValidation\StudentProfile\RecordServicePaymentRequest;
use App\Http\Requests\FormValidation\StudentProfile\RefundAdditionalServicePayment;
use App\Http\Requests\FormValidation\StudentProfile\RefundPaymentHistory;
use App\Http\Requests\FormValidation\StudentProfile\SaveEditPaymentTransaction;
use App\Http\Requests\FormValidation\StudentProfile\SaveNewPaymentSchedule;
use App\Http\Requests\FormValidation\StudentProfile\SaveTransferPaymentData;
use App\Http\Requests\FormValidation\StudentProfile\SendEmailRequest;
use App\Http\Requests\FormValidation\StudentProfile\StudentScholarship;
use App\Model\v2\Student;
use App\Model\v2\StudentAgentCommission;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentInitialPaymentDetails;
use App\Model\v2\StudentMiscellaneousPayment;
use App\Model\v2\StudentScholarship as StudentScholarshipV2;
use App\Model\v2\StudentServicePayment;
use App\Process\StudentProfile\TransferScholarshipProcess;
use App\Services\StudentPaymentService;
use App\Traits\ResponseTrait;
use Carbon\Carbon;
use Domains\Xero\Events\PaymentDetailReadyToSyncToXero;
use Domains\Xero\Events\ScholarshipPaymentReadyToSyncToXero;
use Domains\Xero\Events\XeroCreateStudentInvoiceEvent;
use Domains\Xero\Facades\Xero;
use Domains\Xero\Jobs\SyncCreditNoteFromXero;
use Domains\Xero\Jobs\SyncInvoiceFromXero;
use Domains\Xero\Jobs\SyncMiscellaneousPaymentToXero;
use Domains\Xero\Jobs\SyncPaymentDetailToXero;
use Domains\Xero\Jobs\SyncStudentAgentCommissionToXero;
use Domains\Xero\Jobs\SyncStudentAgentPurchaseOrderToXero;
use Domains\Xero\Jobs\SyncStudentPaymentServiceToXero;
use Domains\Xero\Jobs\SyncStudentScholarshipToXero;
use Domains\Xero\Models\XeroConfig;
use Domains\Xero\Models\XeroCreditNote;
use Domains\Xero\Models\XeroInvoice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;

class StudentPaymentApiController extends Controller
{
    use ResponseTrait;

    protected $studentPaymentService;

    public function __construct(StudentPaymentService $studentPaymentService)
    {
        $this->studentPaymentService = $studentPaymentService;
    }

    public function getAllPaymentsData(Request $request)
    {
        $request->payment_type = 'All';
        $data = $this->studentPaymentService->getAllPaymentsData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getInitialPaymentData(Request $request)
    {
        $request->payment_type = 'Initial';    // TODO::GNG-2576
        $data = $this->studentPaymentService->getInitialOrSchedulePaymentData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getPaymentScheduleData(Request $request)
    {
        $request->payment_type = 'Schedual';    // TODO::GNG-2576
        $data = $this->studentPaymentService->getInitialOrSchedulePaymentData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getXeroFailedPaymentsData(Request $request)
    {
        $data = $this->studentPaymentService->getXeroFailedPaymentsData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getAgentCommissionData(Request $request)
    {
        $data = $this->studentPaymentService->getAgentCommissionData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getSyncLogData(Request $request)
    {
        $data = $this->studentPaymentService->getSyncLogData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getTabSyncLogData(Request $request)
    {
        $data = $this->studentPaymentService->getTabSyncLogData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getAgentBonusData(Request $request)
    {
        $data = $this->studentPaymentService->getAgentBonusData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getPaymentRefundData(Request $request)
    {
        $data = $this->studentPaymentService->getPaymentRefundData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getStudentScholarshipData(Request $request)
    {
        $data = $this->studentPaymentService->getStudentScholarshipData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getMiscellaneousPaymentData(Request $request)
    {
        $data = $this->studentPaymentService->getMiscellaneousPaymentData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getMiscellaneousPaymentFromData(Request $request)
    {
        $data = $this->studentPaymentService->getMiscellaneousPaymentFromData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getServicePaymentData(Request $request)
    {
        $data = $this->studentPaymentService->getServicePaymentData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getServicePaymentFormData(Request $request)
    {
        $data = $this->studentPaymentService->getServicePaymentFormData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getPaymentStatementData(Request $request)
    {
        $data = $this->studentPaymentService->getPaymentStatementData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getPaymentModeList(Request $request)
    {
        $data = $this->studentPaymentService->getPaymentModeList($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getInvoiceNumber(Request $request)
    {
        $data = $this->studentPaymentService->getInvoiceNumber();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getRecordPaymentDetails(Request $request)
    {
        if (Xero::isConnected()) {
            return $this->errorResponse('Xero is connected, So manual record of payment is not permitted.', 'data', '', 200);
        }

        $data = $this->studentPaymentService->getRecordPaymentDetails($request);
        if (isset($data['status']) && $data['status'] == 'error') {
            return $this->errorResponse($data['message'], 'data', '', 200);
        }

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getStudentUpfrontDetails(Request $request)
    {
        $data = $this->studentPaymentService->getStudentUpfrontDetails($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function saveStudentPayUpfrontDetails(PayUpfrontFeeRequest $request)
    {
        $data = $this->studentPaymentService->saveStudentPayUpfrontDetails($request->DTO());
        if ($data['type'] == 'session_error') {
            return $this->errorResponse($data['message'], 'data', '', 200);
        } else {
            return $this->successResponse($data['message'], 'data', '', 200);
        }
    }

    public function saveRecordPaymentDetails(RecordPaymentRequest $request)
    {
        $data = $this->studentPaymentService->saveRecordPaymentDetails($request->DTO());

        return $this->successResponse('Payment paid successfully', 'data', $data);
    }

    public function getPaidPaymentData(Request $request)
    {
        $data = $this->studentPaymentService->getPaidPaymentData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getAgentCommission(Request $request)
    {
        $data = $this->studentPaymentService->getAgentCommission($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function modifyAgentCommissionData(Request $request)
    {
        $data = $this->studentPaymentService->modifyAgentCommissionData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function saveAgentCommission(AgentCommissionRequest $request)
    {
        $data = $this->studentPaymentService->saveAgentCommission($request->DTO());
        if ($data['type'] == 'error') {
            return $this->errorResponse($data['message'], 'data', '', 200);
        } else {
            return $this->successResponse('Agent Commission Info Successfully Updated', 'data', $data);
        }
    }

    public function approveAgentCommission(Request $request)
    {
        if ($request->input('approveType') == 'bulk') {
            $data = $this->studentPaymentService->bulkAproveAgentCommissionData($request);
            if ($data['type'] == 'error') {
                return $this->errorResponse($data['message'], 'data', '', 200);
            } else {
                return $this->successResponse($data['message'], 'data', $data);
            }
        } else {
            $data = $this->studentPaymentService->aproveAgentCommissionData($request);
            if ($data['type'] == 'error') {
                return $this->errorResponse($data['message'], 'data', '', 200);
            } else {
                return $this->successResponse($data['message'], 'data', $data);
            }
        }
    }

    public function disapproveAgentCommission(Request $request)
    {
        $data = $this->studentPaymentService->disapproveAgentCommissionData($request);
        if ($data['type'] == 'error') {
            return $this->errorResponse($data['message'], 'data', '', 200);
        } else {
            return $this->successResponse($data['message'], 'data', $data);
        }
    }

    public function processAgentCommission(Request $request)
    {
        $data = $this->studentPaymentService->processAgentCommissionData($request);
        if ($data['type'] == 'error') {
            return $this->errorResponse($data['message'], 'data', '', 200);
        } else {
            return $this->successResponse($data['message'], 'data', $data);
        }
    }

    public function payAgentCommission(Request $request)
    {
        $data = $this->studentPaymentService->payAgentCommission($request);
        if ($data['status'] == 'error') {
            return $this->errorResponse($data['message'], 'data', '', 200);
        } else {
            return $this->successResponse($data['message'], 'data', $data);
        }
    }

    public function saveStudentScholarshipDetails(StudentScholarship $request)
    {
        $data = $this->studentPaymentService->saveStudentScholarshipDetails($request->DTO());

        return $this->successResponse('Student Scholarship Details Save Successfully', 'data', $data);
    }

    public function updateStudentScholarshipDetails(StudentScholarship $request)
    {
        $isFullyEditable = ($request->input('is_fully_editable') == 1) ? true : false;
        $res = $this->studentPaymentService->updateStudentScholarshipDetails($request->DTO(), $isFullyEditable);
        if ($res['type'] == 'success') {
            return $this->successResponse($res['message'], 'data', $res['data']);
        } else {
            return $this->errorResponse($res['message'], 'data', '', 200);
        }
    }

    public function getPaymentTransactionDetails(Request $request)
    {
        $data = $this->studentPaymentService->getPaymentTransactionDetails($request->input());
        if ($data) {
            return $this->successResponse('Data found successfully', 'data', $data[0]);
        } else {
            return $this->successResponse('No data found successfully', 'data', '');
        }
    }

    public function getPaymentTransactionData(Request $request)
    {
        if (isset($request->canDelete) && $request->canDelete) {
            $res = $this->studentPaymentService->isValidForDeletePaymentTransaction($request->detailId);
            if ($res['type'] == 'error') {
                return $this->errorResponse($res['message'], 'data', '', 200);
            }
        }
        $data = $this->studentPaymentService->getPaymentTransactionData($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function deletePaymentTransaction(Request $request)
    {
        $validatedData = $request->validate([
            'reason_to_delete' => 'required',
        ]);
        $data = $this->studentPaymentService->deletePaymentTransaction($request->input());

        return $this->successResponse('Payment Transaction Delete SuccessFully', 'data', $data);
    }

    public function saveEditPaymentTransaction(SaveEditPaymentTransaction $request)
    {
        $data = $this->studentPaymentService->saveEditPaymentTransaction($request->input());

        return $this->successResponse('Data Updated successfully', 'data', $data);
    }

    public function savePaymentRefundDetails(RefundPaymentHistory $request)
    {
        $data = $this->studentPaymentService->savePaymentRefundDetails($request->input());
        if ($data['type'] == 'session_error') {
            return $this->errorResponse($data['message'], 'data', '', 200);
        } else {
            return $this->successResponse($data['message'], 'data', '', 200);
        }
    }

    public function savePaymentTransactionReverse(Request $request)
    {
        $validatedData = $request->validate([
            'reverse_comment' => 'required',
        ]);
        $data = $this->studentPaymentService->savePaymentTransactionReverse($request->input());

        return $this->successResponse('Payment Transaction Reverse Successfully', 'data', $data);
    }

    public function deletePaymentRefundHistory(Request $request)
    {
        $data = $this->studentPaymentService->deletePaymentRefundHistory($request->input());

        return $this->successResponse('Payment Refund Delete Successfully', 'data', $data);
    }

    public function revertBackPaymentHistory(Request $request)
    {
        $data = $this->studentPaymentService->revertBackPaymentHistory($request->input());
        if ($data['type'] == 'error') {
            return $this->errorResponse($data['message'], 'data', '', 200);
        } else {
            return $this->successResponse('Revert Back to Normal', 'data', $data);
        }
    }

    public function saveInvoiceCreditDetails(InvoiceCreditRequest $request)
    {
        $data = $this->studentPaymentService->saveInvoiceCreditDetails($request->input());
        if ($data['type'] == 'session_error') {
            return $this->errorResponse($data['message'], 'data', '', 200);
        } else {
            return $this->successResponse($data['message'], 'data', '', 200);
        }
    }

    public function deleteStudentScholarship(Request $request)
    {
        $data = $this->studentPaymentService->deleteStudentScholarshipData($request);
        if ($data['type'] == 'error') {
            return $this->errorResponse($data['message'], 'data', '', 200);
        } else {
            return $this->successResponse($data['message'], 'data', '', 200);
        }
    }

    public function deletePaymentSchedule(Request $request)
    {
        $data = $this->studentPaymentService->deletePaymentSchedule($request);

        return $this->successResponse('Payment Schedule Delete Successfully', 'data', $data);
    }

    public function checkSyncStatus(Request $request)
    {
        $res = 0;
        if (count($request->ids) > 0) {
            switch ($request->type) {
                case 'schedule':
                case 'prepayment':
                    $res = StudentInitialPaymentDetails::whereIn('id', $request->ids)
                        ->where('is_synced', StudentInitialPaymentDetails::STATUS_SYNCING)
                        ->where('updated_at', '>=', Carbon::now()->subMinutes(2))
                        ->count();
                    break;

                case 'miscellaneous':
                    $res = 0;
                    break;

                case 'service':
                    $res = 0;
                    break;

                default:
                    $res = 0;
            }
        }

        return $this->successResponse('Sync', 'data', $res);
    }

    public function syncBulkPaymentsFromXero(Request $request)
    {
        if (count($request->primaryIdSet) == 0) {
            return $this->errorResponse('Please select at least one row', 'data', [], 200);
        }

        if (! Xero::isConnected()) {
            return $this->errorResponse('Xero not connected', 'data', '', 200);
        }

        /*if (!XeroConfig::Mapped()) {
            return $this->errorResponse('Please finish mapping xero account from xero setup page..', 'data', '', 200);
        }*/

        switch ($request->action) {
            case 'schedule':
            case 'prepayment':
                return $this->syncBulkInstallmentAndPrepaymentFromXero($request);
                break;

            case 'agent_commission':
                return $this->syncBulkAgentCommissionToXero($request);
                break;

            case 'agent_bonus':
                $data = [];
                break;

            case 'miscellaneous':
                return $this->syncBulkMiscellaneousFromXero($request);
                break;

            case 'service':
                return $this->syncBulkServiceFromXero($request);
                break;

            case 'scholarship':
                return $this->syncBulkScholarshipFromXero($request);
                break;

            default:
                return $this->errorResponse('Something will be wrong. Please try again.', 'data', [], 200);
        }
        // return $this->successResponse('Selected Items Delete Successfully', 'data', $data);

    }

    public function syncBulkInstallmentAndPrepaymentFromXero($request)
    {
        $studPaymentDetailIds = $request->primaryIdSet;
        if (count($studPaymentDetailIds) == 0) {
            return $this->errorResponse('Please select at least one row', 'data', '', 200);
        }
        if (! Xero::isConnected()) {
            return $this->errorResponse('Xero not connected', 'data', '', 200);
        }

        $sync = $alreadySync = 0;
        $jobs = [];
        $syncingIds = [];
        foreach ($studPaymentDetailIds as $id) {
            $paymentData = StudentInitialPaymentDetails::where('id', $id)->with(['xeroInvoice'])->first();
            // $paymentData = StudentInitialPaymentDetails::findOrFail($id);
            // dd($paymentData->isConnectedToXero());
            if ($paymentData->xeroInvoice == null || empty($paymentData->xeroInvoice->xero_invoice_id)) {
                $this->updateSyncStatusForStart($paymentData);
                $jobs[] = new SyncPaymentDetailToXero($id);
                $syncingIds[] = $id;
                $sync++;
            } else {
                $alreadySync++;
            }
        }

        if (count($jobs)) {
            Bus::chain($jobs)->dispatch();
        }

        if ($sync > 0) {
            return $this->successResponse('Selected data synced successfully', 'data', $syncingIds);
        } elseif ($alreadySync > 0) {
            return $this->successResponse('Selected data already synced', 'data', []);
        } else {
            return $this->errorResponse('Something will be wrong. Please try again.', 'data', '', 200);
        }
    }

    private function updateSyncStatusForStart($initialPaymentDetail)
    {
        $initialPaymentDetail->is_synced = StudentInitialPaymentDetails::STATUS_SYNCING;

        return $initialPaymentDetail->save();
    }

    public function syncPaymentSchedule(Request $request)
    {
        if (! $request->id) {
            return $this->errorResponse('Id Missing', 'data', '', 200);
        }
        if (! Xero::isConnected()) {
            return $this->errorResponse('Xero not connected', 'data', '', 200);
        }

        dispatch(new SyncPaymentDetailToXero($request->id));

        // PaymentDetailReadyToSyncToXero::dispatchSync($request->id);
        return $this->successResponse('Payment Schedule Synced', 'data', []);
    }

    public function syncPaymentScheduleFromXero(Request $request)
    {
        if (! $request->id) {
            return $this->errorResponse('Id Missing', 'data', '', 200);
        }

        if (! Xero::isConnected()) {
            return $this->errorResponse('Xero not connected', 'data', '', 200);
        }

        if (! XeroConfig::Mapped()) {
            return $this->errorResponse('Please finish mapping xero accuont from xero setup page..', 'data', '', 200);
        }

        $paymentDetails = StudentInitialPaymentDetails::findOrFail($request->id);
        dispatch(new SyncInvoiceFromXero($paymentDetails->xeroInvoice));

        return $this->successResponse('Payment Schedule is being synced from Xero', 'data', []);
    }

    public function syncMiscellaneousPayment(Request $request)
    {
        if (! $request->id) {
            return $this->errorResponse('Id Missing', 'data', '', 200);
        }
        if (! Xero::isConnected()) {
            return $this->errorResponse('Xero not connected', 'data', '', 200);
        }
        if (! XeroConfig::Mapped()) {
            return $this->errorResponse('Please finish mapping xero accuont from xero setup page..', 'data', '', 200);
        }
        // TODO::add event for Miscellaneous Payment

        dispatch(new SyncMiscellaneousPaymentToXero($request->id));

        return $this->successResponse('Miscellaneous Payment Synced', 'data', []);
    }

    public function syncMiscellaneousPaymentFromXero(Request $request)
    {
        if (! $request->id) {
            return $this->errorResponse('Id Missing', 'data', '', 200);
        }
        if (! Xero::isConnected()) {
            return $this->errorResponse('Xero not connected', 'data', '', 200);
        }
        // TODO::add event for Miscellaneous Payment
        $miscellaneousPayment = StudentMiscellaneousPayment::findOrFail($request->id);
        dispatch(new SyncInvoiceFromXero($miscellaneousPayment->xeroInvoice));

        return $this->successResponse('Miscellaneous Payment is being synced from Xero', 'data', []);
    }

    public function syncServicePayment(Request $request)
    {
        if (! $request->id) {
            return $this->errorResponse('Id Missing', 'data', '', 200);
        }
        if (! Xero::isConnected()) {
            return $this->errorResponse('Xero not connected', 'data', '', 200);
        }
        if (! XeroConfig::Mapped()) {
            return $this->errorResponse('Please finish mapping xero accuont from xero setup page..', 'data', '', 200);
        }
        dispatch(new SyncStudentPaymentServiceToXero($request->id));

        return $this->successResponse('Service Payment Synced', 'data', []);
    }

    public function syncServicePaymentFromXero(Request $request)
    {
        if (! $request->id) {
            return $this->errorResponse('Id Missing', 'data', '', 200);
        }
        if (! Xero::isConnected()) {
            return $this->errorResponse('Xero not connected', 'data', '', 200);
        }
        $servicePayment = StudentServicePayment::findOrFail($request->id);
        dispatch(new SyncInvoiceFromXero($servicePayment->xeroInvoice));

        return $this->successResponse('Service Payment is being synced from Xero', 'data', []);
    }

    public function syncAgentCommission(Request $request)
    {
        if (! $request->id) {
            return $this->errorResponse('Id Missing', 'data', '', 200);
        }

        if (! Xero::isConnected()) {
            return $this->errorResponse('Xero not connected', 'data', '', 200);
        }

        if (! XeroConfig::Mapped()) {
            return $this->errorResponse('Please finish mapping xero account from xero setup page..', 'data', '', 200);
        }

        $commissionData = StudentAgentCommission::find($request->id);
        if (! $commissionData) {
            return $this->errorResponse('Data not found', 'data', '', 200);
        }

        if ($commissionData->is_approved == 0) {
            return $this->errorResponse('Please approve first, then you can sync with Xero.', 'data', '', 200);
        }

        dispatch(new SyncStudentAgentCommissionToXero($request->id));

        return $this->successResponse('Agent Commission Synced', 'data', []);
    }

    public function syncAgentCommissionFromXero(Request $request)
    {
        if (! $request->id) {
            return $this->errorResponse('Id Missing', 'data', '', 200);
        }
        if (! Xero::isConnected()) {
            return $this->errorResponse('Xero not connected', 'data', '', 200);
        }
        $agentCommission = StudentAgentCommission::findOrFail($request->id);
        dispatch(new SyncInvoiceFromXero($agentCommission->xeroInvoice));

        return $this->successResponse('Agent commission is being synced from Xero', 'data', []);
    }

    public function createPOForAgentCommission(Request $request)
    {
        if (! Xero::isConnected()) {
            return $this->errorResponse('Xero not connected', 'data', '', 200);
        }

        switch ($request->type) {
            case 'single':
                return $this->singleCommissionCreateForAgent($request);

            case 'bulk':
                return $this->bulkCommissionCreateForAgent($request);

            default:
                return $this->errorResponse('Something will be wrong. Please try again.', 'data', [], 200);
        }
    }

    private function singleCommissionCreateForAgent($request)
    {
        $id = $request->id;
        if (! $id) {
            return $this->errorResponse('Id Missing', 'data', '', 200);
        }

        $studAgentCommData = StudentAgentCommission::where('id', $id)->with(['xeroInvoice'])->first();
        if ($studAgentCommData->xeroInvoice) {
            $purchaseOrderID = $studAgentCommData->xeroInvoice->xero_data['purchase_order']['PurchaseOrderID'] ?? null;
            if (! empty($purchaseOrderID)) {
                return $this->errorResponse('Already PO created', 'data', '', 200);
            }
        }

        dispatch(new SyncStudentAgentPurchaseOrderToXero($id));

        return $this->successResponse('Successfully generated PO for agent commission.', 'data', []);
    }

    private function bulkCommissionCreateForAgent($request)
    {
        if (! $request->ids) {
            return $this->errorResponse('Data not found', 'data', '', 200);
        }

        $primaryIds = explode(',', $request->ids);
        if (count($primaryIds) == 0) {
            return $this->errorResponse('Please select at least one row', 'data', '', 200);
        }

        /*foreach ($primaryIds as $id){
            dispatch(new SyncStudentAgentPurchaseOrderToXero($id));
        }
        return $this->successResponse('Successfully generated PO for agent commission.', 'data', []);*/

        $create = $alreadyCreated = 0;
        $jobs = [];

        foreach ($primaryIds as $id) {
            $studAgentCommData = StudentAgentCommission::where('id', $id)->with(['xeroInvoice'])->first();

            if ($studAgentCommData->xeroInvoice) {
                $purchaseOrderID = $studAgentCommData->xeroInvoice->xero_data['purchase_order']['PurchaseOrderID'] ?? null;
                if (! empty($purchaseOrderID)) {
                    $alreadyCreated++;
                } else {
                    $jobs[] = new SyncStudentAgentPurchaseOrderToXero($id);
                    $create++;
                }
            } elseif ($studAgentCommData->xeroInvoice == null || empty($studAgentCommData->xeroInvoice->xero_invoice_id)) {
                $jobs[] = new SyncStudentAgentPurchaseOrderToXero($id);
                $create++;
            }

            /*if ($studAgentCommData->xeroInvoice != NULL &&
                isset($studAgentCommData->xeroInvoice->xero_data) &&
                isset($studAgentCommData->xeroInvoice->xero_data['purchase_order']) &&
                isset($studAgentCommData->xeroInvoice->xero_data['purchase_order']['PurchaseOrderID']) &&
                !empty($studAgentCommData->xeroInvoice->xero_data['purchase_order']['PurchaseOrderID'])
            ) {
                $alreadyCreated++;
            } elseif ($studAgentCommData->xeroInvoice == NULL || empty($studAgentCommData->xeroInvoice->xero_invoice_id)) {
                $jobs[] = new SyncStudentAgentPurchaseOrderToXero($id);
                $create++;
            } else {
                $alreadyCreated++;
            }*/
        }

        if (count($jobs)) {
            Bus::chain($jobs)->dispatch();
        }
        if ($create > 0 && $alreadyCreated > 0) {
            return $this->successResponse("Total $create PO created successfully & $alreadyCreated already created.", 'data', []);
        } elseif ($create > 0) {
            return $this->successResponse('PO created successfully', 'data', []);
        } elseif ($alreadyCreated > 0) {
            return $this->errorResponse('Already PO created', 'data', '', 200);
        } else {
            return $this->errorResponse('Something will be wrong. Please try again.', 'data', '', 200);
        }

    }

    public function resyncPaymentTransaction(Request $request)
    {
        if (! $request->id) {
            return $this->errorResponse('Id Missing', 'data', '', 200);
        }
        if (! Xero::isConnected()) {
            return $this->errorResponse('Xero not connected', 'data', '', 200);
        }
        if ($request->isInvoiceCredit) {
            $xeroCreditNote = XeroCreditNote::find($request->id);
            dispatch(new SyncCreditNoteFromXero($xeroCreditNote));

            return $this->successResponse('Transaction synced successfully', 'data', []);
        } else {
            $xeroInvoice = XeroInvoice::find($request->id);
            dispatch(new SyncInvoiceFromXero($xeroInvoice));

            return $this->successResponse('Transaction synced successfully', 'data', []);
        }

    }

    public function resyncAllPaymentTransaction(Request $request)
    {
        $transactionIds = $request->id;
        foreach ($transactionIds as $value) {
            if ($value['isInvoiceCredit']) {
                $xeroCreditNote = XeroCreditNote::find($value['id']);
                dispatch(new SyncCreditNoteFromXero($xeroCreditNote));
            } else {
                $xeroInvoice = XeroInvoice::find($value['id']);
                dispatch(new SyncInvoiceFromXero($xeroInvoice));
            }
        }

        return $this->successResponse('Transaction synced successfully', 'data', []);
    }

    public function addServicePaymentData(AddPaymentAdditionalServiceInformationPaymentData $request)
    {
        return $this->studentPaymentService->addStudentServicesDetails($request);
    }

    public function saveRecordServicePayment(RecordServicePaymentRequest $request)
    {
        return $this->studentPaymentService->saveRecordServicePayment($request);
    }

    public function getStudentServicePaymentEdit(Request $request)
    {
        $data = $this->studentPaymentService->getStudentServicePaymentEdit($request);

        return $this->successResponse('Data Found', 'data', $data);
    }

    public function getStudentServicePaymentData(Request $request)
    {
        $data = $this->studentPaymentService->getStudentServicePaymentData($request);

        return $this->successResponse('Data Found', 'data', $data);
    }

    public function saveRefundPaymentAdditionalServiceInformation(RefundAdditionalServicePayment $request)
    {
        $result = $this->studentPaymentService->updateAdditionalServicePaymentRefund($request->input());

        return $this->successResponse('Refund Payment successfully', 'data', $result);
    }

    public function getMiscellaneousPaymentTransactionData(Request $request)
    {
        $result = $this->studentPaymentService->studentMiscellaneousPaymentData($request);

        return $this->successResponse('Data found successfully', 'data', $result);
    }

    public function getServicePaymentTransactionData(Request $request)
    {
        $result = $this->studentPaymentService->studentServicePaymentData($request);

        return $this->successResponse('Data found successfully', 'data', $result);
    }

    public function saveMiscellaneousPaymentReverseTransactionPaymentRequest(Request $request)
    {
        $validatedData = $request->validate([
            'reversed_comment' => 'required',
        ]);
        $data = $this->studentPaymentService->saveMiscellaneousPaymentReverseTransactionPaymentRequest($request);
        if ($data) {
            return $this->successResponse('Transaction Reversed Successfully', 'data', '');
        } else {
            return $this->errorResponse('Fail To Update', 'data', '');
        }
    }

    public function saveMiscellaneousPayment(RecordMiscellaneousPaymentRequest $request)
    {
        $data = $this->studentPaymentService->saveMiscellaneousPayment($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse('Payment Recorded Successfully', 'data', '');
        } else {
            return $this->errorResponse($data['message'], 'data', '', 200);
        }
    }

    public function saveRevertBackTransactionMiscellaneousPayment(Request $request)
    {
        $data = $this->studentPaymentService->saveMiscellaneousPaymentReverseTransactionPaymentRequest($request);
        if ($data) {
            return $this->successResponse('Revert Successfully', 'data', '');
        } else {
            return $this->errorResponse('Fail To Revert', 'data', '');
        }
    }

    public function generateStudentTaxReceiptPdf(Request $request)
    {
        return $this->studentPaymentService->generateStudentTaxReceiptPdf($request);
    }

    public function generateAgentTaxReceiptPdf(Request $request)
    {
        return $this->studentPaymentService->generateAgentTaxReceiptPdf($request);
    }

    public function generateCombinedCourseInvoicePdf(Request $request)
    {
        return $this->studentPaymentService->generateCombinedCourseInvoicePdf($request);
    }

    public function generateReceiptAdditionalPaymentServicePdf(Request $request)
    {
        return $this->studentPaymentService->generateReceiptAdditionalPaymentServicePdf($request);
    }

    public function generateInvoicesAdditionalPaymentServicePdf(Request $request)
    {
        return $this->studentPaymentService->generateInvoicesAdditionalPaymentServicePdf($request);
    }

    public function generateRefundReceiptAdditionalPaymentServicePdf(Request $request)
    {
        return $this->studentPaymentService->generateRefundReceiptAdditionalPaymentServicePdf($request);
    }

    public function saveReverseTransactionAdditionalPaymentService(Request $request)
    {
        $validatedData = $request->validate([
            'reversed_comment' => 'required',
        ]);
        $data = $this->studentPaymentService->saveReverseTransactionAdditionalPaymentService($request);
        if ($data) {
            return $this->successResponse('Reverse Successfully', 'data', '');
        } else {
            return $this->errorResponse('Fail To Update', 'data', '');
        }
    }

    public function revertBackTransactionAdditionalPaymentService(Request $request)
    {
        $data = $this->studentPaymentService->saveRevertBackTransactionAdditionalPaymentService($request->input());
        if ($data) {
            return $this->successResponse('Revert Back Successfully', 'data', '');
        } else {
            return $this->errorResponse('Fail To Update', 'data', '');
        }
    }

    public function addMiscellaneousRequestDetails(MiscellaneousPaymentRequest $request)
    {
        $data = $this->studentPaymentService->addMiscellaneousRequestDetails($request->DTO());

        return $this->successResponse('Miscellaneous request add successfully', 'data', $data);
    }

    public function editMiscellaneousRequestDetails(MiscellaneousPaymentRequest $request)
    {
        $data = $this->studentPaymentService->editMiscellaneousRequestData($request->DTO());
        if ($data) {
            return $this->successResponse('Miscellaneous details updated', 'data', '');
        } else {
            return $this->errorResponse('Fail To Update', 'data', '');
        }
    }

    public function deleteTransactionAdditionalPaymentService(Request $request)
    {
        $data = $this->studentPaymentService->deleteTransactionAdditionalPaymentServiceData($request);

        return $this->successResponse('Additional Payment Service Delete Successfully', 'data', $data);
    }

    public function generateMiscellaneousPaymentRefundPdf(Request $request)
    {
        return $this->studentPaymentService->generateMiscellaneousPaymentRefundPdf($request);
    }

    public function saveDeleteMiscellaneousTransaction(Request $request)
    {
        $validatedData = $request->validate([
            'reason' => 'required',
        ]);
        $this->studentPaymentService->saveDeleteMiscellaneousTransactionData($request);

        return $this->successResponse('Miscellaneous Transaction Delete Successfully', 'data', '');
    }

    public function generateAgentProInvoicePdf(Request $request)
    {
        return $this->studentPaymentService->generateAgentProInvoicePdf($request);
    }

    public function generateStudentScheduleInvoicePdf(Request $request)
    {
        return $this->studentPaymentService->generateStudentScheduleInvoicePdf($request);
    }

    public function deleteInvoiceCreditData(Request $request)
    {
        return $this->studentPaymentService->deleteInvoiceCreditData($request);
    }

    public function sendMiscellaneousTransactionEmail(SendEmailRequest $request)
    {
        $this->studentPaymentService->sendMiscellaneousTransactionEmail($request);

        return $this->successResponse('Mail Sent Successfully', 'data', '');
    }

    public function getStudentAgentList(Request $request)
    {
        $data = $this->studentPaymentService->getStudentAgentList($request);

        return $this->successResponse('Miscellaneous Transaction Delete Successfully', 'data', $data);
    }

    public function addAgentCommissionInfoDetails(SaveTransferPaymentData $request)
    {
        $data = $this->studentPaymentService->addAgentCommissionInfoDetailsData($request);
        if ($data['status'] == 'error') {
            return $this->errorResponse($data['message'], 'data', '', 200);
        } else {
            return $this->successResponse($data['message'], 'data', '', 200);
        }
    }

    public function getPaymentTransferList(Request $request)
    {
        $data = $this->studentPaymentService->getPaymentTransferData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function generateNewSchedule(SaveNewPaymentSchedule $request)
    {
        // $data = $this->studentPaymentService->generateNewSchedule($request->DTO());
        $data = $this->studentPaymentService->generateNewSchedule($request);
        if ($data['status'] == 'error') {
            return $this->errorResponse($data['message'], 'data', '', 200);
        } else {
            $studentCourse = StudentCourses::findOrFail($request->student_course_id);
            event(new XeroCreateStudentInvoiceEvent($studentCourse->student_id, $studentCourse->course_id));

            return $this->successResponse($data['message'], 'data', '', 200);
        }
    }

    public function getModifyPaymentSchedule(Request $request)
    {
        $data = $this->studentPaymentService->getPaymentDetailsData($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function editStudentPaymentSchedule(editStudentPaymentSchedule $request)
    {
        $data = $this->studentPaymentService->saveEditStudentSchedule($request->input());
        if ($data['status'] == 'error') {
            return $this->errorResponse($data['message'], 'data', '', 200);
        } else {
            return $this->successResponse($data['message'], 'data', '', 200);
        }
    }

    public function getSelectedCourseInvoice(Request $request)
    {
        $data = $this->studentPaymentService->getSelectedCourseInvoiceData($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getSelectedTransactionDetails(Request $request)
    {
        $data = $this->studentPaymentService->getSelectedTransactionDetailsData($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getRecommendedCommissionRate(Request $request)
    {
        $data = $this->studentPaymentService->getRecommendedCommissionRateData($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function syncStudentScholarshipPayment(Request $request)
    {
        if (! $request->id) {
            return $this->errorResponse('Id Missing', 'data', '', 200);
        }
        if (! Xero::isConnected()) {
            return $this->errorResponse('Xero not connected', 'data', '', 200);
        }
        // TODO::GNG-2730 (Add event for scholarship payment)
        // event(new ScholarshipPaymentReadyToSyncToXero($request->id));
        dispatch(new SyncStudentScholarshipToXero($request->id));

        return $this->successResponse('Student Scholarship Synced', 'data', []);
    }

    public function syncStudentScholarshipPaymentFromXero(Request $request)
    {
        if (! $request->id) {
            return $this->errorResponse('Id Missing', 'data', '', 200);
        }
        if (! Xero::isConnected()) {
            return $this->errorResponse('Xero not connected', 'data', '', 200);
        }
        // TODO::add event for Scholarship Payment
        $scholarship = StudentScholarshipV2::findOrFail($request->id);
        dispatch(new SyncCreditNoteFromXero($scholarship->xeroCreditNote));

        return $this->successResponse('Student Scholarship is being synced from Xero', 'data', []);
    }

    // TODO::GNG-2777
    public function bulkDeletePaymentItems(Request $request)
    {
        if (count($request->ids) == 0) {
            return $this->errorResponse('Please select at least one row', 'data', [], 200);
        }
        switch ($request->action) {
            case 'schedule':
            case 'prepayment':
                $data = $this->studentPaymentService->deleteBulkPaymentSchedule($request);
                break;

            case 'agent_commission':
            case 'agent_bonus':
                $data = [];
                break;

            case 'miscellaneous':
                $data = $this->studentPaymentService->deleteBulkMiscellaneousPayment($request);
                break;

            case 'service':
                $data = $this->studentPaymentService->deleteBulkServicePayment($request);
                break;

            case 'scholarship':
                $data = $this->studentPaymentService->deleteBulkScholarshipPayment($request);
                break;

            default:
                return $this->errorResponse('Something will be wrong. Please try again.', 'data', [], 200);
        }

        return $this->successResponse('Selected Items Delete Successfully', 'data', $data);
    }

    public function getGeneratePaymentScheduleFormData(Request $request)
    {
        $data = $this->studentPaymentService->getGeneratePaymentScheduleFormData($request);

        return $this->successResponse('Valid', 'data', $data);
    }

    public function getUpfrontFeeFormData(Request $request)
    {
        $data = $this->studentPaymentService->getUpfrontFeeFormData($request);

        return $this->successResponse('Valid', 'data', $data);
    }

    public function syncBulkAgentCommissionToXero($request)
    {
        $studAgentCommIds = $request->primaryIdSet;
        $pending = $sync = $alreadySync = 0;
        $jobs = [];
        $syncingIds = [];
        foreach ($studAgentCommIds as $id) {
            $paymentData = StudentAgentCommission::where('id', $id)->with(['xeroInvoice'])->first();
            if ($paymentData->is_approved == 0) {
                $pending++;
            } elseif ($paymentData->xeroInvoice == null || empty($paymentData->xeroInvoice->xero_invoice_id)) {
                $jobs[] = new SyncStudentAgentCommissionToXero($id);
                $syncingIds[] = $id;
                $sync++;
            } else {
                $alreadySync++;
            }
        }

        if (count($jobs)) {
            Bus::chain($jobs)->dispatch();
        }

        if ($pending > 0) {
            return $this->errorResponse("Please approve first, then you can sync with Xero. Total $pending items failed to sync", 'data', '', 200);
        } elseif ($sync > 0) {
            return $this->successResponse('Selected data synced successfully', 'data', $syncingIds);
        } elseif ($alreadySync > 0) {
            return $this->successResponse('Selected data already synced', 'data', []);
        } else {
            return $this->errorResponse('Something will be wrong. Please try again.', 'data', '', 200);
        }
    }

    public function syncBulkMiscellaneousFromXero($request)
    {
        $studPaymentDetailIds = $request->primaryIdSet;
        $sync = $alreadySync = 0;
        $jobs = [];
        $syncingIds = [];
        foreach ($studPaymentDetailIds as $id) {
            $paymentData = StudentMiscellaneousPayment::where('id', $id)->with(['xeroInvoice'])->first();
            // $paymentData = StudentInitialPaymentDetails::findOrFail($id);
            // dd($paymentData->isConnectedToXero());
            if ($paymentData->xeroInvoice == null || empty($paymentData->xeroInvoice->xero_invoice_id)) {
                $jobs[] = new SyncMiscellaneousPaymentToXero($id);
                $syncingIds[] = $id;
                $sync++;
            } else {
                $alreadySync++;
            }
        }

        if (count($jobs)) {
            Bus::chain($jobs)->dispatch();
        }

        if ($sync > 0) {
            return $this->successResponse('Selected data synced successfully', 'data', $syncingIds);
        } elseif ($alreadySync > 0) {
            return $this->successResponse('Selected data already synced', 'data', []);
        } else {
            return $this->errorResponse('Something will be wrong. Please try again.', 'data', '', 200);
        }
    }

    public function syncBulkServiceFromXero($request)
    {
        $studPaymentDetailIds = $request->primaryIdSet;
        $sync = $alreadySync = 0;
        $jobs = [];
        $syncingIds = [];
        foreach ($studPaymentDetailIds as $id) {
            $paymentData = StudentServicePayment::where('id', $id)->with(['xeroInvoice'])->first();
            if ($paymentData->xeroInvoice == null || empty($paymentData->xeroInvoice->xero_invoice_id)) {
                $jobs[] = new SyncStudentPaymentServiceToXero($id);
                $syncingIds[] = $id;
                $sync++;
            } else {
                $alreadySync++;
            }
        }

        if (count($jobs)) {
            Bus::chain($jobs)->dispatch();
        }

        if ($sync > 0) {
            return $this->successResponse('Selected data synced successfully', 'data', $syncingIds);
        } elseif ($alreadySync > 0) {
            return $this->successResponse('Selected data already synced', 'data', []);
        } else {
            return $this->errorResponse('Something will be wrong. Please try again.', 'data', '', 200);
        }
    }

    public function syncBulkScholarshipFromXero($request)
    {
        $studScholarshipIds = $request->primaryIdSet;

        if (count($studScholarshipIds) == 0) {
            return $this->errorResponse('Please select at least one row', 'data', '', 200);
        }

        if (! Xero::isConnected()) {
            return $this->errorResponse('Xero not connected', 'data', '', 200);
        }

        $sync = $alreadySync = $transffered = 0;
        $jobs = [];
        $syncingIds = [];
        foreach ($studScholarshipIds as $id) {
            $paymentData = StudentScholarshipV2::where('id', $id)->with(['xeroCreditNote'])->first();
            if ($paymentData->is_transfer == 1) {
                $transffered++;
            } elseif ($paymentData->xeroInvoice == null || empty($paymentData->xeroInvoice->xero_invoice_id)) {
                $jobs[] = new SyncStudentScholarshipToXero($id);
                $syncingIds[] = $id;
                $sync++;
            } else {
                $alreadySync++;
            }
        }

        if (count($jobs)) {
            Bus::chain($jobs)->dispatch();
        }

        if ($sync > 0) {
            return $this->successResponse('Selected data synced successfully', 'data', $syncingIds);
        } elseif ($alreadySync > 0) {
            return $this->successResponse('Selected data already synced', 'data', []);
        } elseif ($transffered > 0) {
            return $this->errorResponse('Transferred scholarship is unable to sync.', 'data', []);
        } else {
            return $this->errorResponse('Something will be wrong. Please try again.', 'data', '', 200);
        }
    }

    public function getOutStandingAmountFromXero(Request $request)
    {
        if (! $request->student_id) {
            return $this->errorResponse('Student ID not found', 'data', '', 200);
        }

        if (! Xero::isConnected()) {
            return $this->errorResponse('Xero not connected', 'data', '', 200);
        }

        try {
            $student = Student::findOrFail($request->student_id);
            if ($student->isXeroContactCreatated()) {
                $xeroContact = $student->xeroContact;
                dispatch_sync(new \Domains\Xero\Jobs\SyncContactPrepaymentFromXero($xeroContact));
                $outstandingBalance = $xeroContact->fresh()->outstanding_balance ? $xeroContact->fresh()->outstanding_balance : 0;
            } else {
                $student->fresh()->asXeroContact();
                $outstandingBalance = 0;
            }

            $data['outstanding_balance'] = abs($outstandingBalance);

            return $this->successResponse('Outstanding balance updated from xero', 'data', $data);
        } catch (\Exception $e) {
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getUnallocatedCreditAmountFromXero(Request $request)
    {
        if (! $request->student_id) {
            return $this->errorResponse('Student ID not found', 'data', '', 200);
        }

        if (! Xero::isConnected()) {
            return $this->errorResponse('Xero not connected', 'data', '', 200);
        }

        try {
            $student = Student::findOrFail($request->student_id);
            if ($student->isXeroContactCreatated()) {
                $xeroContact = $student->xeroContact;
                dispatch_sync(new \Domains\Xero\Jobs\SyncContactFromXero($xeroContact));
                $unallocatedCreditAmount = $xeroContact->fresh()->unallocated_credit ?? 0;
            } else {
                $student->fresh()->asXeroContact();
                $unallocatedCreditAmount = 0;
            }

            $data['unallocatedCreditAmount'] = abs($unallocatedCreditAmount);

            return $this->successResponse('Unallocated credit amount updated from xero', 'data', $data);
        } catch (\Exception $e) {
            throw new ApplicationException($e->getMessage());
        }
    }

    public function saveTransferScholarshipData(Request $request, TransferScholarshipProcess $process)
    {
        // return $this->successResponse('In progress', 'data', []);

        $this->validate($request, [
            'new_student_course_id' => 'required',
        ], [
            'new_student_course_id.required' => 'Please select course.',
        ]);

        // DB::beginTransaction();
        try {
            $res = $process->run($request->input());

            return $this->successResponse('Scholarship Transfer successfully.', 'data', $res, 200);
        } catch (\Exception $e) {
            // DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }
}
