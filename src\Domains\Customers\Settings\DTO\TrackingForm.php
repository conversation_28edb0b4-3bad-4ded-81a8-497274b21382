<?php

namespace Domains\Customers\Settings\DTO;

use Domains\Customers\Settings\Models\SettingTracker;
use Livewire\Wireable;
use Support\Traits\ArrayToProps;

class TrackingForm implements Wireable
{
    use ArrayToProps;

    public $form;

    public $group;

    public int $status;

    public ?array $data;

    public $icon;

    public $route;

    public $label;

    public $required;

    public $toggle = true;

    public $permission;

    public function isCompleted()
    {
        return $this->status == SettingTracker::STATUS_COMPLETE;
    }

    public function key()
    {
        return $this->group.'.'.$this->form;
    }

    public function isActive($formKey)
    {
        [$group, $form] = explode('.', $formKey);
        // print_r([$group, $this->group, $form, $this->form]);
        // die();

        return $this->group == $group && $this->form == $form;
    }

    public function toLivewire()
    {
        return $this->toArray();
    }

    public static function fromLivewire($value)
    {
        return self::LazyFromArray($value);
    }
}
