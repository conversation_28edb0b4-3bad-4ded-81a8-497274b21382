<script setup>
import SyncSelect from '@spa/components/AsyncComponents/Select/SyncSelect.vue';
import AsyncSelect from '@spa/components/AsyncComponents/Select/AsyncSelect.vue';
import CreateForm from '@spa/modules/country/CountryForm.vue';
import { useCountryStore } from '@spa/stores/modules/country/useCountryStore.js';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import { computed } from 'vue';
import { count } from '@progress/kendo-data-query/dist/npm/array.operators';

const props = defineProps({
    modelValue: [String, Number, Array, Object],
    label: String,
    className: String,
    optionValue: {
        type: String,
        default: 'id',
    },
    optionLabel: {
        type: String,
        default: 'name',
    },
    disabled: Boolean,
    clearable: {
        type: Boolean,
        default: true,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    async: {
        type: Boolean,
        default: true,
    },
    readonly: Boolean,
    useChips: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: 'Select Country',
    },
    hasCreateAction: {
        type: Boolean,
        default: false,
    },
    valid: {
        type: Boolean,
        default: true,
    },
    validationMessage: {
        type: String,
        default: '',
    },
    indicaterequired: {
        type: Boolean,
        default: false,
    },
    required: {
        type: Boolean,
        default: false,
    },
    initFormData: {
        type: Object,
        default: () => ({}),
    },
    filters: {
        type: Object,
        default: () => ({}),
    },
    forceReload: {
        type: Boolean,
        default: false,
    },
    style: {
        type: Object,
        default: () => ({}),
    },
});

const emit = defineEmits(['update:modelValue', 'created']);
const vModel = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});
const store = props.async ? useCountryStore() : null;
const countries = !props.async ? window['kendo_countries_codes'] : null;
</script>

<template>
    <AsyncSelect
        :label="label"
        :className="className"
        :optionValue="optionValue"
        :optionLabel="optionLabel"
        :disabled="disabled"
        :store="store"
        v-model="vModel"
        :clearable="clearable"
        :multiple="multiple"
        :readonly="readonly"
        :useChips="useChips"
        :placeholder="placeholder"
        :hasCreateAction="hasCreateAction"
        :valid="valid"
        :validationMessage="validationMessage"
        :indicaterequired="indicaterequired"
        :required="required"
        :initFormData="initFormData"
        :filters="filters"
        :forceReload="forceReload"
        :style="style"
        v-if="async"
    >
        <template #createDialog>
            <CreateForm :position="'center'" @success="(res) => emit('created', res)" />
        </template>
    </AsyncSelect>
    <SyncSelect
        :label="label"
        :className="className"
        :optionValue="optionValue"
        :optionLabel="optionLabel"
        :disabled="disabled"
        :store="countries"
        v-model="vModel"
        :clearable="clearable"
        :multiple="multiple"
        :readonly="readonly"
        :useChips="useChips"
        :placeholder="placeholder"
        :hasCreateAction="hasCreateAction"
        :valid="valid"
        :validationMessage="validationMessage"
        :indicaterequired="indicaterequired"
        :required="required"
        :initFormData="initFormData"
        :filters="filters"
        :forceReload="forceReload"
        :style="style"
        v-else
    >
        <template #createDialog>
            <CreateForm :position="'center'" @success="(res) => emit('created', res)" />
        </template>
    </SyncSelect>
</template>

<style scoped></style>
