<?php

namespace App\Model;

use App\Model\v2\Users;
use App\Traits\ResponseTrait;
use Auth;
use Config;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Agent extends Model
{
    use LogsActivity;
    use ResponseTrait;

    protected $table = 'rto_agents';

    // Log safe, non-sensitive attributes for Agent
    protected $logAttributes = [
        'college_id', 'agency_name', 'contact_person', 'primary_email', 'alertnet_email', 'website',
        'telephone', 'fax', 'mobile1', 'mobile2', 'industry_id', 'account_manager_id', 'super_agent_id',
        'total_employess', 'notes', 'office_address', 'office_country', 'office_city', 'office_state', 'office_postcode', 'office_ABN',
        'postal_address', 'postal_country', 'postal_city', 'postal_state', 'postal_postcode', 'postal_ABN',
        'target_primary_country', 'target_secondry_country', 'targeted_country', 'status', 'is_lock',
        'bank_name', 'bank_branch', 'bank_account_name', 'bank_account_number', 'BSB', 'bank_swift_code', 'bank_country',
        'user_id', 'isCompleteStepNo',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly($this->logAttributes)
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => "Agent has been {$eventName}");
    }

    public function tapActivity(Activity $activity, string $eventName)
    {
        $activity->log_name = (new self)->getMorphClass();
        $identifier = $this->agency_name ?? (string) $this->id;
        $activity->description = "Agent {$identifier} {$eventName}";
    }

    public function saveAgentRecords($collegeId, $request)
    {
        $objRtoAgent = new Agent;
        $userId = Auth::user()->id;
        $objRtoAgent->college_id = $collegeId;
        $objRtoAgent->agency_name = ($request->input('agency_name') != '') ? $request->input('agency_name') : null;
        $objRtoAgent->contact_person = ($request->input('contact_person') != '') ? $request->input('contact_person') : null;
        $objRtoAgent->primary_email = ($request->input('primary_email') != '') ? $request->input('primary_email') : null;
        $objRtoAgent->alertnet_email = ($request->input('alertnet_email') != '') ? $request->input('alertnet_email') : null;
        $objRtoAgent->website = ($request->input('website') != '') ? $request->input('website') : null;
        $objRtoAgent->telephone = ($request->input('telephone') != '') ? $request->input('telephone') : null;
        $objRtoAgent->fax = ($request->input('fax') != '') ? $request->input('fax') : null;
        $objRtoAgent->mobile1 = ($request->input('mobile1') != '') ? $request->input('mobile1') : null;
        $objRtoAgent->mobile2 = ($request->input('mobile2') != '') ? $request->input('mobile2') : null;
        $objRtoAgent->account_manager_id = ($request->input('account_manager_id') != '') ? $request->input('account_manager_id') : 0;
        $objRtoAgent->super_agent_id = ($request->input('super_agent_id') != '') ? $request->input('super_agent_id') : 0;
        $objRtoAgent->total_employess = ($request->input('total_employess') != '') ? $request->input('total_employess') : null;
        $objRtoAgent->notes = ($request->input('notes') != '') ? $request->input('notes') : null;
        $objRtoAgent->postal_country = ($request->input('postal_country') != '') ? $request->input('postal_country') : 0;
        $objRtoAgent->postal_address = ($request->input('postal_address') != '') ? $request->input('postal_address') : null;
        $objRtoAgent->postal_city = ($request->input('postal_city') != '') ? $request->input('postal_city') : null;
        $objRtoAgent->postal_state = ($request->input('postal_state') != '') ? $request->input('EPL_istening_score') : null;
        $objRtoAgent->postal_postcode = ($request->input('postal_postcode') != '') ? $request->input('postal_state') : null;
        $objRtoAgent->postal_ABN = ($request->input('postal_ABN') != '') ? $request->input('postal_ABN') : null;
        $objRtoAgent->office_country = ($request->input('office_country') != '') ? $request->input('office_country') : 0;
        $objRtoAgent->office_address = ($request->input('office_address') != '') ? $request->input('office_address') : null;
        $objRtoAgent->office_city = ($request->input('office_city') != '') ? $request->input('office_city') : null;
        $objRtoAgent->office_state = ($request->input('office_state') != '') ? $request->input('office_state') : null;
        $objRtoAgent->office_postcode = ($request->input('office_postcode') != '') ? $request->input('office_postcode') : null;
        $objRtoAgent->office_ABN = ($request->input('office_ABN') != '') ? $request->input('office_ABN') : null;
        $objRtoAgent->industry_id = ($request->input('industry_id') != '') ? $request->input('industry_id') : 0;
        $objRtoAgent->target_primary_country = ($request->input('target_primary_country') != '') ? $request->input('target_primary_country') : 0;
        $objRtoAgent->target_secondry_country = ($request->input('target_secondry_country') != '') ? $request->input('target_secondry_country') : 0;
        $objRtoAgent->status = ($request->input('status') != '') ? $request->input('status') : null;

        $objRtoAgent->bank_country = ($request->input('bank_country') != '') ? $request->input('bank_country') : null;

        $objRtoAgent->bank_name = ($request->input('bank_name') != '') ? $request->input('bank_name') : null;
        $objRtoAgent->bank_account_name = ($request->input('bank_account_name') != '') ? $request->input('bank_account_name') : null;
        $objRtoAgent->bank_branch = ($request->input('bank_branch') != '') ? $request->input('bank_branch') : null;
        $objRtoAgent->bank_account_number = ($request->input('bank_account_number') != '') ? $request->input('bank_account_number') : null;
        $objRtoAgent->BSB = ($request->input('BSB') != '') ? $request->input('BSB') : null;
        $objRtoAgent->bank_swift_code = ($request->input('bank_swift_code') != '') ? $request->input('bank_swift_code') : null;

        $objRtoAgent->created_by = $userId;
        $objRtoAgent->updated_by = $userId;

        if ($objRtoAgent->save()) {
            $lastInsertedId = $objRtoAgent->id;

            $rootFolder = Config::get('constants.arrCollegeRootFolder');

            $dataArr = ['college_id' => $collegeId, 'folder_name' => $rootFolder['AgentFiles'], 'user_id' => $userId];

            $objCommonModel = new CommonModel;
            $clgMaterialParentId = $objCommonModel->getMainParentId($dataArr);

            $agentDataArr = [
                'college_id' => $collegeId,
                'parent_id' => $clgMaterialParentId,
                'file_name' => $lastInsertedId,
                'size' => null,
                'type' => 'Dir',
                'original_name' => null,
                'file_path' => null,
                'user_id' => $userId,
            ];
            $objCommonModel->addCollegeMaterialInfo($agentDataArr);

            return $lastInsertedId;
        }

        return false;
    }

    public function editAgentRecords($id, $collegeId, $request)
    {

        $objRtoAgent = \App\Model\v2\Agent::find($id);
        $userId = Auth::user()->id;
        $objRtoAgent->college_id = $collegeId;

        $objRtoAgent->agency_name = ($request->input('agency_name') != '') ? $request->input('agency_name') : null;
        $objRtoAgent->contact_person = ($request->input('contact_person') != '') ? $request->input('contact_person') : null;
        if (! empty($request->input('primary_email'))) {
            $objRtoAgent->primary_email = ($request->input('primary_email') != '') ? $request->input('primary_email') : null;
        }
        $objRtoAgent->alertnet_email = ($request->input('alertnet_email') != '') ? $request->input('alertnet_email') : null;
        $objRtoAgent->website = ($request->input('website') != '') ? $request->input('website') : null;
        $objRtoAgent->telephone = ($request->input('telephone') != '') ? $request->input('telephone') : null;
        $objRtoAgent->fax = ($request->input('fax') != '') ? $request->input('fax') : null;
        $objRtoAgent->mobile1 = ($request->input('mobile1') != '') ? $request->input('mobile1') : null;
        $objRtoAgent->mobile2 = ($request->input('mobile2') != '') ? $request->input('mobile2') : null;
        $objRtoAgent->account_manager_id = ($request->input('account_manager_id') != '') ? $request->input('account_manager_id') : 0;
        $objRtoAgent->super_agent_id = ($request->input('super_agent_id') != '') ? $request->input('super_agent_id') : 0;
        $objRtoAgent->total_employess = ($request->input('total_employess') != '') ? $request->input('total_employess') : null;
        $objRtoAgent->notes = ($request->input('notes') != '') ? $request->input('notes') : null;
        $objRtoAgent->postal_country = ($request->input('postal_country') != '') ? $request->input('postal_country') : 0;
        $objRtoAgent->postal_address = ($request->input('postal_address') != '') ? $request->input('postal_address') : null;
        $objRtoAgent->postal_city = ($request->input('postal_city') != '') ? $request->input('postal_city') : null;
        $objRtoAgent->postal_state = ($request->input('postal_state') != '') ? $request->input('postal_state') : null;
        $objRtoAgent->postal_postcode = ($request->input('postal_postcode') != '') ? $request->input('postal_postcode') : null;
        $objRtoAgent->postal_ABN = ($request->input('postal_ABN') != '') ? $request->input('postal_ABN') : null;
        $objRtoAgent->office_country = ($request->input('office_country') != '') ? $request->input('office_country') : 0;
        $objRtoAgent->office_address = ($request->input('office_address') != '') ? $request->input('office_address') : null;
        $objRtoAgent->office_city = ($request->input('office_city') != '') ? $request->input('office_city') : null;
        $objRtoAgent->office_state = ($request->input('office_state') != '') ? $request->input('office_state') : null;
        $objRtoAgent->office_postcode = ($request->input('office_postcode') != '') ? $request->input('office_postcode') : null;
        $objRtoAgent->office_ABN = ($request->input('office_ABN') != '') ? $request->input('office_ABN') : null;
        $objRtoAgent->industry_id = ($request->input('industry_id') != '') ? $request->input('industry_id') : 0;
        $objRtoAgent->target_primary_country = ($request->input('target_primary_country') != '') ? $request->input('target_primary_country') : 0;
        $objRtoAgent->target_secondry_country = ($request->input('target_secondry_country') != '') ? $request->input('target_secondry_country') : 0;
        //  $objRtoAgent->status = ($request->input('status') != '') ? $request->input('status') : null;
        $objRtoAgent->bank_country = ($request->input('bank_country') != '') ? $request->input('bank_country') : null;

        $objRtoAgent->bank_name = ($request->input('bank_name') != '') ? $request->input('bank_name') : null;
        $objRtoAgent->bank_account_name = ($request->input('bank_account_name') != '') ? $request->input('bank_account_name') : null;
        $objRtoAgent->bank_branch = ($request->input('bank_branch') != '') ? $request->input('bank_branch') : null;
        $objRtoAgent->bank_account_number = ($request->input('bank_account_number') != '') ? $request->input('bank_account_number') : null;
        $objRtoAgent->BSB = ($request->input('BSB') != '') ? $request->input('BSB') : null;
        $objRtoAgent->bank_swift_code = ($request->input('bank_swift_code') != '') ? $request->input('bank_swift_code') : null;

        $objRtoAgent->updated_by = $userId;
        $objRtoAgent->save();
    }

    public function editAgentUserId($id, $arrUsers)
    {
        $objRtoAgent = Agent::find($id);
        $userId = Auth::user()->id;
        $objRtoAgent->user_id = $arrUsers;
        $objRtoAgent->updated_by = $userId;
        $objRtoAgent->save();
    }

    public function viewAgentRecords($request, $perPage)
    {
        $college_id = Auth::user()->college_id;
        if ($request->isMethod('post')) {
            $fieldName = $request['searchField'];
            $fieldValue = $request['searchValue'];
            if ($fieldName != 'allAgent') {
                $users = Agent::where([[$fieldName, '=', $fieldValue]])
                    ->where([['college_id', $college_id]])
                    ->where('is_tenant_default', 0)
                    ->paginate($perPage);
            } else {
                $users = Agent::where([['college_id', $college_id]])
                    ->where('is_tenant_default', 0)
                    ->orderBy('id', 'DESC')
                    ->paginate($perPage);
            }
        } else {
            $users = Agent::where([['college_id', $college_id]])
                ->where('is_tenant_default', 0)
                ->orderBy('id', 'DESC')
                ->paginate($perPage);
        }

        return $users;
    }

    public function viewAgentRecordsEmail($perPage)
    {
        return Agent::from('rto_agents as ra')
            ->leftjoin('rto_agent_status as ras', 'ras.id', '=', 'ra.status')
            ->where('ra.college_id', Auth::user()->college_id)
            ->where('ra.is_tenant_default', 0)
            ->select('ra.*', 'ras.status_type')
            ->orderBy('ra.id', 'DESC')
            ->paginate($perPage);
    }

    public function getAgentList($collegeId)
    {
        $arrAgent = Agent::select('id', 'agency_name')
            ->where('college_id', $collegeId)
            ->where('is_tenant_default', 0)
            ->orderBy('agency_name', 'ASC')
            ->pluck('agency_name', 'id')
            ->toArray();

        $agentRecord[''] = '- - Select Agent - -';

        return $returnAgentRecord = $agentRecord + $arrAgent;
    }

    public function getAgentNameLogin($userId)
    {
        return $arrAgent = Agent::where('user_id', $userId)
            ->get(['agency_name', 'id'])->toArray();
    }

    public function getAgentUserId($agentId)
    {
        return $arrAgent = Agent::where('id', $agentId)
            ->select(['user_id'])->get();
    }

    public function getAgentDetail($agentId)
    {
        return Agent::findOrFail($agentId);
    }

    public function getAgentData($collegeId)
    {
        $arrAgentData = Agent::where(function ($query) use ($collegeId) {
            $query->where('college_id', '=', $collegeId)->orWhere('college_id', '=', 0);
        })
            ->where('is_tenant_default', 0)
            ->orderBy('agency_name', 'ASC')
            ->pluck('agency_name', 'id')
            ->toArray();

        if (count($arrAgentData) > 0) {
            $result = $arrAgentData;
        } else {
            $result[''] = 'No Agent Record Found';
        }

        return $result;
    }

    public function getStaffFilterList($collegeId)
    {
        $arrList = [];
        $arrStaffList = Agent::join('rto_staff_and_teacher', 'rto_staff_and_teacher.id', '=', 'rto_agents.account_manager_id')
            ->where('rto_agents.college_id', '=', $collegeId)
            ->where('rto_agents.is_tenant_default', 0)
            ->groupBy('rto_agents.account_manager_id')
            ->get(['rto_staff_and_teacher.first_name', 'rto_staff_and_teacher.last_name', 'rto_agents.account_manager_id']);

        for ($i = 0; $i < count($arrStaffList); $i++) {
            $arrList[$arrStaffList[$i]['account_manager_id']] = $arrStaffList[$i]['first_name'].' '.$arrStaffList[$i]['last_name'];
        }
        $arrListSelect['0'] = '--Select Account Manager--';
        $arrAccount = $arrListSelect + $arrList;

        return $arrAccount;
    }

    public function filterAgentEmailData($arrAccountManager, $countryRecord, $agentStatus)
    {

        // echo $arrAccountManager.'========='.$countryRecord.'-------'.$agentStatus;exit;
        $sql = Agent::from('rto_agents as ra')
            ->leftjoin('rto_agent_status as ras', 'ras.id', '=', 'ra.status')
            ->where('ra.college_id', '=', Auth::user()->college_id)
            ->where('ra.is_tenant_default', 0)
            ->orderBy('ra.id', 'DESC');

        if (! empty($arrAccountManager)) {
            $sql->where('ra.account_manager_id', '=', $arrAccountManager);
        }

        if (! empty($countryRecord) && $countryRecord > 0) {
            $sql->where('ra.target_primary_country', '=', $countryRecord);
        }

        if (! empty($agentStatus)) {
            $sql->where('ra.status', '=', $agentStatus);
        }
        $result = $sql->get(['ra.*', 'ras.status_type']);

        return $result;
    }

    public function viewAgentPaymentList($college_id, $perPage)
    {
        return Agent::where('college_id', '=', $college_id)
            ->where('is_tenant_default', 0)
            ->orderBy('id', 'DESC')
            ->paginate($perPage);
    }

    public function getAgentPaymentList($collegeId, $request, $arrAgentStatus)
    {

        $requestData = $_REQUEST;
        $columns = [
            // datatable column index  => database column name
            0 => 'ra.id',
            1 => 'ra.agency_name',
            2 => 'ra.contact_person',
            3 => 'ra.primary_email',
            // 4 => 'rto_users.username',
            4 => 'ra.status',
            5 => 'ra.agent_code',
            6 => 'ra.telephone',
        ];

        $query = Agent::from('rto_agents as ra')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'ra.user_id')
            ->where('ra.college_id', '=', $collegeId)
            ->where('ra.is_tenant_default', 0);

        if (! empty($requestData['search']['value'])) {   // if there is a search parameter, $requestData['search']['value'] contains search parameter
            $searchVal = $requestData['search']['value'];
            $query->where(function ($query) use ($columns, $searchVal, $requestData, $arrAgentStatus) {
                $flag = 0;
                foreach ($columns as $key => $value) {
                    $searchVal = $requestData['search']['value'];
                    if ($key == 5 && array_search(ucfirst($searchVal), $arrAgentStatus)) {
                        $searchStatus = array_keys($arrAgentStatus, ucfirst($searchVal));
                        $searchVal = $searchStatus[0];
                    }
                    if ($requestData['columns'][$key]['searchable'] == 'true') {
                        if ($flag == 0) {
                            $query->where($value, 'like', '%'.$searchVal.'%');
                            $flag = $flag + 1;
                        } else {
                            $query->orWhere($value, 'like', '%'.$searchVal.'%');
                        }
                    }
                }
            });
        }

        $temp = $query->orderBy($columns[$requestData['order'][0]['column']], $requestData['order'][0]['dir']);

        $totalData = count($temp->get());
        $totalFiltered = count($temp->get());

        $resultArr = $query->skip($requestData['start'])
            ->take($requestData['length'])
            ->select('ra.id', 'ra.agency_name', 'ra.contact_person', 'ra.primary_email', 'rto_users.username', 'ra.status', 'ra.agent_code', 'ra.telephone')
            ->get();

        $data = [];
        foreach ($resultArr as $row) {
            $nestedData = [];
            $actionHtml = '';
            $actionHtml .= '<li><a href="'.route('spa.manage-users.agents.profile', ['id' => encryptIt($row['id'])]).'#payment" class="link-black text-sm" data-toggle="tooltip" data-original-title="Search" > <i class="fa fa-search"></i></a></li>';
            // $actionHtml .= '<li><a href="'.route('payment-history', ['id' => $row['id']]).'" class="link-black text-sm" data-toggle="tooltip" data-original-title="Search" > <i class="fa fa-search"></i></a></li>';
            $action = '<div class="action-overlay">
                            <ul class="icon-actions-set">
                              '.$actionHtml.'
                            </ul>
                        </div>';
            $nestedData[] = $row['id'].$action;
            $nestedData[] = $row['agency_name'];
            $nestedData[] = $row['contact_person'];
            $nestedData[] = $row['primary_email'];
            $nestedData[] = (! array_key_exists($row['status'], $arrAgentStatus)) ? '' : $arrAgentStatus[$row['status']];
            $nestedData[] = $row['agent_code'];
            $nestedData[] = $row['telephone'];
            $data[] = $nestedData;
        }

        $json_data = [
            'draw' => intval($requestData['draw']), // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
            'recordsTotal' => intval($totalData), // total number of records
            'recordsFiltered' => intval($totalFiltered), // total number of records after searching, if there is no searching then totalFiltered = totalData
            'data' => $this->convertToISO8859_1($data),   // total data array
        ];

        return $json_data;
    }

    public function filterAgentPaymentList($collegeId, $filter_by, $search_str, $agent_status)
    {

        $users = Agent::where('college_id', '=', $collegeId)
            ->where('is_tenant_default', 0)
            ->orderBy('id', 'DESC')
            ->select('rto_agents.*');

        if ($filter_by == 'allAgent' || $filter_by == '') {
            return $users->get();
        } else {
            if ($filter_by == 'id' && ! empty($search_str)) {
                $users->where('id', 'like', '%'.$search_str.'%');

                return $users->get();
            }
            if ($filter_by == 'agency_name' && ! empty($search_str)) {
                $users->where('agency_name', 'like', '%'.$search_str.'%');

                return $users->get();
            }
            if ($filter_by == 'agent_status' && $agent_status != '' && $agent_status > 0) {
                $users->where('status', $agent_status);

                return $users->get();
            } elseif ($filter_by == 'agent_status' && $agent_status == '') {
                return $users->get();
            }
            if ($filter_by == 'primary_email' && ! empty($search_str)) {
                $users->where('primary_email', 'like', '%'.$search_str.'%');

                return $users->get();
            }
        }
    }

    public function checkAgentInfoExist($collegeId, $agentStatusId)
    {
        return Agent::where('college_id', $collegeId)
            ->where('is_tenant_default', 0)
            ->where('status', $agentStatusId)
            ->get()->count();
    }

    public function getFilterAgentListArr($collegeId, $filterBy, $searchString, $agentStatus)
    {

        $sql = Agent::from('rto_agents as ra')
            ->leftjoin('rto_agents as ra1', 'ra1.id', '=', 'ra.super_agent_id')
            ->leftjoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'ra.account_manager_id')
            ->leftjoin('rto_agent_status as ras', 'ras.id', '=', 'ra.status')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'ra.user_id')
            ->where('ra.college_id', $collegeId)
            ->where('ra.is_tenant_default', 0);

        if ($filterBy == 'status' && $agentStatus > 0) {
            $sql->where('ra.status', '=', $agentStatus);
        } elseif (! empty($searchString)) {
            if ($filterBy == 'id' && $searchString > 0) {
                $sql->where('ra.id', '=', $searchString);
            } elseif ($filterBy == 'agency_name' || $filterBy == 'primary_email') {
                $sql->where('ra.agency_name', 'like', '%'.$searchString.'%');
                $sql->orWhere('ra.primary_email', 'like', '%'.$searchString.'%');
            } elseif ($filterBy == 'allAgent') {
                $sql->where('ra.id', '=', $searchString);
                $sql->orWhere('ra.agency_name', 'like', '%'.$searchString.'%');
                $sql->orWhere('ra.contact_person', 'like', '%'.$searchString.'%');
                $sql->orWhere('ra.primary_email', 'like', '%'.$searchString.'%');
                $sql->orWhere('ra.telephone', 'like', '%'.$searchString.'%');
                $sql->orWhere('ra.agent_code', 'like', '%'.$searchString.'%');
            }
        }

        $result = $sql->orderBy('id', 'DESC')->get(['ra.*',
            'ra1.agency_name as super_agent',
            'ras.status_type',
            'rst.name_title',
            'rst.first_name',
            'rto_users.username',
            'rst.last_name']);

        foreach ($result as $row) {
            $row->agent_code = (! empty($row->agent_code)) ? $row->agent_code : '';
            $row->super_agent = (! empty($row->super_agent)) ? $row->super_agent : '';
            $row->office_state = (! empty($row->office_state)) ? $row->office_state : '';
            $row->account_manager = (! empty($row->name_title) && (! empty($row->first_name) || ! empty($row->last_name))) ? $row->name_title.' '.$row->first_name.' '.$row->last_name : '';
        }

        return $result;
    }

    public function getAgentDatatableList($collegeId, $request, $isZohoConnect = false)
    {
        $requestData = $_REQUEST;
        $columns = [
            // datatable column index  => database column name
            0 => 'ra.id',
            1 => 'ra.id',
            2 => 'ra.agency_name',
            3 => 'ra.contact_person',
            4 => 'ra.telephone',
            5 => 'ra.primary_email',
            6 => 'ra.agent_code',
            7 => 'ra.office_state',
            8 => 'ra1.agency_name',
            // 8 => 'rto_users.username',
            9 => 'rst.first_name',
            10 => 'ras.status_type',
            11 => 'ra.is_lock',
        ];

        $query = Agent::from('rto_agents as ra')
            ->leftjoin('rto_agents as ra1', 'ra1.id', '=', 'ra.super_agent_id')
            ->leftjoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'ra.account_manager_id')
            ->leftjoin('rto_agent_status as ras', 'ras.id', '=', 'ra.status')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'ra.user_id')
            ->where('ra.college_id', $collegeId)
            ->where('ra.is_tenant_default', 0);

        if (! empty($requestData['search']['value'])) {   // if there is a search parameter, $requestData['search']['value'] contains search parameter
            $searchVal = $requestData['search']['value'];
            $query->where(function ($query) use ($columns, $searchVal, $requestData) {
                $flag = 0;
                foreach ($columns as $key => $value) {
                    $searchVal = $requestData['search']['value'];
                    if ($key == 11 && ($searchVal == 'Unlock' || $searchVal == 'unlock')) {
                        $searchVal = 1;
                    } elseif ($key == 11 && ($searchVal == 'lock' || $searchVal == 'Lock')) {
                        $searchVal = 0;
                    }
                    if ($requestData['columns'][$key]['searchable'] == 'true') {
                        if ($flag == 0) {
                            $query->where($value, 'like', '%'.$searchVal.'%');
                            $flag = $flag + 1;
                        } else {
                            $query->orWhere($value, 'like', '%'.$searchVal.'%');
                        }
                    }
                }
            });
        }

        $temp = $query->orderBy($columns[$requestData['order'][0]['column']], $requestData['order'][0]['dir']);

        $totalData = count($temp->get());
        $totalFiltered = count($temp->get());

        $resultArr = $query->skip($requestData['start'])
            ->take($requestData['length'])
            ->select('ra1.agency_name as super_agent', 'ra.contact_person', 'ra.telephone',
                'ra.agent_code', 'ras.status_type', 'rst.name_title', 'rst.first_name',
                'rto_users.username', 'rst.last_name', 'ra.id', 'ra.primary_email', 'ra.office_state',
                'ra.account_manager_id', 'ra.agency_name', 'ra.is_lock', 'ra.super_agent_id')
            ->get();

        $data = [];
        foreach ($resultArr as $row) {
            $status_loc = ($row['is_lock'] == 1) ? 'Unlock' : 'Lock';
            $status_icon = ($row['is_lock'] == 1) ? 'fa fa-unlock ' : 'fa fa-lock ';
            $enable_disable = ($row['is_lock'] == 1) ? 'Enable/Terminate the selected agent?' : 'Disable/Terminate the selected agent?';
            $account_manager = (! empty($row['name_title']) && (! empty($row['first_name']) || ! empty($row['last_name']))) ? $row['name_title'].' '.$row['first_name'].' '.$row['last_name'] : '';
            $actionHtml = '';
            $actionHtml .= '<li><a href="'.route('edit-agent', ['id' => $row['id']]).'" class="link-black text-sm" data-toggle="tooltip" data-original-title="Edit Selected Agent profile"> <i class="fa fa-edit"></i> </a></li>';
            $actionHtml .= '<li><span data-toggle="modal" class="delete" data-id="'.$row['id'].'" data-target="#deleteModal">'.'<a class="link-black text-sm delete" data-toggle="tooltip" data-original-title="Delete" href="javascript:;"><i class="fa fa-remove"></i> </a></span></li>';
            $actionHtml .= '<li><span data-toggle="modal" class="editAgentCode" data-id="'.$row['id'].'" data-target="#editAgentModal">'.'<a class="link-black text-sm" data-toggle="tooltip" data-original-title="Edit Selected agent code" href="javascript:;"><i class="fa fa-cog"></i> </a></span></li>';
            $actionHtml .= '<li><a class="link-black text-sm" data-toggle="tooltip" data-original-title="View/Upload/Download the selected agent document" href="/list-agent-materials/AgentFiles/'.$row['id'].'/0'.'"><i class="fa fa-file-code"></i> </a></li>';
            $actionHtml .= '<li><a class="link-black text-sm" data-toggle="tooltip" data-original-title="View agent communication log" href="/agent-communication-add-data/'.$row['id'].'"><i class="fa fa-file"></i> </a></li>';
            $actionHtml .= '<li><a class="link-black text-sm change_status" data-toggle="tooltip" data-id="'.$row['id'].'" data-status="'.$row['is_lock'].'" data-original-title="'.$enable_disable.'"  href="javascript:;"><i class="'.$status_icon.'"></i> </a></li>';
            $actionHtml .= '<li><a class="link-black text-sm createUser" data-toggle="tooltip" data-id="'.$row['id'].'" data-agent-name="'.$row['agency_name'].'" data-original-title="Create User"><i class="fa fa-user"></i> </a></li>';
            $actionHtml .= '<li><a class="link-black text-sm addStatus" data-toggle="tooltip" data-id="'.$row['id'].'" data-agent-name="'.$row['agency_name'].'" data-original-title="Add Agent Status" ><i class="fa fa-plus"></i> </a></li>';
            $actionHtml .= '<li><a href="'.route('agent-students', ['id' => $row['id']]).'" class="link-black text-sm" data-toggle="tooltip" data-original-title="View Students" ><i class="fa fa-users"></i> </a></li>';
            $actionHtml .= '<li><a href="javascript:;" class="exportStudentList link-black text-sm" data-id="'.$row['id'].'" data-toggle="tooltip" data-original-title="Export Student List" ><i class="fa fa-download"></i> </a></li>';

            if ($isZohoConnect) {
                $actionHtml .= '<li><a href="javascript:void(0);" class="syncSingleAgentFromZoho link-black text-sm" data-toggle="tooltip" data-agent-id="'.$row['id'].'" data-toggle="tooltip" data-original-title="Sync From Zoho" ><i class="fa fa-arrow-down"></i> </a></li>';
                $actionHtml .= '<li><a href="javascript:void(0);" class="syncSingleAgentToZoho link-black text-sm" data-toggle="tooltip" data-agent-id="'.$row['id'].'" data-toggle="tooltip" data-original-title="Sync To Zoho" ><i class="fa fa-sync"></i> </a></li>';
            }

            $action = '<div class="action-overlay"><ul class="icon-actions-set">'.$actionHtml.'</ul></div>';
            $checkbox = '<input type="checkbox" name="" class="check_all" value="'.$row['id'].'">';

            $nestedData = [];
            $nestedData[] = $checkbox.$action;
            $nestedData[] = $row['id'];
            $nestedData[] = $row['agency_name'];
            $nestedData[] = $row['contact_person'];
            $nestedData[] = $row['telephone'];
            $nestedData[] = $row['primary_email'];
            $nestedData[] = $row['agent_code'];
            $nestedData[] = $row['super_agent'];
            $nestedData[] = $row['office_state'];
            $nestedData[] = $account_manager;
            $nestedData[] = $row['status_type'];
            $nestedData[] = $status_loc;

            $data[] = $nestedData;
        }

        $json_data = [
            'draw' => intval($requestData['draw']), // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
            'recordsTotal' => intval($totalData), // total number of records
            'recordsFiltered' => intval($totalFiltered), // total number of records after searching, if there is no searching then totalFiltered = totalData
            'data' => $this->convertToISO8859_1($data), // total data array
        ];

        return $json_data;
    }

    public function getAgentCodeById($agentId)
    {
        $bankDetail = Agent::where('id', '=', $agentId)->get();

        return $bankDetail;
    }

    public function editAgentCode($request)
    {
        $objAgentEdit = new Agent;
        $objAgentEdit = Agent::find($request->input('id'));
        $objAgentEdit->agent_code = $request->input('agent_code');
        $objAgentEdit->created_by = Auth::user()->id;
        $objAgentEdit->updated_by = Auth::user()->id;
        $objAgentEdit->save();
    }

    public function editAgentStatus($agentId, $agentStatus)
    {
        $objAgentStatus = new Agent;
        $objAgentStatus = Agent::find($agentId);
        $objAgentStatus->is_lock = ($agentStatus == 1) ? 0 : 1;
        $objAgentStatus->created_by = Auth::user()->id;
        $objAgentStatus->updated_by = Auth::user()->id;
        $objAgentStatus->save();

        $objUserStatus = Users::find($objAgentStatus->user_id);
        if ($objUserStatus) {
            $objUserStatus->status = ($agentStatus == 1) ? 0 : 1;
            $objUserStatus->save();
        }

        return true;
    }

    public function getAgentListByStatusAndCountry($perPage, $agentStatus, $targetPrimaryCountry, $collegeId)
    {
        // $collegeId = Auth::user()->college_id;
        $sql = Agent::leftjoin('rto_staff_and_teacher', 'rto_staff_and_teacher.id', '=', 'rto_agents.account_manager_id')
            ->leftjoin('rto_agent_status', 'rto_agent_status.id', '=', 'rto_agents.status')
            ->leftjoin('rto_agent_profile_status', 'rto_agent_profile_status.agent_id', '=', 'rto_agents.id')
            ->where('rto_agents.college_id', $collegeId)
            ->where('rto_agents.status', $agentStatus)
            ->where('rto_agents.is_tenant_default', 0)
            ->select('rto_staff_and_teacher.first_name', 'rto_staff_and_teacher.last_name', 'rto_agents.id', 'rto_agents.agency_name', 'rto_agents.contact_person', 'rto_agents.primary_email', 'rto_agents.telephone', 'rto_agents.mobile1', 'rto_agents.agency_name', 'rto_agents.office_ABN', 'rto_agents.office_address', 'rto_agents.office_country', 'rto_agents.office_city', 'rto_agents.office_state', 'rto_agents.office_postcode', 'rto_agents.target_secondry_country', 'rto_agents.target_primary_country', 'rto_agents.target_primary_country', 'rto_agents.bank_name', 'rto_agents.bank_branch', 'rto_agents.bank_account_name', 'rto_agents.bank_account_number', 'rto_agents.BSB', 'rto_agents.bank_swift_code', 'rto_agents.bank_country', 'rto_agent_profile_status.end_date', 'rto_agent_status.status_type');
        if ($targetPrimaryCountry != '' && $targetPrimaryCountry > 0) {
            $sql->where('rto_agents.target_primary_country', $targetPrimaryCountry);
        }
        if (! empty($perPage)) {
            return $sql->paginate($perPage);
        } else {
            return $sql->get();
        }
    }

    public function getAgentCountByCountryId($countryId, $collegeId)
    {

        return Agent::where('college_id', '=', $collegeId)
            ->where('is_tenant_default', 0)
            ->where(function ($query) use ($countryId) {
                $query->where('bank_country', '=', $countryId)
                    ->orwhere('office_country', '=', $countryId)
                    ->orwhere('target_primary_country', '=', $countryId)
                    ->orwhere('target_secondry_country', '=', $countryId);
            })
            ->count();
    }

    public function getAllAgentInfo($collegeId)
    {

        $result = Agent::from('rto_agents as ra')
            ->where('ra.college_id', $collegeId)
            ->where('ra.is_tenant_default', 0)
            ->groupBy('ra.id')
            ->get(['ra.id', 'ra.agency_name', 'ra.primary_email as email']);

        return $result;
    }

    public function getAgentResendRequest($collegeId, $formId)
    {

        return Agent::from('rto_agents as ra')
            ->leftjoin('rto_survey_send_request as rssr', 'rssr.agent_id', '=', 'ra.id')
            ->where('ra.college_id', $collegeId)
            ->where('ra.is_tenant_default', 0)
            ->where('rssr.form_name_id', $formId)
            ->groupBy('ra.id')
            ->get(['ra.id',
                'ra.agency_name',
                'ra.primary_email as email',
                'rssr.group_id',
                'rssr.supervisor',
                'rssr.before_submit',
                'rssr.created_at']);
    }

    public function getFilterAgentListInfo($collegeId, $searchBy, $status)
    {

        $sql = Agent::where('college_id', $collegeId)
            ->where('is_tenant_default', 0);

        if ($searchBy == 'status' && $status > 0) {
            $sql->where('status', '=', $status);
        }
        $result = $sql->get(['id', 'agency_name', 'primary_email as email']);

        return $result;
    }
}
