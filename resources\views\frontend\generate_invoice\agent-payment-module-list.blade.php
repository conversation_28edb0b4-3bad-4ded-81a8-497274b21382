@php
$routeGet = explode("/", Route::getCurrentRoute()->uri());
 $activeVar= $routeGet[0];
@endphp

<ul class="sidemenu">
    <li @if($activeVar=='view-agent-payment-list') class="icon-active" @endif>
        <a href="{{ route('view-agent-payment-list')}}">Search Agent</a>
    </li>
    <li @if($activeVar=='process-commission-agent') class="icon-active" @endif>
        <a href="{{ route('process-commission-agent',array('id'=>$agentId)) }}">Process Commission</a>
    </li>
    <li @if($activeVar=='payment-history') class="icon-active" @endif>
        <a href="{{ route('spa.manage-users.agents.profile', array('id'=>encryptIt($agentId)))}}#payment">Payment History</a>
    </li>
    <li @if($activeVar=='credit-bonus-allocation') class="icon-active" @endif>
        <a href="{{ route('credit-bonus-allocation',array('id'=>$agentId))}}">Credit/Bonus Allocation</a>
    </li>
</ul>