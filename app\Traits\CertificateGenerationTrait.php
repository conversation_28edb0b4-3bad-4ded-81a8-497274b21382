<?php

namespace App\Traits;

use App;
use App\Helpers\Helpers;
use App\Model\v2\Colleges;
use App\Model\v2\ResultGrade;
use App\Model\v2\StudentCertificateRegister;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentSubjectEnrolment;
use App\Services\CertificateContentReplacer;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Support\Services\UploadService;

trait CertificateGenerationTrait
{
    private function getStudentData($studentId, $courseId)
    {
        return StudentCourses::with(['student', 'course'])
            ->where('student_id', $studentId)
            ->where('course_id', $courseId)
            ->first();
    }

    private function getGradeData($collegeId)
    {
        $whereArr = ['college_id' => $collegeId];
        $gradeDatas = ResultGrade::Where($whereArr)->select('*')->get()->toArray();

        return [
            'gradePointData' => collect($gradeDatas)->pluck('grade_point', 'id')->toArray(),
            'gradeData' => collect($gradeDatas)->pluck('grade', 'id')->toArray(),
        ];
    }

    private function getEnrolments($studentId, $courseId)
    {
        return StudentSubjectEnrolment::with(['semester', 'unit', 'subject'])
            ->where('student_id', $studentId)
            ->where('course_id', $courseId)
            ->get();
    }

    private function generateQrCode($data)
    {
        $qrCode = QrCode::size(200)
            ->format('png')
            ->generate($data['verification_url']);

        return 'data:image/png;base64,'.base64_encode($qrCode);
    }

    private function prepareCertificateData($college, $student, $uuid, $otherData, $enrolments, $qrCodeBase64, ?array $chunkSizes = null)
    {
        $competencyCalculation = ResultGrade::where('competency_calculation', '1')->pluck('id')->toArray();
        $unitChunk = max(1, (int) ($chunkSizes['unit'] ?? 10));
        $transcriptChunk = max(1, (int) ($chunkSizes['trascript'] ?? 2));

        return [
            'student' => [
                'fullname' => $student->student->name_title.' '.$student->student->first_name.' '.$student->student->family_name,
                'firstname' => $student->student->first_name,
                'lastname' => $student->student->family_name,
                'id' => $student->student->generated_stud_id,
                'dob' => date('d M Y', strtotime($student->student->DOB)),
            ],
            'course' => [
                'code' => $student->course->course_code ?? '',
                'name' => $student->course->course_name ?? '',
                'certificate_text' => $student->course->course_certificate_text ?? '',
                'delivery_mode' => $this->getCourseDeliveryMode($student),
                'duration' => $this->getCourseDurationStr($student->total_weeks, $student->course_duration_type),
            ],
            'college' => [
                'name' => $college->college_name ?? '',
                'signature' => $this->getCollegeRelatedImage($college->college_signature, 'College Signature') ?? '',
                'dean_name' => $college->dean_name ?? '',
                'dean_signature' => $this->getCollegeRelatedImage($college->dean_signature, 'Dean Signature') ?? '',
                'admission_manager_signature' => $this->getCollegeRelatedImage($college->admission_manager_signature, 'Admission Manager Signature') ?? '',
                'student_support_signature' => $this->getCollegeRelatedImage($college->student_support_signature, 'Student Support Signature') ?? '',
            ],
            'certificate' => [
                'uuid' => $uuid ?? '',
                'issued_on' => date('d M Y'),
                'expired_on' => $student->certificate_expired_on,
            ],
            'studentcourse' => [
                'course_startdate' => date('d M Y', strtotime($student->start_date)),
                'course_enddate' => date('d M Y', strtotime($student->finish_date)),
            ],
            'qr_code' => '<img src="'.$qrCodeBase64.'" alt="QR Code" />',
            'qr_code_src' => $qrCodeBase64,
            'unit' => [
                'otherData' => $otherData,
                'list' => $enrolments->map(function ($enrolment) {
                    if ($enrolment->unit && in_array($enrolment->final_outcome, ['C', 'RPL', 'CT'])) {
                        return [
                            'code' => $enrolment->unit->unit_code,
                            'name' => $enrolment->unit->unit_name,
                            'year' => ($enrolment->activity_finish_date != '' &&
                                $enrolment->activity_finish_date != '0000-00-00' &&
                                strtotime($enrolment->activity_finish_date) !== false) ?
                                date('Y', strtotime($enrolment->activity_finish_date)) : '-',
                            'result' => $enrolment->final_outcome,
                        ];
                    }

                    return null;
                })->filter()->values()->chunk($unitChunk)->toArray(),
                'listwithresult' => $enrolments->map(function ($enrolment) {
                    if ($enrolment->unit && in_array($enrolment->final_outcome, ['C', 'RPL', 'RCC', 'CT', 'WD', 'NYC'])) {
                        return [
                            'code' => $enrolment->unit->unit_code,
                            'name' => $enrolment->unit->unit_name,
                            'year' => ($enrolment->activity_finish_date != '' &&
                                $enrolment->activity_finish_date != '0000-00-00' &&
                                strtotime($enrolment->activity_finish_date) !== false) ?
                                date('Y', strtotime($enrolment->activity_finish_date)) : '-',
                            'result' => $enrolment->final_outcome,
                        ];
                    }

                    return null;
                })->filter()->values()->chunk($unitChunk)->toArray(),
                'trascript' => collect($enrolments)->groupBy('semester_id')->map(function ($items) use ($competencyCalculation) {
                    return [
                        'semester_name' => optional($items->first()->semester)->semester_name ?? 'N/A',
                        'data' => $items->filter(function ($item) use ($competencyCalculation) {
                            return ! empty($item->mark_outcome) && in_array($item->mark_outcome, $competencyCalculation);
                        }),
                    ];
                })->values()->chunk($transcriptChunk)
                    ->map(function ($chunk) {
                        return [
                            'total_count' => $chunk->sum(fn ($item) => count($item['data'])),
                            'data' => $chunk->toArray(),
                        ];
                    })
                    ->toArray(),
            ],
            'cricos' => [
                'course' => [
                    'code' => $student->course->cricos_code ?? '',
                ],
            ],
        ];
    }

    // removed buildGradeLegendHtml: handled by CertificateContentReplacer

    private function getCourseDeliveryMode($student)
    {
        $deliveryModes = [];

        if ($student->internal == 'Y') {
            $deliveryModes[] = StudentCourses::DELIVERY_MODE_CLASSROOM;
        }
        if ($student->external == 'Y') {
            $deliveryModes[] = StudentCourses::DELIVERY_MODE_ONLINE;
        }
        if ($student->workplace_based_delivery == 'Y') {
            $deliveryModes[] = StudentCourses::DELIVERY_MODE_BLENDED;
        }

        return $deliveryModes ? implode(', ', $deliveryModes) : 'N/A';
    }

    private function getCourseDurationStr($duration, $type)
    {
        $types = [
            '1' => 'Day',
            '2' => 'Week',
            '3' => 'Month',
            '4' => 'Year',
        ];

        if (! isset($types[$type])) {
            return 'N/A';
        }

        $typeName = $types[$type];
        if ($duration > 1) {
            $typeName .= 's';
        }

        return "$duration $typeName";
    }

    private function getCollegeRelatedImage($imgName, $placeholderText = 'image')
    {
        if (empty($imgName)) {
            return null;
        }
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);
        $imgEmbedPath = UploadService::imageEmbed($destinationPath['view'].$imgName);
        if (! $imgEmbedPath) {
            return null;
        }

        return '<img src="'.$imgEmbedPath.'" alt="'.$placeholderText.'" style="height: auto; width: 80px;" />';
    }

    private function generatePdf($templates, $data, $fileName = null, $isDownload = false)
    {
        $replacer = new CertificateContentReplacer($data);
        $processedHtml = $replacer->replace($templates->html_data);
        $extractCSS = $replacer->extractStyles($templates->html_data);

        $width_px = 707;
        $height_px = 1000;
        $width_pt = $width_px * 0.75;
        $height_pt = $height_px * 0.75;

        if (! is_dir(Config::get('constants.certificateTempFontPath'))) {
            mkdir(Config::get('constants.certificateTempFontPath'), 0777);
        }

        $pdf = App::make('dompdf.wrapper');
        $pdf->setPaper([0, 0, $width_pt, $height_pt]);
        $pdf->setOptions([
            'isRemoteEnabled' => true,
            'fontDir' => Config::get('constants.certificateTempFontPath'),
        ]);

        $pageBreakTableTopPx = $this->extractPageBreakTop($templates);
        $pdf->loadView('certificates.pdf', compact('processedHtml', 'extractCSS', 'pageBreakTableTopPx'));

        $pdf->output();
        $dom_pdf = $pdf->getDomPDF();
        $canvas = $dom_pdf->get_canvas();

        // Pagination: extract settings and render if enabled
        $pagination = $this->extractPaginationSettings($templates);

        if ($canvas && ($pagination['enabled'] ?? false)) {
            [$x, $y, $fontSize] = $this->computePaginationPosition($canvas, $pagination);
            $canvas->page_text($x, $y, 'Page {PAGE_NUM} of {PAGE_COUNT}', null, $fontSize, [0, 0, 0]);
        }

        if ($isDownload) {
            $filePath = Config::get('constants.uploadFilePath.StudentCertificate');
            $destinationPath = Helpers::changeRootPath($filePath);

            $pdfContent = $pdf->output();
            $tmpPath = tempnam(sys_get_temp_dir(), 'pdf_');
            file_put_contents($tmpPath, $pdfContent);
            $upload_success = UploadService::uploadAs($destinationPath['view'], new \Illuminate\Http\File($tmpPath), $fileName.'.pdf');
            info('file uploaded form generatePdf', [$upload_success]);
            @unlink($tmpPath);

            return UploadService::download($destinationPath['view'].$fileName.'.pdf');
        }

        return $pdf->stream();
    }

    /**
     * Extract chunk sizes for unit and transcript from template metadata.
     */
    private function extractChunkSettings($templates): array
    {
        $defaults = ['unit' => 10, 'trascript' => 2];
        $metadata = $templates->metadata ?? null;
        $decoded = is_string($metadata) ? json_decode($metadata, true) : (is_array($metadata) ? $metadata : []);
        if (! is_array($decoded)) {
            return $defaults;
        }
        $chunk = $decoded['settings']['chunk'] ?? [];
        $unit = isset($chunk['unit']) && is_numeric($chunk['unit']) ? (int) $chunk['unit'] : $defaults['unit'];
        $trans = isset($chunk['trascript']) && is_numeric($chunk['trascript']) ? (int) $chunk['trascript'] : $defaults['trascript'];

        return [
            'unit' => max(1, $unit),
            'trascript' => max(1, $trans),
        ];
    }

    /**
     * Extract page-break-table top offset (px) from template metadata.
     */
    private function extractPageBreakTop($templates): int
    {
        $default = 130;
        $metadata = $templates->metadata ?? null;
        if (! $metadata) {
            return $default;
        }
        if (is_string($metadata)) {
            $decoded = json_decode($metadata, true);
        } else {
            $decoded = is_array($metadata) ? $metadata : [];
        }
        if (! is_array($decoded)) {
            return $default;
        }
        $top = $decoded['settings']['page_break_table']['top'] ?? $default;
        $top = is_numeric($top) ? (int) $top : $default;

        return $top > 0 ? $top : $default;
    }

    /**
     * Extract overflow note settings
     */
    private function extractOverflowSettings($templates): array
    {
        $defaults = ['enabled' => false, 'text' => 'Units Achieved (continued)'];
        $metadata = $templates->metadata ?? null;
        $decoded = is_string($metadata) ? json_decode($metadata, true) : (is_array($metadata) ? $metadata : []);
        if (! is_array($decoded)) {
            return $defaults;
        }
        $ov = $decoded['settings']['overflow'] ?? [];
        $enabled = (bool) ($ov['enabled'] ?? false);
        $text = (string) ($ov['text'] ?? $defaults['text']);

        return ['enabled' => $enabled, 'text' => $text !== '' ? $text : $defaults['text']];
    }

    /**
     * Extract pagination settings from template metadata with safe defaults.
     */
    private function extractPaginationSettings($templates): array
    {
        $defaults = [
            'enabled' => false,
            'position' => 'bottom_right',
            'offset_x' => 0,
            'offset_y' => 0,
            'font_size' => 9,
        ];

        $metadata = $templates->metadata ?? null;
        if (! $metadata) {
            return $defaults;
        }

        $decoded = is_string($metadata) ? json_decode($metadata, true) : $metadata;
        if (! is_array($decoded)) {
            return $defaults;
        }

        $pagination = $decoded['settings']['pagination'] ?? [];
        if (! is_array($pagination)) {
            return $defaults;
        }

        return [
            'enabled' => (bool) ($pagination['enabled'] ?? $defaults['enabled']),
            'position' => $pagination['position'] ?? $defaults['position'],
            'offset_x' => (int) ($pagination['offset_x'] ?? $defaults['offset_x']),
            'offset_y' => (int) ($pagination['offset_y'] ?? $defaults['offset_y']),
            'font_size' => (int) ($pagination['font_size'] ?? $defaults['font_size']),
        ];
    }

    /**
     * Compute final (x, y) and font size for the pagination text based on page size and settings.
     */
    private function computePaginationPosition($canvas, array $pagination): array
    {
        $pageWidth = $canvas->get_width();
        $pageHeight = $canvas->get_height();
        $margin = 50;
        $fontSize = max(6, min(48, (int) ($pagination['font_size'] ?? 9)));

        $approxTextWidth = 10;

        $position = $pagination['position'] ?? 'bottom_right';
        switch ($position) {
            case 'top_left':
                $x = $margin;
                $y = $margin + $fontSize;
                break;
            case 'top_center':
                $x = $pageWidth / 2 - $approxTextWidth / 2;
                $y = $margin + $fontSize;
                break;
            case 'top_right':
                $x = $pageWidth - $approxTextWidth - $margin;
                $y = $margin + $fontSize;
                break;
            case 'bottom_left':
                $x = $margin;
                $y = $pageHeight - $margin;
                break;
            case 'bottom_center':
                $x = $pageWidth / 2 - $approxTextWidth / 2;
                $y = $pageHeight - $margin;
                break;
            case 'bottom_right':
            default:
                $x = $pageWidth - $approxTextWidth - $margin;
                $y = $pageHeight - $margin;
                break;
        }

        // Apply offsets (positive X -> right, positive Y -> down)
        $x += (int) ($pagination['offset_x'] ?? 0);
        $y += (int) ($pagination['offset_y'] ?? 0);

        return [$x, $y, $fontSize];
    }

    public function handleCertificateGeneration($params)
    {
        $college = Colleges::find($params['collegeId']);

        if ($params['isDownload']) {
            $fileName = str_replace(' ', '_', $params['templates']->name).'_'.time().'_'.$params['studentId'];
            $params['dataValue']['certificate_number_formate_id'] = $params['templates']->certificate_number_formate_id;
            $params['dataValue']['issueDate'] = date('Y-m-d');
            $recoredId = StudentCertificateRegister::saveCertificateRegisterFromPdfBeta($params['collegeId'], $params['dataValue'], $params['studentId'], $fileName);

            $qrCodeData = [
                'certificate_id' => $params['uuid'],
                'student_id' => $params['studentId'],
                'course_id' => $params['student']->course_id,
                'issue_date' => date('Y-m-d'),
                'verification_url' => route('verify.certificate', ['id' => encryptIt($recoredId)]),
            ];

            $qrCodeBase64 = $this->generateQrCode($qrCodeData);
            $chunks = $this->extractChunkSettings($params['templates']);
            $data = $this->prepareCertificateData($college, $params['student'], $params['uuid'], $params['otherData'], $params['enrolments'], $qrCodeBase64, $chunks);
            // Pass overflow settings to content replacer via data
            $data['settings']['overflow'] = $this->extractOverflowSettings($params['templates']);

            return [
                'pdf' => $this->generatePdf($params['templates'], $data, $fileName, true),
                'fileName' => $fileName,
                'studentId' => $params['studentId'],
            ];
        }

        $dummyData = [
            'certificate_id' => 'PREVIEW-'.$params['uuid'],
            'student_id' => 'PREVIEW-'.$params['student']->student_id,
            'course_id' => 'PREVIEW-'.$params['student']->course_id,
            'issue_date' => date('Y-m-d'),
            'verification_url' => 'https://example.com/preview',
        ];

        $qrCodeBase64 = $this->generateQrCode($dummyData);
        $chunks = $this->extractChunkSettings($params['templates']);
        $data = $this->prepareCertificateData($college, $params['student'], $params['uuid'], $params['otherData'], $params['enrolments'], $qrCodeBase64, $chunks);
        // Pass overflow settings to content replacer via data
        $data['settings']['overflow'] = $this->extractOverflowSettings($params['templates']);

        return [
            'pdf' => $this->generatePdf($params['templates'], $data),
            'studentId' => $params['studentId'],
        ];
    }

    public function handleBulkDownload($results)
    {
        $collegeId = Auth::user()->college_id;
        $filePath = config('constants.uploadFilePath.TempFiles');
        $destination = Helpers::changeRootPath($filePath, null, $collegeId);

        File::ensureDirectoryExists($destination['default'], 0777, true);

        $zipFileName = 'certificates_'.time().'.zip';
        $zipPath = $destination['default'].$zipFileName;

        $zip = new \ZipArchive;

        if ($zip->open($zipPath, \ZipArchive::CREATE) === true) {
            foreach ($results as $result) {
                if (! empty($result['pdf']) && ! empty($result['fileName'])) {
                    $parsedPath = parse_url($result['pdf'], PHP_URL_PATH);
                    $s3Key = ltrim(strstr($parsedPath, 'uploads/'), '/');

                    if (Storage::disk(config('filesystems.upload_disk'))->exists($s3Key)) {
                        $pdfContent = Storage::disk(config('filesystems.upload_disk'))->get($s3Key);
                        $zip->addFromString($result['fileName'].'.pdf', $pdfContent);
                    }
                }
            }
            $zip->close();
        }

        return response()->download($zipPath)->deleteFileAfterSend(true);
    }

    public function handleStudentCardGeneration($params)
    {
        $data = $this->prepareStudentCardData($params['student']);

        if ($params['isDownload']) {
            return [
                'pdf' => $this->generateStudentCardPdf($params, $data, $params['fileName'], true),
                'fileName' => $params['fileName'],
                'studentId' => $params['studentId'],
            ];
        }

        return [
            'pdf' => $this->generateStudentCardPdf($params, $data),
            'studentId' => $params['studentId'],
        ];
    }

    private function prepareStudentCardData($student, $college = null)
    {
        $college = $college ?? $student->college;
        $profilePicPath = '';
        if ($student->profile_picture) {
            $filePath = Config::get('constants.uploadFilePath.StudentPics');
            $destinationPath = Helpers::changeRootPath($filePath, $student->id);
            $profilePicPath = UploadService::imageEmbed($destinationPath['view'].$student->profile_picture);
        }

        if (empty($profilePicPath)) {
            $defaultAvatarPath = public_path('dist/img/avatar6.png');
            if (file_exists($defaultAvatarPath)) {
                $imageData = file_get_contents($defaultAvatarPath);
                $profilePicPath = 'data:image/png;base64,'.base64_encode($imageData);
            }
        }

        $collegeLogoPath = '';
        if ($college->college_logo) {
            $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
            $destinationPath = Helpers::changeRootPath($filePath);
            $collegeLogoPath = UploadService::imageEmbed($destinationPath['view'].$college->college_logo);
        }

        return [
            'full_name' => collect([
                $student->name_title ?? null,
                $student->first_name ?? null,
                $student->family_name ?? null,
            ])->filter()->implode(' '),
            'first_name' => $student->first_name,
            'last_name' => $student->family_name,
            'student_id' => $student->generated_stud_id,
            'email' => $student->email,
            'date_of_birth' => date('d M Y', strtotime($student->DOB)),
            'profile_picture' => $profilePicPath,
            'phone' => $student->current_mobile_phone,
            'address' => collect([
                $student->current_building_name ?? null,
                $student->current_unit_detail ?? null,
                $student->current_street_no ?? null,
                $student->current_street_name ?? null,
                $student->current_city ?? null,
                $student->current_state ?? null,
                $student->current_postcode ?? null,
            ])->filter()->implode(' '),
            'college_name' => $college->college_name,
            'college_logo' => $collegeLogoPath,
            'exp_date' => $student->latest_course_end_date ? date('d M Y', strtotime($student->latest_course_end_date)) : 'N/A',

            'issue_date' => date('d M Y'),
        ];
    }

    /*private function getStudentCardExpiryDate($student)
    {
        // Check if we have latestCourse relationship loaded
        if ($student->relationLoaded('latestCourse') && $student->latestCourse && !empty($student->latestCourse->finish_date)) {
            return date('d M Y', strtotime($student->latestCourse->finish_date));
        }

        // Check if we have latest_course_end_date from the query (for bulk operations)
        if (isset($student->latest_course_end_date) && !empty($student->latest_course_end_date)) {
            return date('d M Y', strtotime($student->latest_course_end_date));
        }

        // Fallback: if student has studentCourses relationship loaded, find current course
        if ($student->relationLoaded('studentCourses')) {
            $currentCourse = $student->studentCourses
                ->where('status', StudentCourses::STATUS_CURRENT_STUDENT)
                ->sortByDesc('finish_date')
                ->first();

            if ($currentCourse && !empty($currentCourse->finish_date)) {
                return date('d M Y', strtotime($currentCourse->finish_date));
            }
        }

        return null;
    }*/

    private function generateStudentCardPdf($params, $data, $fileName = 'student-card', $isDownload = false)
    {
        $template = $params['template'];
        $replacer = new CertificateContentReplacer($data);
        $processedHtml = $replacer->replace($template->html_data);
        $extractCSS = $replacer->extractStyles($template->html_data);

        $width_px = floor($params['width_px'] / 2);
        $height_px = floor($params['height_px'] / 2);

        $width_pt = $width_px * 0.75;
        $height_pt = $height_px * 0.75;

        $pdf = App::make('dompdf.wrapper');
        //        $pdf->setPaper('A4');
        $pdf->setPaper([0, 0, $width_pt, $height_pt]);

        //        $htmlWithMargin = '<div style="margin-top: 100px;">'.$processedHtml.'</div>';

        $pageBreakTableTopPx = $this->extractPageBreakTop((object) ['metadata' => $template->metadata ?? null]);
        $pdf->loadView('certificates.pdf', [
            'processedHtml' => $processedHtml,
            'extractCSS' => $extractCSS,
            'pageBreakTableTopPx' => $pageBreakTableTopPx,
        ]);
        // $pdf->output();

        if ($isDownload) {
            return $pdf->download($fileName.'.pdf');
        }

        return $pdf->stream();
    }
}
