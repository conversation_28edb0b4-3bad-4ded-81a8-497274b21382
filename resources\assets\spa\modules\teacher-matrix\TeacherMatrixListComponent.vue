<template>
    <AsyncGrid
        :columns="filteredColumns"
        :store="store"
        :show-refresh-button="false"
        :show-filter-button="false"
        :add-permissions="null"
        :enableSelection="false"
        :has-create-action="true"
        :create-btn-label="'Assign Subject'"
        :has-export="false"
        :has-filters="true"
        :gridStyle="{
            height: '350px',
        }"
        @handel-reset="
            () => {
                initFilters();
                store.selected = [];
            }
        "
        :hasActions="true"
        :actions="['delete', 'edit']"
        :filter-columns="3"
    >
        <template #filters>
            <FilterBlockWrapper label="Knowledge Level">
                <EnumSelect
                    enum-class="GalaxyAPI\Enums\KnowledgeLevelEnum"
                    v-model="store.filters.knowledgeLevel"
                    :has-select-all="true"
                    placeholder="Select Knowledge Level"
                />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Course">
                <CoursesSelect v-model="store.filters.courseId" />
            </FilterBlockWrapper>
        </template>
        <template #bulk-actions> </template>
        <template #body-cell-knowledge_level="{ props }">
            <Badge :variant="getBadgeVariant(props.dataItem?.knowledge_level)">{{
                props.dataItem?.knowledge_level_name
            }}</Badge>
        </template>
        <template #body-cell-course="{ props }">
            <div class="inline">
                <span class="text-gray-500">
                    {{ props.dataItem?.course_code }}
                </span>
                -
                <p class="inline w-auto text-sm leading-5 text-gray-700">
                    {{ props.dataItem?.course_name }}
                </p>
            </div>
        </template>
        <template #body-cell-teacher_name="{ props }">
            {{ props.dataItem?.teacher_name ?? 'N/A' }}
        </template>
        <template #body-cell-subject_code="{ props }">
            {{ props.dataItem?.subject_code ?? 'N/A' }}
        </template>
        <template #body-cell-subject_name="{ props }">
            {{ props.dataItem?.subject_name ?? 'N/A' }}
        </template>
        <template #body-cell-actions="{ props }">
            <div class="flex justify-start gap-1">
                <Tooltip
                    :anchor-element="'target'"
                    :position="'top'"
                    :parentTitle="true"
                    :tooltipClassName="'flex !p-1'"
                    :class="'w-full'"
                >
                    <Button
                        :variant="'icon'"
                        @click="store.edit(props.dataItem)"
                        class="cursor-pointer text-gray-400"
                        title="Edit"
                    >
                        <icon :name="'pencil'" :width="20" :height="20" :fill="'currentColor'" />
                    </Button>
                </Tooltip>
                <Tooltip
                    :anchor-element="'target'"
                    :position="'top'"
                    :parentTitle="true"
                    :tooltipClassName="'flex !p-1'"
                    :class="'w-full'"
                >
                    <Button
                        :variant="'icon'"
                        @click="store.confirmDelete(props.dataItem)"
                        class="cursor-pointer"
                        title="Delete"
                    >
                        <icon :name="'delete'" :width="20" :height="20" />
                    </Button>
                </Tooltip>
            </div>
        </template>
    </AsyncGrid>
    <TeacherMatrixForm />
</template>

<script setup>
import { computed, provide } from 'vue';
import { useTeacherMatrixStore } from '@spa/stores/modules/teacher/useTeacherMatrixStore.js';
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { onMounted, watch } from 'vue';
import FormatDate from '@spa/components/FormatDate.vue';
import { Tooltip } from '@progress/kendo-vue-tooltip';
import Badge from '@spa/components/badges/Badge.vue';
import Select from '@spa/components/AsyncComponents/Select/AsyncSelect.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import TeacherMatrixForm from '@spa/modules/teacher-matrix/TeacherMatrixForm.vue';
import Button from '@spa/components/Buttons/Button.vue';
import EnumSelect from '@spa/components/AsyncComponents/Select/EnumSelect.vue';
import CoursesSelect from '@spa/modules/teacher/partials/CoursesSelect.vue';

const props = defineProps({
    filters: {
        type: Object,
        default: () => ({}),
    },
    teacherId: {
        type: Number,
        default: null,
    },
});

provide('teacherId', props.filters?.teacherId);

const store = useTeacherMatrixStore();
const columns = [
    {
        name: 'teacher_name',
        title: 'Name',
        field: 'teacher_id',
        replace: true,
        sortable: true,
    },
    {
        name: 'subject_code',
        title: 'Subject Id',
        field: 'subject_id',
        sortable: true,
        replace: true,
    },
    {
        name: 'subject_name',
        title: 'Subject Name',
        field: 'subject_id',
        sortable: true,
        replace: true,
    },
    {
        name: 'course',
        title: 'Course',
        field: 'course_id',
        sortable: true,
        replace: true,
    },
    {
        name: 'knowledge_level',
        title: 'Knowledge Level',
        field: 'knowledge_level',
        sortable: true,
        replace: true,
    },
];

const filteredColumns = computed(() => {
    return props.filters?.teacherId
        ? columns.filter((col) => col.name !== 'teacher_name')
        : columns;
});
const getBadgeVariant = (value) => {
    let badgeMapping = {
        1: 'purple',
        2: 'secondary',
    };
    return badgeMapping[value] || 'default';
};
const initFilters = () => {
    const filters = {
        courseId: null,
        knowledgeLevel: null,
        ...props.filters,
    };
    store.filters = filters;
};

onMounted(() => {
    initFilters();
});

watch(
    () => props.filters,
    (newVal) => {
        initFilters();
    }
);
</script>
