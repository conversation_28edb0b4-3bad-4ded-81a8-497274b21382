<?php

namespace App\Http\Controllers\v2\api;

use App\Exceptions\ApplicationException;
use App\Helpers\Helpers;
use App\Http\Requests\RoomRequest;
use App\Http\Requests\VenueRequest;
use App\Model\SendMail;
use App\Model\v2\AgentDocumentChecklist;
use App\Model\v2\AgentEmailTemplateSetting;
use App\Model\v2\AgentStatus;
use App\Model\v2\AssessmentDueDate;
use App\Model\v2\CampusVenue;
use App\Model\v2\CertificateAttribute;
use App\Model\v2\CertificateIdFormate;
use App\Model\v2\Checklist;
use App\Model\v2\Classroom;
use App\Model\v2\CollegeCampus;
use App\Model\v2\CollegeDetails;
use App\Model\v2\Colleges;
use App\Model\v2\Country;
use App\Model\v2\Courses;
use App\Model\v2\CourseType;
use App\Model\v2\EmailParameter;
use App\Model\v2\EmailTemplate;
use App\Model\v2\GalaxyQueue;
use App\Model\v2\GradingType;
use App\Model\v2\GteDocumentMaster;
use App\Model\v2\GteStudentDocuments;
use App\Model\v2\InterventionStrategy;
use App\Model\v2\InterventionType;
use App\Model\v2\InvoiceSetting;
use App\Model\v2\Language;
use App\Model\v2\LetterParameter;
use App\Model\v2\LetterSetting;
use App\Model\v2\OfferDocumentChecklist;
use App\Model\v2\OfferTrackingStatus;
use App\Model\v2\OnboardSetupProcess;
use App\Model\v2\OSHC;
use App\Model\v2\OSHCProvider;
use App\Model\v2\ResultGrade;
use App\Model\v2\ServiceName;
use App\Model\v2\ServicesFee;
use App\Model\v2\SetupSection;
use App\Model\v2\SetupSectionOption;
use App\Model\v2\SmtpSetup;
use App\Model\v2\StudentIdFormate;
use App\Repositories\OnboardRepository;
use App\Traits\CommonTrait;
use App\Traits\ResponseTrait;
use Domains\Customers\Settings\Models\SettingTracker;
// use DB;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use JsonException;
use Nette\Schema\ValidationException;
use Support\DTO\MailAddress;
use Support\Mails\EmailService;
use Support\Services\UploadService;

class OnboardApiController extends BaseController
{
    use CommonTrait;
    use ResponseTrait;

    protected $certificateIdFormate;

    protected $CertificateIdFormate;

    protected $colleges;

    protected $collegeDetails;

    protected $setupSection;

    protected $setupSectionOption;

    protected $offerDocumentChecklist;

    protected $agentDocumentChecklist;

    protected $campusVenue;

    protected $courses;

    protected $checklist;

    protected $offerTrackingStatus;

    protected $country;

    protected $classroom;

    protected $collegecampus;

    protected $onboardSetupProcess;

    protected $smtpSetup;

    protected $courseType;

    protected $gteDocument;

    protected $servicesFee;

    protected $servicesName;

    protected $Language;

    protected $agentStatus;

    protected $gradingType;

    protected $resultGrade;

    protected $OSHC;

    protected $OSHCProvider;

    protected $InterventionStrategy;

    protected $InterventionType;

    protected $AssessmentDueDate;

    protected $invoiceSettings;

    protected $AgentEmailTemplateSetting;

    protected $EmailTemplate;

    protected $letterSetting;

    protected $collegesModel;

    public function __construct(
        Colleges $collegesModel,
        CollegeDetails $collegeDetails,
        SetupSection $setupSection,
        SetupSectionOption $setupSectionOption,
        OfferDocumentChecklist $offerDocumentChecklist,
        AgentDocumentChecklist $agentDocumentChecklist,
        CampusVenue $campusVenue,
        Courses $courses,
        Checklist $checklist,
        OfferTrackingStatus $offerTrackingStatus,
        Country $country,
        Classroom $classroom,
        CollegeCampus $campus,
        OnboardSetupProcess $onboardSetupProcess,
        SmtpSetup $smtpSetup,
        CourseType $courseType,
        GteDocumentMaster $gteDocument,
        ServicesFee $servicesFee,
        ServiceName $serviceName,
        Language $language,
        AgentStatus $agentStatus,
        GradingType $gradingType,
        ResultGrade $resultGrade,
        OSHC $oshc,
        OSHCProvider $oshcProvider,
        InterventionStrategy $interventionstrategy,
        InterventionType $Interventiontype,
        CertificateIdFormate $certificateIdFormate,
        AssessmentDueDate $assessmentDueDate,
        InvoiceSetting $invoiceSetting,
        AgentEmailTemplateSetting $agentemailtemplatesetting,
        EmailTemplate $emailtemplate,
        LetterSetting $letterSetting
    ) {
        $this->colleges = new OnboardRepository($collegesModel);
        $this->collegeDetails = new OnboardRepository($collegeDetails);
        $this->setupSection = new OnboardRepository($setupSection);
        $this->setupSectionOption = new OnboardRepository($setupSectionOption);
        $this->offerDocumentChecklist = new OnboardRepository($offerDocumentChecklist);
        $this->agentDocumentChecklist = new OnboardRepository($agentDocumentChecklist);
        $this->campusVenue = new OnboardRepository($campusVenue);
        $this->courses = new OnboardRepository($courses);
        $this->checklist = new OnboardRepository($checklist);
        $this->offerTrackingStatus = new OnboardRepository($offerTrackingStatus);
        $this->country = new OnboardRepository($country);
        $this->classroom = new OnboardRepository($classroom);
        $this->collegecampus = new OnboardRepository($campus);
        $this->onboardSetupProcess = new OnboardRepository($onboardSetupProcess);
        $this->smtpSetup = new OnboardRepository($smtpSetup);
        $this->courseType = new OnboardRepository($courseType);
        $this->gteDocument = new OnboardRepository($gteDocument);
        $this->servicesFee = new OnboardRepository($servicesFee);
        $this->servicesName = new OnboardRepository($serviceName);
        $this->Language = new OnboardRepository($language);
        $this->agentStatus = new OnboardRepository($agentStatus);
        $this->gradingType = new OnboardRepository($gradingType);
        $this->resultGrade = new OnboardRepository($resultGrade);
        $this->OSHC = new OnboardRepository($oshc);
        $this->OSHCProvider = new OnboardRepository($oshcProvider);
        $this->InterventionStrategy = new OnboardRepository($interventionstrategy);
        $this->InterventionType = new OnboardRepository($Interventiontype);
        $this->CertificateIdFormate = new OnboardRepository($certificateIdFormate);
        $this->AssessmentDueDate = new OnboardRepository($assessmentDueDate);
        $this->invoiceSettings = new OnboardRepository($invoiceSetting);
        $this->AgentEmailTemplateSetting = new OnboardRepository($agentemailtemplatesetting);
        $this->EmailTemplate = new OnboardRepository($emailtemplate);
        $this->letterSetting = new OnboardRepository($letterSetting);
    }

    public function stateData(Request $request)
    {
        $stateList = Config::get('constants.arrTraningOrgazinationState');
        foreach ($stateList as $key => $value) {
            $data[] = ['Id' => $key, 'Name' => $value];
        }

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getSection(Request $request)
    {
        $sectionData = $this->setupSectionOption->selectdata(['id as Id', 'name as Name'])->get()->toArray();

        return $this->successResponse('Data found successfully', 'data', $sectionData);
    }

    public function getCountry(Request $request)
    {
        $countryData = $this->country->selectdata(['id as Id', 'name as Name'])->get()->toArray();

        return $this->successResponse('Data found successfully', 'data', $countryData);
    }

    public function getTrainingLocation(Request $request)
    {
        $roomData = $this->campusVenue->selectdata(['id as Id', DB::raw('CONCAT(rto_venue.state,",",rto_venue.postcode) AS Name')])->get()->toArray();

        return $this->successResponse('Data found successfully', 'data', $roomData);
    }

    public function getVenueLocation(Request $request)
    {
        $data = $this->campusVenue->with(['campus'])->where('college_id', $request->college_id)->get()->toarray();
        $resArr = [];
        foreach ($data as $row) {
            $resArr[] = [
                'Id' => $row['id'],
                'Name' => $row['state'].(isset($row['campus'][0]['name']) ? ', '.$row['campus'][0]['name'] : '').(! empty($row['postcode']) ? ', '.$row['postcode'] : ''),
            ];
        }

        return $this->successResponse('Data found successfully', 'data', $resArr);
    }

    public function getCampusList(Request $request)
    {
        $campus = $this->collegecampus->getWhere(['college_id' => $request->college_id, 'status' => 1], ['name as Name', 'id as Id'])->toArray();

        return $this->successResponse('Data found successfully', 'data', $campus);
    }

    public function getVenueList(Request $request)
    {
        $venueArr = $this->campusVenue->getWhere(['college_id' => $request->college_id])->toArray();
        $venue = [];
        foreach ($venueArr as $row) {
            $venue[] = [
                'Id' => $row['id'],
                'Name' => $row['venue_code'].' : '.$row['venue_name'],
            ];
        }

        return $this->successResponse('Data found successfully', 'data', $venue);
    }

    public function getCampusDataForEdit(Request $request)
    {

        $campus = CollegeCampus::find($request->id);

        return $this->successResponse('Data found successfully', 'data', $campus);
    }

    public function updateCampusData(Request $request)
    {
        $data['status_default'] = (isset($request->status_default) && $request->status_default == 'on') ? 1 : 0;
        $data['status'] = (isset($request->status) && $request->status == 'on') ? 1 : 0;
        $data['name'] = $request->name;
        $campus = CollegeCampus::where('id', $request->id)->update($data);

        return $this->successResponse('Update successfully', 'data', $campus);
    }

    public function deleteCampusDetails(Request $request)
    {
        $primaryId = $request->input('id');
        if (! empty($primaryId)) {

            $venueCount = CampusVenue::where(['campus_id' => $primaryId, 'set_active' => 1])->count();
            if ($venueCount == 0) {
                $res = CollegeCampus::where('id', $primaryId)->delete();

                return $this->successResponse('Delete successfully.', 'data', $res);
            } else {
                return $this->errorResponse('Campus cannot be deleted.', 'data', [], 200);
            }
        } else {
            return $this->errorResponse('Record not found', 'data', [], 200);
        }
    }

    public function getCampusDeatils(Request $request)
    {

        $campus = CollegeCampus::from('rto_campus as rc')
            ->leftjoin('rto_venue as rv', 'rv.campus_id', '=', 'rc.id')
            // ->leftjoin('rto_classroom', 'rto_classroom.venue_id', '=', 'rv.id')
            ->select('rc.id', 'rc.name as campus_name', DB::raw('GROUP_CONCAT( rv.id) AS venue_ids'), DB::raw('COUNT( rv.id) AS total_venue'))
            ->groupBy('rc.id')
            ->orderBy('rc.name', 'ASC')
            ->get()
            ->toarray();

        foreach ($campus as $key => $value) {
            $campus[$key]['total_room'] = Classroom::WhereIN('venue_id', explode(',', $value['venue_ids']))->get()->count();
        }

        return $this->successResponse('Data found successfully', 'data', $campus);
    }

    public function SectionType(Request $request)
    {
        $type = $request->sectionType;
        $sectionType = $this->setupSectionOption->with(['ee'])->where('id', $type)->get()->toarray();
        if ($sectionType) {
            $resArr = [];
            foreach ($sectionType[0]['ee'] as $row) {
                $resArr[] = [
                    'value' => $row['id'],
                    'text' => $row['type_name'],
                ];
            }

            return $this->successResponse('Data found successfully', 'data', $resArr);
        } else {
            return $this->errorResponse('No data found', 'data', []);
        }
    }

    public function sectionCourseType(Request $request)
    {
        $whereArr = ['college_id' => $request->college_id];
        $courseType = $this->courses->getWhere($whereArr, [DB::raw('CONCAT(rto_courses.course_code,":",rto_courses.course_name) AS text'), 'id as value'])->toArray();

        return $this->successResponse('data found successfully', 'data', $courseType);
    }

    public function sectionVenueList(Request $request)
    {
        $whereArr = ['college_id' => $request->college_id];
        $venueList = $this->campusVenue->getWhere($whereArr, [DB::raw('CONCAT(rto_venue.state,":",rto_venue.venue_code) AS text'), DB::raw('CONCAT(rto_venue.state,":",rto_venue.venue_code) AS value')])->toArray();

        return $this->successResponse('data found successfully', 'data', $venueList);
    }

    public function sectionData(Request $request)
    {
        $data['data'] = $this->setupSection->getSectionData($request);
        $data['total'] = $this->setupSection->getSectionData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function OfferLetterData(Request $request)
    {
        $data['data'] = $this->offerDocumentChecklist->getOfferLetterData($request);
        $data['total'] = $this->offerDocumentChecklist->getOfferLetterData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function agentDocumentsData(Request $request)
    {
        $data['data'] = $this->agentDocumentChecklist->getAgentDocumentsData($request);
        $data['total'] = $this->agentDocumentChecklist->getAgentDocumentsData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function offerTrackingData(Request $request)
    {
        $data['data'] = $this->offerTrackingStatus->getOfferTrackingData($request);
        $data['total'] = $this->offerTrackingStatus->getOfferTrackingData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function customChecklistData(Request $request)
    {
        $data['data'] = $this->checklist->getCustomCheckListData($request);
        $data['total'] = $this->checklist->getCustomCheckListData($request, true);

        return $this->successResponse('Data found successfully', 'data', mb_convert_encoding($data, 'UTF-8', 'UTF-8'));
    }

    public function venueData(Request $request)
    {
        $data['data'] = $this->campusVenue->getVenueData($request);
        $data['total'] = $this->campusVenue->getVenueData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function roomData(Request $request)
    {
        $data['data'] = $this->classroom->getRoomData($request);
        $data['total'] = $this->classroom->getRoomData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function updateIsDefault(Request $request)
    {
        $id = $request->id;
        $typeId = $request->type;
        $college_id = $request->college_id;
        $setStatus = $this->setupSection->Where(['college_id' => $college_id, 'type' => $typeId])->update(['is_default' => '0']);
        $updateStatus = $this->setupSection->Where(['college_id' => $college_id, 'type' => $typeId, 'id' => $id])->update(['is_default' => '1']);

        return $this->successResponse('Data found successfully', 'data', $updateStatus);
    }

    private function returnResponse($res, $successMsg)
    {
        if ($res) {
            return $this->successResponse($successMsg, 'data');
        } else {
            return $this->errorResponse('Something will be wrong. Please try again.', 'data');
        }
    }

    public function updateRoomStatus(Request $request)
    {
        $setActive = ($request->status == 1) ? 0 : 1;

        $res = Classroom::Where('id', $request->id)->update(['status' => $setActive]);

        return $this->returnResponse($res, 'Room status update successfully.');
    }

    public function updateVenueStatus(Request $request)
    {
        $setActive = ($request->set_active == 1) ? 0 : 1;
        if (! $setActive) {
            $roomCount = Classroom::Where('venue_id', $request->id)->where('status', 1)->get()->count();
            if ($roomCount > 0) {
                return $this->errorResponse('Venue can not be deactive', 'data', [], 200);
            }
        }
        $res = $this->campusVenue->Where(['college_id' => $request->college_id, 'id' => $request->id])->update(['set_active' => (($request->set_active == 1) ? '0' : '1')]);

        $resultAll = CampusVenue::where(['campus_id' => $request->campusId])->get()->toArray();
        if (count($resultAll) > 0) {
            $result = CampusVenue::where(['campus_id' => $request->campusId, 'set_active' => 1])->get()->toArray();
            if (count($result) == 0) {
                CollegeCampus::where('id', $request->campusId)->update(['status' => 0]);
            } else {
                CollegeCampus::where('id', $request->campusId)->update(['status' => 1]);
            }
        }

        return $this->returnResponse($res, 'Venue status update successfully.');
    }

    public function getOfferDocument(Request $request)
    {
        $fields = ['document_name', 'document_type', 'student_origin', 'is_compulsory', 'is_active'];
        $data = $this->offerDocumentChecklist->getWhere(['id' => $request->id], $fields)->first();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getAgentDocument(Request $request)
    {
        $data = $this->agentDocumentChecklist->getWhere(['id' => $request->id], ['document_name', 'is_compulsory', 'is_active'])->first();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getOfferTracking(Request $request)
    {
        $data = $this->offerTrackingStatus->getWhere(['id' => $request->id])->first();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCustomChecklist(Request $request)
    {
        $data = $this->checklist->getWhere(['id' => $request->id], ['mandatory', 'checklist_name', 'description', 'checklist_for'])->first();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCollegeDetail(Request $request)
    {
        $data = $this->collegeDetails->getWhere(['college_id' => $request->college_id])->first();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getVenueDetail(Request $request)
    {
        $data = $this->campusVenue->getWhere(['id' => $request->id])->first();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getRoomDetail(Request $request)
    {
        $data = $this->classroom->getWhere(['id' => $request->id])->first();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function deleteOfferLetter(Request $request)
    {
        $data = $this->offerDocumentChecklist->delete($request->id);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function deleteAgentDocument(Request $request)
    {
        $data = $this->agentDocumentChecklist->delete($request->id);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function deleteOfferTracking(Request $request)
    {
        $data = $this->offerTrackingStatus->delete($request->id);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function deleteCustomChecklist(Request $request)
    {
        $data = $this->checklist->delete($request->id);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function deleteVenueDetails(Request $request)
    {
        $res = $this->campusVenue->delete($request->id);
        if ($res) {
            $this->classroom->where(['venue_id' => $request->id])->delete($request->id);
        }

        return $this->returnResponse($res, 'Venue address delete successfully.');
    }

    public function deleteVenueAddress(Request $request)
    {
        $data = ['building_name' => '', 'unit_detail' => '', 'street_no' => '', 'street_name' => '', 'sub_urb' => '', 'country' => '', 'state' => '', 'postcode' => ''];
        if ($request->id) {
            $res = $this->campusVenue->update($data, $request->id);

            return $this->returnResponse($res, 'Venue address delete successfully.');
        } else {
            return $this->errorResponse('Something will be wrong. Please try again.', 'data');
        }
    }

    public function deleteRoomDetails(Request $request)
    {
        $res = $this->classroom->delete($request->id);

        return $this->returnResponse($res, 'Room delete successfully.');
    }

    public function uploadCollegeLogo(Request $request)
    {
        $collegeId = $request->post('college_id');
        $req = $request->input('metadata', []);
        $uploadData = json_decode($req, true);
        $originalFileName = $uploadData['fileName'];

        $collegeLogo = $request->file();
        if (isset($collegeLogo['file'])) {
            $file = $collegeLogo['file'];
            $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
            $destinationPath = Helpers::changeRootPath($filePath, null, $collegeId);

            $college_name = str_replace(' ', '_', $request->input('college_name'));
            $logo_name = str_replace(' ', '_', hashFileName($originalFileName));
            $filename = 'logo'.'-'.$college_name.'-'.$logo_name;

            // $res = $file->move($destinationPath['default'], $filename);
            $res = UploadService::uploadAs($destinationPath['view'], $file, $filename);
            info('file uploaded form College Logo', [$res]);
            if ($res && ! empty($collegeId)) {
                $this->colleges->update(['college_logo' => $filename], $collegeId);
                echo json_encode(['uploaded' => true, 'fileUid' => $filename, 'status' => 'success', 'message' => 'College logo upload successfully.']);
                exit;
            }
        }
        echo json_encode(['status' => 'error', 'message' => 'Something will be wrong. Please try again.']);
        exit;
    }

    public function uploadSignatureLogo(Request $request)
    {
        $collegeId = $request->post('college_id');
        $req = $request->input('metadata');
        $uploadData = json_decode($req, true);
        $originalFileName = $uploadData['fileName'];

        $collegeLogo = $request->file();
        if (isset($collegeLogo['file'])) {
            $file = $collegeLogo['file'];
            $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
            $destinationPath = Helpers::changeRootPath($filePath, null, $collegeId);

            $college_name = str_replace(' ', '_', $request->input('college_name'));
            $logo_name = str_replace(' ', '_', hashFileName($originalFileName));
            $filename = 'signature'.'-'.$college_name.'-'.$logo_name;

            // $res = $file->move($destinationPath['default'], $filename);
            $res = UploadService::uploadAs($destinationPath['view'], $file, $filename);
            info('file uploaded form Signature', [$res]);
            if ($res && ! empty($collegeId)) {
                $this->colleges->update(['college_signature' => $filename], $collegeId);
                echo json_encode(['uploaded' => true, 'fileUid' => $filename, 'status' => 'success', 'message' => 'college signature upload successfully.']);
                exit;
            }
        }
        echo json_encode(['status' => 'error', 'message' => 'Something will be wrong. Please try again.']);
        exit;
    }

    public function uploadDeanSignature(Request $request)
    {
        $collegeId = $request->post('college_id');
        $req = $request->input('metadata');
        $uploadData = json_decode($req, true);
        $originalFileName = $uploadData['fileName'];

        $collegeLogo = $request->file();
        if (isset($collegeLogo['file'])) {
            $file = $collegeLogo['file'];
            $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
            $destinationPath = Helpers::changeRootPath($filePath, null, $collegeId);

            $college_name = str_replace(' ', '_', $request->input('college_name'));
            $logo_name = str_replace(' ', '_', hashFileName($originalFileName));
            $filename = 'dean_signature'.'-'.$college_name.'-'.$logo_name;

            // $res = $file->move($destinationPath['default'], $filename);
            $res = UploadService::uploadAs($destinationPath['view'], $file, $filename);
            info('file uploaded form Dean Signature', [$res]);
            if ($res && ! empty($collegeId)) {
                $this->colleges->update(['dean_signature' => $filename], $collegeId);
                echo json_encode(['uploaded' => true, 'fileUid' => $filename, 'status' => 'success', 'message' => 'Dean signature upload successfully.']);
                exit;
            }
        }
        echo json_encode(['status' => 'error', 'message' => 'Something will be wrong. Please try again.']);
        exit;
    }

    public function uploadAdmissionManagerSignature(Request $request)
    {
        $collegeId = $request->post('college_id');
        $req = $request->input('metadata');
        $uploadData = json_decode($req, true);
        $originalFileName = $uploadData['fileName'];

        $collegeLogo = $request->file();
        if (isset($collegeLogo['file'])) {
            $file = $collegeLogo['file'];
            $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
            $destinationPath = Helpers::changeRootPath($filePath, null, $collegeId);

            $college_name = str_replace(' ', '_', $request->input('college_name'));
            $logo_name = str_replace(' ', '_', hashFileName($originalFileName));
            $filename = 'admission_manager_signature'.'-'.$college_name.'-'.$logo_name;

            // $res = $file->move($destinationPath['default'], $filename);
            $res = UploadService::uploadAs($destinationPath['view'], $file, $filename);
            info('file uploaded form Dean Admission Manager Signature', [$res]);
            if ($res && ! empty($collegeId)) {
                $this->colleges->update(['admission_manager_signature' => $filename], $collegeId);
                echo json_encode(['uploaded' => true, 'fileUid' => $filename, 'status' => 'success', 'message' => 'Admission Manager signature upload successfully.']);
                exit;
            }
        }
        echo json_encode(['status' => 'error', 'message' => 'Something will be wrong. Please try again.']);
        exit;
    }

    public function uploadStudentSupportSignature(Request $request)
    {
        $collegeId = $request->post('college_id');
        $req = $request->input('metadata');
        $uploadData = json_decode($req, true);
        $originalFileName = $uploadData['fileName'];

        $collegeLogo = $request->file();
        if (isset($collegeLogo['file'])) {
            $file = $collegeLogo['file'];
            $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
            $destinationPath = Helpers::changeRootPath($filePath, null, $collegeId);

            $college_name = str_replace(' ', '_', $request->input('college_name'));
            $logo_name = str_replace(' ', '_', hashFileName($originalFileName));
            $filename = 'student_support_signature'.'-'.$college_name.'-'.$logo_name;

            // $res = $file->move($destinationPath['default'], $filename);
            $res = UploadService::uploadAs($destinationPath['view'], $file, $filename);
            info('file uploaded form Student Support Signature', [$res]);
            if ($res && ! empty($collegeId)) {
                $this->colleges->update(['student_support_signature' => $filename], $collegeId);
                echo json_encode(['uploaded' => true, 'fileUid' => $filename, 'status' => 'success', 'message' => 'Student support signature upload successfully.']);
                exit;
            }
        }
        echo json_encode(['status' => 'error', 'message' => 'Something will be wrong. Please try again.']);
        exit;
    }

    public function removeCollegeLogo(Request $request)
    {
        $collegeId = $request->post('college_id');
        if (! empty($collegeId)) {
            $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
            $destinationPath = Helpers::changeRootPath($filePath, null, $collegeId);

            $fileName = Colleges::find($collegeId)->college_logo;
            UploadService::delete($destinationPath['default'], $fileName);
            // $dirPath = public_path() . $destinationPath['view'];
            // dd($dirPath);
            // if (is_dir($dirPath)) {
            //     $this->delete_directory($dirPath);
            // }
        }

        $result = ['status' => 'success', 'message' => 'College logo remove successfully.'];
        echo json_encode($result);
        exit;
    }

    public function removeSignatureLogo(Request $request)
    {
        $collegeId = $request->post('college_id');
        if (! empty($collegeId)) {
            $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
            $destinationPath = Helpers::changeRootPath($filePath, null, $collegeId);
            $dirPath = public_path().$destinationPath['view'];
            if (is_dir($dirPath)) {
                $this->delete_directory($dirPath);
            }
        }
        $result = ['status' => 'success', 'message' => 'Signature remove successfully.'];
        echo json_encode($result);
        exit;
    }

    public function delete_directory($dirname)
    {
        if (is_dir($dirname)) {
            $dir_handle = opendir($dirname);
        }
        if (! $dir_handle) {
            return false;
        }
        while ($file = readdir($dir_handle)) {
            if ($file != '.' && $file != '..') {
                if (! is_dir($dirname.'/'.$file)) {
                    unlink($dirname.'/'.$file);
                } else {
                    $this->delete_directory($dirname.'/'.$file);
                }
            }
        }
        closedir($dir_handle);
        rmdir($dirname);

        return true;
    }

    public function getCourseType(Request $request)
    {
        $data['data'] = $this->courseType->getCourseType($request);
        $data['total'] = $this->courseType->getCourseType($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseTypeName(Request $request)
    {
        $data = $this->courseType->Where(['status' => 1])->whereIn('college_id', [Auth::user()->college_id, 0])->select('title as Name', 'id as Id')->get()->toArray();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function updateCourseTypeStatus(Request $request)
    {
        if ($request->status == 1) {
            $updateStatus = $this->courseType->Where(['id' => $request->id])->update(['status' => '0']);
        } else {
            $updateStatus = $this->courseType->Where(['id' => $request->id])->update(['status' => '1']);
        }

        return $this->successResponse('Course Status Updated successfully', 'data', $updateStatus);
    }

    public function getCountryDetails(Request $request)
    {
        $fields = ['name', 'countrycode', 'countrylevel', 'nationality', 'region', 'absvalue'];
        $data = $this->country->getWhere(['id' => $request->id], $fields)->first();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCountryData(Request $request)
    {
        $data['data'] = $this->country->getCountryList($request);
        $data['total'] = $this->country->getCountryList($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function gteDocumentData(Request $request)
    {
        $data['data'] = $this->gteDocument->gteDocumentData($request);
        $data['total'] = $this->gteDocument->gteDocumentData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getGteDocumentDetails(Request $request)
    {
        $fields = ['document_name'];
        $data = $this->gteDocument->getWhere(['id' => $request->id], $fields);
        echo json_encode($data);
        exit;
    }

    public function gteAddedServicesFeeData(Request $request)
    {
        $data['data'] = $this->servicesFee->gteAddedServicesFeeData($request);
        $data['total'] = $this->servicesFee->gteAddedServicesFeeData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getServicesName(Request $request)
    {
        $data = $this->servicesName->selectdata(['id as Id', 'service_name as Name'])->get()->toArray();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getServicesFeeDetails(Request $request)
    {
        $fields = ['service_name', 'service_fee'];
        $data = $this->servicesFee->getWhere(['id' => $request->id], $fields)->first();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getLanguageData(Request $request)
    {
        $data['data'] = $this->Language->getLanguageData($request);
        $data['total'] = $this->Language->getLanguageData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getLanguageDetails(Request $request)
    {
        $fields = ['name', 'abs_value'];
        $data = $this->Language->getWhere(['id' => $request->id], $fields)->first();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getAgentStatusData(Request $request)
    {
        $data['data'] = $this->agentStatus->getAgentStatusData($request);
        $data['total'] = $this->agentStatus->getAgentStatusData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getAgentStatusDetails(Request $request)
    {
        $fields = ['status_type', 'duration', 'publish'];
        $data = $this->agentStatus->getWhere(['id' => $request->id], $fields)->first();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getGradingTypeName(Request $request)
    {
        $data = $this->gradingType->selectdata(['id as Id', 'grading_name as Name'])->get()->toArray();
        $dataadd = [['Id' => 0, 'Name' => 'Add New Grade Type as Name']];

        return $this->successResponse('Data found successfully', 'data', array_merge($data, $dataadd));
    }

    public function competencyGradeData(Request $request)
    {
        $data['data'] = $this->resultGrade->getResultGradeData($request);
        $data['total'] = $this->resultGrade->getResultGradeData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getResultGradeDetails(Request $request)
    {
        $fields = ['grading_type', 'use_marks', 'force_matching_outcome_code', 'maximum_marks', 'marks', 'grade_title', 'trascript_display', 'grade', 'grade_point', 'competency_calculation', 'final_outcome_code', 'unit_of_study_status_code', 'description'];
        $data = $this->resultGrade->getWhere(['id' => $request->id], $fields)->first();
        $data['use_marks'] = (isset($data->use_marks) && $data->use_marks == '1') ? 'Yes' : 'No';
        $data['force_matching_outcome_code'] = (isset($data->force_matching_outcome_code) && $data->force_matching_outcome_code == '1') ? 'Yes' : 'No';
        $data['competency_calculation'] = (isset($data->competency_calculation) && $data->competency_calculation == '1') ? 'Yes' : 'No';

        // $data['final_outcome_code'] = (isset($data->final_outcome_code) && $data->final_outcome_code == '20') ? 0 : 1;
        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function deleteResultGrade(Request $request)
    {
        $data = $this->resultGrade->delete($request->id);
        $result = ['status' => 'success', 'message' => 'Result Grade Deleted Successfully'];
        echo json_encode($result);
        exit;
    }

    public function getOSHCInfoData(Request $request)
    {
        $data['data'] = $this->OSHC->getOSHCInfoData($request);
        $data['total'] = $this->OSHC->getOSHCInfoData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getOSHCProviderName(Request $request)
    {
        $data = $this->OSHCProvider->selectdata(['id as Id', 'provider_name as Name'])->get()->toArray();
        if ($request->edit == 0) {
            $dataadd = [['Id' => 0, 'Name' => 'Add New Provider Name']];

            return $this->successResponse('Data found successfully', 'data', array_merge($data, $dataadd));
        }

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getOSHCDuration(Request $request)
    {
        for ($i = 1; $i <= 60; $i++) {
            $data[] = ['Id' => $i, 'Name' => $i];
        }

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function updateOSHCstatus(Request $request)
    {
        $updateStatus = $this->OSHC->updateOSHCActiveStatus($request->input());

        return $this->successResponse('OSHC Status Update successfully', 'data', $updateStatus);
    }

    public function getInterventionStrategyData(Request $request)
    {
        $data['data'] = $this->agentStatus->getInterventionStrategyData($request);
        $data['total'] = $this->agentStatus->getInterventionStrategyData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getInterventionType(Request $request)
    {
        $data = $this->InterventionType->selectdata(['id as Id', 'intervention_type as Name'])->get()->toArray();
        $customeArray = [['Id' => 0, 'Name' => 'Add New Intervention Strategy Type']];

        return $this->successResponse('Data found successfully', 'data', array_merge($data, $customeArray));
    }

    public function getCertificateIdFormateData(Request $request)
    {
        $data['data'] = $this->CertificateIdFormate->getCertificateIdFormateData($request);
        $data['total'] = $this->CertificateIdFormate->getCertificateIdFormateData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCertificateTypeList(Request $request)
    {
        $certificateTypeList = Config::get('constants.certificateType');
        foreach ($certificateTypeList as $key => $value) {
            $data[] = ['value' => $key, 'text' => $value];
        }

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCertificateIdFormateDetails(Request $request)
    {
        $fields = ['type', 'prefix', 'auto_number', 'suffix', 'id'];
        $data = $this->CertificateIdFormate->getWhere(['college_id' => Auth::user()->college_id, 'id' => $request->id], $fields)->first();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getAssessmentDueDateDetails(Request $request)
    {
        $data = $this->AssessmentDueDate->getData(['college_id' => $request->college_id]);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getInvoiceSettingDetails(Request $request)
    {
        $data = $this->invoiceSettings->getValue([
            'college_id' => $request->college_id,
            'key' => $request->key,
        ], 'value');

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function setInvoiceSettingData(Request $request)
    {
        $request->validate([
            'value' => 'required|integer|min:0', // for days_before_due_date only
        ], [
            'value.required' => 'The days before due date field is required.',
            'value.integer' => 'The days value must be an integer.',
            'value.min' => 'The value must be at least 1 day.',
        ]);
        $whereArr = [
            'college_id' => $request->college_id,
            'key' => $request->key,
        ];
        $data['created_by'] = Auth::user()->id;
        $data['updated_by'] = Auth::user()->id;
        $data['value'] = $request->value;

        try {
            // $data = $this->invoiceSettings->modify(['value' => $request->value], $whereArr);
            $data = InvoiceSetting::updateOrCreate($whereArr, $data);

            return $this->successResponse('Update successfully', 'data', $data);
        } catch (ValidationException $e) {
            return $this->errorResponse('Fail to Update', 'data', []);
        }
    }

    public function getAgentTemplateName(Request $request)
    {
        $data = $this->EmailTemplate->selectdata(['id as Id', 'template_name as Name'])->get()->toArray();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getNotificationTemplateDetails(Request $request)
    {
        $fields = ['template_id'];
        $emailTemplateId = 1;
        $data = $this->AgentEmailTemplateSetting->getWhere(['id' => $emailTemplateId], $fields)->first();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getLetterDetails(Request $request)
    {
        $fields = ['header', 'footer', 'watermark', 'college_id'];
        $collegeId = ($request->college_id) ?? Auth::user()->college_id;
        if ($collegeId > 0) {
            $data = $this->letterSetting->getWhere(['college_id' => $collegeId], $fields)->first();
            if ($data) {
                $filePath = Config::get('constants.uploadFilePath.LetterSetting');
                $destinationPath = Helpers::changeRootPath($filePath, null, $data->college_id);
                $data->file_name = $data->watermark;
                // $data->watermark = fileUrl($destinationPath['view'] . $data->watermark);
                $data->watermark = UploadService::preview($destinationPath['view'].$data->watermark);

                return $this->successResponse('Data found successfully', 'data', $data);
            } else {
                return $this->successResponse('No data found', 'data', []);
            }
        }

        return $this->errorResponse('Something will be wrong. Please try again.', 'data', [], 200);
    }

    public function getLetterDefaultParameter(Request $request)
    {
        $data = LetterParameter::select('*')->get()->toArray();
        $resultData = array_map(function ($item) {
            try {
                if (! empty($item['parameter_detail']) && is_string($item['parameter_detail'])) {
                    $item['parameter_detail'] = json_decode($item['parameter_detail'], true, 512, JSON_THROW_ON_ERROR);
                } else {
                    $item['parameter_detail'] = [];
                }
            } catch (JsonException $e) {
                $item['parameter_detail'] = [];
            }

            return $item;
        }, $data);

        return $this->successResponse('Data found successfully', 'data', $resultData);
    }

    public function getEmailDefaultParameter(Request $request)
    {
        $data = EmailParameter::select('*')->get()->toArray();
        $resultData = array_map(function ($item) {
            try {
                if (! empty($item['parameter_detail']) && is_string($item['parameter_detail'])) {
                    $item['parameter_detail'] = json_decode($item['parameter_detail'], true, 512, JSON_THROW_ON_ERROR);
                } else {
                    $item['parameter_detail'] = [];
                }
            } catch (JsonException $e) {
                $item['parameter_detail'] = [];
            }

            return $item;
        }, $data);

        return $this->successResponse('Data found successfully', 'data', $resultData);
    }

    public function getCertificateDefaultAttributes(Request $request)
    {
        $templateType = $request->input('template_type', 'certificate');

        $data = CertificateAttribute::select('*')
            ->where('template_type', $templateType)
            ->get()
            ->toArray();

        $resultData = array_map(function ($item) {
            // Add sample data based on attribute type and tag
            $sampleValue = $this->generateSampleAttributeValue($item['tag'], $item['type']);

            $item['sample_value'] = $sampleValue;
            $item['parameter_value'] = '['.$item['tag'].']';

            return $item;
        }, $data);

        return $this->successResponse('', 'data', $resultData);
    }

    private function generateSampleAttributeValue($tag, $type)
    {
        // Generate realistic sample data based on the attribute tag and type
        $sampleData = [
            // Student attributes
            'student.firstname' => 'John',
            'first_name' => 'John',
            'student.lastname' => 'Smith',
            'last_name' => 'Smith',
            'student.fullname' => 'John Smith',
            'full_name' => 'John Smith',
            'student.dob' => '15 March 1995',
            'date_of_birth' => '15 March 1995',
            'student.id' => 'STU001234',
            'student_id' => 'STU001234',
            'email' => '<EMAIL>',
            'phone' => '+61 412 345 678',
            'address' => '123 Main Street, Sydney NSW 2000',
            'profile_picture' => 'student_photo.jpg',

            // Course attributes
            'course.code' => 'BSB50420',
            'course.name' => 'Diploma of Leadership and Management',
            'cricos.course.code' => 'CRC95123G',
            'studentcourse.course_startdate' => '01 Feb 2024',
            'studentcourse.course_enddate' => '31 Jan 2025',
            'exp_date' => '31 Jan 2027',
            'course.delivery_mode' => 'Online, Blended',
            'course.duration' => '4 Weeks / 28 Days / 1 Month',
            'course.certificate_text' => 'Introduction to Customer Service, Leadership Fundamentals, Basic Barista Skills, etc.',

            // Certificate attributes
            'certificate.expired_on' => '31 Jan 2027',
            'certificate.issued_on' => '15 March 2025',
            'certificate.uuid' => 'CERT-2025-001234',
            'issue_date' => '15/02/2025',

            // Unit attributes
            'unit.list' => 'Unit enrollment list will be displayed in table format',
            'unit.list.with.result' => 'Unit enrollment list with results will be displayed in table format',
            'unit.interim.table' => 'Interim assessment results will be displayed in table format',

            // College attributes
            'college_name' => 'Churchill Institute',
            'college_logo' => 'churchill_logo.png',

            'college.name' => 'Kingswell Institute',
            'college.signature' => $this->sampleSignatureImage('sign1.png', 'college_signature.png'),
            'college.dean_name' => 'Dr. Michael Seamer',
            'college.dean_signature' => $this->sampleSignatureImage('sign2.jpg', 'dean_signature.png'),
            'college.admission_manager_signature' => $this->sampleSignatureImage('sign1.png', 'admission_manager_signature.png'),
            'college.student_support_signature' => $this->sampleSignatureImage('sign2.jpg', 'student_support_signature.png'),
        ];

        // Return specific sample data if available, otherwise generate based on type
        if (isset($sampleData[$tag])) {
            return $sampleData[$tag];
        }

        // Fallback based on type
        switch ($type) {
            case 'text':
                return 'Sample Text Value';
            case 'date':
                return date('d M Y');
            case 'table':
                return 'Table data will be displayed in structured format';
            case 'image':
                return 'sample_image.jpg';
            default:
                return 'Sample Value';
        }
    }

    private function sampleSignatureImage($imgName, $defaultText)
    {
        $defaultAvatarPath = public_path('dist/img/'.$imgName);
        if (file_exists($defaultAvatarPath)) {
            $imageData = file_get_contents($defaultAvatarPath);
            $imgEmbedPath = 'data:image/png;base64,'.base64_encode($imageData);

            return '<img src="'.$imgEmbedPath.'" alt="Signature" style="height: auto; width: 150px;" />';
        }

        return $defaultText;
    }

    public function getTemplateDetails(Request $request)
    {
        $fields = ['email_subject', 'content'];
        $data = $this->EmailTemplate->getWhere(['id' => $request->template_id], $fields)->first();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getStateList(Request $request)
    {
        $arrState = Config::get('constants.onboardSetupStateList');
        foreach ($arrState as $key => $value) {
            $data[] = ['Id' => $key, 'Name' => $value];
        }

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function ajaxAction(Request $request)
    {

        $action = $request->input('action');
        $data = $request->input('data');
        unset($data['tracking_form']);

        switch ($action) {

            case 'general_info':
                return $this->saveGeneralInfo($data);
            case 'college_url':
                return $this->saveCollegeURL($data);
            case 'training_identify':
                return $this->saveTrainingIdentifier($data);
            case 'main_location':
                return $this->saveCollegeAddress($data);
            case 'same_as_residential_address':
                return $this->sameAsResidentialAddress($data);
            case 'remove_address':
                $this->removeCollegeAddress($data);
                break;
            case 'bank_detail':
                $result = ['status' => 'success', 'message' => 'Bank details save successfully.'];
                echo json_encode($result);
                exit;
            case 'bank_details':
                return $this->saveBankDetail($data);
            case 'remove_bank_detail':
                $this->removeBankDetail($data);
                break;
            case 'vsl_info':
                $this->saveVslInfo($data);
                break;
            case 'reset_vsl':
                $this->resetVslInfo($data);
                break;
            case 'saveOfferDocuments':
                $this->saveOfferDocuments($data);
                break;
            case 'manage_section':
                $this->saveSetupSection($data);
                break;
            case 'saveAgentDocuments':
                return $this->saveAgentDocuments($data);
            case 'offer_leter':
                $data = $request->input('data');
                $result = ['status' => 'success'];
                echo json_encode($result);
                exit;
            case 'agent_document':
                $data = $request->input('data');
                $result = ['status' => 'success'];
                echo json_encode($result);
                exit;
            case 'offer_track':
                $data = $request->input('data');
                $result = ['status' => 'success'];
                echo json_encode($result);
                exit;
            case 'saveOfferTrackingStatus':
                $this->saveOfferTrackingStatus($data);
                break;
            case 'saveCustomChecklist':
                $this->saveCustomChecklist($data);
                break;
            case 'custom_checklist':
                $data = $request->input('data');
                $result = ['status' => 'success'];
                echo json_encode($result);
                exit;
            case 'saveVenueDetails':
                $this->saveVenueDetails($data);
                break;
            case 'saveVenueAddress':
                $this->saveVenueAddress($data);
                break;
            case 'saveRoom':
                return $this->saveRoom($data);
            case 'savecampus':
                return $this->saveCampus($data);
            case 'register':
                $this->onboardStatusUpdate($data);
                break;
            case 'save_smtp_info':
                $this->saveSmtpInfo($data);
                break;
            case 'addCountry':
                $this->addCountry($data);
                break;
            case 'editCountry':
                $this->editCountry($data);
                break;
            case 'deleteCountry':
                $this->deleteCountry($data);
                break;
            case 'save_gtedocument':
                $this->saveGteDocument($data);
                break;
            case 'remove_gtedocument':
                $this->removeGteDocument($data);
                break;
            case 'added_services_fee':
                $this->addedServicesFee($data);
                break;
            case 'remove_services_fee':
                $this->removeServicesFee($data);
                break;
            case 'add_language':
                $this->addLanguage($data);
                break;
            case 'edit_language':
                $this->editLanguage($data);
                break;
            case 'delete_language':
                $this->deleteLanguage($data);
                break;
            case 'add_agent_status':
                return $this->addAgentStatus($data);
            case 'delete_agent_status':
                $this->deleteAgentStatus($data);
                break;
            case 'default_griding':
                $this->getDefaultResultGradeData();
                break;
            case 'save_result_grade':
                $this->manageResultGrade($data);
                break;
            case 'show_result_grade':
                $this->getResultGradeDataSelect($request);
                break;
            case 'add_oshc_info':
                $this->addOSHCInfo($data);
                break;
            case 'get_edit_oshc_info':
                $this->getEditOSHCInfoData($data);
                break;
            case 'edit_oshc_info':
                $this->updateOSHCInfoData($data);
                break;
            case 'delete_oshc_info':
                $this->deleteOSHCInfo($data);
                break;
            case 'test_smtp':
                $this->test_SmtpConnection($data);
                break;
            case 'add_intervention_strategy':
                $this->addInterventionStrategy($data);
                break;
            case 'edit_intervention_strategy':
                $this->editInterventionStrategy($data);
                break;
            case 'get_intervention_edit_detail':
                $this->getInterventionStrategyDetails($data);
                break;
            case 'delete_intervention_strategy':
                $this->deleteInterventionStrategy($data);
                break;
            case 'save_certificate_id_formate':
                $this->saveCertificateIdFormate($data);
                break;
            case 'update_certificate_id_formate':
                $this->editCertificateIdFormate($data);
                break;
            case 'delete_certificate_id_formate':
                return $this->deleteCertificateIdFormate($data);
            case 'update_assessment_due_date':
                $this->updateAssessmentDueDate($data);
                break;
            case 'update_agent_email_template':
                $this->updateAgentEmailTemplate($data);
                break;
            case 'letter_setting':
                return $this->addletter($data);
        }
        exit;
    }

    public function updateApiAllowedDomains($domains = '', $api_enabled = false)
    {
        $domains = explode(',', $domains);
        $domains = array_map('trim', $domains);
        $domains = array_filter($domains);
        $cleanedDomains = [];

        $currentTenant = tenant();
        $tenantDomain = $currentTenant->domain ?? $currentTenant->id ?? null;

        $shortcourseDomain = config('app.shortcourse_domain');
        $defaultShortCourseDomain = $tenantDomain.$shortcourseDomain;

        $domains = array_merge([$defaultShortCourseDomain], $domains);

        foreach ($domains as $domain) {

            $host = parse_url($domain, PHP_URL_HOST) ?: $domain;

            $host = preg_replace('/^www\./', '', $host); // Remove 'www.' if present

            // Validate the domain format
            if ((config('constants.allowlocalhostforshortcourseapi') && $host == 'localhost') || preg_match('/^(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$/', $host)) {
                // Add the whole valid domain to the cleaned array
                $cleanedDomains[] = "[{$host}]";
            }
        }
        $domains = $cleanedDomains;

        $domains = array_unique($domains);
        $domains = implode(',', $domains);

        $currentTenant = tenant();
        $currentTenant->setMeta('short_course_api_domains', $domains);
        $currentTenant->setMeta('short_course_api_enabled', ($api_enabled) ? 'enabled' : 'disabled');
    }

    public function saveGeneralInfo($data)
    {
        $validator = Validator::make($data, [
            'RTO_code' => 'required',
            'legal_name' => 'required',
            'contact_email' => 'required|email',
            'short_course_contact_email' => 'nullable|email',
            'short_course_helpdesk_email' => 'nullable|email',
            'college_lms_portal' => 'nullable|url',
        ], [
            'RTO_code.required' => 'RTO Code is required.',
            'legal_name.required' => 'Legal Name is required.',
            'contact_email.required' => 'Contact Email is required.',
            'contact_email.email' => 'Contact Email must be a valid email address.',
            'short_course_contact_email.email' => 'Sort Course Contact Email must be a valid email address.',
            'short_course_helpdesk_email.email' => 'Sort Course Help Desk Email must be a valid email address.',
            'college_lms_portal.url' => 'LMS portal url must be a valid url address.',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => $validator->errors()->first(),
            ]);
        }
        /*
        save allowed domains for api
        */
        // $apiEnabled = (isset($data['enable_shortcourse_api']) && ! empty($data['enable_shortcourse_api'])) ? true : false;
        // $this->updateApiAllowedDomains($data['allowed_domains'] ?? '', $apiEnabled);

        $courseTypes = $data['course_type_for_api'] ?? '';
        $colorProfile = $data['color_profile'] ?? '';
        $colorProfile = preg_replace('/[^a-fA-F0-9]/', '', ltrim($colorProfile, '#'));
        $colorProfile = preg_match('/^([a-fA-F0-9]{3}|[a-fA-F0-9]{6})$/', $colorProfile) ? '#'.strtoupper(strlen($colorProfile) == 3 ? preg_replace('/(.)/', '$1$1', $colorProfile) : $colorProfile) : false;
        // $data['course_type_for_api'] = ($courseTypes && is_array($courseTypes)) ? implode(',', $courseTypes) : $courseTypes;
        // $data['color_profile'] = ! empty($colorProfile) ? $colorProfile : null;
        if (isset($data['short_course_contact_same_as_contact']) && $data['short_course_contact_same_as_contact'] == 1) {
            $data['short_course_contact_person'] = $data['contact_person'];
            $data['short_course_contact_phone'] = $data['contact_phone'];
            $data['short_course_fax'] = $data['fax'];
            $data['short_course_contact_email'] = $data['contact_email'];
        } else {
            $data['short_course_contact_same_as_contact'] = 0;
        }
        $collegeDetails = $this->colleges->show($data['id'])->toArray();
        if (empty($collegeDetails)) {
            $saveCollege = $this->colleges->create($data);
            $saveCollegeDetail = $this->collegeDetails->create($data);
        } else {
            $saveCollege = $this->colleges->update($data, $data['id']);
            $saveCollegeDetail = $this->collegeDetails->update($data, $data['id']);
            if ($saveCollege) {
                $this->onboardSetupProcess->modify(['is_general' => '1'], ['college_id' => $data['id']]);
            }
        }
        SettingTracker::Completed(request('data.tracking_form'));
        $result = ['status' => 'success', 'message' => 'General information save successfully.'];

        return response()->json($result);
    }

    public function saveCollegeURL($data)
    {
        if (! empty($data['college_id'])) {
            $res = $this->colleges->update($data, $data['college_id']);
            if ($res) {
                $result = ['status' => 'success', 'message' => 'URL save successfully.', 'data' => $data];
            } else {
                $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
            }
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }

        return response()->json($result);
    }

    public function saveTrainingIdentifier($data)
    {

        if (! empty($data['id'])) {
            // $form = $data['tracking_form'];
            $id = $data['id'];
            unset($data['_token']);
            // unset($data['tracking_form']);
            unset($data['id']);
            $res = $this->collegeDetails->modify($data, ['college_id' => $id]);
            if ($res) {
                $this->onboardSetupProcess->modify(['is_training' => '1'], ['college_id' => $id]);
                $result = ['status' => 'success', 'message' => 'Training organisation identifier save successfully.'];
            } elseif ($res == '0') {
                $result = ['status' => 'success', 'message' => 'Training organisation already saved.'];
            }
        }

        SettingTracker::Completed(request('data.tracking_form'));

        return response()->json($result);
    }

    public function saveCollegeAddress($data)
    {
        $dataArr = [];
        $rowArr = [];
        $whereArr = ['college_id' => $data['college_id']];

        if (count($data) == 2 && isset($data['postal_same_street'])) {
            dd('ss');
            if ($data['postal_same_street'] == 0) {
                $result = ['status' => 'success', 'message' => 'Location save successfully.', 'data' => []];

                return response()->json($result);
            } else {
                $rowArr = $this->collegeDetails->getData($whereArr);
                $dataArr = [
                    'postal_address' => $rowArr[0]['street_address'],
                    'postal_suburb' => $rowArr[0]['street_suburb'],
                    'postal_state' => $rowArr[0]['street_state'],
                    'postal_postcode' => $rowArr[0]['street_postcode'],
                    'postal_same_street' => '0',
                ];
            }
        } else {

            $data['postal_same_street'] = '0';
            $dataArr = $data;
        }
        $res = $this->collegeDetails->modify($dataArr, $whereArr);

        if ($res) {
            SettingTracker::Completed(request('data.tracking_form'));
            $this->onboardSetupProcess->modify(['is_location' => '1'], ['college_id' => $data['college_id']]);
            $result = ['status' => 'success', 'message' => 'Location save successfully.', 'data' => $dataArr];
        } elseif ($res == '0' && (empty($rowArr) || @$rowArr[0]['street_address'] != '')) {
            SettingTracker::Completed(request('data.tracking_form'));
            $result = ['status' => 'success', 'message' => 'Location details already saved.', 'data' => $dataArr];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }

        return response()->json($result);
    }

    public function sameAsResidentialAddress($data)
    {
        $whereArr = ['college_id' => $data['college_id']];
        $dataArr = ['postal_same_street' => '1'];
        $this->collegeDetails->modify($dataArr, $whereArr);
        SettingTracker::Completed(request('data.tracking_form'));
        $this->onboardSetupProcess->modify(['is_location' => '1'], ['college_id' => $data['college_id']]);
        $result = ['status' => 'success', 'message' => 'Location save successfully.', 'data' => $dataArr];

        return response()->json($result);
    }

    public function removeCollegeAddress($data)
    {
        $type = $data['type'];
        $dataArr = [];
        if ($type == 'college_address') {
            $dataArr = [
                'street_address' => '',
                'street_suburb' => '',
                'street_state' => '',
                'street_postcode' => '',
            ];
        }
        if ($type == 'postal_address') {
            $dataArr = [
                'postal_address' => '',
                'postal_suburb' => '',
                'postal_state' => '',
                'postal_postcode' => '',
            ];
        }

        $res = $this->collegeDetails->modify($dataArr, ['college_id' => $data['college_id']]);
        if ($res) {
            $result = ['status' => 'success', 'message' => 'Location details remove successfully.'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }
        echo json_encode($result);
        exit;
    }

    public function saveBankDetail($data)
    {
        $res = $this->collegeDetails->modify($data, ['college_id' => $data['college_id']]);
        if ($res) {
            $this->onboardSetupProcess->modify(['is_bank' => '1'], ['college_id' => $data['college_id']]);
            SettingTracker::Completed(request('data.tracking_form'));
            $result = ['status' => 'success', 'message' => 'Bank details save successfully.', 'data' => $data];
        } elseif ($res == '0') {
            SettingTracker::Completed(request('data.tracking_form'));
            $result = ['status' => 'success', 'message' => 'Bank details already saved.'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }

        return response()->json($result);
    }

    public function removeBankDetail($data)
    {
        $type = $data['type'];
        $dataArr = [];
        if ($type == 'bank_details') {
            $dataArr = [
                'bank_name' => '',
                'bank_branch' => '',
                'bank_account_name' => '',
                'bank_account_number' => '',
                'bank_BSB' => '',
                'bank_swift_code' => '',
            ];
        }
        $res = $this->collegeDetails->modify($dataArr, ['college_id' => $data['college_id']]);
        if ($res) {
            $result = ['status' => 'success', 'message' => 'Bank details remove successfully.'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }
        echo json_encode($result);
        exit;
    }

    public function saveVslInfo($data)
    {
        unset($data['is_vet_study']);
        $res = $this->collegeDetails->modify($data, ['college_id' => $data['college_id']]);
        if ($res) {
            $this->onboardSetupProcess->modify(['is_vsl' => '1'], ['college_id' => $data['college_id']]);
            $result = ['status' => 'success', 'message' => 'VSL info save successfully.'];
        } elseif ($res == '0') {
            $result = ['status' => 'success', 'message' => 'VSL info already saved.'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }
        echo json_encode($result);
        exit;
    }

    public function resetVslInfo($data)
    {
        unset($data['is_vet_study']);
        $dataArr = [];
        $dataArr = [
            'orgazination_id' => '',
            'vsl_user_name' => '',
        ];
        $res = $this->collegeDetails->modify($dataArr, ['college_id' => $data['college_id']]);
        if ($res) {
            $result = ['status' => 'success', 'message' => 'VSL info reset successfully.'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }
        echo json_encode($result);
        exit;
    }

    public function saveOfferDocuments($data)
    {
        $data['is_compulsory'] = (isset($data['is_compulsory']) && $data['is_compulsory'] == 'on') ? 1 : 0;
        $data['is_active'] = (isset($data['is_active']) && $data['is_active'] == 'on') ? 1 : 0;
        if (isset($data['id'])) {
            $res = $this->offerDocumentChecklist->update($data, $data['id']);
        } else {
            if (is_array($data['student_origin'])) {
                $tempData = $data;
                foreach ($data['student_origin'] as $origin) {
                    $tempData['student_origin'] = $origin;
                    $this->offerDocumentChecklist->create($tempData);
                    $res = true;
                }
            } else {
                $res = $this->offerDocumentChecklist->create($data);
            }
        }
        if ($res) {
            $this->onboardSetupProcess->modify(['is_offer_document' => '1'], ['college_id' => $data['college_id']]);
            $result = ['status' => 'success', 'message' => 'Offer Documents save successfully.'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }
        echo json_encode($result);
        exit;
    }

    public function saveAgentDocuments($data)
    {
        $data['is_compulsory'] = (isset($data['is_compulsory']) && $data['is_compulsory'] == 'on') ? 1 : 0;
        $data['is_active'] = (isset($data['is_active']) && $data['is_active'] == 'on') ? 1 : 0;
        if (isset($data['id'])) {
            $res = $this->agentDocumentChecklist->update($data, $data['id']);
        } else {
            $res = $this->agentDocumentChecklist->create($data);
        }

        if ($res) {
            $this->onboardSetupProcess->modify(['is_agent_document' => '1'], ['college_id' => $data['college_id']]);
            SettingTracker::Completed(request('data.tracking_form'));
            $result = ['status' => 'success', 'message' => 'Agent Documents save successfully.'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }

        return response()->json($result);
    }

    public function saveOfferTrackingStatus($data)
    {
        $data['is_allowagent'] = (isset($data['is_allowagent']) && $data['is_allowagent'] == 'on') ? 1 : 0;
        if (isset($data['id'])) {
            $res = $this->offerTrackingStatus->update($data, $data['id']);
        } else {
            $res = $this->offerTrackingStatus->create($data);
        }

        if ($res) {
            $this->onboardSetupProcess->modify(['is_offer_status' => '1'], ['college_id' => $data['college_id']]);
            $result = ['status' => 'success', 'message' => 'Offer tracking status save successfully.'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }
        echo json_encode($result);
        exit;
    }

    public function saveCustomChecklist($data)
    {
        $data['mandatory'] = (isset($data['mandatory']) && $data['mandatory'] == 'on') ? 'yes' : 'no';
        if (isset($data['id'])) {
            $res = $this->checklist->update($data, $data['id']);
        } else {
            $res = $this->checklist->create($data);
        }

        if ($res) {
            $this->onboardSetupProcess->modify(['is_custom_checklist' => '1'], ['college_id' => $data['college_id']]);
            $result = ['status' => 'success', 'message' => 'Custom Checklist save successfully.'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }
        echo json_encode($result);
        exit;
    }

    public function saveSetupSection($data)
    {
        $data['value'] = ($data['section'] == 1) ? $data['value_select'] : $data['value'];
        unset($data['value_select']);
        $res = $this->setupSection->create($data);

        if ($res) {
            $this->onboardSetupProcess->modify(['is_section' => '1'], ['college_id' => $data['college_id']]);
            $result = ['status' => 'success', 'message' => 'Section save successfully.'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }
        echo json_encode($result);
        exit;
    }

    public function saveVenueDetails($data)
    {
        // Validate using VenueRequest (static rules builder)
        $validator = Validator::make($data, VenueRequest::rulesFor($data), VenueRequest::messagesFor());

        if ($validator->fails()) {
            $result = ['status' => 'error', 'message' => $validator->errors()->first()];
            echo json_encode($result);
            exit;
        }

        if (isset($data['id'])) {
            $res = $this->campusVenue->update($data, $data['id']);
        } else {
            $res = $this->campusVenue->create($data);
        }
        if ($res) {
            $result = ['status' => 'success', 'message' => 'Venue details save successfully.'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }
        echo json_encode($result);
        exit;
    }

    public function saveVenueAddress($data)
    {
        // Validate using VenueRequest if venue_code is present
        if (isset($data['venue_code'])) {
            $validator = Validator::make($data, VenueRequest::rulesFor($data), VenueRequest::messagesFor());

            if ($validator->fails()) {
                $result = ['status' => 'error', 'message' => $validator->errors()->first()];
                echo json_encode($result);
                exit;
            }
        }

        $data['status_default'] = (isset($data['status_default'])) ? $data['status_default'] : 0;
        $data['venue_effective_from_date'] = ($data['venue_effective_from_date']) ? date('Y-m-d', strtotime($data['venue_effective_from_date'])) : 'NUll';
        $data['venue_effective_to_date'] = ($data['venue_effective_to_date']) ? date('Y-m-d', strtotime($data['venue_effective_to_date'])) : 'NUll';
        $data['created_by'] = Auth::user()->id;
        $data['updated_by'] = Auth::user()->id;
        if (isset($data['id'])) {
            $res = $this->campusVenue->update($data, $data['id']);
        } else {
            $res = $this->campusVenue->create($data);
        }
        if ($res) {
            $result = ['status' => 'success', 'message' => 'Venue address save successfully.'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }
        echo json_encode($result);
        exit;
    }

    public function saveRoom($data)
    {
        // Validate using RoomRequest (static rules builder)
        $validator = Validator::make($data, RoomRequest::rulesFor($data), RoomRequest::messagesFor());

        if ($validator->fails()) {
            echo json_encode(['status' => 'error', 'message' => $validator->errors()->first()]);
            exit;
        }

        $loginUserId = Auth::user()->id;
        $data['status'] = (isset($data['status']) && $data['status'] == 'on') ? 1 : 0;

        try {
            if (isset($data['id'])) {
                $updateArr = [
                    'room_id' => $data['room_id'],
                    'room_name' => $data['room_name'],
                    'max_capacity' => $data['max_capacity'],
                    'status' => $data['status'],
                    'updated_by' => $loginUserId,
                ];
                $res = $this->classroom->update($updateArr, $data['id']);
            } else {
                $data['created_by'] = $data['updated_by'] = $loginUserId;
                $res = $this->classroom->create($data);
            }

            if ($res) {
                $this->onboardSetupProcess->modify(['is_room' => '1'], ['college_id' => $data['college_id']]);
                echo json_encode(['status' => 'success', 'message' => 'Room details save successfully.']);
            } else {
                echo json_encode(['status' => 'error', 'message' => 'Something will be wrong. Please try again.']);
            }
        } catch (\Exception $e) {
            echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        }

        exit;
    }

    public function saveCampus($data)
    {

        $data['status_default'] = (isset($data['status_default']) && $data['status_default'] == 'on') ? 1 : 0;
        $data['status'] = 1;
        $checkname = $this->collegecampus->Where(['college_id' => $data['college_id'], 'name' => $data['name']])->get()->count();
        if ($checkname > 0) {
            $result = ['status' => 'error', 'message' => 'Campus name already exist'];
        } else {
            $saveCampus = $this->collegecampus->create($data);
            if ($saveCampus) {
                SettingTracker::Completed(request('data.tracking_form'));
                $result = ['status' => 'success', 'message' => 'Campus save successfully.', 'redirect_url' => route('view-campus', $saveCampus->id)];
            } else {
                $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
            }
        }

        return response()->json($result);
    }

    public function onboardStatusUpdate($data)
    {
        $smtpData = $this->smtpSetup->modify($data['seralize_data'], ['college_id' => $data['seralize_data']['college_id']]);
        if ($smtpData) {
            $this->onboardSetupProcess->modify(['is_smtp' => '1'], ['college_id' => $data['seralize_data']['college_id']]);
        }
        if (! empty($data['onboard_setup']['id'])) {
            $setup = $this->colleges->update($data['onboard_setup'], $data['onboard_setup']['id']);
            if ($setup) {
                $result = ['status' => 'success', 'message' => 'Thank you for registering!', 'redirect_url' => route('dashboard')];
            } else {
                $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
            }
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }
        echo json_encode($result);
        exit;
    }

    public function saveSmtpInfo($data)
    {
        unset($data['_token']);
        $collegeId = $data['college_id'];
        $smtpSetup = $this->smtpSetup->getWhere(['college_id' => $collegeId])->first();
        $lastStatus = $smtpSetup->status;
        $smtpSetup->fill($data);

        // $smtpData = $this->smtpSetup->modify($data, ['college_id' => $collegeId]);
        if ($smtpSetup->save()) {
            $this->onboardSetupProcess->modify(['is_smtp' => '1'], ['college_id' => $collegeId]);
            $result = ['status' => 'success', 'message' => 'SMTP details save successfully.'];
            if (isset($data['status']) && $lastStatus != $data['status']) {
                (new EmailService)
                    ->from(new MailAddress($smtpSetup->email))
                    ->to(new MailAddress(config('mail.support.address')))
                    ->subject('SMTP Details Updated')
                    ->sendUsingMailMessage(function ($message) use ($data) {
                        $message->greeting('Hello '.config('mail.support.name').',')
                            ->line('SMTP Details Updated Successfully.')
                            ->line('Tenant ID: '.tenant('id'))
                            ->line('User: '.Auth::user()->name)
                            ->line('SMTP Configuration: '.($data['status'] == 1 ? 'Using Custom SMTP' : 'Using Default SMTP'))
                            // ->line('Thank you for using Galaxy.')
                            ->salutation(config('mail.support.name'));
                    });
            }
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }

        echo json_encode($result);
        exit;
    }

    public function addCountry($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $countryCount = Country::where('name', $data['name'])->count();
        $nationalityCount = Country::where('nationality', $data['nationality'])->count();
        if ($countryCount == 0 && $nationalityCount == 0) {
            $result = $this->country->create($data);
            $result = ['status' => 'success', 'message' => 'Country added successfully'];
        } else {
            $msg = ($countryCount == 1) ? 'Country name already exist' : 'Nationality name already exist';
            $result = ['status' => 'error', 'message' => $msg];
        }
        echo json_encode($result);
        exit;
    }

    public function editCountry($data)
    {

        $data['college_id'] = Auth::user()->college_id;
        $collageArr = [0, $data['college_id']];
        $countryCount = Country::whereIn('college_id', $collageArr)
            ->where('name', '=', $data['name'])
            ->where('id', '!=', $data['id'])
            ->count();
        $nationalityCount = Country::whereIn('college_id', $collageArr)
            ->where('nationality', '=', $data['nationality'])
            ->where('id', '!=', $data['id'])
            ->count();

        // dd($nationalityCount);
        if ($countryCount == 0 && $nationalityCount == 0) {

            if (isset($data['id'])) {
                $this->country->update($data, $data['id']);
                $result = ['status' => 'success', 'message' => 'Country updated successfully'];
            }
        } else {
            $msg = ($countryCount == 1) ? 'Country name already exist' : 'Nationality name already exist';
            $result = ['status' => 'error', 'message' => $msg];
        }
        echo json_encode($result);
        exit;
    }

    public function deleteCountry($data)
    {
        $res = $this->country->delete($data['id']);
        if ($res) {
            $result = ['status' => 'success', 'message' => 'Country deleted successfully'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }
        echo json_encode($result);
        exit;
    }

    public function saveGteDocument($data)
    {

        if (isset($data['id'])) {
            $res = $this->gteDocument->update($data, $data['id']);
            $result = ['status' => 'success', 'message' => 'GTE document updated successfully.'];
        } else {
            $res = $this->gteDocument->create($data);
            if ($res) {
                $result = ['status' => 'success', 'message' => 'GTE document created successfully.'];
            } else {
                $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
            }
        }
        echo json_encode($result);
        exit;
    }

    public function removeGteDocument($data)
    {
        $docId = GteStudentDocuments::where('gte_document_id', $data['id'])->first();
        if (empty($docId)) {
            $res = $this->gteDocument->delete($data['id']);
            if ($res) {
                $result = ['status' => 'success', 'message' => 'GTE document deleted successfully'];
            } else {
                $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
            }
        } else {
            $result = ['status' => 'error', 'message' => 'GTE Document cannot be Deleted'];
        }
        echo json_encode($result);
        exit;
    }

    public function addedServicesFee($data)
    {

        $data['college_id'] = Auth::user()->college_id;
        $serviceNameCount = ServicesFee::where('service_name', $data['service_name'])->count();
        if (isset($data['id'])) {
            $serviceNameCount = ServicesFee::where('service_name', $data['service_name'])->where('id', '!=', $data['id'])->count();
            if ($serviceNameCount == 0) {
                $this->servicesFee->update($data, $data['id']);
                $result = ['status' => 'success', 'message' => 'Services fee updated successfully.'];
            } else {
                $result = ['status' => 'error', 'message' => 'Duplicate service name found.'];
            }
        } elseif ($serviceNameCount == 0) {
            $res = $this->servicesFee->create($data);
            if ($res) {
                SettingTracker::Completed(request('data.tracking_form'));
                $result = ['status' => 'success', 'message' => 'Services fee added successfully.'];
            } else {
                $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
            }
        } else {
            $result = ['status' => 'error', 'message' => 'Duplicate service name found.'];
        }

        echo json_encode($result);
        exit;
    }

    public function removeServicesFee($data)
    {
        $res = $this->servicesFee->delete($data['id']);
        if ($res) {
            $result = ['status' => 'success', 'message' => 'Services fee data deleted successfully'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }
        echo json_encode($result);
        exit;
    }

    public function addLanguage($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $lnameCount = Language::where('name', $data['name'])->count();
        $absvalueCount = Language::where('abs_value', $data['abs_value'])->count();
        if ($lnameCount == 0 && $absvalueCount == 0) {
            $result = $this->Language->create($data);
            $result = ['status' => 'success', 'message' => 'Language added successfully'];
        } else {
            $msg = ($lnameCount == 1) ? 'Language name already exist' : 'ABS Value already exist';
            $result = ['status' => 'error', 'message' => $msg];
        }
        echo json_encode($result);
        exit;
    }

    public function editLanguage($data)
    {

        $data['college_id'] = Auth::user()->college_id;
        $collageArr = [0, $data['college_id']];
        $lnameCount = Language::whereIn('college_id', $collageArr)
            ->where('name', '=', $data['name'])
            ->where('id', '!=', $data['id'])
            ->count();
        $absvalueCount = Language::whereIn('college_id', $collageArr)
            ->where('abs_value', '=', $data['abs_value'])
            ->where('id', '!=', $data['id'])
            ->count();

        if ($lnameCount == 0 && $absvalueCount == 0) {

            if (isset($data['id'])) {
                $this->Language->update($data, $data['id']);
                $result = ['status' => 'success', 'message' => 'Lunguage updated successfully'];
            }
        } else {
            $msg = ($lnameCount == 1) ? 'Language name already exist' : 'Abs Value already exist';
            $result = ['status' => 'error', 'message' => $msg];
        }
        echo json_encode($result);
        exit;
    }

    public function deleteLanguage($data)
    {
        $res = $this->Language->delete($data['id']);
        if ($res) {
            $result = ['status' => 'success', 'message' => 'Language deleted successfully'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }
        echo json_encode($result);
        exit;
    }

    public function addAgentStatus($data)
    {
        $data['publish'] = (isset($data['publish']) && $data['publish'] == 'on') ? 1 : 0;
        $data['college_id'] = Auth::user()->college_id;

        $agentStatusCount = AgentStatus::where('status_type', $data['status_type'])->count();
        if (isset($data['id'])) {
            $agentStatusCount = AgentStatus::where('status_type', $data['status_type'])->where('id', '!=', $data['id'])->count();
            if ($agentStatusCount == 0) {
                $this->agentStatus->update($data, $data['id']);
                $result = ['status' => 'success', 'message' => 'Agent Status updated successfully.'];
            } else {
                $result = ['status' => 'error', 'message' => 'Already Exist Agent Status .'];
            }
        } elseif ($agentStatusCount == 0) {
            $res = $this->agentStatus->create($data);
            if ($res) {
                SettingTracker::Completed(request('data.tracking_form'));
                $result = ['status' => 'success', 'message' => 'Agent Status add successfully.'];
            } else {
                $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
            }
        } else {
            $result = ['status' => 'error', 'message' => 'Already Exist Agent Status.'];
        }

        return response()->json($result);
    }

    public function deleteAgentStatus($data)
    {
        $res = $this->agentStatus->delete($data['id']);
        if ($res) {
            $result = ['status' => 'success', 'message' => 'Agent Status deleted successfully'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }
        echo json_encode($result);
        exit;
    }

    public function getDefaultResultGradeData()
    {
        echo json_encode([
            'arrFinalOutcome' => $this->convertConstantsFormat(Config::get('constants.arrFinalOutcome')),
            'arrUnitOfStudyStatus' => $this->convertConstantsFormat(Config::get('constants.arrUnitOfStudyStatus')),
        ]);
        exit;
    }

    public function manageResultGrade($data)
    {
        $loginData = Auth::user();
        if (isset($data['use_marks']) && $data['use_marks'] == 'No') {
            $data['maximum_marks'] = null;
            $data['grade_point'] = null;
            $data['marks'] = 0;
        }

        $data['use_marks'] = (isset($data['use_marks']) && $data['use_marks'] == 'Yes') ? 1 : 0;
        $data['force_matching_outcome_code'] = (isset($data['force_matching_outcome_code']) && $data['force_matching_outcome_code'] == 'Yes') ? 1 : 0;
        $data['competency_calculation'] = (isset($data['competency_calculation']) && $data['competency_calculation'] == 'Yes') ? 1 : 0;
        $data['college_id'] = $loginData->college_id;
        $data['created_by'] = $loginData->id;
        $data['updated_by'] = $loginData->id;

        try {
            DB::beginTransaction();
            if (isset($data['id'])) {
                $res = $this->resultGrade->update($data, $data['id']);
                $result = ['status' => 'success', 'message' => 'Result Grade updated successfully.'];
            } else {
                if (isset($data['grading_name']) && $data['grading_name'] != '') {
                    $createGradingType = $this->gradingType->create($data);
                    $gradingTypeId = $createGradingType->id;
                    $data['grading_type'] = $gradingTypeId;
                }
                if (isset($data['grading_type']) && $data['grading_type'] != 0) {
                    $createResultGrade = $this->resultGrade->create($data);
                    $result = ['status' => 'success', 'message' => 'Result Grade created successfully.'];
                } else {
                    $result = ['status' => 'error', 'message' => 'Please Enter Grade Type Name'];
                }
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
        echo json_encode($result);
        exit;
    }

    public function getResultGradeDataSelect(Request $request)
    {
        $data = $request->input('data');
        $gradeType = $data['grading_type'];
        $data['college_id'] = Auth::user()->college_id;
        $resultArr = ResultGrade::where('college_id', $data['college_id'])->where('grading_type', $gradeType)
            ->limit(1)->get(['use_marks', 'force_matching_outcome_code']);

        if ($resultArr->count() > 0) {
            $result['use_marks'] = (isset($resultArr[0]->use_marks) && $resultArr[0]->use_marks == '1') ? 'Yes' : 'No';
            $result['force_matching_outcome_code'] = (isset($resultArr[0]->force_matching_outcome_code) && $resultArr[0]->force_matching_outcome_code == '1') ? 'Yes' : 'No';
            $result['status'] = 'disabled';
        } else {
            $result['use_marks'] = '';
            $result['force_matching_outcome_code'] = '';
            $result['status'] = 'enable';
        }
        echo json_encode($result);
        exit;
    }

    public function addOSHCInfo($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $providerNameCount = OSHCProvider::where('provider_name', $data['provider_name'])->count();

        if (isset($data['provider_id']) && $data['provider_id'] == 0) {
            if ($providerNameCount == 0) {
                $createOshcProvider = $this->OSHCProvider->create($data);
                $providerId = $createOshcProvider->id;
                $data['provide_id'] = $providerId;
                $res = $this->OSHC->create($data);
                SettingTracker::Completed(request('data.tracking_form'));
                $result = ['status' => 'success', 'message' => 'OSHC Record Saved successfully.'];
            } else {
                $result = ['status' => 'error', 'message' => 'Already Exist provider Name.'];
            }
        }
        if (isset($data['provider_id']) && $data['provider_id'] != 0) {
            $data['provide_id'] = $data['provider_id'];
            $res = $this->OSHC->create($data);
            SettingTracker::Completed(request('data.tracking_form'));
            $result = ['status' => 'success', 'message' => 'OSHC Record Saved successfully.'];
        }
        echo json_encode($result);
        exit;
    }

    public function updateOSHCInfoData($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        OSHC::where('id', $data['id'])->update($data);
        $result = ['status' => 'success', 'message' => 'OSHC Record Saved successfully.'];
        echo json_encode($result);
        exit;
    }

    public function getEditOSHCInfoData($data)
    {

        $res = OSHC::select('rto_oshc.*', 'rto_oshc_providers.provider_name')->leftjoin('rto_oshc_providers', 'rto_oshc_providers.id', '=', 'rto_oshc.id')->where('rto_oshc.id', $data['id'])->get();
        echo json_encode(['status' => 'success', 'message' => 'OSHC Record found successfully', $res]);
        exit;
    }

    public function deleteOSHCInfo($data)
    {

        $oshc_id = $data['id'];
        $provide_id = $this->OSHC->getWhere(['id' => $oshc_id]);
        $provide_id_count = Oshc::where('provide_id', $provide_id[0]->provide_id)->get()->count();
        if ($provide_id_count == 1) {
            $res2 = $this->OSHCProvider->delete($provide_id[0]->provide_id);
            $result = ['status' => 'success', 'message' => 'OSHC Record Deleted successfully', $res2];
        }
        $res1 = $this->OSHC->delete($oshc_id);
        if ($res1) {
            $result = ['status' => 'success', 'message' => 'OSHC Record Deleted successfully'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }

        echo json_encode($result);
        exit;
    }

    public function addInterventionStrategy($data)
    {

        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = Auth::user()->id;
        $data['updated_by'] = Auth::user()->id;
        $interventiontypeidcount = InterventionStrategy::where('intervention_type_id', $data['intervention_type_id'])->count();
        $strategycount = InterventionStrategy::where('strategy', $data['strategy'])->count();

        if (isset($data['intervention_type_id']) && $data['intervention_type_id'] == 0) {
            if ($strategycount == 0) {
                $InterventionType = $this->InterventionType->create($data);
                $InterventionTypeId = $InterventionType->id;
                $data['intervention_type_id'] = $InterventionTypeId;
                $res = $this->InterventionStrategy->create($data);
                $result = ['status' => 'success', 'message' => 'Intervention Strategy Added Successfully.'];
            } else {
                $result = ['status' => 'error', 'message' => 'Duplicate Intervention Strategy Found.'];
            }
        } elseif (isset($data['intervention_type_id']) && $data['intervention_type_id'] != 0) {

            if ($strategycount == 0) {
                $res = $this->InterventionStrategy->create($data);
                $result = ['status' => 'success', 'message' => 'Intervention Strategy Added Successfully.'];
            } else {
                $result = ['status' => 'error', 'message' => 'Duplicate Intervention Strategy Found.'];
            }
        }
        echo json_encode($result);
        exit;
    }

    public function editInterventionStrategy($data)
    {
        // dd($data);
        $data['updated_by'] = Auth::user()->id;

        $interventionStrategy = InterventionStrategy::find($data['id']);

        if (! $interventionStrategy) {
            $result = ['status' => 'error', 'message' => 'Intervention Strategy not found.'];
            echo json_encode($result);
            exit;
        }
        $strategyExists = InterventionStrategy::where('strategy', $data['strategy'])->where('id', '!=', $data['id'])->exists();
        if ($strategyExists) {
            $result = ['status' => 'error', 'message' => 'Duplicate Intervention Strategy Found'];
            echo json_encode($result);
            exit;
        }
        // Handle intervention type creation if needed
        if (isset($data['intervention_type_id']) && $data['intervention_type_id'] == 0) {
            $interventionType = $this->InterventionType->create($data);
            $data['intervention_type_id'] = $interventionType->id;
        }

        $interventionStrategy->update($data);

        $result = ['status' => 'success', 'message' => 'Intervention Strategy Updated Successfully'];
        echo json_encode($result);
        exit;
    }

    public function getInterventionStrategyDetails($data)
    {
        $interventionStrategy = InterventionStrategy::find($data['id'])->toArray();
        if (! $interventionStrategy) {
            $result = ['status' => 'error', 'message' => 'Intervention Strategy not found.'];
        } else {
            $result = ['status' => 'success', 'message' => 'Intervention Strategy Added Successfully.', 'data' => $interventionStrategy];
        }
        echo json_encode($result);
        exit;
    }

    public function deleteInterventionStrategy($data)
    {

        $InterventionStrategy_id = $data['id'];
        $intervention_type_id = $this->InterventionStrategy->getWhere(['id' => $InterventionStrategy_id])->first()->intervention_type_id;
        $intervention_type_id_count = InterventionStrategy::where('intervention_type_id', $intervention_type_id)->count();
        if ($intervention_type_id_count == 1) {

            $res2 = $this->InterventionType->delete($intervention_type_id);
            $result = ['status' => 'success', 'message' => 'Intervention Strategy Deleted Successfully', $res2];
        }
        $res1 = $this->InterventionStrategy->delete($InterventionStrategy_id);
        if ($res1) {
            $result = ['status' => 'success', 'message' => 'Intervention Strategy Deleted Successfully'];
        } else {
            $result = ['status' => 'error', 'message' => 'Something will be wrong. Please try again.'];
        }
        echo json_encode($result);
        exit;
    }

    public function saveCertificateIdFormate($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = Auth::user()->id;
        $data['updated_by'] = Auth::user()->id;
        $data['last_auto_increment_number'] = $data['auto_number'];
        $resultData = $this->CertificateIdFormate->Where(['type' => $data['type']])->count();
        if ($resultData == 0) {
            $this->CertificateIdFormate->create($data);
            $result = ['status' => 'success', 'message' => 'Certificate Id Formate Record Created successfully.'];
        } else {
            $result = ['status' => 'error', 'message' => 'Certificate Type already exist.'];
        }
        echo json_encode($result);
        exit;
    }

    public function editCertificateIdFormate($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = Auth::user()->id;
        $data['updated_by'] = Auth::user()->id;
        $data['last_auto_increment_number'] = $data['auto_number'];
        $res = $this->CertificateIdFormate->modify($data, ['id' => $data['id']]);
        $result = ['status' => 'success', 'message' => 'Certificate Id Formate Record Updated Successfully.'];
        echo json_encode($result);
        exit;
    }

    public function deleteCertificateIdFormate($data)
    {
        $primaryId = $data['id'];
        if (! empty($primaryId)) {
            $res = $this->CertificateIdFormate->delete($primaryId);

            return $this->successResponse('Delete successfully.', 'data', $res);
        } else {
            return $this->errorResponse('Record not found', 'data', []);
        }
    }

    public function updateAssessmentDueDate($data)
    {
        $college_id = $data['college_id'];
        $userId = Auth::user()->id;
        $data['created_by'] = $userId;
        $data['updated_by'] = $userId;
        $college_id_Count = AssessmentDueDate::where('college_id', $college_id)->count();
        $data['feature_enabled'] = (isset($data['feature_enabled']) && $data['feature_enabled'] == 'on') ? 1 : 0;
        $data['default_due_day'] = ($data['feature_enabled'] == '0') ? null : $data['default_due_day'];
        if ($college_id_Count == 0) {
            $res = $this->AssessmentDueDate->create($data);
            $result = ['status' => 'success', 'message' => 'Assessment Due Date Created successfully.'];
        } else {
            $res = $this->AssessmentDueDate->modify($data, ['college_id' => $data['college_id']]);
            $result = ['status' => 'success', 'message' => 'Assessment Due Date Updated Successfully.'];
        }
        echo json_encode($result);
        exit;
    }

    public function test_SmtpConnection($data)
    {
        $toemail = $data['toEmail'];
        if ($toemail != '') {
            $mailData = [
                'from' => '',
                'fromName' => 'noreply',
                'to' => $toemail,
                'page' => 'mail.student-offer-email',
                'subject' => 'GALAXY 360 Test Mail',
                'body' => 'GALAXY 360 Test Mail',
                'data' => ['content' => 'Test mail'],
                'attachFile' => [],
            ];
            $sendMail = new SendMail;
            $sendTestMail = $sendMail->sendSmtpTestMail($mailData);
            if ($sendTestMail['status']) {
                $result = ['status' => 'success', 'message' => 'Email sent successfully'];
            } else {
                $result = ['status' => 'error', 'message' => $sendTestMail['message']];
            }
        } else {
            $result = ['status' => 'error', 'message' => 'Please Valid  Enter Email Address'];
        }
        echo json_encode($result);
        exit;
    }

    public function failedJobsData(Request $request)
    {

        $post = ($request->input()) ? $request->input() : [];
        $columnArr = [
            'id',
            'payload',
            'exception',
            DB::raw("DATE_FORMAT(failed_at, '%d %b %Y') as failed_at"),
        ];
        $columns = [
            'id' => 'id',
            'uuid' => 'payload',
            'tenant' => 'payload',
            'failed_at' => 'failed_at',
        ];
        $query = DB::table('failed_jobs')
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);

        $resultArr = $this->gridDataPagination($query, $post, false);
        // $count = $this->gridDataPagination($query, $post, true);

        $result = [];
        $no = 1;
        foreach ($resultArr as $res) {
            $jsonDecode = json_decode($res->payload, true);
            $result[] = [
                'id' => $no,
                'uuid' => $jsonDecode['uuid'],
                'tenant' => $jsonDecode['tenant_id'],
                'failed_at' => $res->failed_at,
            ];
            $no++;
        }
        $data['data'] = $result;
        $data['total'] = count($result);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function updateAgentEmailTemplate($data)
    {

        $college_id = $data['college_id'];
        $userId = Auth::user()->id;
        $data['created_by'] = $userId;
        $data['updated_by'] = $userId;
        $templateId = 1;
        $emailTemplateSettingData = AgentEmailTemplateSetting::find($templateId);
        if (! empty($emailTemplateSettingData)) {
            $res = $this->AgentEmailTemplateSetting->modify($data, ['id' => $templateId]);
            $result = ['status' => 'success', 'message' => 'Agent Email Template Updated Successfully.'];
        } else {
            $res = $this->AgentEmailTemplateSetting->create($data);
            $result = ['status' => 'success', 'message' => 'Agent Email Template Created successfully.'];
        }
        echo json_encode($result);
        exit;
    }

    public function addletter($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = Auth::user()->id;
        $data['updated_by'] = Auth::user()->id;
        $id = 1;
        $letterSettingDataId = LetterSetting::find($id);

        if (! empty($letterSettingDataId)) {
            $this->letterSetting->update($data, $id);
            SettingTracker::Completed(request('data.tracking_form'));

            $result = ['status' => 'success', 'message' => 'Letter Updated successfully.'];
        } else {
            $this->letterSetting->create($data);
            SettingTracker::Completed(request('data.tracking_form'));
            $result = ['status' => 'success', 'message' => 'Letter Created successfully.'];
        }

        return response()->json($result);
        exit;
    }

    public function uploadLatterSetting(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $req = $request->input('metadata');
        $uploadData = json_decode($req, true);
        $letterSetting = $request->file();
        if (isset($letterSetting['watermark'])) {
            $file = $letterSetting['watermark'];
            $filePath = Config::get('constants.uploadFilePath.LetterSetting');
            $destinationPath = Helpers::changeRootPath($filePath, null, $collegeId);
            $filename = hashFileName($uploadData['fileName']);
            // $res = $file->move($destinationPath['default'], $filename);
            $res = UploadService::uploadAs($destinationPath['view'], $file, $filename);
            info('file uploaded form watermark', [$res]);
            if ($res && ! empty($collegeId)) {
                $whereArr = ['college_id' => $collegeId];
                $existingData = $this->letterSetting->getWhere($whereArr)->first();
                if (! empty($existingData->watermark)) {
                    $existingFilePath = $destinationPath['default'].$existingData->watermark;
                    if (file_exists($existingFilePath)) {
                        unlink($existingFilePath);
                    }
                    $this->letterSetting->modify(['watermark' => $filename], $whereArr);
                    echo json_encode(['uploaded' => true, 'fileUid' => $filename, 'status' => 'success', 'message' => 'Watermark uoloaded successfully.']);
                    exit;
                } else {
                    $this->letterSetting->create(['watermark' => $filename, 'college_id' => $collegeId]);
                    echo json_encode(['uploaded' => true, 'fileUid' => $filename, 'status' => 'success', 'message' => 'Watermark uoloaded successfully.']);
                    exit;
                }
            }
        }
        echo json_encode(['status' => 'error', 'message' => 'Something will be wrong. Please try again.']);
        exit;
    }

    public function getStudentIdFormateData(Request $request)
    {

        $data = StudentIdFormate::where('college_id', Auth::user()->college_id)->get();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function saveStudentIdFormate(Request $request)
    {

        $newPositionsOfList = explode(',', $request->input('newPositionsOfList'));

        $preview = (string) $request->input('previewformate');
        $auto = (string) $request->input('auto_number_value');
        $alpha = (string) $request->input('alphabeat_value');

        // Server-side validation: total length <= 10; auto-increment 1–5 digits; preview must end with auto; prefix (preview without auto) <= 5 chars
        if ($preview === '' || strlen($preview) > 10 || ! preg_match('/^[A-Za-z0-9]+$/', $preview)) {
            return $this->errorResponse('Student ID must be alphanumeric and not exceed 10 characters.', 'data', '', 200);
        }
        if (! preg_match('/^\d{1,5}$/', $auto)) {
            return $this->errorResponse('Auto increment number must be 1 to 5 digits.', 'data', '', 200);
        }
        if (substr($preview, -strlen($auto)) !== $auto) {
            return $this->errorResponse('The Student ID must end with the auto increment number.', 'data', '', 200);
        }
        // Validate alphabeat itself (letters up to 5)
        if (! preg_match('/^[A-Za-z]{0,5}$/', $alpha)) {
            return $this->errorResponse('The Alphabet prefix must be letters only and up to 5 in length.', 'data', '', 200);
        }
        // Validate total prefix length (preview without auto) up to 5
        $prefix = substr($preview, 0, strlen($preview) - strlen($auto));
        if (strlen($prefix) > 5) {
            return $this->errorResponse('The user-set prefix must be at most 5 characters.', 'data', '', 200);
        }
        $studentIdFormate = StudentIdFormate::find($request->input('id'));

        $studentIdFormate->position1 = (! empty($newPositionsOfList[0])) ? trim($newPositionsOfList[0]) : '';
        $studentIdFormate->position2 = (! empty($newPositionsOfList[1])) ? trim($newPositionsOfList[1]) : '';
        $studentIdFormate->position3 = (! empty($newPositionsOfList[2])) ? trim($newPositionsOfList[2]) : '';
        $studentIdFormate->alphabeat = $request->input('alphabeat_value');
        $studentIdFormate->yeardigit = ($request->input('year_digit_value') == 2) ? date('y') : date('Y');
        $studentIdFormate->auto_increment = $request->input('auto_number_value');
        $studentIdFormate->last_increment = $request->input('auto_number_value');
        $studentIdFormate->created_by = Auth::user()->id;
        $studentIdFormate->updated_by = Auth::user()->id;
        $studentIdFormate->save();

        return $this->successResponse('Data Saved successfully', 'data', '');
    }

    public function getGlobalQueue(Request $request)
    {
        $data = $this->courseType->getGlobalQueueData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getProgressGlobalQueue(Request $request)
    {
        $request['inProgress'] = true;
        $data = $this->courseType->getGlobalQueueData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getQueueDetails(Request $request)
    {
        $data = $this->courseType->getGlobalQueueDetail($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getScheduleTypeData(Request $request)
    {
        $data = $this->invoiceSettings->getValue([
            'college_id' => $request->college_id,
            'key' => $request->key,
        ], 'value');

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function setScheduleTypeData(Request $request)
    {
        $whereArr = [
            'college_id' => $request->college_id,
            'key' => $request->key,
        ];
        $data['created_by'] = Auth::user()->id;
        $data['updated_by'] = Auth::user()->id;
        $data['value'] = $request->value;

        try {
            $data = InvoiceSetting::updateOrCreate($whereArr, $data);

            return $this->successResponse('Update successfully', 'data', $data);
        } catch (ValidationException $e) {
            return $this->errorResponse('Fail to Update', 'data', []);
        }
    }

    public function deleteRecordData(Request $request)
    {

        if ($request->type == 1 && $request->isDeleteFail) {
            $status[] = GalaxyQueue::STATUS_FAILED;
            $status[] = GalaxyQueue::STATUS_COMPLETE;
        } else {
            $status[] = ($request->type == 1) ? GalaxyQueue::STATUS_COMPLETE : GalaxyQueue::STATUS_PENDING;
        }
        try {
            GalaxyQueue::whereIn('status', $status)->delete();

            return $this->successResponse('Update successfully', 'data', '');
        } catch (\Exception $e) {
            return $this->errorResponse('Fail to Update', 'data', []);
        }
    }

    public function getTeamsConditions(Request $request)
    {
        $currentTenant = tenant();
        if (! $currentTenant) {
            return $this->errorResponse('Tenant not found. Unable to save data.', 'data', [], 404);
        }
        $data['teams_conditions_short_course'] = $currentTenant->getMeta('teams_conditions_short_course');

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function saveTeamsConditions(Request $request)
    {
        $currentTenant = tenant();
        if (! $currentTenant) {
            return $this->errorResponse('Tenant not found. Unable to save data.', 'data', [], 404);
        }
        $currentTenant->setMeta('teams_conditions_'.$request->title, $request->teams_condition);

        return $this->successResponse('Set  successfully', 'data', '');
    }

    public function getPrivacyPolicy(Request $request)
    {
        $currentTenant = tenant();
        if (! $currentTenant) {
            return $this->errorResponse('Tenant not found. Unable to save data.', 'data', [], 404);
        }
        $data['privacy_policy_short_course'] = $currentTenant->getMeta('privacy_policy_short_course');

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function savePrivacyPolicy(Request $request)
    {
        $currentTenant = tenant();
        if (! $currentTenant) {
            return $this->errorResponse('Tenant not found. Unable to save data.', 'data', [], 404);
        }
        $currentTenant->setMeta('privacy_policy_'.$request->title, $request->privacy_policy);

        return $this->successResponse('Set privacy policy successfully', 'data', '');
    }
}
