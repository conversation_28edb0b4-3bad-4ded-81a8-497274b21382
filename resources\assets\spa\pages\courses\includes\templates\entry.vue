<template>
    <div>
        <div class="flex">
            <div
                :class="[
                    'hidden w-80 max-w-80 flex-10 duration-300 animate-in fade-in-50 lg:block',
                    visible
                        ? 'absolute left-0 top-[90px] !block bg-white pb-4 md:top-[74px]'
                        : 'block',
                ]"
                v-if="CourseNeedsTemplates"
            >
                <Button
                    type="button"
                    class="absolute -right-3.5 top-10 z-10 flex h-8 w-8 shrink-0 rotate-180 items-center justify-center rounded-full bg-gradient-to-r from-[#1E93FF] to-[#06B6D4] transition-all hover:bg-gradient-to-t lg:hidden"
                    @click="handlHideSidebar"
                >
                    <icon :name="'rightarrow'" :width="16" :height="16" :fill="'currentColor'" />
                </Button>
                <div
                    :class="[
                        'sticky top-10 max-h-[calc(100vh-74px)] overflow-y-auto duration-300 animate-in fade-in-50',
                    ]"
                >
                    <CourseTemplatesList
                        :isProfile="isProfile"
                        :templates="allTemplates"
                        :selected="currentTemplate"
                        @load="loadNewTemplate"
                        @duplicate="duplicateTemplate"
                        @delete="deleteTemplate"
                        @createnew="createNewTemplateBtn"
                    />
                </div>
            </div>
            <div class="flex-grow rounded-r-md bg-white p-6 duration-300 animate-in fade-in-50">
                <div class="space-y-6 rounded-lg border-none p-0">
                    <div class="space-y-1">
                        <div class="flex flex-nowrap justify-between gap-2 md:gap-4">
                            <div
                                class="flex w-full items-center gap-4"
                                :class="
                                    !CourseNeedsTemplates ? 'border-b border-b-gray-200 pb-2' : ''
                                "
                            >
                                <Button
                                    class="toggleSidebarMenu btn-secondary btn-icon block h-8 w-8 rounded-full hover:bg-primary-blue-500 hover:text-white lg:hidden"
                                    @click="handlShowSidebar"
                                >
                                    <icon
                                        :name="'leftarrow'"
                                        :width="16"
                                        :height="16"
                                        :fill="'currentColor'"
                                    />
                                </Button>
                                <FormSectionTitle
                                    :isProfile="isProfile"
                                    :text="getUnitSectionTitle"
                                    :info="true"
                                    :syncable="true"
                                    @edititem="editTemplate"
                                    @sync="forceSync"
                                    :hasBottomBorder="false"
                                />
                            </div>
                            <div
                                class="flex shrink-0 justify-end gap-3"
                                v-if="
                                    !currentTemplate.is_master_template &&
                                    !newTemplate &&
                                    CourseNeedsTemplates
                                "
                            >
                                <Button
                                    :size="'xs'"
                                    :variant="'secondary'"
                                    @click="duplicateTemplate(currentTemplate)"
                                >
                                    <icon
                                        :name="'copy'"
                                        :width="16"
                                        :height="16"
                                        :fill="'#9CA3AF'"
                                    />
                                    <span class="min-w-fit">Duplicate Template</span>
                                </Button>
                                <Button
                                    :size="'xs'"
                                    :variant="'secondary'"
                                    @click="deleteTemplate(currentTemplate)"
                                >
                                    <icon :name="'delete'" :width="16" :height="16" />
                                    <span class="min-w-fit text-red-700">Delete Template</span>
                                </Button>
                            </div>
                        </div>
                        <div class="flex space-x-4" v-if="CourseNeedsTemplates">
                            <div class="flex items-center space-x-1 text-gray-500">
                                <div>
                                    <icon
                                        :name="'author'"
                                        :width="16"
                                        :height="16"
                                        :fill="'#9CA3AF'"
                                    />
                                </div>
                                <div>
                                    Created by
                                    {{ currentTemplate?.creator?.name || 'Unknown User' }}
                                </div>
                            </div>
                            <div
                                class="flex items-center space-x-1"
                                v-if="currentTemplate?.creator || currentTemplate?.updater"
                            >
                                <div>
                                    <icon
                                        :name="'pen'"
                                        :width="16"
                                        :height="16"
                                        :fill="'#9CA3AF'"
                                    />
                                </div>
                                <div
                                    v-if="currentTemplate?.updater"
                                    class="flex items-center space-x-1 text-gray-500"
                                >
                                    <span>Updated</span>
                                    <span v-if="currentTemplate?.updated_text">{{
                                        currentTemplate?.updated_text
                                    }}</span>
                                    <datedisplay
                                        v-else
                                        :date="currentTemplate?.updated_at"
                                        :prefix="on"
                                        :pt="{ text: 'text-sm text-gray-500' }"
                                    />
                                    <span
                                        >by
                                        {{ currentTemplate?.updater?.name || 'Unknown User' }}</span
                                    >
                                </div>
                                <div v-else class="flex items-center space-x-1 text-gray-500">
                                    <span>Created</span>
                                    <span v-if="currentTemplate?.created_text">{{
                                        currentTemplate?.created_text
                                    }}</span>
                                    <datedisplay
                                        v-else
                                        :date="currentTemplate?.created_at"
                                        :prefix="on"
                                        :pt="{ text: 'text-sm text-gray-500' }"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    <courseinfo :coursedata="getCourseData" />
                    <div
                        class="flex items-start justify-between rounded-md border border-gray-200 p-4"
                        v-if="
                            !showTemplateForm &&
                            (isProfile || !currentTemplate.is_master_template) &&
                            CourseNeedsTemplates
                        "
                    >
                        <div class="grid grid-cols-1 gap-6 md:flex">
                            <div class="col-span-1 max-w-60 space-y-1">
                                <div class="font-medium text-gray-500">Major/Template Name</div>
                                <div class="text-gray-700">
                                    {{ currentTemplate.template_name }}
                                </div>
                            </div>
                            <div class="col-span-1 space-y-1">
                                <div class="font-medium text-gray-500">
                                    No of Core {{ isHigherEd ? 'Subjects' : 'Units' }}
                                </div>
                                <div class="text-gray-700">
                                    {{ currentTemplate.no_of_core_subject }}
                                </div>
                            </div>
                            <div class="col-span-1 space-y-1">
                                <div class="font-medium text-gray-500">
                                    No of Elective {{ isHigherEd ? 'Subjects' : 'Units' }}
                                </div>
                                <div class="text-gray-700">
                                    {{ currentTemplate.no_of_elective_subject }}
                                </div>
                            </div>
                            <div class="col-span-1 space-y-1">
                                <div class="font-medium text-gray-500">Set to Default</div>
                                <div class="text-gray-700">
                                    {{ currentTemplate.set_default ? 'Yes' : 'No' }}
                                </div>
                            </div>
                            <div class="col-span-1 space-y-1">
                                <div class="font-medium text-gray-500">Set to Active</div>
                                <div class="text-gray-700">
                                    {{ currentTemplate.set_active ? 'Yes' : 'No' }}
                                </div>
                            </div>
                        </div>
                        <Button :size="'sm'" :variant="'tertiary'" @click="editTemplate">
                            <div class="flex items-center space-x-1">
                                <icon :name="'pencil'" :width="16" :height="16" :fill="'#1890FF'" />
                                <span>Edit</span>
                            </div>
                        </Button>
                    </div>
                    <div v-else-if="!CourseNeedsTemplates">
                        <CoursePackagingRules :course="course" :currentTemplate="currentTemplate" />
                    </div>
                    <div>
                        <CourseTemplateForm
                            :formData="getTemplateFormData"
                            :course="course"
                            :isProfile="isProfile"
                            :hasTemplates="hasTemplateCreated"
                            v-if="showTemplateForm && CourseNeedsTemplates"
                            @saved="handleTemplateSave"
                            @cancel="closeTemplateForm"
                            @decided="handleTemplateOptionChange"
                        />
                    </div>
                    <div v-if="hasPackagingRules">
                        <GlobalContextLoader
                            :context="'loading-template-units'"
                            :type="'skeleton-list'"
                            :size="'single'"
                        >
                            <div
                                class="-mt-0.5 rounded-md rounded-tl-none rounded-tr-md border p-8"
                            >
                                <ManageUnits
                                    :course="course"
                                    :selectedUnits="selectedUnits"
                                    :currentTemplate="currentTemplate"
                                    :isMoodleConnect="isMoodleConnect"
                                    @added="updateCourseUnits"
                                    @deleted="handleUnitDeleted"
                                    @copyfromtemplate="copyFromOtherTemplate"
                                />
                            </div>
                        </GlobalContextLoader>
                    </div>
                    <div v-else class="space-y-1 rounded-md border p-4">
                        <div class="text-lg font-medium text-gray-700">
                            Provide packaging rules for this course.
                        </div>
                        <div class="text-gray-500">
                            The packaging regulations specify the quantity of elective and core
                            units required for this course. Therefore, please indicate the requisite
                            number of core units and elective units to be included in this course.
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <transition name="fade">
            <div v-if="copyTemplateUnitsWindow">
                <CopyTemplateUnits :visible="copyTemplateUnitsWindow" @closed="closeCopyWindow" />
            </div>
        </transition>
        <navigationbuttons @completed="handleSubmit" />
    </div>
</template>

<script>
import debounce from 'lodash/debounce';
import { mapState } from 'pinia';
import { router } from '@inertiajs/vue3';

import draggable from 'vuedraggable';
import { NumericTextBox, RadioGroup } from '@progress/kendo-vue-inputs';
import { useCoursesStore } from '@spa/stores/modules/courses';
import SelectedCourseInfo from '@spa/pages/courses/includes/SelectedCourseInfo.vue';
import PrimaryButton from '@spa/components/Buttons/PrimaryButton.vue';
import SecondaryButton from '@spa/components/Buttons/SecondaryButton.vue';
import Button from '@spa/components/Buttons/Button';
import CompetencyElements from '@spa/pages/courses/includes/CompetencyElements.vue';
import NavigationButtons from '@spa/pages/courses/includes/NavigationButtons.vue';
import FormSectionTitle from '@spa/pages/courses/commons/FormSectionTitle.vue';
import FormatDate from '@spa/components/FormatDate.vue';
import CoursePackagingRules from '@spa/pages/courses/includes/templates/CoursePackagingRules.vue';
import ManageUnits from '@spa/pages/courses/includes/templates/ManageUnits.vue';
import GlobalContextLoader from '@spa/components/Loader/GlobalContextLoader.vue';

import { courseSubjectsData, courseSubjectsDropdownData } from '@spa/services/courseFormResource';
import courseTemplateResource from '@spa/services/courseTemplateResource';

import useConfirm from '@spa/services/useConfirm';
import IconInput from '@spa/components/IconInput.vue';
import CourseTemplateForm from '@spa/pages/coursetemplates/includes/CourseTemplateForm.vue';
import CourseTemplatesList from '@spa/pages/courses/includes/templates/CourseTemplatesList.vue';
import Badge from '@spa/components/badges/Badge.vue';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import globalHelper from '@spa/plugins/global-helper';
import CopyTemplateUnits from '@spa/pages/courses/includes/templates/CopyTemplateUnits.vue';

export default {
    setup() {
        const store = useCoursesStore();
        const globalLoaderStore = useLoaderStore();
        const confirm = useConfirm();
        const templateResource = courseTemplateResource();
        const deleteTemplate = (item) => {
            const $confirm = useConfirm();
            const currentTemplate = store.getCurrentTemplate;
            let templateId = parseInt(item.id);
            if (isNaN(templateId) || templateId < 0) return false;
            const alertMessage = `Are you sure you want to remove selected template (${item.template_name})?`;
            $confirm.require({
                message: alertMessage,
                header: 'Confirmation',
                icon: 'pi pi-exclamation-triangle',
                accept: async () => {
                    try {
                        store.setTemplateProcess(true);
                        store.setTemplateReady(false);
                        let resp = await $http.delete(
                            route('spa.courses.templates.destroy', templateId)
                        );
                        store.setTemplateProcess(false);
                        store.setTemplateReady(true);
                        if (resp['success']) {
                            store.setCourseProgress(resp.data.progress);
                            store.setTemplates(resp.data.templates);
                            if (currentTemplate.id == item.id) {
                                //load the master template
                                store.setCurrentTemplate(store.getMasterTemplate);
                                loadTemplateUnits();
                            }
                        }
                    } catch (error) {
                        store.setTemplateProcess(false);
                        store.setTemplateReady(true);
                    }
                },
                reject: () => {
                    store.setTemplateProcess(false);
                    store.setTemplateReady(true);
                    //callback to execute when user rejects the action
                },
                onHide: () => {
                    //Callback to execute when dialog is hidden
                },
            });
        };
        const duplicateTemplate = (item) => {
            const $confirm = useConfirm();
            let templateId = parseInt(item.id);
            if (isNaN(templateId) || templateId < 0) return false;
            const alertMessage = `Are you sure you want to duplicate selected template (${item.template_name})?`;
            $confirm.require({
                message: alertMessage,
                header: 'Confirmation',
                icon: 'pi pi-exclamation-triangle',
                accept: async () => {
                    try {
                        globalLoaderStore.startContextLoading('loading-template-units');
                        store.setTemplateProcess(true);
                        store.setTemplateReady(false);
                        let resp = await $http.post(
                            route('spa.courses.templates.duplicate', templateId)
                        );
                        store.setTemplateProcess(false);
                        store.setTemplateReady(true);
                        if (resp['success']) {
                            store.setCourseProgress(resp.data.progress);
                            store.setTemplates(resp.data.templates);
                            store.setUnits(resp.data.units);
                            store.setCourseSubjects(resp.data.subjects);
                            store.setCurrentTemplate(resp.data.newtemplate);
                        }
                    } catch (error) {
                        store.setTemplateProcess(false);
                        store.setTemplateReady(true);
                    } finally {
                        globalLoaderStore.stopContextLoading('loading-template-units');
                    }
                },
                reject: () => {
                    store.setTemplateProcess(false);
                    store.setTemplateReady(true);
                    //callback to execute when user rejects the action
                },
                onHide: () => {
                    //Callback to execute when dialog is hidden
                },
            });
        };
        const loadTemplateUnits = async () => {
            const currentTemplate = store.getCurrentTemplate || [];
            const currentCourseId = currentTemplate.course_id || null;
            const currentTemplateId = currentTemplate.id || null;
            globalLoaderStore.startContextLoading('loading-template-units');
            await $http
                .get(route('spa.courses.loadtemplateunits', [currentCourseId, currentTemplateId]))
                .then((resp) => {
                    if (resp.success) {
                        store.setUnits(resp.units);
                        store.setCourseSubjects(resp.subjects);
                    }
                    globalLoaderStore.stopContextLoading('loading-template-units');
                })
                .finally(() => {
                    globalLoaderStore.stopContextLoading('loading-template-units');
                });
            return;
        };
        return {
            deleteTemplate,
            duplicateTemplate,
            globalLoaderStore,
            loadTemplateUnits,
        };
    },
    props: {
        setupMode: String,
        isMoodleConnect: Boolean,
    },
    data: function () {
        return {
            drag: false,
            packageSaving: false,
            selectUnitsVisible: false,
            coreCount: 0,
            electivesCount: 0,
            subjectData: [],
            allSubjects: [],
            templatesSearchText: '',
            filteredTemplates: [],
            loadingTemplateUnits: false,
            newTemplate: false,
            showTemplateForm: false,
            manageCompetencyElements: false,
            visible: false,
            copyTemplateUnitsWindow: false,
            showUnitsTab: false,
            showSubjectsTab: false,
        };
    },
    components: {
        courseinfo: SelectedCourseInfo,
        PrimaryButton,
        SecondaryButton,
        competencyelements: CompetencyElements,
        navigationbuttons: NavigationButtons,
        numericinput: NumericTextBox,
        draggable,
        FormSectionTitle,
        IconInput,
        CourseTemplateForm,
        RadioGroup,
        badge: Badge,
        Button,
        datedisplay: FormatDate,
        //new portion
        GlobalContextLoader,
        CourseTemplatesList,
        CoursePackagingRules,
        ManageUnits,
        CopyTemplateUnits,
    },
    mounted() {},
    computed: {
        ...mapState(useCoursesStore, [
            'course',
            'updateCourse',
            'selectedUnits',
            'availableUnits',
            'setUnits',
            'setUnitVariables',
            'updateCoursePackaging',
            'setCourseSubjects',
            'setCourseProgress',
            'formInits',
            'operationMode',
            'setTemplates',
            'setCurrentTemplate',
            'allTemplates',
            'currentTemplate',
            'masterTemplate',
            'createCurrentTemplateBuffer',
            'setCurrentTemplateFromBuffer',
        ]),
        getCourseData: function () {
            return this.course;
        },
        isHigherEd() {
            return this.isCourseHigherEd(this.course.course_type_id || null);
        },
        isProfile() {
            return true;
            return this.operationMode === 'profile';
        },
        hasPackagingRules() {
            const core = this.currentTemplate?.no_of_core_subject || 0;
            const elective = this.currentTemplate?.no_of_elective_subject || 0;
            return (
                (this.currentTemplate.is_master_template && core + elective > 0) ||
                (this.currentTemplate.id > 0 && core + elective > 0)
            );
        },
        getUnitSectionTitle() {
            if (!this.CourseNeedsTemplates) return 'Manage Units';
            const currentTemplateName = this.currentTemplate.template_name || 'Major Not Defined';
            if (this.newTemplate) return 'Create New Major';
            return this.isProfile
                ? `${currentTemplateName ? currentTemplateName : 'Manage Units'}`
                : 'Add Units';
        },
        getTemplateFormData() {
            if (this.newTemplate) {
                return {
                    template_name: '',
                    no_of_core_subject: this.currentTemplate.no_of_core_subject || null,
                    no_of_elective_subject: this.currentTemplate.no_of_elective_subject || null,
                    set_default: false,
                    set_active: true,
                    is_master_template: false,
                };
            }
            return this.currentTemplate || [];
        },
        hasTemplateCreated() {
            const createdTemplate = this.allTemplates.find((item) => item.is_master_template !== 1);
            return createdTemplate ? true : false;
        },
        getMasterTemplate() {
            return this.allTemplates.find((item) => item.is_master_template === 1);
        },
        //new methods
        CourseNeedsTemplates() {
            return !this.isCourseShortCourse(this.course.course_type_id || null);
        },
    },
    methods: {
        handleSubmit() {
            this.$emit('saved', []);
        },
        createNewTemplateBtn(template = {}) {
            const templateData = this.getMasterTemplate;
            this.newTemplate = true;
            this.showTemplateForm = true;
            this.createCurrentTemplateBuffer();
            this.setCurrentTemplate({
                id: null,
                template_name: null,
                course_id: templateData.course_id || null,
                college_id: templateData.college_id || null,
                no_of_core_subject: templateData.no_of_core_subject || null,
                no_of_elective_subject: templateData.no_of_elective_subject || null,
                set_default: 0,
                set_active: 1,
                is_master_template: 0,
            });
        },
        loadNewTemplate(currentTemplate) {
            const selectedTemplate = this.currentTemplate.id || null;
            this.newTemplate = false;
            this.showTemplateForm = false;
            this.setCurrentTemplate(currentTemplate);
            this.packaging = {
                core: this.currentTemplate.no_of_core_subject || null,
                elective: this.currentTemplate.no_of_elective_subject || null,
            };
            if (selectedTemplate !== currentTemplate.id) {
                //load only when the template is changed
                this.loadTemplateUnits();
            }
        },
        editTemplate() {
            this.newTemplate = false;
            this.showTemplateForm = true;
        },
        closeTemplateForm() {
            if (this.newTemplate) this.setCurrentTemplateFromBuffer();
            this.newTemplate = this.showTemplateForm = false;
        },
        handleTemplateSave(resp) {
            this.newTemplate = this.showTemplateForm = false;
            this.updateCourse(resp.course);
            this.setUnitVariables(resp.units);
            this.setTemplates(resp.templates);
            this.setCurrentTemplate(resp.saved);
            this.showTemplateForm = false;
        },
        handleUnitDeleted(resp) {
            this.updateCourseUnits(resp);
        },
        updateCourseUnits(resp) {
            if (resp.progress) {
                this.setCourseProgress(resp.progress);
            }
            if (resp.units) {
                this.setUnits(resp.units);
            }
            if (resp.subjects) {
                this.setCourseSubjects(resp.subjects);
            }
            if (resp.templates) {
                this.setTemplates(resp.subjects);
            }
        },
        handleTemplateOptionChange(optionValue) {
            const masterTemplate = this.getMasterTemplate;
            const createdTemplate = this.allTemplates.find((item) => item.is_master_template !== 1);
            if (optionValue === 'yes') {
                //create the new template
                //if template already created load that or show the new template form
                if (createdTemplate) {
                    this.loadNewTemplate(createdTemplate);
                } else {
                    const newTemplate = {
                        id: null,
                        college_id: masterTemplate.college_id || null,
                        template_name: '',
                        course_id: masterTemplate.course_id || null,
                        no_of_core_subject: masterTemplate.no_of_core_subject || 0,
                        no_of_elective_subject: masterTemplate.no_of_elective_subject || 0,
                        set_default: 0,
                        set_active: 1,
                        is_master_template: 0,
                    };
                    this.createNewTemplateBtn(newTemplate);
                }
            } else {
                //load the master template
                this.newTemplate = this.showTemplateForm = false;
                this.loadNewTemplate(masterTemplate);
            }
        },
        // deleteTemplate(item){
        //     $this.deleteTemplate(item)
        // }
        handlShowSidebar() {
            this.visible = true;
        },
        handlHideSidebar() {
            this.visible = false;
        },
        forceSync() {
            this.globalLoaderStore.startContextLoading('sync');
            this.globalLoaderStore.startContextLoading('loading-template-units');
            $http
                .post(route('spa.courses.forcesync'), {
                    course: this.course?.id,
                })
                .then((resp) => {
                    globalHelper.methods.showPopupSuccess(
                        'Sync with course legacy data successfully.',
                        'Success'
                    );
                    window.location.reload();
                })
                .catch((error) => {
                    // Handle the error
                    globalHelper.methods.showPopupError(
                        'Failed to sync with course legacy data.',
                        'Error'
                    );
                    this.globalLoaderStore.stopContextLoading('sync');
                    this.globalLoaderStore.stopContextLoading('loading-template-units');
                });
        },
        copyFromOtherTemplate() {
            this.copyTemplateUnitsWindow = !this.copyTemplateUnitsWindow;
        },
        closeCopyWindow() {
            this.copyTemplateUnitsWindow = false;
        },
    },
};
</script>
