<template>
    <div class="space-y-6">
        <div class="space-y-4">
            <FormSectionTitle :isProfile="isProfile" :text="'Hourse & Fees'" :info="true" />
            <courseinfo :coursedata="getCourseData" :class="'mb-4'" />
            {{ getFeeHoursData }}
            <k-form
                @submit="handleSubmit"
                :initial-values="getFeeHoursData"
                :ignoreModified="ignoreFormModified"
            >
                <formcontent :coursedata="getCourseData" />
            </k-form>
        </div>
    </div>
</template>

<script>
import { router } from '@inertiajs/vue3';
import { useForm } from '@inertiajs/vue3';
import axios from 'axios';
import { Form } from '@progress/kendo-vue-form';
import FormContent from './forms/HoursFeesFormContent.vue';
import SelectedCourseInfo from './SelectedCourseInfo.vue';
import { mapState } from 'pinia';
import { useCoursesStore } from '@spa/stores/modules/courses';
import FormSectionTitle from '@spa/pages/courses/commons/FormSectionTitle.vue';

import { courseGeneral, courseFeeDuration } from '@spa/services/courseFormResource';

export default {
    props: {},
    data: function () {
        return {
            cancelSource: axios.CancelToken.source(),
        };
    },
    components: {
        'k-form': Form,
        formcontent: FormContent,
        courseinfo: SelectedCourseInfo,
        FormSectionTitle,
    },
    computed: {
        ...mapState(useCoursesStore, [
            'course',
            'fees',
            'updateCourse',
            'operationMode',
            'updateFormState',
            'formState',
            'operationMode',
        ]),
        getCourseData: function () {
            return this.courseGeneral(this.course);
        },
        getFeeHoursData: function () {
            return this.courseFeeDuration(this.fees);
        },
        ignoreFormModified: function () {
            return this.operationMode === 'update';
        },
        isProfile() {
            return this.operationMode === 'profile';
        },
    },
    methods: {
        courseGeneral,
        courseFeeDuration,
        cancelsubmit() {
            if (this.cancelSource) {
                this.cancelSource.cancel('Request cancel');
            }
            this.cancelSource = axios.CancelToken.source();
        },
        handleSubmit(dataItem) {
            if (this.formState > 0) {
                return;
            }
            this.updateFormState(1);
            dataItem.activate_course = this.formSaveState;
            $http
                .put(this.route('spa.courses.savehoursfees'), dataItem, {
                    cancelToken: this.cancelSource.token,
                })
                .then((resp) => {
                    if (resp['success']) {
                        this.updateCourse(resp.course);
                        this.$emit('saved', resp);
                    }
                })
                .catch((error) => {
                    console.log('Form saving process cancelled');
                })
                .finally(() => {
                    this.updateFormState(0);
                });
        },
    },
    watch: {
        formState(newFormState, oldFormState) {
            if (newFormState === 2) {
                this.cancelsubmit();
            }
        },
    },
};
</script>
