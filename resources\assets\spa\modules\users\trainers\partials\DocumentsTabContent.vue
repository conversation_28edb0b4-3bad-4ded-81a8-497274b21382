<script setup>
import { computed } from 'vue';
import Card from '@spa/components/Card/Card.vue';
import TeacherMatrixListComponent from '@spa/modules/teacher-matrix/TeacherMatrixListComponent.vue';
import LeaveInfoListComponent from '@spa/modules/teachers/leave-info/LeaveInfoListComponent.vue';
import DocumentsListComponent from '@spa/modules/teacher-profile/documents/DocumentsListComponent.vue';

const props = defineProps({
    store: Object,
});
</script>

<template>
    <Card :variant="'compact'" :pt="{ root: 'bg-gray-100', content: 'p-0' }">
        <template #header>
            <div class="flex items-center gap-2">
                <h2 class="text-lg font-medium">Documents</h2>
            </div>
        </template>
        <template #content>
            <div v-if="store.formData?.id">
                <DocumentsListComponent :teacherId="store.formData?.id" />
            </div>
        </template>
    </Card>
</template>

<style scoped></style>
