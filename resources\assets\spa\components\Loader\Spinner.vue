<template>
    <div :class="rootClass" v-if="show">
        <svg
            xmlns="http://www.w3.org/2000/svg"
            :class="spinnerClass"
            :width="spinnerSize"
            :height="spinnerSize"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="1"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="lucide lucide-loader-circle-icon lucide-loader-circle animate-spin text-primary-blue-500"
        >
            <path d="M21 12a9 9 0 1 1-6.219-8.56" />
        </svg>
        <span :class="textClass" v-if="showText">{{ loadingText }}</span>
    </div>
</template>
<script>
import { twMerge } from 'tailwind-merge';

export default {
    props: {
        show: {
            type: Boolean,
            default: true,
        },
        showText: { type: Boolean, default: true },
        loadingText: {
            type: String,
            default: 'Please Wait...',
        },
        pt: {
            type: Object,
            default: {},
        },
        size: {
            type: String,
            default: '',
        },
    },
    computed: {
        textClass() {
            return twMerge('text-base font-medium', this.pt.text);
        },
        rootClass() {
            return twMerge(
                'flex h-full w-full items-center justify-center gap-4 p-4',
                this.pt.root
            );
        },
        spinnerClass() {
            return twMerge('animate-spin', this.pt.spinner);
        },
        spinnerSize() {
            const sizeMap = {
                xs: 12,
                sm: 14,
                base: 16,
                md: 18,
                lg: 24,
                xl: 28,
                '2xl': 36,
                '3xl': 48,
            };
            return sizeMap[this.size] || 50;
        },
    },
};
</script>
<style lang=""></style>
