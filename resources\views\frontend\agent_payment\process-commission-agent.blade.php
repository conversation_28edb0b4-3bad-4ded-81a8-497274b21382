@extends('frontend.layouts.frontend')
@section('title', $pagetitle )

@section('content')

@php
$routeGet = explode("/", Route::getCurrentRoute()->uri());
$activeVar = $routeGet[0];
@endphp

<!-- Main content -->
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="custom-header">
                <div class="row">
                    <div class="col-md-offset-11 col-md-1">
                        <span class="pull-right add-btn-block">
                            <a href="{{ route('view-agent-payment-list') }}" data-toggle="tooltip" data-original-title="Go back" >
                                <div class="btn-add"><i class="fa fa-reply"></i></div>
                            </a>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="nav-tabs-custom">
                <ul class="nav nav-tabs">
<!--                    <li class="{{ ($activeVar=='view-agent-payment-list') ? 'icon-active'  : '' }} ">
                        <a href="{{ route('view-agent-payment-list')}}">Search Agent</a></li>-->
                    <li class="active {{ ($activeVar=='process-commission-agent' ) ? 'icon-active' : '' }}"> 
                        <a href="{{ route('process-commission-agent',array('id'=>$agentId)) }}">Process Commission</a></li>
                    <li class="{{ ($activeVar=='payment-history') ? 'icon-active' : '' }}">
                        <a href="{{ route('spa.manage-users.agents.profile', array('id'=>encryptIt($agentId)))}}#payment">Payment History</a></li>
                    <li class="{{ ($activeVar=='credit-bonus-allocation') ? 'icon-active'  : '' }}">
                        <a href="{{ route('credit-bonus-allocation',array('id'=>$agentId))}}">Credit/Bonus Allocation</a></li>
                </ul>
                <div class="tab-content">
                    <div class="active tab-pane padding-30">
                        <div class="row form-horizontal">
                            <div class="col-md-12">
                                <div class="box box-info">
                                    <div class="custom-header">
                                        <h3 class="box-title">Process Commission for Agent : {{ $arrAgentDetail->agency_name }}</h3>
                                        <div style="clear:both"></div>
                                    </div>
                                    {{ Form::model(array('method' => 'post'),['class' => 'form-horizontal vertical-add-form','id'=>'processCommissionAgent']) }}
                                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                    <div class="box-body">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                {{ Form::hidden('agent_id',  $agentId, array('class' => 'form-control', 'id' => 'agent_id')) }}
                                                <label for="view_option" class="col-sm-4 control-label">View Option: </label>
                                                <div class="col-sm-6">
                                                    {{ Form::select('view_option', $arrAgentVewOption , null, array('class' => 'form-control', 'id' => 'view_option')) }}
                                                </div>   
                                            </div>
                                            <span id="search_by_div">
                                                <div class="form-group">
                                                    <label for="search_by" class="col-sm-4 control-label">Search By: </label>
                                                    <div class="col-sm-6">
                                                        {{ Form::select('search_by', $arrAgentSearchBy , null, array('class' => 'form-control', 'id' => 'search_by')) }}
                                                    </div>   
                                                </div>
                                            </span>    
                                            <div class="form-group">
                                                <label for="date_option" class="col-sm-4 control-label">Date Option: </label>
                                                <div class="col-sm-6">
                                                    {{ Form::select('date_option', $arrDateOption , null, array('class' => 'form-control ', 'id' => 'date_option')) }}
                                                </div>   
                                            </div>
                                            <span id="betweenDateField" style="display: none">
                                                <div class="form-group">
                                                    <label for="from_date" class="col-sm-4 control-label">From Date: </label>
                                                    <div class="col-sm-6">
                                                        {{ Form::text('from_date', date('d-m-Y') , array('class' => 'form-control dateField', 'id' => 'from_date','placeholder'=>'dd-mm-yyyy')) }}
                                                    </div>   
                                                </div>
                                            </span>
                                            <span id="tillDateField">
                                                <div class="form-group">
                                                    <label for="to_date" class="col-sm-4 control-label">To Date: </label>
                                                    <div class="col-sm-6">
                                                        {{ Form::text('to_date', date('d-m-Y'), array('class' => 'form-control dateField', 'id' => 'to_date','placeholder'=>'dd-mm-yyyy')) }}
                                                    </div>   
                                                </div>
                                            </span>
                                            <div class="form-group no-margin">
                                                <label class="col-sm-4">&nbsp;</label>
                                                <div class="col-sm-5" style="margin: 25px 0px 0px -11px;">
                                                    <input name="Add" id="viewData" class="btn btn-info" value="View" type="button">
                                                </div>
                                            </div>

                                        </div>
                                    </div>

                                    {{ Form::close() }}
                                </div>
                            </div>
                            <!--                            <div class="col-md-3">
                                                            <div class="box box-info">
                                                                <div class="custom-header">
                                                                    <h3 class="box-title">Agent Payment</h3>
                                                                    <a class="link-black text-sm pull-right" data-toggle="tooltip" data-original-title="Edit" style="font-size: 24px;" href="http://www.rtomanager.dev/apply-online/31"><i class="fa fa-edit"></i></a>
                                                                    <div style="clear:both"></div>
                                                                </div>     
                                                                <div class="box-body">
                                                                    <div id="midsidebar">
                                                                        @include('frontend.agent_payment.agent-payment-module-list')
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>-->
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="custom-header">
                                    <div class="row">
                                        <div class="col-md-10">
                                            <h3 class="box-title">Payment Schedule For Student</h3>
                                        </div>
                                        <div class="col-md-2">
                                            <span  class="pull-right add-btn-block">
                                                @if($isXeroConnect)
                                                    @if($syncOption == 'createPO')
                                                    <a href="javascript:void(0);" class="link-black text-sm pull-right" id="createPO" data-toggle="tooltip" data-original-title="Create PO for Commission" style="display: none;">
                                                        <div class="btn-add">
                                                            <i class="fa fa-sync"></i>
                                                        </div>
                                                    </a>
                                                    @else
                                                    <a href="javascript:void(0);" class="link-black text-sm pull-right" id="syncToXero" data-toggle="tooltip" data-original-title="Sync to Xero" style="display: none;">
                                                        <div class="btn-add">
                                                            <i class="fa fa-sync"></i>
                                                        </div>
                                                    </a>
                                                    @endif
                                                @endif
                                                {{--@if($sessionPermissionData=="yes")
                                                <a class="link-black text-sm pull-right approve-process" id="approve_process" data-toggle="tooltip" data-original-title="Process" style="font-size: 24px; display: none;" href="javascript:;">
                                                <div class="btn-add"><i class="fa fa-dot-circle-o"></i></div></a>
                                                @endif--}}
                                                <a class="link-black text-sm pull-right approve-check" id="approve_commission" data-toggle="tooltip" 
                                                data-original-title="Approve Commission" style="font-size: 24px;" href="javascript:;">
                                                <div class="btn-add">
                                                    <i class="fa fa-check"></i>
                                                </div>
                                                </a>
                                                <a class="link-black text-sm pull-right exportExcel" data-toggle="tooltip" data-original-title="Export To Ms Excel" style="font-size: 24px;" 
                                                href="javascript:;">
                                                <div class="btn-add">
                                                <i class="fa fa-file-excel" aria-hidden="true"></i>     
                                                </div>
                                                </a>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="box-body table-responsive no-padding">

                                    <table class="table table-hover table-custom" id="processCommission">
                                        <thead>
                                            <tr>
                                                <th scope="col">Check All<br />
                                                    {{ Form::checkbox('check_or_not',  1 , false, array('class' => 'check_or_not', 'id' => 'check_or_not')) }}
                                                </th>
                                                <th scope="col">Transaction <br/>No</th>
                                                <th scope="col">Invoice <br/>No</th>
                                                <th scope="col">Student <br/>Id</th>
                                                <th scope="col">Student <br/>Name</th>
                                                <th scope="col">Course <br/>Name</th>
                                                <th scope="col">Due <br/>Date</th>
                                                <th scope="col">Paid<br />Date</th>
                                                <th scope="col">Transaction <br/>Amount</th>
                                                <th scope="col">Refunded</th>
                                                <th scope="col">Commission</th>
                                                <th scope="col">GST</th>
                                                <th scope="col">Total <br/>Paid</th>
                                                <th scope="col">Commission <br/>Payable</th>
                                                <th scope="col">Remarks</th>
                                            </tr>
                                        </thead>
                                        <tbody id="dataAppend">
                                            <tr>
                                                <td colspan="16" style="text-align: center">
                                                    <p style="color:red;">No Record Found</p>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div> 
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--Processing Modal-->
    <div class="modal fade" id="processingModal" role="dialog">
        {{ Form::open( array('method' => 'post', 'id' => 'processing_modal_form', 'class' => 'form-horizontal vertical-add-form')) }}
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Processing Agent Commission</h4>
                </div>
                <div class="modal-body">
                    <div class="box box-info">
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <div class="col-sm-5">
                                            <label id="invalid_amount_message" class="col-md-12 green-font"></label>
                                        </div>
                                    </div>
                                    <div class="form-group margin-minus-15">
                                        <label for="formatted_invoice_no_value" class="col-sm-4 control-label">Invoice Number : </label>
                                        <div class="col-sm-8">
                                            <label id="formatted_invoice_no_value" class="control-label margin-b-10">NA</label>
                                            {{ Form::hidden('hidden_processing_id', null, array('class' => '', 'id' => 'hidden_processing_id')) }}
                                        </div>
                                    </div>
                                    <div class="form-group margin-minus-15">
                                        <label for="total_amount_payable" class="col-sm-4 control-label">Payable Amount : </label>
                                        <div class="col-sm-5">
                                            <label id="total_amount_payable_value" class="control-label margin-b-10">0 </label>
                                        </div>
                                    </div>
                                    <div class="form-group margin-minus-15">
                                        <label for="gst_amount" class="control-label col-sm-4">GST Amount : </label>
                                        <div class="col-sm-5">
                                            <label id="gst_amount_value" class="control-label margin-b-10">0 </label>
                                        </div>
                                    </div>
                                    <div class="form-group margin-minus-15">
                                        <label for="total_amount" class="control-label col-sm-4">Total Paid Amount : </label>
                                        <div class="col-sm-5">
                                            <label id="total_amount_value" class="control-label margin-b-10">0 </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="payment_mode" class="control-label  col-sm-4">Payment Mode : </label>
                                    <div class="col-sm-5">
                                        {{ Form::select('payment_mode',$arrPaymentMode,null, array('class' => 'form-control', 'id' => 'payment_mode')) }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="commission_paid_date" class="control-label col-sm-4">Commission Paid Date : </label>
                                    <div class="col-sm-5">
                                        {{ Form::text('commission_paid_date',date('d-m-Y'), array('class' => 'form-control', 'id' => 'commission_paid_date')) }}
                                    </div>
                                </div>
                                <div class="form-group ">
                                    <label for="remarks_processing" class="control-label col-sm-4">Remarks :<span id="" class="required-field">*<div></div></span></label>
                                    <div class="col-sm-7">
                                        {{ Form::textarea('remarks_processing', null , array('class' => 'form-control ', 'id' => 'remarks_processing' , 'rows' => '3')) }}
                                    </div>
                                </div>
                                <div class="col-sm-12 text-center">
                                    <input name="Add" class="btn btn-info" id="submit_processing_commission" value="Process Commission" type="submit">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">                    
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
        {{ Form::close() }}
    </div>
    <!--End Processing Modal-->

    <!--Delete Module-->
    <div class="modal fade" id="deleteModal" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Delete Record</h4>
                </div>
                <div class="modal-body">
                    <div class="box box-info">
                        <div class="box-body">
                            <p> You want to delete record. Are you sure?</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                    <button class="btn btn-success yes-sure" type="button">Yes</button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>
    <!-- End Delete Module-->

    <!--Disapprove Module-->
    <div class="modal fade" id="disapproveModal" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Disapprove Record</h4>
                </div>
                <div class="modal-body">
                    <div class="box box-info">
                        <div class="box-body">
                            <p> Are you sure you want to disapprove this record? </p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                    <button class="btn btn-success yes-sure-disapprove" type="button">Yes</button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>
    <!-- End Disapprove Module-->

    <!--Sync Confirm Modal-->
    <div class="modal fade" id="syncConfirmModal" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Sync To Xero</h4>
                </div>
                <div class="modal-body">
                    <div class="box box-info">
                        <div class="box-body">
                            <p> Are you sure you want to sync this selected item with xero?</p>
                            <input type="hidden" name="ids" class="commissionIds" value="" />
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                    <button class="btn btn-success syncConfirm" type="button">Yes</button>
                </div>
            </div>
        </div>
    </div>
    <!-- End Sync Confirm Modal-->

    <!--Create PO Confirm Modal-->
    <div class="modal fade" id="poConfirmModal" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Create PO</h4>
                </div>
                <div class="modal-body">
                    <div class="box box-info">
                        <div class="box-body">
                            <p> Are you sure you want to create PO for this selected item with xero?</p>
                            <input type="hidden" name="ids" class="commissionIds" value="" />
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                    <button class="btn btn-success poConfirm" type="button">Yes</button>
                </div>
            </div>
        </div>
    </div>
    <!-- End PO Confirm Modal-->
</section>
<!-- Main content -->

<style>
    .error {
        border: 1px solid red !important;
    }
    #midsidebar ul{
        list-style: none;
        padding: 0 !important;
    }
    #midsidebar ul li a{
        color: #15a3f5;
        font-size: 14px;
        line-height: 22px;
    }
    #midsidebar ul li.active{
        background: #e7e7e7;
    }
    #midsidebar ul li.active a{
        padding: 0 0 0 10px !important;
    }
</style>

@endsection