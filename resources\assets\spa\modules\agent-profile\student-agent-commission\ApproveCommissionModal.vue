<template>
    <k-dialog
        :visibleDialog="visible"
        :hideOnOverlayClick="true"
        :fixedActionBar="false"
        :width="'500px'"
        :isDisabled="loading"
        :isSubmitting="loading"
        @modalclose="handleClose"
    >
        <template #title>
            <div class="flex items-center gap-3">
                <span class="font-semibold text-gray-800">Approve Commission</span>
            </div>
        </template>

        <template #content>
            <div class="space-y-4">
                <p class="text-gray-700">Are you sure you want to approve this commission?</p>
                <div>
                    <label
                        for="approve-remarks"
                        class="mb-2 block text-sm font-medium text-gray-700"
                    >
                        Remarks (optional):
                    </label>
                    <textarea
                        id="approve-remarks"
                        v-model="remarks"
                        placeholder="Enter remarks..."
                        rows="4"
                        class="resize-vertical w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-700 focus:outline-none focus:ring-1 focus:ring-blue-700"
                    ></textarea>
                </div>
            </div>
        </template>

        <template #footer>
            <div class="mt-5 flex justify-end gap-3">
                <Button variant="secondary" @click="handleClose" :disabled="loading">
                    Cancel
                </Button>
                <Button variant="primary" @click="handleApprove" :disabled="loading">
                    {{ loading ? 'Approving...' : 'Approve' }}
                </Button>
            </div>
        </template>
    </k-dialog>
</template>

<script>
import KendoDialog from '@spa/components/KendoModals/KendoDialog.vue';
import Button from '@spa/components/Buttons/Button.vue';
export default {
    components: {
        'k-dialog': KendoDialog,
        Button,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        item: {
            type: Object,
            default: null,
        },
        loading: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['close', 'approve'],
    data() {
        return {
            remarks: '',
        };
    },
    watch: {
        visible(newVisible) {
            if (newVisible) {
                this.remarks = '';
            }
        },
    },
    methods: {
        handleClose() {
            this.remarks = '';
            this.$emit('close');
        },
        handleApprove() {
            this.$emit('approve', {
                item: this.item,
                remarks: this.remarks.trim(),
            });
        },
    },
};
</script>
