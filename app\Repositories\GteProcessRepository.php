<?php

namespace App\Repositories;

use App\Model\v2\GteDocumentMaster;
use App\Model\v2\GteEnrollmentDocuments;
use App\Model\v2\GtePaymentsDocuments;
use App\Model\v2\GteProcessComment;
use App\Model\v2\GteProcessDocuments;
use App\Model\v2\GteStudentDocuments;
use App\Model\v2\GteVisaDocuments;
use App\Model\v2\Student;
use App\Model\v2\StudentCourses;
use App\Traits\CommonTrait;
use DB;
use Illuminate\Database\Eloquent\Model;

class GteProcessRepository
{
    use CommonTrait;

    // model property on class instances
    protected $model;

    // Constructor to bind model to repo
    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    // Get all instances of model
    public function all()
    {
        return $this->model->all();
    }

    // create a new record in the database
    public function create(array $data)
    {
        return $this->model->create($data);
    }

    // update record in the database
    public function update(array $data, $id)
    {
        $record = $this->model->find($id);

        return $record->update($data);
    }

    public function delete($id)
    {
        return $this->model->destroy($id);
    }

    public function modify(array $data, array $where)
    {
        return $this->model->where($where)->update($data);
    }

    public function getWhere($whereArr, $fields = '*')
    {
        return $this->model->where($whereArr)->select($fields)->get()->first();
    }

    public function getData($whereArr, $fields = '*')
    {
        return $this->model->where($whereArr)->select($fields)->get()->toArray();
    }

    public function deleteAll($ids)
    {
        return $this->model->whereIn('id', $ids)->delete();
    }

    public function checkStatus($whereArr)
    {
        return $this->model->where($whereArr)->orderBy('id', 'desc')->first();
    }

    public function getStudentData($request, $countOnly = false)
    {
        $collegeId = $request->user()->college_id;
        $post = ($request->input()) ? $request->input() : [];
        $customFilterParts = (isset($post['filter']) && isset($post['filter']['filters'])) ? $post['filter']['filters'] : [];

        // echo "<pre>";print_r($customFilterParts); exit;

        $columnArr = [
            'rs.id',
            'rsc.id as student_course_id',
            'rs.profile_picture',
            DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as student_name"),
            'rs.application_reference_id',
            'rsc.offer_status',
            'rs.created_at as createdAt',
            DB::raw("CONCAT(DATE_FORMAT(rsc.start_date, '%d %b %Y'),' - ',DATE_FORMAT(rsc.finish_date, '%d %b %Y')) as study_peroid"),
            // DB::raw("CONCAT(rsc.start_date,'-',rsc.finish_date) as study_peroid"),
            'ra.agency_name as agent_name',
            'rs.student_type',
            DB::raw("CONCAT(rc.course_code,' - ',rc.course_name) as course_list"),
            'rsc.coe_name', 'rc.id as course_id',
            'rs.status', 'rsc.offer_id', 'rsc.status as checkstatus',
            'rsc.intake_date',
            // DB::raw("DATE_FORMAT(rsc.intake_date, '%d %b %Y') as intake_date"),
            // date('m-d-Y', strtotime('rsc.intake_date')),
            'rs.DOB',
            'rs.USI',
            'rs.stud_id_type',
            'rs.is_applicant',
            'rs.is_offered',
            'rs.is_student',
            'campus.name as campus_name',
            'ru.name',
            'rs.generated_stud_id as generatedStudId',
            'campus.name as campus',
        ];

        /* This array use for filterable only */
        $columns = [
            'application_reference_id' => 'rs.application_reference_id',
            'student_name' => dbRawL10("CONCAT(rs.first_name,' ',rs.family_name) as student_name"),
            'createdAt' => 'rs.created_at as createdAt',
            'study_peroid' => dbRawL10("CONCAT(DATE_FORMAT(rsc.start_date, '%d %b %Y'),' - ',DATE_FORMAT(rsc.finish_date, '%d %b %Y')) as study_peroid"),
            'course_list' => dbRawL10("CONCAT(rc.course_code,' - ',rc.course_name) as course_list"),
            'student_type' => 'rs.student_type',
            'agent_name' => 'ra.agency_name as agent_name',
            'offer_status' => 'rsc.offer_status',
            'intake_date' => 'rsc.intake_date',
        ];

        $query = Student::alias('rto_students as rs')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->leftjoin('rto_campus as campus', 'campus.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rs.created_by')
            ->where(['rs.college_id' => $collegeId, 'is_offered' => 1])
            ->where('rsc.offer_status', '!=', 'Enrolled')
            ->orderBy('rs.id', 'desc')
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);

        foreach ($customFilterParts as $filter) {
            if (isset($filter['field']) && isset($filter['value']) && $filter['field'] == 'extra') {
                /* grid wise custom filter apply here */
                $query->where(function ($childQuery) use ($filter) {
                    foreach ($filter['value'] as $fieldName => $fieldvalue) {
                        if (count($fieldvalue) > 0) {
                            if ($fieldName == 'course') {
                                $childQuery->whereIn('rc.id', $fieldvalue);
                            } elseif ($fieldName == 'agent') {
                                $childQuery->whereIn('ra.id', $fieldvalue);
                            } elseif ($fieldName == 'status') {
                                $childQuery->whereIn('rsc.id', $fieldvalue);
                            }
                        }
                    }
                });
            }
        }

        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function uploadedOfferLetterData($request, $countOnly = false)
    {

        $collegeId = $request->college_id;
        $studentId = $request->student_id;

        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'rgpd.id',
            'rgpd.college_id',
            'rgpd.student_id',
            'rgpd.document_name',
            'rgpd.file_original_name',
            'ru.name as added_by',
            'approval.name as approval_name',
            DB::raw('DATE_FORMAT(rgpd.updated_at, "%d-%b-%Y") as approval_date'),
            'rgpd.created_at as date',
            'rgpd.status',
            'rgpd.comment',
            DB::raw('DATE_FORMAT(rgpd.created_at, "%d-%b-%Y") as uploaded_date'),
        ];

        /* This array use for filterable only */
        $columns = [
            'date' => 'rgpd.created_at as date',
            'file_original_name' => 'rgpd.file_original_name',
            'added_by' => 'ru.name as added_by',
            'status' => 'rgpd.status',
        ];

        $query = GteProcessDocuments::from('rto_gte_process_documents as rgpd')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rgpd.created_by')
            ->leftjoin('rto_users as approval', 'approval.id', '=', 'rgpd.updated_by')
            ->where(['rgpd.college_id' => $collegeId, 'rgpd.student_id' => $studentId])
            ->orderBy('rgpd.id', 'desc')
            ->select($columnArr)
            ->limit(1);

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        if ($countOnly) {
            $result = $query->get()->count();
        } else {
            $result = $query->get()->toArray();
        }

        return $result;
    }

    public function previousOfferLetterData($request, $countOnly = false)
    {

        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $post = ($request->input()) ? $request->input() : [];

        // dd($post);
        $columnArr = [
            'rgpd.id',
            'rgpd.college_id',
            'rgpd.student_id',
            'rgpd.document_name',
            'rgpd.file_original_name',
            'ru.name as added_by',
            'approval.name as approval_name',
            DB::raw('DATE_FORMAT(rgpd.updated_at, "%d-%b-%Y") as approval_date'),
            'rgpd.created_at as date',
            'rgpd.status',
            'rgpd.comment',
            DB::raw('DATE_FORMAT(rgpd.created_at, "%d-%b-%Y") as uploaded_date'),
        ];

        $whereArr = [
            'rgpd.college_id' => $collegeId,
            'rgpd.student_id' => $studentId,
        ];
        $columns = [
            'date' => 'rgpd.created_at as date',
            'file_original_name' => 'rgpd.file_original_name',
            'added_by' => 'ru.name as added_by',
            'status' => 'rgpd.status as status',
        ];

        $lastId = GteProcessDocuments::from('rto_gte_process_documents as rgpd')->where($whereArr)->orderBy('rgpd.id', 'desc')->first();

        $query = GteProcessDocuments::from('rto_gte_process_documents as rgpd')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rgpd.created_by')
            ->leftjoin('rto_users as approval', 'approval.id', '=', 'rgpd.updated_by')
            ->where($whereArr);

        if (isset($lastId)) {
            $query->whereNotIn('rgpd.id', [$lastId->id])->select($columnArr);
        } else {
            $query->select($columnArr);
        }

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        if ($countOnly) {
            $result = $query->get()->count();
        } else {
            $result = $query->get()->toArray();
        }

        return $result;
    }

    public function uploadedPaymentData($request, $countOnly = false)
    {

        $collegeId = $request->college_id;
        $studentId = $request->student_id;

        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'rgpds.id',
            'rgpds.college_id',
            'rgpds.student_id',
            'rgpds.document_name',
            'rgpds.file_original_name',
            'ru.name as added_by',
            'approval.name as approval_name',
            DB::raw('DATE_FORMAT(rgpds.updated_at, "%d-%b-%Y") as approval_date'),
            'rgpds.created_at as date',
            'rgpds.status',
            'rgpds.comment',
            DB::raw('DATE_FORMAT(rgpds.created_at, "%d-%b-%Y") as uploaded_date'),
        ];

        /* This array use for filterable only */
        $columns = [
            'date' => 'rgpds.created_at as date',
            'file_original_name' => 'rgpds.file_original_name',
            'added_by' => 'ru.name as added_by',
            'status' => 'rgpds.status',
        ];

        $query = GtePaymentsDocuments::from('rto_gte_payment_document as rgpds')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rgpds.created_by')
            ->leftjoin('rto_users as approval', 'approval.id', '=', 'rgpds.updated_by')
            ->where(['rgpds.college_id' => $collegeId, 'rgpds.student_id' => $studentId])
            ->orderBy('rgpds.id', 'desc')
            ->select($columnArr)
            ->limit(1);

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        if ($countOnly) {
            $result = $query->get()->count();
        } else {
            $result = $query->get()->toArray();
        }

        return $result;
    }

    public function previousPaymentData($request, $countOnly = false)
    {

        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'rgpds.id',
            'rgpds.college_id',
            'rgpds.student_id',
            'rgpds.document_name',
            'rgpds.file_original_name',
            'ru.name as added_by',
            'approval.name as approval_name',
            DB::raw('DATE_FORMAT(rgpds.updated_at, "%d-%b-%Y") as approval_date'),
            'rgpds.created_at as date',
            'rgpds.status',
            'rgpds.comment',
            DB::raw('DATE_FORMAT(rgpds.created_at, "%d-%b-%Y") as uploaded_date'),
        ];

        $whereArr = [
            'rgpds.college_id' => $collegeId,
            'rgpds.student_id' => $studentId,
        ];
        $columns = [
            'date' => 'rgpds.created_at as date',
            'file_original_name' => 'rgpds.file_original_name',
            'added_by' => 'ru.name as added_by',
            'status' => 'rgpds.status as status',
        ];

        $lastId = GtePaymentsDocuments::from('rto_gte_payment_document as rgpds')->where($whereArr)->orderBy('rgpds.id', 'desc')->first();

        $query = GtePaymentsDocuments::from('rto_gte_payment_document as rgpds')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rgpds.created_by')
            ->leftjoin('rto_users as approval', 'approval.id', '=', 'rgpds.updated_by')
            ->where($whereArr);

        if (isset($lastId)) {
            $query->whereNotIn('rgpds.id', [$lastId->id])->select($columnArr);
        } else {
            $query->select($columnArr);
        }

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        if ($countOnly) {
            $result = $query->get()->count();
        } else {
            $result = $query->get()->toArray();
        }

        return $result;
    }

    public function uploadedConfirmationEnrollData($request, $countOnly = false)
    {

        $collegeId = $request->college_id;
        $studentId = $request->student_id;

        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'rged.id',
            'rged.college_id',
            'rged.student_id',
            'rged.document_name',
            'rged.file_original_name',
            'ru.name as added_by',
            'approval.name as approval_name',
            DB::raw('DATE_FORMAT(rged.updated_at, "%d-%b-%Y") as approval_date'),
            'rged.created_at as date',
            'rged.status',
            'rged.comment',
            DB::raw('DATE_FORMAT(rged.created_at, "%d-%b-%Y") as uploaded_date'),
        ];

        /* This array use for filterable only */
        $columns = [
            'date' => 'rged.created_at as date',
            'file_original_name' => 'rged.file_original_name',
            'added_by' => 'ru.name as added_by',
            'status' => 'rged.status',
        ];

        $query = GteEnrollmentDocuments::from('rto_gte_enrollment_document as rged')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rged.created_by')
            ->leftjoin('rto_users as approval', 'approval.id', '=', 'rged.updated_by')
            ->where(['rged.college_id' => $collegeId, 'rged.student_id' => $studentId])
            ->orderBy('rged.id', 'desc')
            ->select($columnArr)
            ->limit(1);

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        if ($countOnly) {
            $result = $query->get()->count();
        } else {
            $result = $query->get()->toArray();
        }

        return $result;
    }

    public function previousConfirmationEnrollData($request, $countOnly = false)
    {

        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'rged.id',
            'rged.college_id',
            'rged.student_id',
            'rged.document_name',
            'rged.file_original_name',
            'ru.name as added_by',
            'approval.name as approval_name',
            DB::raw('DATE_FORMAT(rged.updated_at, "%d-%b-%Y") as approval_date'),
            'rged.created_at as date',
            'rged.status',
            'rged.comment',
            DB::raw('DATE_FORMAT(rged.created_at, "%d-%b-%Y") as uploaded_date'),
        ];

        $whereArr = [
            'rged.college_id' => $collegeId,
            'rged.student_id' => $studentId,
        ];
        $columns = [
            'date' => 'rged.created_at as date',
            'file_original_name' => 'rged.file_original_name',
            'added_by' => 'ru.name as added_by',
            'status' => 'rged.status as status',
        ];

        $lastId = GteEnrollmentDocuments::from('rto_gte_enrollment_document as rged')->where($whereArr)->orderBy('rged.id', 'desc')->first();

        $query = GteEnrollmentDocuments::from('rto_gte_enrollment_document as rged')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rged.created_by')
            ->leftjoin('rto_users as approval', 'approval.id', '=', 'rged.updated_by')
            ->where($whereArr);

        if (isset($lastId)) {
            $query->whereNotIn('rged.id', [$lastId->id])->select($columnArr);
        } else {
            $query->select($columnArr);
        }

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        if ($countOnly) {
            $result = $query->get()->count();
        } else {
            $result = $query->get()->toArray();
        }

        return $result;
    }

    public function uploadedVisaData($request, $countOnly = false)
    {

        $collegeId = $request->college_id;
        $studentId = $request->student_id;

        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'rgvd.id',
            'rgvd.college_id',
            'rgvd.student_id',
            'rgvd.document_name',
            'rgvd.file_original_name',
            'ru.name as added_by',
            'approval.name as approval_name',
            DB::raw('DATE_FORMAT(rgvd.updated_at, "%d-%b-%Y") as approval_date'),
            'rgvd.created_at as date',
            'rgvd.status',
            'rgvd.comment',
            DB::raw('DATE_FORMAT(rgvd.created_at, "%d-%b-%Y") as uploaded_date'),
        ];

        /* This array use for filterable only */
        $columns = [
            'date' => 'rgvd.created_at as date',
            'file_original_name' => 'rgvd.file_original_name',
            'added_by' => 'ru.name as added_by',
            'status' => 'rgvd.status',
        ];

        $query = GteVisaDocuments::from('rto_gte_visa_document as rgvd')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rgvd.created_by')
            ->leftjoin('rto_users as approval', 'approval.id', '=', 'rgvd.updated_by')
            ->where(['rgvd.college_id' => $collegeId, 'rgvd.student_id' => $studentId])
            ->orderBy('rgvd.id', 'desc')
            ->select($columnArr)
            ->limit(1);

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        if ($countOnly) {
            $result = $query->get()->count();
        } else {
            $result = $query->get()->toArray();
        }

        return $result;
    }

    public function previousVisaData($request, $countOnly = false)
    {

        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'rgvd.id',
            'rgvd.college_id',
            'rgvd.student_id',
            'rgvd.document_name',
            'rgvd.file_original_name',
            'ru.name as added_by',
            'approval.name as approval_name',
            DB::raw('DATE_FORMAT(rgvd.updated_at, "%d-%b-%Y") as approval_date'),
            'rgvd.created_at as date',
            'rgvd.status',
            'rgvd.comment',
            DB::raw('DATE_FORMAT(rgvd.created_at, "%d-%b-%Y") as uploaded_date'),
        ];

        $whereArr = [
            'rgvd.college_id' => $collegeId,
            'rgvd.student_id' => $studentId,
        ];
        $columns = [
            'date' => 'rgvd.created_at as date',
            'file_original_name' => 'rgvd.file_original_name',
            'added_by' => 'ru.name as added_by',
            'status' => 'rgvd.status as status',
        ];

        $lastId = GteVisaDocuments::from('rto_gte_visa_document as rgvd')->where($whereArr)->orderBy('rgvd.id', 'desc')->first();

        $query = GteVisaDocuments::from('rto_gte_visa_document as rgvd')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rgvd.created_by')
            ->leftjoin('rto_users as approval', 'approval.id', '=', 'rgvd.updated_by')
            ->where($whereArr);

        if (isset($lastId)) {
            $query->whereNotIn('rgvd.id', [$lastId->id])->select($columnArr);
        } else {
            $query->select($columnArr);
        }

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        if ($countOnly) {
            $result = $query->get()->count();
        } else {
            $result = $query->get()->toArray();
        }

        return $result;
    }

    public function getGteDetails($studentId, $studCourseId = '')
    {

        $query = StudentCourses::from('rto_student_courses as rsc')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->where('rs.id', '=', $studentId)
                                // ->select($columns)
            ->select(
                DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as student_name"),
                'ra.agency_name as agent_name',
                'rs.application_reference_id',
                'rs.profile_picture',
                'ra.primary_email as agent_email',
                DB::raw("CONCAT(rc.course_code,' - ',rc.course_name) as course_name"),
                'rsc.id as student_course_id',
                'rs.id as student_id')
            ->orderBy('rsc.start_date', 'desc');

        if (! empty($studCourseId)) {
            $query->where('rsc.id', $studCourseId);
        }
        $result = $query->get();

        if ($result) {
            $studentId = $result[0]['student_id'];
            $result[0]['profile_pic'] = $this->getStudentProfilePicPath($studentId, $result[0]['profile_picture']);
        }

        return $result;
    }

    public function getAgentDetails($studentCourseId)
    {

        $result = StudentCourses::from('rto_student_courses as rsc')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->where('rsc.id', '=', $studentCourseId)
            ->select('ra.agency_name as agent_name', 'ra.primary_email as agent_email')
            ->get();

        return $result;
    }

    public function uploadedGteDocumentData($request, $countOnly = false)
    {

        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $gteDocumentId = $request->uploadid;

        $post = ($request->input()) ? $request->input() : [];
        $customFilterParts = (isset($post['filter']) && isset($post['filter']['filters'])) ? $post['filter']['filters'] : [];

        $columnArr = [
            'rgsd.id',
            'rgsd.college_id',
            'rgsd.student_id',
            'rgsd.document_file_name',
            'rgsd.file_original_name',
            DB::raw('DATE_FORMAT(rgsd.updated_at, "%d-%b-%Y") as approval_date'),
            'ru.name as added_by',
            'approval.name as approval_name',
            'rgsd.created_at as date',
            'rgsd.status',
            'rgsd.comment',
            DB::raw('DATE_FORMAT(rgsd.created_at, "%d-%b-%Y") as uploaded_date'),
        ];

        /* This array use for filterable only */
        $columns = [
            'date' => 'rgsd.created_at as date',
            'file_original_name' => 'rgsd.file_original_name',
            'added_by' => 'ru.name as added_by',
            'status' => 'rgsd.status',
        ];

        $query = GteStudentDocuments::from('rto_gte_student_document as rgsd')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rgsd.created_by')
            ->leftjoin('rto_users as approval', 'approval.id', '=', 'rgsd.updated_by')
            ->where(['rgsd.college_id' => $collegeId, 'rgsd.student_id' => $studentId,  'rgsd.gte_document_id' => $gteDocumentId])
            ->orderBy('rgsd.id', 'desc')
            ->select($columnArr)
            ->limit(1);

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        if ($countOnly) {
            $result = $query->get()->count();
        } else {
            $result = $query->get()->toArray();
        }

        return $result;
    }

    public function previousGteDocumentsData($request, $countOnly = false)
    {

        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $gteDocumentId = $request->uploadid;

        $post = ($request->input()) ? $request->input() : [];
        $customFilterParts = (isset($post['filter']) && isset($post['filter']['filters'])) ? $post['filter']['filters'] : [];

        $columnArr = [
            'rgsd.id',
            'rgsd.college_id',
            'rgsd.student_id',
            'rgsd.document_file_name',
            'rgsd.file_original_name',
            'ru.name as added_by',
            'approval.name as approval_name',
            DB::raw('DATE_FORMAT(rgsd.updated_at, "%d-%b-%Y") as approval_date'),
            'rgsd.created_at as date',
            'rgsd.status',
            'rgsd.comment',
            DB::raw('DATE_FORMAT(rgsd.created_at, "%d-%b-%Y") as uploaded_date'),
        ];

        /* This array use for filterable only */
        $columns = [
            'date' => 'rgsd.created_at as date',
            'file_original_name' => 'rgsd.file_original_name',
            'added_by' => 'ru.name as added_by',
            'status' => 'rgsd.status',
        ];

        $whereArr = [
            'rgsd.college_id' => $collegeId,
            'rgsd.student_id' => $studentId,
            'rgsd.gte_document_id' => $gteDocumentId,
        ];

        $lastId = GteStudentDocuments::from('rto_gte_student_document as rgsd')->where($whereArr)->orderBy('rgsd.id', 'desc')->first();

        $query = GteStudentDocuments::from('rto_gte_student_document as rgsd')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rgsd.created_by')
            ->leftjoin('rto_users as approval', 'approval.id', '=', 'rgsd.updated_by')
            ->where($whereArr);

        if (isset($lastId)) {
            $query->whereNotIn('rgsd.id', [$lastId->id])->select($columnArr);
        } else {
            $query->select($columnArr);
        }

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);
        if ($countOnly) {
            $result = $query->get()->count();
        } else {
            $result = $query->get()->toArray();
        }

        return $result;
    }

    public function getGTEDocumentData($studentId)
    {

        $result = GteDocumentMaster::from('rto_gte_document_master as rgdm')->get()->toArray();
        for ($i = 0; $i < count($result); $i++) {
            $whereArr = [
                'rgsd.college_id' => $result[$i]['college_id'],
                'rgsd.student_id' => $studentId,
                'rgsd.gte_document_id' => $result[$i]['id'],
            ];
            $status = GteStudentDocuments::from('rto_gte_student_document as rgsd')->where($whereArr)->orderBy('rgsd.id', 'desc')->limit(1)->get()->toArray();

            $result[$i]['status'] = (isset($status[0])) ? $status[0]['status'] : 'Not Uploaded';

        }

        return $result;

    }

    public function gteProcessComment($request)
    {
        $columnArr = [

            'college_id',
            'student_id',
            'offer_comment',
            'document_comment',
            'payment_comment',
            'confirmation_comment',
            'visa_status_comment',
        ];
        $columns = [

            'college_id' => 'college_id',
            'student_id' => 'student_id',
            'offer_comment' => 'offer_comment',
            'document_comment' => 'document_comment',
            'payment_comment' => 'payment_comment',
            'confirmation_comment' => 'confirmation_comment',
            'visa_status_comment' => 'visa_status_comment',

        ];
        $query = GteProcessComment::from('rto_gte_process_comments');

    }

    public function getManageofferResult($request)
    {

        $collegeId = $request->user()->college_id;
        $categoryId = $request->input('id');

        $query = Student::alias('rto_students as rs')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->where(['rs.college_id' => $collegeId, 'is_offered' => 1])
            ->where('rsc.offer_status', '!=', 'Enrolled');

        if ($categoryId == 1) {
            $query->select(['rc.id', DB::raw('count(rc.id) as total'), 'rc.course_code', 'rc.course_name'])
                ->orderBy('total', 'desc')
                ->groupBy('rc.course_code');

            $resultArr = $query->get();
            $result = [];
            foreach ($resultArr as $row) {
                $fullText = $row->course_code.': '.$row->course_name;
                $subText = (strlen($fullText) > 20) ? (substr($fullText, 0, 20).'...') : $fullText;
                $result[] = [
                    'id' => $row->id,
                    'category_id' => $categoryId,
                    'type' => 'checkbox',
                    'hasChild' => false,
                    'field' => 'course',
                    'value' => $row->id,
                    'original' => $fullText,
                    'subtext' => $subText." ($row->total)",
                ];
            }

            return $result;
        }
        if ($categoryId == 2) {
            $query->select(['ra.id', DB::raw('count(ra.id) as total'), 'ra.agency_name'])
                ->orderBy('total', 'desc')
                ->groupBy('ra.agency_name');

            $resultArr = $query->get();
            $result = [];
            foreach ($resultArr as $row) {
                $fullText = $row->agency_name;
                $subText = (strlen($fullText) > 20) ? (substr($fullText, 0, 20).'...') : $fullText;
                $result[] = [
                    'id' => $row->id,
                    'category_id' => $categoryId,
                    'type' => 'checkbox',
                    'hasChild' => false,
                    'field' => 'agent',
                    'value' => $row->id,
                    'original' => $fullText,
                    'subtext' => $subText." ($row->total)",
                ];
            }

            return $result;
        }
        if ($categoryId == 3) {
            $query->select(['rsc.id', DB::raw('count(rsc.id) as total'), 'rsc.offer_status'])
                ->orderBy('total', 'desc')
                ->groupBy('rsc.offer_status');

            $resultArr = $query->get();
            $result = [];
            foreach ($resultArr as $row) {
                $fullText = $row->offer_status;
                $subText = (strlen($fullText) > 20) ? (substr($fullText, 0, 20).'...') : $fullText;
                $result[] = [
                    'id' => $row->id,
                    'category_id' => $categoryId,
                    'type' => 'checkbox',
                    'hasChild' => false,
                    'field' => 'status',
                    'value' => $row->id,
                    'original' => $fullText,
                    'subtext' => $subText." ($row->total)",
                ];
            }

            return $result;
        }

    }
}
