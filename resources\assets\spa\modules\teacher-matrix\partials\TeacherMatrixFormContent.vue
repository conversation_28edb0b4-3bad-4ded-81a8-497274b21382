<template>
    <!-- {{ store.formData }} -->
</template>
<script setup>
import { computed } from 'vue';
import { Field } from '@progress/kendo-vue-form';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormRadioGroup from '@spa/components/KendoInputs/FormRadioGroup.vue';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import Card from '@spa/components/Card/Card.vue';
import { teacherConfig } from '@spa/config/teacherConfig.js';
import { useCountryStore } from '@spa/stores/modules/config/useCountryStore.js';
import TeacherSelect from '@spa/modules/teacher/TeacherSelect.vue';
import SubjectsCheckbox from '@spa/modules/teacher/partials/SubjectsCheckbox.vue';
import CoursesSelect from '@spa/modules/teacher/partials/CoursesSelect.vue';
import { useCoursesStore } from '@spa/stores/modules/courses/useCoursesStore.js';

const props = defineProps({
    store: {
        type: Object,
        required: true,
    },
});

const courseStore = useCoursesStore();

const countryStore = useCountryStore();

const { knowledge_level_code } = teacherConfig;

const knowledgeLevelOptions = computed(() => {
    return Object.entries(knowledge_level_code).map(([key, value]) => ({
        text: value,
        value: key,
    }));
});
</script>
<style lang=""></style>
