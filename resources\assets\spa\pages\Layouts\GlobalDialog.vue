<script setup>
import { Dialog, DialogActionsBar } from '@progress/kendo-vue-dialogs';
// import { Button as KButton } from '@progress/kendo-vue-buttons';
import { Input as KInput } from '@progress/kendo-vue-inputs';
import { useAppStore } from '@spa/stores/modules/config/useAppStore.js';
import { ref, watch } from 'vue';
import Button from '@spa/components/Buttons/Button.vue';

const appStore = useAppStore();

// For prompt dialogs
const promptValue = ref('');

// Watch for current dialog changes to reset prompt value
watch(
    () => appStore.currentDialog,
    (newDialog) => {
        if (newDialog && newDialog.config.type === 'prompt') {
            promptValue.value = newDialog.config.defaultValue || '';
        }
    },
    { immediate: true }
);

// Watch prompt input changes
watch(promptValue, (newValue) => {
    if (appStore.currentDialog && appStore.currentDialog.config.onInput) {
        appStore.currentDialog.config.onInput(newValue);
    }
});

const handleActionClick = (action) => {
    appStore.executeAction(action, appStore.currentDialog.id);
};

const handleConfirm = () => {
    appStore.confirmCurrentDialog();
};

const handleCancel = () => {
    appStore.cancelCurrentDialog();
};

const handleClose = () => {
    // Handle X button or ESC key - only if not persistent
    if (!appStore.currentDialog?.config.persistent) {
        appStore.cancelCurrentDialog();
    }
};

// Calculate z-index based on dialog position in queue
const getZIndex = (dialog) => {
    const baseIndex = dialog.config.zIndex || 1000;
    const dialogIndex = appStore.dialogQueue.findIndex((d) => d.id === dialog.id);
    return baseIndex + dialogIndex;
};
</script>

<template>
    <!-- Render all dialogs in queue -->
    <div v-for="(dialog, index) in appStore.dialogQueue" :key="dialog.id">
        <Dialog
            v-if="index === 0"
            :title="dialog.config.title || 'Please confirm'"
            :style="{ zIndex: getZIndex(dialog) }"
            :dialog-class="'tw-dialog min-w-[400px]'"
            @close="handleClose"
        >
            <!-- Regular dialog content -->
            <div
                :style="{ minHeight: dialog.config.minHeight }"
                v-if="!dialog.config.type || dialog.config.type !== 'prompt'"
            >
                <p :style="{ textAlign: dialog.config.align }">
                    {{ dialog.config.message || 'Are you sure you want to continue?' }}
                </p>
            </div>

            <!-- Prompt dialog content -->
            <div :style="{ minHeight: dialog.config.minHeight }" v-else>
                <p :style="{ textAlign: dialog.config.align }">
                    {{ dialog.config.message || 'Please enter a value:' }}
                </p>
                <div style="margin: 0 25px 25px 25px">
                    <KInput
                        v-model="promptValue"
                        @keyup.enter="handleConfirm"
                        @keyup.esc="handleCancel"
                        style="width: 100%"
                    />
                </div>
            </div>

            <DialogActionsBar
                style="justify-content: flex-end; display: flex; gap: 8px; padding: 1rem"
            >
                <!-- Custom Actions -->
                <template v-if="dialog.config.actions && dialog.config.actions.length > 0">
                    <Button
                        v-for="action in dialog.config.actions"
                        :key="action.label"
                        :variant="action.color || 'base'"
                        :class="action.class"
                        :disabled="action.disabled"
                        @click="handleActionClick(action)"
                    >
                        {{ action.label }}
                    </Button>
                </template>

                <!-- Default Actions -->
                <template v-else-if="dialog.config.showDefaultActions !== false">
                    <Button variant="secondary" @click="handleCancel">
                        {{ dialog.config.type === 'prompt' ? 'Cancel' : 'No' }}
                    </Button>
                    <Button variant="primary" @click="handleConfirm">
                        {{ dialog.config.type === 'prompt' ? 'OK' : 'Yes' }}
                    </Button>
                </template>
            </DialogActionsBar>
        </Dialog>
    </div>

    <!-- Queue indicator (optional) -->
    <div
        v-if="appStore.dialogCount > 1"
        class="dialog-queue-indicator"
        :style="{
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'rgba(0,0,0,0.7)',
            color: 'white',
            padding: '5px 10px',
            borderRadius: '15px',
            fontSize: '12px',
            zIndex: 9999,
        }"
    >
        {{ appStore.dialogCount - 1 }} more dialog(s) queued
    </div>
</template>

<style scoped>
.dialog-queue-indicator {
    pointer-events: none;
    user-select: none;
}
</style>
