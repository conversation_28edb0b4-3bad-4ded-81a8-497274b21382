<template>
    <Layout :noSpacing="true" :loading="true" :pt="{ wrapper: 'h-full' }">
        <template v-slot:pageTitleContent>
            <PageTitleContent
                :title="isStudentId ? 'Student ID Builder' : 'Certificate Builder'"
                :linkurl="
                    route(isStudentId ? 'spa.student-id-builder.list' : 'spa.certificate.templates')
                "
                :back="true"
            />
        </template>
        <Toolbar
            :selectedTextNode="selectedNode"
            :updateTextProperties="updateTextProperties"
            :deleteSelectedText="handleDelete"
            :updateShapeProperties="updateShapeProperties"
            :selectedNode="selectedNode"
            :certificateEmbedeUrl="certificateEmbedeUrl"
            @save="storeData({}, false)"
            @saveAs="openSavePopup = true"
            @preview="handleOpenPreview"
            @undo="handleUndo"
            @redo="handleRedo"
            @toggle="handleToggleSidebar"
            @exportPng="exportAsPng"
            @viewAttributes="handleViewAttributes"
            v-model:certificateName="certificateName"
            :disabled="!enableSave || isSaving"
            :canUndo="canUndo"
            :canRedo="canRedo"
            :templateName="selectedCertificate.name"
            :isStudentCardTemplate="isStudentCardTemplate"
        />
        <div class="flex h-fit">
            <div
                class="absolute left-1 top-1/2 z-10 col-span-1 flex h-fit -translate-y-1/2 rounded-lg border border-gray-200 bg-white shadow"
                ref="positionRef"
            >
                <div class="border-r">
                    <ul>
                        <template v-for="(menu, index) in menus">
                            <li
                                class="flex cursor-pointer flex-col items-center justify-center gap-y-1 text-black/85 transition-colors hover:border-r-2 hover:border-primary-blue-500 hover:bg-primary-blue-50 hover:text-primary-blue-500"
                            >
                                <VMenu
                                    v-if="
                                        ![
                                            'texts',
                                            'qr_codes',
                                            'images',
                                            'logo',
                                            'profile',
                                        ].includes(menu.slug)
                                    "
                                    placement="right-start"
                                    :distance="8"
                                    :show-triggers="['hover']"
                                    :hide-triggers="['click', 'hover']"
                                    :auto-hide="true"
                                    @show="hideAttributeMenu"
                                >
                                    <button
                                        class="flex h-[68px] w-[80px] flex-col items-center justify-center"
                                        @click="handleMenuClick($event, menu)"
                                        draggable="true"
                                        @dragstart="onDragStart($event, menu?.slug)"
                                    >
                                        <IconDocumentRibbon24Regular
                                            class="h-5 w-5"
                                            v-if="menu.slug === 'templates'"
                                        />
                                        <template v-else-if="menu.slug === 'background'">
                                            <svg
                                                class="h-5 w-5 animate-spin text-blue-500"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                v-if="
                                                    loaderStore.contextLoaders['upload-background']
                                                "
                                            >
                                                <circle
                                                    class="opacity-25"
                                                    cx="12"
                                                    cy="12"
                                                    r="10"
                                                    stroke="currentColor"
                                                    stroke-width="4"
                                                ></circle>
                                                <path
                                                    class="opacity-75"
                                                    fill="currentColor"
                                                    d="M4 12a8 8 0 018-8v8H4z"
                                                ></path>
                                            </svg>
                                            <IconResizeImage24Regular class="h-5 w-5" v-else />
                                        </template>
                                        <IconBraces24Regular
                                            class="h-5 w-5"
                                            v-else-if="menu.slug === 'attributes'"
                                        />
                                        <IconShapes24Regular
                                            class="h-5 w-5"
                                            v-else-if="menu.slug === 'shapes'"
                                        />
                                        <span class="text-xs">{{ menu.label }}</span>
                                    </button>
                                    <template #popper="{ hide }">
                                        <div
                                            class="max-h-[550px] min-h-[500px] w-[300px] space-y-4 overflow-y-scroll rounded border border-gray-200 bg-white p-4 shadow-xl"
                                        >
                                            <!-- Background Menu -->
                                            <div
                                                class="flex flex-col gap-3"
                                                v-if="menu.slug === 'background'"
                                            >
                                                <p class="text-sm font-medium">Custom Background</p>
                                                <hr />
                                                <Button
                                                    variant="secondary"
                                                    size="xs"
                                                    class="w-full"
                                                    @click="triggerBackgroundUpload"
                                                >
                                                    <IconAdd24Regular /> Add Custom Background
                                                </Button>
                                                <Button
                                                    variant="danger"
                                                    size="xs"
                                                    class="w-full"
                                                    @click="removeBackground"
                                                    v-if="backgroundImage && backgroundImageSrc"
                                                >
                                                    <IconAdd24Regular /> Remove Background
                                                </Button>
                                                <hr />
                                                <div class="space-y-4">
                                                    <p class="text-sm font-medium">Format</p>
                                                    <RadioGroup
                                                        :data-items="format"
                                                        v-model="selectedFormat"
                                                    />
                                                    <div
                                                        class="flex items-center gap-4"
                                                        v-if="showCustomFormat"
                                                    >
                                                        <div class="space-y-1">
                                                            <p class="text-xs">Width</p>
                                                            <NumericTextBox
                                                                :style="{ width: '100' }"
                                                                :placeholder="'width'"
                                                                v-model="customWidth"
                                                            />
                                                        </div>
                                                        <div class="space-y-1">
                                                            <p class="text-xs">height</p>
                                                            <NumericTextBox
                                                                :style="{ width: '100' }"
                                                                :placeholder="'height'"
                                                                v-model="customHeight"
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="space-y-4">
                                                    <p class="text-sm font-medium">Orientation</p>
                                                    <RadioGroup
                                                        :data-items="orientation"
                                                        v-model="selectedOrientation"
                                                    />
                                                </div>
                                            </div>

                                            <!-- Attributes Menu -->
                                            <div
                                                class="flex flex-col gap-3"
                                                v-if="menu.slug === 'attributes'"
                                            >
                                                <p class="text-sm font-medium">Attributes</p>
                                                <hr />
                                                <ul
                                                    class="h-[calc(100vh-200px)] space-y-4 overflow-y-auto"
                                                >
                                                    <li
                                                        v-for="(
                                                            group, parent
                                                        ) in attributesStore.groupedAttributes"
                                                        :key="parent"
                                                        class="space-y-3 border-b pb-4"
                                                    >
                                                        <p
                                                            class="flex items-center border-l-2 border-primary-blue-500 bg-primary-blue-25 px-2 py-1 text-base text-sm font-medium capitalize text-gray-900"
                                                        >
                                                            {{ parent }} Attributes
                                                        </p>
                                                        <template
                                                            v-for="(attrs, index) in group"
                                                            :key="index"
                                                        >
                                                            <VMenu
                                                                placement="right-end"
                                                                delay="300"
                                                                :triggers="[]"
                                                                :hide-triggers="['click', 'hover']"
                                                                :auto-hide="true"
                                                                ref="attributeMenu"
                                                                :shown="isMenuVisible === attrs.id"
                                                                :distance="16"
                                                            >
                                                                <button
                                                                    class="w-full space-y-3 rounded p-1 hover:bg-primary-blue-50"
                                                                    @click="
                                                                        handleAttributeClick(attrs)
                                                                    "
                                                                    @mouseenter="
                                                                        showAttributeMenu(attrs)
                                                                    "
                                                                    @mousedown="hideAttributeMenu"
                                                                    draggable="true"
                                                                    @dragstart="
                                                                        onAttributesDragStart(
                                                                            $event,
                                                                            attrs
                                                                        )
                                                                    "
                                                                >
                                                                    <div
                                                                        class="space-y-1 text-left"
                                                                    >
                                                                        <p
                                                                            class="text-xs text-gray-700"
                                                                        >
                                                                            {{ attrs.name }}
                                                                        </p>
                                                                        <p
                                                                            class="text-xs text-gray-400"
                                                                        >
                                                                            {{
                                                                                attrs.parameter_value
                                                                            }}
                                                                        </p>
                                                                    </div>
                                                                </button>
                                                                <template #popper>
                                                                    <div
                                                                        class="min-w-64 space-y-2 p-3"
                                                                    >
                                                                        <h3
                                                                            class="text-xs font-medium"
                                                                        >
                                                                            Sample Value
                                                                        </h3>
                                                                        <p
                                                                            class="text-xs text-gray-600"
                                                                        >
                                                                            Click or drag an
                                                                            attribute to add it!
                                                                        </p>
                                                                        <AttributeDetails
                                                                            :attribute="attrs"
                                                                        />
                                                                        <div
                                                                            class="flex justify-end gap-2"
                                                                        >
                                                                            <Button
                                                                                variant="primary"
                                                                                size="xs"
                                                                                class="text-xs"
                                                                                @click="
                                                                                    handleAttributeClick(
                                                                                        attrs
                                                                                    )
                                                                                "
                                                                            >
                                                                                Use
                                                                            </Button>
                                                                            <CopyToClipboard
                                                                                :text="
                                                                                    attrs.parameter_value
                                                                                "
                                                                                :class="'btn-secondary h-[1.87rem] text-xs leading-[1.87rem]'"
                                                                                :auto-hide="false"
                                                                                @click="
                                                                                    hideAllPoppers()
                                                                                "
                                                                            >
                                                                                <span
                                                                                    >Copy Only</span
                                                                                >
                                                                            </CopyToClipboard>
                                                                        </div>
                                                                    </div>
                                                                </template>
                                                            </VMenu>
                                                        </template>
                                                    </li>
                                                </ul>
                                            </div>

                                            <!-- Shapes Menu -->
                                            <div
                                                class="flex flex-col gap-3"
                                                v-if="menu.slug === 'shapes'"
                                            >
                                                <p class="text-sm font-medium">Shapes</p>
                                                <hr />
                                                <div class="grid grid-cols-3 gap-3">
                                                    <template
                                                        v-for="(shape, index) in shapes"
                                                        :key="shape.value"
                                                    >
                                                        <div class="flex flex-col gap-1">
                                                            <button
                                                                class="group relative cursor-pointer overflow-hidden border p-6 hover:border-primary-blue-300 hover:bg-gray-800/30 focus:border-primary-blue-500"
                                                                @click="
                                                                    handleLoadShape($event, shape);
                                                                    hide();
                                                                "
                                                                draggable="true"
                                                                @dragstart="
                                                                    onShapesDragStart($event, shape)
                                                                "
                                                            >
                                                                <icon :name="shape.value" />
                                                                <span
                                                                    class="invisible absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:visible group-hover:opacity-100"
                                                                >
                                                                    <IconAdd24Regular
                                                                        class="h-8 w-8 font-bold text-white"
                                                                    />
                                                                </span>
                                                            </button>
                                                            <p class="text-center text-xs">
                                                                {{ shape.label }}
                                                            </p>
                                                        </div>
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </VMenu>
                                <button
                                    v-else
                                    class="flex h-[68px] w-[80px] flex-col items-center justify-center"
                                    @click="handleMenuClick($event, menu)"
                                    @mouseenter="handleMenuChange($event, menu)"
                                    @mouseleave="onSidebarLeave"
                                    draggable="true"
                                    @dragstart="onDragStart($event, menu?.slug)"
                                >
                                    <IconTextT24Regular
                                        class="h-5 w-5"
                                        v-if="menu.slug === 'texts'"
                                    />
                                    <template v-else-if="menu.slug === 'images'">
                                        <svg
                                            class="h-5 w-5 animate-spin text-blue-500"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            v-if="loaderStore.contextLoaders['upload-image']"
                                        >
                                            <circle
                                                class="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                stroke-width="4"
                                            ></circle>
                                            <path
                                                class="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8v8H4z"
                                            ></path>
                                        </svg>
                                        <IconImageCopy24Regular class="h-5 w-5" v-else />
                                    </template>

                                    <IconScanQrCode24Regular
                                        class="h-5 w-5"
                                        v-else-if="menu.slug === 'qr_codes'"
                                    />
                                    <IconPersonSquare24Regular
                                        class="h-5 w-5"
                                        v-else-if="menu.slug === 'profile'"
                                    />
                                    <IconImageGlobe24Regular
                                        class="h-5 w-5"
                                        v-else-if="menu.slug === 'logo'"
                                    />
                                    <span class="text-xs">{{ menu.label }}</span>
                                </button>
                            </li>
                        </template>
                    </ul>
                </div>
            </div>
            <div
                class="relative z-0 col-span-3 flex flex-1 flex-col items-center justify-center overflow-y-auto"
                :style="{ height: 'fit-content' }"
            >
                <div class="my-8 space-y-4">
                    <div
                        class="relative border border-gray-200 bg-white"
                        @dragover.prevent
                        @drop="onDrop"
                    >
                        <v-stage ref="stageRef" :config="stageConfig" @click="handleStageClick">
                            <v-layer ref="layerRef">
                                <v-image
                                    v-if="backgroundImage"
                                    :config="{
                                        x: 0,
                                        y: 0,
                                        image: backgroundImage,
                                        width: stageConfig.width,
                                        height: stageConfig.height,
                                        name: backgroundImageSrc,
                                        type: 'BACKGROUND',
                                    }"
                                />
                                <DraggableShapes
                                    v-for="(shapeItem, index) in shapeElements"
                                    :key="'shape-' + shapeItem.id"
                                    ref="shapeNodes"
                                    :shapeItem="shapeItem"
                                    :updateSize="updateShapeSize"
                                    :onSelect="handleSelectNode"
                                    @dragstart="hideToolbar"
                                    @dragend="handleDragEnd"
                                    @dragmove="onDragMove"
                                    :isSelected="isSelected"
                                />
                                <EditableText
                                    v-for="(textItem, index) in textElements"
                                    :key="textItem.id"
                                    ref="textNodes"
                                    :textItem="textItem"
                                    :updateText="updateText"
                                    :updateSize="updateSize"
                                    :onSelect="handleSelectNode"
                                    :onHoverSelect="handleHoverSelectNode"
                                    @dragstart="hideToolbar"
                                    @dragend="handleDragEnd"
                                    @dragmove="onDragMove"
                                    :selectedNode="selectedNode"
                                    :isSelected="isSelected"
                                />
                                <DraggableImage
                                    v-for="(imageItem, index) in imageElements"
                                    :key="imageItem.id"
                                    ref="imageNodes"
                                    :imageItem="imageItem"
                                    :updateSize="updateImageSize"
                                    :onSelect="handleSelectNode"
                                    @dragstart="hideToolbar"
                                    @dragend="handleDragEnd"
                                    @dragmove="onDragMove"
                                    :isSelected="isSelected"
                                />
                                <v-image
                                    v-if="qrImage"
                                    ref="qrNode"
                                    :config="{
                                        id: `qr-${Date.now()}`,
                                        type: 'QR',
                                        x: qrConfig.x,
                                        y: qrConfig.y,
                                        width: qrConfig.width,
                                        height: qrConfig.height,
                                        image: qrImage || null,
                                        name: qrSrc || null,
                                        draggable: true,
                                    }"
                                    @dblclick="handleQrDblClick"
                                    @dbltap="handleQrDblClick"
                                    @click="handleSelectNode(qrNode)"
                                    @tap="handleSelectNode(qrNode)"
                                    @transform="handleQrTransform"
                                    @dragend="handleDragEnd($event, qrNode)"
                                    @dragmove="onDragMove"
                                />

                                <v-image
                                    v-if="logoImage"
                                    ref="logoNode"
                                    :config="{
                                        id: `logo-${Date.now()}`,
                                        type: 'LOGO',
                                        x: logoConfig.x,
                                        y: logoConfig.y,
                                        width: logoConfig.width,
                                        height: logoConfig.height,
                                        image: logoImage || null,
                                        name: logoSrc || null,
                                        draggable: true,
                                    }"
                                    @dblclick="handleQrDblClick"
                                    @dbltap="handleQrDblClick"
                                    @click="handleSelectNode(logoNode)"
                                    @tap="handleSelectNode(logoNode)"
                                    @transform="handleLogoTransform"
                                    @dragend="handleDragEnd($event, logoNode)"
                                    @dragmove="onDragMove"
                                />

                                <Transformer
                                    :selectedNode="selectedNode"
                                    :stageRef="stageRef"
                                    ref="transformerRef"
                                />

                                <HoverTransformer
                                    :selectedNode="hoveredNode"
                                    ref="hoveredTransformerRef"
                                />

                                <v-line
                                    v-for="(guide, index) in guidelines"
                                    :key="'guide-' + index"
                                    :config="guide"
                                />
                                <QuickActionBar
                                    ref="actionBarRef"
                                    :selectedNode="selectedNode"
                                    @deleteNode="handleDelete"
                                    @duplicateNode="handleDuplicate"
                                    @mouseover="handleActionMouseOver"
                                />
                            </v-layer>
                        </v-stage>
                    </div>
                </div>
            </div>
        </div>
        <AttributePopup :visible="openAttributePopup" @submit="handleAttributeSubmit" />
        <!--        <ViewAttributesPopup-->
        <!--            :visible="openViewAttributesPopup"-->
        <!--            @close="openViewAttributesPopup = false"-->
        <!--            :templateType="isStudentId ? 'student-id' : 'certificate'"-->
        <!--        />-->
        <PreviewPopupWithData
            :visible="openPreviewPopup"
            @save="handleToolbarSave"
            :data="savedObj"
            @stopLoading="isIframeLoad"
            :certificateEmbedeUrl="certificateEmbedeUrl"
            @close="closePreviewPopup"
            type="html"
        />
        <SaveTemplatePopup
            :dataItem="selectedCertificate"
            :certificateIdFormate="certificateIdFormate"
            :visible="openSavePopup"
            @submit="handleSaveAs"
            :prefillWithDataItem="editMetaVisible"
            @close="
                () => {
                    openSavePopup = false;
                    editMetaVisible = false;
                }
            "
            :isStudentId="isStudentId"
        />
        <TemplateSettingsPopup
            :visible="settingsVisible"
            :template="selectedCertificate"
            @submit="handleSettingsSubmit"
            @close="settingsVisible = false"
        />
        <LoadingPopup :visible="loaderStore.contextLoaders['generate']" />
        <div
            class="fixed bottom-3 right-9 flex w-24 items-center gap-2 rounded-md bg-white p-2 shadow"
            v-if="showStatus"
        >
            <template v-if="isSaving">
                <!-- Spinner -->
                <svg class="h-6 w-6 animate-spin text-blue-500" fill="none" viewBox="0 0 24 24">
                    <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                    ></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8H4z"></path>
                </svg>
                <span class="font-medium text-gray-600">Saving...</span>
            </template>

            <template v-else>
                <!-- Checkmark -->
                <IconCheckmarkCircle24Filled class="h-6 w-6 text-green-500" />
                <span class="font-medium text-gray-600">Saved</span>
            </template>
        </div>
        <div
            class="fixed bottom-4 right-3 flex flex-col items-center gap-2 rounded-md bg-white p-1 shadow"
        >
            <VDropdown
                placement="left-end"
                :triggers="['click', 'hover']"
                class="flex items-center justify-center"
            >
                <template #default>
                    <Button variant="icon" class="tw-icon-btn h-8.5 w-8.5">
                        <IconInfo24Regular class="size-7 text-gray-500" />
                    </Button>
                </template>
                <template #popper>
                    <div class="w-80 space-y-2 p-4 text-sm">
                        <div class="text-sm font-medium text-gray-800">Info</div>
                        <LabelValuePair
                            :pt="{ root: 'flex gap-2' }"
                            label="Name:"
                            :value="selectedCertificate.name"
                        />
                        <LabelValuePair
                            :pt="{ root: 'flex gap-2' }"
                            label="Format:"
                            :value="certificateFormate"
                        />
                        <LabelValuePair
                            :pt="{ root: 'flex gap-2', value: 'capitalize' }"
                            label="Orientation:"
                            :value="selectedCertificate.orientation"
                        />
                    </div>
                </template>
            </VDropdown>
            <Button
                variant="icon"
                class="tw-icon-btn h-8.5 w-8.5"
                v-tooltip.left="'Edit'"
                @click="handleEditMeta"
            >
                <IconPen24Regular class="size-7 text-gray-500" />
            </Button>
            <Button
                variant="icon"
                class="tw-icon-btn h-8.5 w-8.5"
                v-tooltip.left="'Settings'"
                @click="settingsVisible = true"
            >
                <IconSettings24Regular class="size-7 text-gray-500" />
            </Button>
        </div>
        <input
            type="file"
            ref="fileInput"
            @change="handleImageUpload"
            accept="image/*"
            class="hidden"
        />
        <input
            type="file"
            ref="bgFileInput"
            @change="handleImageUpload($event, 'background')"
            accept="image/*"
            class="hidden"
        />
    </Layout>
</template>
<script setup>
import {
    IconAdd24Regular,
    IconBraces24Regular,
    IconDocumentRibbon24Regular,
    IconImageCopy24Regular,
    IconResizeImage24Regular,
    IconScanQrCode24Regular,
    IconShapes24Regular,
    IconTextT24Regular,
    IconCheckmarkCircle24Filled,
    IconInfo24Regular,
    IconImageGlobe24Regular,
    IconPersonSquare24Regular,
    IconSettings24Regular,
    IconPen24Regular,
} from '@iconify-prerendered/vue-fluent';
import { NumericTextBox, RadioGroup } from '@progress/kendo-vue-inputs';
import Button from '@spa/components/Buttons/Button.vue';
import AttributePopup from '@spa/modules/certificate-builder/AttributePopup.vue';
import ViewAttributesPopup from '@spa/modules/certificate-builder/ViewAttributesPopup.vue';
import DraggableImage from '@spa/modules/certificate-builder/DraggableImage.vue';
import DraggableShapes from '@spa/modules/certificate-builder/DraggableShapes.vue';
import EditableText from '@spa/modules/certificate-builder/EditableText.vue';
import PreviewPopupWithData from '@spa/modules/certificate-builder/PreviewPopupWithData.vue';
import QuickActionBar from '@spa/modules/certificate-builder/QuickActionBar.vue';
import Transformer from '@spa/modules/certificate-builder/Transformer.vue';
import Toolbar from '@spa/modules/certificate-builder/Toolbar.vue';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import globalHelper from '@spa/plugins/global-helper';
import useCertificateResource from '@spa/services/certificates/certificateResource';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import { useCertificateStore } from '@spa/stores/modules/certificate.store';
import Konva from 'konva';
import {
    computed,
    nextTick,
    onBeforeUnmount,
    onMounted,
    onUnmounted,
    ref,
    watch,
    onBeforeMount,
} from 'vue';
import TemplateSettingsPopup from '@spa/modules/certificate-builder/TemplateSettingsPopup.vue';
import { useImage } from 'vue-konva';
import data from './certificateProps.json';
import SaveTemplatePopup from '@spa/modules/certificate-builder/SaveTemplatePopup.vue';
import HoverTransformer from '@spa/modules/certificate-builder/HoverTransformer.vue';
import LoadingPopup from '@spa/modules/certificate-builder/LoadingPopup.vue';
import { router } from '@inertiajs/vue3';
import { debounce } from 'lodash';
import Popover from '@spa/components/Popover/Popover.vue';
import LabelValuePair from '@spa/components/LabelValuePair/LabelValuePair.vue';
import { useCertificateAttributeStore } from '@spa/stores/modules/certificateattribute/useCertificateAttributeStore.js';
import AttributeDetails from '@spa/modules/certificate-builder/AttributeDetails.vue';
import { hideAllPoppers } from 'floating-vue';
import CopyToClipboard from '@spa/components/CopyAction/CopyToClipboard.vue';
import Spinner from '@spa/components/Loader/Spinner.vue';

Konva._fixTextRendering = true;

const loaderStore = useLoaderStore();

const sampleQR = '/v2/img/sample-qr.png';
const sampleProfile = '/v2/img/sample-profile.png';
const sampleLogo = '/v2/img/sample-logo.png';

// Guidelines
const GUIDELINE_OFFSET = 5;
const guidelines = ref([]);
const certificateStore = useCertificateStore();
const props = defineProps({
    // templates: {
    //     type: Array,
    //     default: () => [],
    // },
    selectedCertificate: {
        type: Object,
        default: () => [],
    },
    certificateIdFormate: {
        type: Object,
        default: () => [],
    },
    embedeUrl: {
        type: String,
        default: () => '',
    },
});
const openInfo = ref(false);
const actionBarRef = ref(null);
// const templates = computed(() => data.defaultTemplates);
const certificateEmbedeUrl = ref(props.embedeUrl);

const certificateName = ref('');

// CONSTANTS
const fixedImageWidth = 200;

const menus = computed(() => {
    const commonMenus = [
        {
            label: 'Background',
            slug: 'background',
            showPopover: true,
        },
        {
            label: 'Images',
            slug: 'images',
            showPopover: false,
        },
        {
            label: 'Texts',
            slug: 'texts',
            showPopover: false,
        },
        {
            label: 'Attributes',
            slug: 'attributes',
            showPopover: true,
        },
        {
            label: 'Shapes',
            slug: 'shapes',
            showPopover: true,
        },
    ];

    if (isStudentId.value) {
        return [
            ...commonMenus,
            {
                label: 'Profile',
                slug: 'profile',
                showPopover: false,
            },
            {
                label: 'Logo',
                slug: 'logo',
                showPopover: false,
            },
        ];
    } else {
        return [
            ...commonMenus,
            {
                label: 'QR Codes',
                slug: 'qr_codes',
                showPopover: false,
            },
        ];
    }
});

const orientation = [
    {
        label: 'Portrait',
        value: 'portrait',
    },
    {
        label: 'Horizontal',
        value: 'horizontal',
    },
];

const format = computed(() => {
    if (isStudentId.value) {
        return [
            {
                label: 'CR80',
                value: 'cr80',
            },
            {
                label: 'Custom',
                value: 'custom',
            },
        ];
    }
    return [
        {
            label: 'A4',
            value: 'a4',
        },
        {
            label: 'US Letter',
            value: 'us-letter',
        },
        {
            label: 'Custom',
            value: 'custom',
        },
    ];
});

const shapes = [
    {
        label: 'Vertical Line',
        value: 'line-vert',
    },
    {
        label: 'Horizontal Line',
        value: 'line',
    },
    {
        label: 'Rectangle',
        value: 'rect',
    },
    {
        label: 'Circle',
        value: 'circle',
    },
    {
        label: 'Ellipse',
        value: 'ellipse',
    },
    {
        label: 'Rectangle Fill',
        value: 'rect-fill',
    },
    {
        label: 'Circle Fill',
        value: 'circle-fill',
    },
    {
        label: 'Ellipse Fill',
        value: 'ellipse-fill',
    },
];

const canvasSize = ref({
    width: window.innerWidth,
    height: window.innerHeight,
    fill: '#D1D5DB',
});

const stageConfig = ref({
    width: 707,
    height: 1000,
    fill: 'white',
    stroke: '#D1D5DB',
    strokeWidth: 1,
});

// REACTIVE STATES
const currentMenu = ref('templates');
const selectedFormat = ref('a4');
const showCustomFormat = ref(false);
const selectedOrientation = ref('portrait');
const customWidth = ref(700);
const customHeight = ref(1000);
const textElements = ref([]);
const selectedNode = ref(null);
const shapeElements = ref([]);
const transformerRef = ref(null);
const hoveredNode = ref(null);
const hoveredTransformerRef = ref(null);

const layerRef = ref(null);
const stageRef = ref(null);
const fileInput = ref(null);
const backgroundImage = ref(null);
const backgroundImageSrc = ref(null);
const bgFileInput = ref(null);

const imageElements = ref([]);
const settingsVisible = ref(false);
const handleSettingsSubmit = async (values) => {
    await $certificate.updateTemplateSettings(props.selectedCertificate, values, {
        onSuccess: () => {
            settingsVisible.value = false;
        },
        showToast: true,
        reFetch: false,
    });
};

// Undo/Redo History
const history = ref([{ text: [], images: [], shapes: [], selectedNodeId: null }]);
const historyStep = ref(0);

const openAttributePopup = ref(false);
const openPreviewPopup = ref(false);
const openViewAttributesPopup = ref(false);
const savedObj = ref({});
const isDataSaved = ref(true);
const openSavePopup = ref(false);

const isNewTemplate = ref(true);
const selectedTemplate = ref(null);
// COMPUTED VALUES
const attrsLists = computed(() => {
    return textElements.value.filter((item) => item.type === 'ATTRS');
});

const textLists = computed(() => {
    return textElements.value.filter((item) => item.type === 'TEXT');
});

const enableSave = computed(() => {
    return historyStep.value > 0;
});

const imagePosition = ref({
    x: null,
    y: null,
});

const positionRef = ref(null);
const templateType = ref('certificate');

onMounted(() => {
    const url = new URL(window.location.href);
    const certificateId = url.searchParams.get('certificateId');
    const type = url.searchParams.get('type');
    if (type === 'student-id') {
        templateType.value = type;
        qrSrc.value = sampleProfile;
        selectedFormat.value = 'cr80';
    } else {
        qrSrc.value = sampleQR;
        selectedFormat.value = 'a4';
    }

    if (!certificateId) {
        router.visit(route('spa.certificate.templates'));
        globalHelper.methods.showPopupError('Missing certificate ID. Redirecting...', 'Error');
    }
});

const isStudentId = computed(() => {
    return templateType.value === 'student-id';
});

const getDefaultY = () => {
    const button = positionRef?.value?.$el || positionRef?.value;
    const stage = stageRef?.value?.getNode();

    if (!button || !stage) return;

    const { top: buttonTop } = button.getBoundingClientRect();
    const { top: canvasTop } = stage.container().getBoundingClientRect();

    const stageHeight = stageConfig.value.height;

    const relativeY = buttonTop - canvasTop;

    const defaultY = Math.max(0, Math.min(relativeY, stageHeight - 50)) + 30;
    return defaultY;
};

const qrImage = ref(null);
const qrSrc = ref(sampleQR);
const qrNode = ref(null);

const qrConfig = ref({
    id: `qr-${Date.now()}`,
    type: 'QR',
    x: 100,
    y: getDefaultY(),
    width: 100,
    height: 100,
    image: null,
    name: null,
    draggable: true,
});

const logoImage = ref(null);
const logoSrc = ref(sampleLogo);
const logoNode = ref(null);

const logoConfig = ref({
    id: `logo-${Date.now()}`,
    type: 'LOGO',
    x: 100,
    y: getDefaultY(),
    width: 100,
    height: 28,
    image: null,
    name: null,
    draggable: true,
});

watch(qrSrc, (newSrc) => {
    qrConfig.value.name = newSrc || sampleQR;
});

watch(logoSrc, (newSrc) => {
    logoConfig.value.name = newSrc || sampleLogo;
});

// IMAGES CANVAS
const addImage = (image, imageSrc, height) => {
    imageElements.value.push({
        id: `image-${Date.now()}`,
        type: 'IMAGE',
        x: imagePosition.value.x !== null ? imagePosition.value.x : 100,
        y: imagePosition.value.y !== null ? imagePosition.value.y : getDefaultY(),
        width: fixedImageWidth,
        height: height,
        isEditing: false,
        image: image,
        name: imageSrc,
    });
    saveState();
};

const handleSelectNode = (node) => {
    if (!node) return;
    selectedNode.value = node;
    if (selectedNode.value) {
        transformerRef.value?.attachTransformer(selectedNode.value);
        actionBarRef.value.attachToolbar(selectedNode.value);
        hoveredNode.value = null;
        hoveredTransformerRef.value.detachTransformer();
    }
};

const handleHoverSelectNode = (node) => {
    if (!node) return;
    hoveredNode.value = node;
    hoveredTransformerRef.value?.attachTransformer(hoveredNode.value);
    // const getHoveredNode = node.getNode();
    // const getSelectedNode = selectedNode.value?.getNode();
    // if (getSelectedNode && getHoveredNode._id !== getSelectedNode._id) {
    // } else {
    //     hoveredNode.value = null;
    //     hoveredTransformerRef.value.detachTransformer();
    // }
};

const updateShapeSize = (id, size) => {
    // const shape = shapeElements.value.find((t) => t.id === id);
    // if (shape) {
    //     shape.width = size.width;
    //     shape.height = size.height;
    // }
    // saveState();
    const index = shapeElements.value.findIndex((t) => t.id === id);
    if (index !== -1) {
        shapeElements.value[index] = {
            ...shapeElements.value[index],
            width: size.width,
            height: size.height,
        };
        saveState();
    }
};

const updateImageSize = (id, size) => {
    const image = imageElements.value.find((t) => t.id === id);
    if (image) {
        image.width = size.width;
        image.height = size.height;
    }
    saveState();
};

const handleFormatChange = (e) => {
    const value = e.value;
    selectedFormat.value = value;
};

const triggerFileUpload = () => {
    console.log('triggerFileUpload', fileInput.value);
    nextTick(() => {
        if (fileInput.value) {
            fileInput.value.click();
        }
    });
};

const triggerBackgroundUpload = () => {
    nextTick(() => {
        if (bgFileInput.value) {
            bgFileInput.value.click();
        }
    });
};

const removeBackground = () => {
    backgroundImage.value = null;
    backgroundImageSrc.value = null;
    saveState();
};

const handleImageUpload = async (event, type) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
        // 1️⃣ Upload file to Laravel
        const formData = new FormData();
        formData.append('image', file);

        const uploadResponse = await $certificate.uploadImage(formData, {
            type,
        });

        if (!uploadResponse.uploaded) {
            console.error('Upload failed');
            return;
        }

        const uploadedFileName = uploadResponse.fileName;
        const imageSrc = uploadResponse.url; // base64 from storeImage()

        const image = new Image();
        image.crossOrigin = 'Anonymous';
        image.src = imageSrc;

        image.onload = () => {
            if (type === 'background') {
                backgroundImageSrc.value = imageSrc;
                backgroundImage.value = image;
            } else {
                const originalWidth = image.width;
                const originalHeight = image.height;
                const aspectRatio = originalHeight / originalWidth;
                const calculatedHeight = fixedImageWidth * aspectRatio;

                addImage(image, imageSrc, calculatedHeight);
            }

            nextTick(() => {
                layerRef.value.getNode().batchDraw();
            });
        };
        saveState();
    } catch (error) {
        console.error('Error uploading image:', error);
    }
};

const handleImageUpload1 = (event, type) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = () => {
        const image = new Image();
        image.src = reader.result;

        image.onload = () => {
            const layer = stageRef.value.getStage().getLayers();

            if (type === 'background') {
                backgroundImageSrc.value = reader.result;
                backgroundImage.value = image;
                const layer = stageRef.value.getStage().getLayers()[0];

                // Remove existing background image if any
                const existingBackground = layer.find('.background');
                if (existingBackground) {
                    existingBackground.destroy();
                }

                const backgroundNode = new Konva.Image({
                    image: image,
                    width: stageConfig.value.width,
                    height: stageConfig.value.height,
                    name: reader.result,
                    className: 'background',
                    listening: false, // Disable mouse events on background
                });

                // Add background to the beginning of the layer (behind everything)
                layer.add(backgroundNode);
                backgroundNode.moveToBottom();
                layer.batchDraw();
            } else {
                const originalWidth = image.width;
                const originalHeight = image.height;
                const imageSrc = reader.result;

                // Maintain aspect ratio
                const aspectRatio = originalHeight / originalWidth;
                const calculatedHeight = fixedImageWidth * aspectRatio;
                addImage(image, imageSrc, calculatedHeight);
            }
            nextTick(() => {
                layerRef.value.getNode().batchDraw();
            });
        };
    };
    reader.readAsDataURL(file);
    saveState();
};

// TEXT AND ATTRS CANVAS
const addText = async (text = null, position = { x: null, y: null }) => {
    const stageWidth = stageConfig.value.width;
    const defaultY = getDefaultY();
    const newElement = {
        id: Date.now(),
        type: text ? 'ATTRS' : 'TEXT',
        text: text || 'Double Click To Edit',
        x: position.x !== null ? position.x - stageWidth / 4 : 100,
        y: position.y !== null ? position.y : defaultY,
        width: stageConfig.value.width / 2,
        isEditing: false,
        fontSize: 16,
        fontFamily: 'Arial',
        fill: 'black',
        align: 'center',
        textDecoration: '',
        fontStyle: 'normal',
        padding: 10,
        strokeWidth: 1,
        lineHeight: 1.5,
        stroke: 'red',
    };
    textElements.value.push(newElement);
    sidebar.value = false;
    saveState();
};

const updateText = (id, newText) => {
    const text = textElements.value.find((t) => t.id === id);
    if (text) text.text = newText;
    saveState();
};

const updateSize = (id, newWidth, newHeight) => {
    const element = textElements.value.find((el) => el.id === id);
    if (element) {
        element.width = newWidth;
        element.height = newHeight;
    }

    saveState();
};

const updateTextProperties = (properties) => {
    if (selectedNode.value) {
        const node = selectedNode.value.getNode();
        const existingAttrs = node.attrs;
        node.setAttrs({ ...existingAttrs, ...properties });
        const nodeId = existingAttrs.id; // fallback handling
        const index = textElements.value.findIndex((el) => el.id === nodeId);

        if (index !== -1) {
            // Merge existing properties with updated ones
            textElements.value[index] = {
                ...textElements.value[index],
                ...properties,
            };
        }
        node.getLayer().batchDraw();
        saveState();
    }
};

const updateShapeProperties = (properties) => {
    if (selectedNode.value) {
        const node = selectedNode.value.getNode();
        const existingAttrs = node.attrs;

        node.setAttrs({ ...existingAttrs, ...properties });
        node.getLayer().batchDraw();

        const nodeId = node._id || node._id(); // You might just use `node._id()` here safely
        const targetElement = textElements.value.find((el) => el.id === nodeId);

        if (targetElement && properties.fontFamily) {
            targetElement.fontFamily = properties.fontFamily;
        }
    }
    saveState();
};

const handleDelete = () => {
    if (!selectedNode.value) return;
    const node = selectedNode.value.getNode();
    if (node.attrs.type === 'TEXT' || node.attrs.type === 'ATTRS') {
        textElements.value = textElements.value.filter(
            (item) => item.id !== selectedNode.value.getNode().attrs.id
        );
    } else if (node.attrs.type === 'IMAGE') {
        imageElements.value = imageElements.value.filter(
            (item) => item.id !== selectedNode.value.getNode().attrs.id
        );
    } else if (node.attrs.type === 'SHAPE') {
        shapeElements.value = shapeElements.value.filter(
            (item) => item.id !== selectedNode.value.getNode().attrs.id
        );
    } else if (node.attrs.type === 'QR') {
        qrImage.value = null;
        qrNode.value = null;
    } else if (node.attrs.type === 'LOGO') {
        logoImage.value = null;
        logoNode.value = null;
    }

    handleDetach();
    saveState();
};

const addAttributePopup = () => {
    openAttributePopup.value = true;
};

const closeAttributePopup = () => {
    openAttributePopup.value = false;
};

const handleAttributeSubmit = (values) => {
    addText(values.attribute_name);
    closeAttributePopup();
};

const $certificate = useCertificateResource('spa/certificates');

const saveStageState = (data = {}, autoSave = true) => {
    const isNonEmptyObject = data && Object.keys(data).length > 0;

    if (stageRef.value) {
        const stageRefNode = stageRef.value.getNode();
        let stageData = stageRef.value.getNode().toJSON();
        const json = stageRefNode.toJSON();
        const dataURL = stageRefNode.toDataURL({
            pixelRatio: 0.5,
        });

        savedObj.value = JSON.parse(json);

        const htmlContent = generateHTML();
        const compressedHTML = htmlContent
            .replace(/\n/g, '') // remove newline escapes
            .replace(/\t/g, '') // remove tabs
            .replace(/\s{2,}/g, ' ') // collapse multiple spaces
            .replace(/>\s+</g, '><') // remove space between tags
            .trim();
        isDataSaved.value = true;
        const templateData = {
            json_data: stageData,
            html_data: compressedHTML,
            paper_size: stageData.attrs?.width > stageData.attrs?.height ? 'landscape' : 'portrait',
            orientation:
                stageData.attrs?.width > stageData.attrs?.height ? 'landscape' : 'portrait',
            thumbnail: dataURL,
            fonts_used: getFonts.value,
        };
        payload.value = {
            ...payload.value,
            ...templateData,
            ...(isNonEmptyObject ? { ...data, id: null } : {}),
        };
    }
};

const storeData = async (data = {}, autoSave = true) => {
    const isNonEmptyObject = data && Object.keys(data).length > 0;
    if (!autoSave) {
        handleDetach();
        await nextTick();
    }
    saveStageState(data, autoSave);
    if (isNonEmptyObject) {
        await $certificate.saveTemplate(
            payload.value,
            {},
            isStudentId.value ? 'student-id' : 'certificate'
        );
    } else {
        if (selectedTemplate.value) {
            await $certificate.updateTemplate(
                payload.value,
                {
                    autoSave: autoSave,
                },
                isStudentId.value ? 'student-id' : 'certificate'
            );
            // globalHelper.methods.showPopupSuccess(
            //     "Template updated successfully",
            //     "Success",
            // );
        } else {
            globalHelper.methods.showPopupError('Template is not loaded', 'Error');
        }
    }
};

const isSavePreview = ref(false);

const closePreviewPopup = () => {
    openPreviewPopup.value = false;
    // if (isSavePreview.value) {
    //     router.visit(route("spa.certificate.templates"));
    // }
};

const handleOpenPreview = () => {
    saveStageState();

    openPreviewPopup.value = true;
    // openPreviewPopup.value = true;
    loaderStore.startContextLoading('generate');
    onEmbedLoad();
};

const handleDuplicate = (node) => {
    if (node.type === 'TEXT' || node.type === 'ATTRS') {
        textElements.value.push(node);
    } else if (node.type === 'IMAGE') {
        imageElements.value.push(node);
    } else if (node.type === 'SHAPE') {
        shapeElements.value.push(node);
    }
    saveState();
};

const isSaving = ref(false);
const showStatus = ref(false);

const performAutoSave = () => {
    isSaving.value = true;
    showStatus.value = true;

    storeData({});

    setTimeout(() => {
        isSaving.value = false;
        setTimeout(() => {
            showStatus.value = false;
        }, 1000);
    }, 1500);
};

const debounceStoreData = debounce(() => {
    performAutoSave();
}, 3000);

const saveState = () => {
    history.value = history.value.slice(0, historyStep.value + 1);
    history.value.push({
        text: JSON.parse(JSON.stringify(textElements.value)),
        images: JSON.parse(JSON.stringify(imageElements.value)),
        shapes: JSON.parse(JSON.stringify(shapeElements.value)),
        qr: JSON.parse(JSON.stringify(qrConfig.value)),
        logo: JSON.parse(JSON.stringify(logoConfig.value)),
        selectedNodeId: selectedNode.value?.id || null,
    });
    historyStep.value++;
    debounceStoreData();
};

const canUndo = computed(() => historyStep.value > 1);
const canRedo = computed(() => historyStep.value < history.value.length - 1);

const restoreState = (state) => {
    textElements.value.splice(
        0,
        textElements.value.length,
        ...JSON.parse(JSON.stringify(state.text))
    );
    imageElements.value = state.images.map((image) => {
        const [imageObj] = useImage(image.name);
        return {
            ...image,
            image: imageObj,
        };
    });

    shapeElements.value.splice(
        0,
        shapeElements.value.length,
        ...JSON.parse(JSON.stringify(state.shapes))
    );

    if (state.qr) {
        qrConfig.value = { ...state.qr };
    }

    if (state.logo) {
        logoConfig.value = { ...state.logo };
    }
    // if (selectedNode.value) {
    //     actionBarRef.value.attachToolbar(selectedNode.value);
    // }
};

const handleUndo = () => {
    if (historyStep.value === 0) return;
    historyStep.value--;
    const state = history.value[historyStep.value];
    restoreState(state);

    const selectedId = selectedNode.value?.getNode().attrs.id;
    const stillExists =
        textElements.value.some((el) => el.id === selectedId) ||
        shapeElements.value.some((el) => el.id === selectedId) ||
        imageElements.value.some((el) => el.id === selectedId);

    if (!stillExists) {
        handleDetach();
    }
    if (hoveredTransformerRef.value) {
        hoveredTransformerRef.value.detachTransformer();
    }
};

const handleRedo = () => {
    if (historyStep.value >= history.value.length - 1) return;
    historyStep.value++;
    restoreState(history.value[historyStep.value]);
    handleDetach();
};

const handleKeydown = (e) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'z') {
        e.preventDefault();
        handleUndo();
    } else if ((e.ctrlKey || e.metaKey) && e.key === 'y') {
        e.preventDefault();
        handleRedo();
    }
};

onMounted(() => {
    window.addEventListener('keydown', handleKeydown);
    // saveState(); // Save initial state
});

onUnmounted(() => {
    window.removeEventListener('keydown', handleKeydown);
});

const hideToolbar = () => {
    // Hide QuickActionBar (implement logic as needed)
};

const handleDragEnd = (e, nodeRef) => {
    if (!nodeRef) return;
    const node = nodeRef.getNode();
    guidelines.value = [];
    const elements = [...textElements.value, ...imageElements.value, ...shapeElements.value];
    const itemIndex = elements.findIndex((item) => item.id === node.attrs.id);

    if (itemIndex !== -1) {
        elements[itemIndex].x = e.target.x();
        elements[itemIndex].y = e.target.y();
    }

    if (node.attrs.type === 'QR' || node.attrs.type === 'PROFILE') {
        qrConfig.value.x = e.target.x();
        qrConfig.value.y = e.target.y();
    }

    if (node.attrs.type === 'LOGO') {
        logoConfig.value.x = e.target.x();
        logoConfig.value.y = e.target.y();
    }

    selectedNode.value = nodeRef;
    if (selectedNode.value) {
        transformerRef.value?.attachTransformer(selectedNode.value, true);
        actionBarRef.value.attachToolbar(selectedNode.value);
    }

    saveState();
};

const getImageStyle = (attrs) => {
    return {
        position: 'absolute',
        left: attrs.x + 'px',
        top: attrs.y + 'px',
        width: attrs.width + 'px',
        height: attrs.height + 'px',
    };
};

const handleWheel = (e) => {
    e.evt.preventDefault();

    const stage = stageRef.value.getNode();
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();

    const mousePointTo = {
        x: (pointer.x - stage.x()) / oldScale,
        y: (pointer.y - stage.y()) / oldScale,
    };

    // how to scale? Zoom in? Or zoom out?
    let direction = e.evt.deltaY > 0 ? 1 : -1;

    // when we zoom on trackpad, e.evt.ctrlKey is true
    // in that case lets revert direction
    if (e.evt.ctrlKey) {
        direction = -direction;
    }

    const scaleBy = 1.01;
    const newScale = direction > 0 ? oldScale * scaleBy : oldScale / scaleBy;

    stage.scale({ x: newScale, y: newScale });

    const newPos = {
        x: pointer.x - mousePointTo.x * newScale,
        y: pointer.y - mousePointTo.y * newScale,
    };
    stage.position(newPos);
};

const sidebar = ref(false);

const handleToggleSidebar = () => {
    sidebar.value = !sidebar.value;
};

const handleLoadTemplate = (json, type = '') => {
    let jsonData = json.json_data;
    if (type !== 'default') {
        selectedTemplate.value = json.id;
    }

    if (jsonData) {
        const children = jsonData?.children?.[0]?.children ?? [];

        const textItems =
            children.filter((item) => item.attrs.type === 'TEXT' || item.attrs.type === 'ATTRS') ||
            [];

        const allImages = children.filter((item) => item.attrs.type === 'IMAGE');

        const background = children.find((item) => item.attrs.type === 'BACKGROUND');

        const qr = children.find((item) => item.attrs.type === 'QR');
        if (qr) {
            // const [image] = useImage(sampleQR);
            const [image] = useImage(qrSrc.value);
            qrConfig.value = {
                ...qr.attrs,
            };
            watch(image, (newImage) => {
                if (newImage) {
                    qrImage.value = newImage;
                }
            });
            qrSrc.value = qrSrc.value;
        }

        if (background) {
            const [bgImage] = useImage(background.attrs.name);

            watch(bgImage, (newImage) => {
                if (newImage) {
                    backgroundImage.value = newImage;
                }
            });

            backgroundImageSrc.value = background.attrs.name;
        }

        if (isStudentId.value) {
            const logo = children.find((item) => item.attrs.type === 'LOGO');
            if (logo) {
                const [logoImageObj] = useImage(logoSrc.value);
                watch(logoImageObj, (newImage) => {
                    if (newImage) {
                        logoImage.value = newImage;
                    }
                });
                logoConfig.value = {
                    ...logo.attrs,
                };
                logoSrc.value = logo.attrs.name;
            }
        }

        const shapeItems = children.filter((item) => item.attrs.type === 'SHAPE');
        if (allImages.length > 0) {
            const imageItems = allImages || [];
            imageElements.value = imageItems.map((image) => {
                const [imageObj] = useImage(image.attrs.name);

                return {
                    ...image.attrs,

                    image: imageObj,
                };
            });
        }
        textElements.value = textItems.map((text) => ({
            ...text.attrs,
            fontSize: text.attrs.fontSize || 12,
        }));
        shapeElements.value = shapeItems.map((item) => item.attrs);

        const fonts = children
            .filter((item) => item.className === 'Text' || item.className === 'ATTRS')
            .map((item) => item.attrs.fontFamily)
            .filter((value, index, self) => value && self.indexOf(value) === index);
        if (fonts && fonts.length > 0) {
            fonts.forEach((font) => {
                const fontName = font.replace(/\s+/g, '+'); // Replace spaces with '+'
                const linkId = `google-font-${fontName}`;

                // Avoid adding the same link multiple times
                if (!document.getElementById(linkId)) {
                    const link = document.createElement('link');
                    link.id = linkId;
                    link.rel = 'stylesheet';
                    link.href = `https://fonts.googleapis.com/css2?family=${fontName}&display=swap`;
                    document.head.appendChild(link);
                }
            });
        }
    }
};

const handleLoadTemplate1 = (json) => {
    imageElements.value = [];
    textElements.value = [];
    backgroundImage.value = null;
    isNewTemplate.value = false;
    let jsonData = json.json_data;
    selectedTemplate.value = json.id;
    if (jsonData) {
        const textItems =
            jsonData.children[0].children.filter(
                (item) => item.attrs.type === 'TEXT' || item.attrs.type === 'ATTRS'
            ) || [];

        const allImages = jsonData.children[0].children.filter(
            (item) => item.className === 'Image'
        );

        if (allImages.length > 0) {
            // Find background image
            const backgroundImageNode = allImages.find((item) => item.className === 'background');
            if (backgroundImageNode) {
                const [bgImage] = useImage(backgroundImageNode.attrs.name);
                watch(bgImage, (newImage) => {
                    if (newImage) {
                        backgroundImage.value = newImage;
                    }
                });
                backgroundImageSrc.value = backgroundImageNode.attrs.name;
            }

            // Filter out background image and get other images
            const imageItems = allImages.filter((item) => item.className !== 'background');
            textElements.value = textItems.map((text) => text.attrs);
            imageElements.value = imageItems.map((image) => {
                const [imageObj] = useImage(image.attrs.name);
                return {
                    ...image.attrs,
                    image: imageObj,
                };
            });
        }
        textElements.value = textItems.map((text) => text.attrs);
    }
};

const extractFontStyle = (styleString = '') => {
    const style = styleString.toLowerCase().trim();

    const fontStyle = style.includes('italic') ? 'italic' : 'normal';

    return fontStyle;
};

const extractFontWeight = (styleString = '') => {
    const style = styleString.toLowerCase().trim();

    const fontWeight = style.includes('bold') ? 'bold' : 'normal';

    return fontWeight;
};

// const exportAsPng = () => {
//     const dataURL = stageRef.value.getNode().toDataURL({
//         pixelRatio: 0.3, // double resolution
//     });
//
//     const link = document.createElement('a');
//     link.download = 'stage.png';
//     link.href = dataURL;
//     document.body.appendChild(link);
//     link.click();
//     document.body.removeChild(link);
// };

// Guidelines Functions
const getLineGuideStops = (skipShape) => {
    const stage = stageConfig.value;
    let vertical = [0, stage.width / 2, stage.width];
    let horizontal = [0, stage.height / 2, stage.height];
    const elements = [...textElements.value, ...imageElements.value];

    elements.forEach((text) => {
        if (text === skipShape) return;
        let box = text;
        vertical.push(box.x, box.x + box.width, box.x + box.width / 2);
        horizontal.push(box.y, box.y + box.height, box.y + box.height / 2);
    });

    return {
        vertical,
        horizontal,
    };
};

const getObjectSnappingEdges = (node) => {
    let box = node;
    return {
        vertical: [
            { guide: box.x, offset: 0 },
            { guide: box.x + box.width / 2, offset: -box.width / 2 },
            { guide: box.x + box.width, offset: -box.width },
        ],
        horizontal: [
            { guide: box.y, offset: 0 },
            { guide: box.y + box.height / 2, offset: -box.height / 2 },
            { guide: box.y + box.height, offset: -box.height },
        ],
    };
};

const getGuides = (lineGuideStops, itemBounds) => {
    let guides = [];
    lineGuideStops.vertical.forEach((lineGuide) => {
        itemBounds.vertical.forEach((itemBound) => {
            if (Math.abs(lineGuide - itemBound.guide) < GUIDELINE_OFFSET) {
                guides.push({
                    lineGuide,
                    offset: itemBound.offset,
                    orientation: 'V',
                });
            }
        });
    });
    lineGuideStops.horizontal.forEach((lineGuide) => {
        itemBounds.horizontal.forEach((itemBound) => {
            if (Math.abs(lineGuide - itemBound.guide) < GUIDELINE_OFFSET) {
                guides.push({
                    lineGuide,
                    offset: itemBound.offset,
                    orientation: 'H',
                });
            }
        });
    });
    return guides;
};

const drawGuides = (guides) => {
    guidelines.value = guides.map((lg) => ({
        points: lg.orientation === 'H' ? [-6000, 0, 6000, 0] : [0, -6000, 0, 6000],
        stroke: lg.orientation === 'V' ? '#F11169' : '#C40CE5',
        strokeWidth: 0.5,
        dash: [4, 6],
        x: lg.orientation === 'V' ? lg.lineGuide : 0,
        y: lg.orientation === 'H' ? lg.lineGuide : 0,
    }));
};

const onDragMove = (e) => {
    guidelines.value = [];

    const node = e.target;
    const stage = node.getStage();

    // Get original attributes
    const attrs = { ...node.attrs };

    // Snap logic
    const lineGuideStops = getLineGuideStops(attrs);
    const itemBounds = getObjectSnappingEdges(attrs);
    const guides = getGuides(lineGuideStops, itemBounds);

    // Apply snapping if guides exist
    if (guides.length) {
        drawGuides(guides);

        guides.forEach((lg) => {
            if (lg.orientation === 'V') attrs.x = lg.lineGuide + lg.offset;
            if (lg.orientation === 'H') attrs.y = lg.lineGuide + lg.offset;
        });
    }

    // Temporarily apply attrs to calculate box properly
    node.setAttrs(attrs);

    // Get the actual bounding box (transformed)
    const box = node.getClientRect();
    const nodeWidth = box.width;
    const nodeHeight = box.height;

    // Bounds calculation
    const maxX = stage.width() - nodeWidth;
    const maxY = stage.height() - nodeHeight;

    const clampedX = Math.max(0, Math.min(attrs.x, maxX));
    const clampedY = Math.max(0, Math.min(attrs.y, maxY));

    // Finally set the bounded position
    node.setAttrs({ x: clampedX, y: clampedY });
};

const handleCreateTemplate = () => {
    stageRef.value.getNode().clear();
};

const beforeUnloadHandler = (event) => {
    if (!isDataSaved.value) {
        event.preventDefault();
        event.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
    }
};

onMounted(() => {
    window.addEventListener('beforeunload', beforeUnloadHandler);
});

onUnmounted(() => {
    window.removeEventListener('beforeunload', beforeUnloadHandler);
});

const addShape = (type) => {
    let defaultConfig = {
        id: `${type}-${Date.now()}`,
        x: imagePosition.value.x !== null ? imagePosition.value.x : 100,
        y: imagePosition.value.y !== null ? imagePosition.value.y : getDefaultY(),
        strokeWidth: 0,
        draggable: true,
        type: 'SHAPE',
        variant: type,
    };
    let configMapping = {
        circle: {
            radius: 70,
            fill: 'transparent',
            strokeWidth: 1,
            stroke: 'black',
            width: 100,
            height: 100,
        },
        'circle-fill': {
            radius: 70,
            fill: 'black',
            width: 100,
            height: 100,
        },
        ellipse: {
            radiusX: 100,
            radiusY: 50,
            fill: 'transparent',
            strokeWidth: 1,
            stroke: 'black',
            width: 100,
            height: 50,
        },
        'ellipse-fill': {
            radiusX: 100,
            radiusY: 50,
            fill: 'black',
            width: 100,
            height: 50,
        },
        line: {
            points: [0, 0, 200, 0],
            strokeWidth: 1,
            stroke: 'black',
            hitStrokeWidth: 10,
        },
        'line-vert': {
            points: [0, 0, 0, 200],
            height: 200,
            strokeWidth: 1,
            stroke: 'black',
            hitStrokeWidth: 10,
        },
        rect: {
            width: 100,
            height: 50,
            fill: 'transparent',
            strokeWidth: 1,
            stroke: 'black',
        },
        'rect-fill': {
            width: 100,
            height: 50,
            fill: 'black',
        },
    };
    let typeConfig = configMapping[type];
    shapeElements.value.push({
        ...defaultConfig,
        ...typeConfig,
    });
    sidebar.value = false;
    saveState();
};

const handleLoadShape = (e, shape) => {
    addShape(shape.value);
};

const onDragStart = (event, menu) => {
    event.dataTransfer.setData('type', menu);
};

const isSelected = ref(false);

const onDrop = (event) => {
    isSelected.value = true;
    const type = event.dataTransfer.getData('type');
    const stage = stageRef.value.getNode();
    const template = event.dataTransfer.getData('template');
    const attribute = event.dataTransfer.getData('attribute');
    const shape = event.dataTransfer.getData('shape');
    const stageContainer = stage.container();
    const rect = stageContainer.getBoundingClientRect();

    // Calculate mouse position relative to stage
    const position = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
    };

    imagePosition.value = position;

    if (type === 'texts') {
        addText(null, position);
    } else if (type === 'images') {
        triggerFileUpload();
    } else if (type === 'qr_codes' || type === 'profile') {
        qrConfig.value = {
            ...qrConfig.value,
            x: position.x,
            y: position.y,
        };
        addQRCode();
    } else if (type === 'logo') {
        logoConfig.value = {
            ...logoConfig.value,
            x: position.x,
            y: position.y,
        };
        addLogo();
    }

    if (template) {
        handleLoadTemplate(JSON.parse(template));
    }

    if (attribute) {
        addText(`[${JSON.parse(attribute).tag}]`, position);
    }

    if (shape) {
        handleLoadShape(event, JSON.parse(shape));
    }
};

const payload = ref({});

onMounted(() => {
    if (props.selectedCertificate && Object.keys(props.selectedCertificate).length > 0) {
        const data = props.selectedCertificate;
        if (isStudentId.value && data.json_data?.attrs?.width === 648) {
            selectedFormat.value = 'cr80';
        } else {
            selectedFormat.value = 'custom';
            if (data.json_data?.attrs?.width < 400) {
                stageConfig.value = {
                    width: data.json_data?.attrs?.width * 2,
                    height: data.json_data?.attrs?.height * 2,
                };
            } else {
                stageConfig.value = {
                    width: data.json_data?.attrs?.width,
                    height: data.json_data?.attrs?.height,
                };
            }
        }

        handleLoadTemplate(data);
        isNewTemplate.value = false;
        const initial = {
            name: data.name || '',
            certificate_type: parseInt(data.type) || null,
            is_design_template: parseInt(data.certificate_number_formate_id || 0),
        };
        const templateData = {
            secure_id: data.secureId,
            // name: certificateName.value || "Untitled Template",
            name: data.name || 'Untitled Template',
            json_data: data.json_data,
            html_data: data.html_data,
            paper_size: data.paper_size,
            orientation: data.orientation,
            thumbnail: data.thumbnail,
            is_default: data.is_default,
            id: data.id || '',
            // type: "certificate",
            type: data.certificate_type,
            certificate_number_formate_id: data.certificate_number_formate_id,
            fonts_used: data.font_used,
        };
        payload.value = { ...templateData };
        certificateStore.setInitialData(initial);
    }
});

const certificateFormate = computed(() => {
    if (!props.certificateIdFormate) {
        return null;
    }
    const selectedFormat = props.certificateIdFormate.find(
        (item) => item.id == props.selectedCertificate.certificate_number_formate_id
    );

    return selectedFormat?.name || 'N/A';
});

const onTemplateDragStart = (event, template) => {
    event.dataTransfer.setData('template', JSON.stringify(template));
};

const onAttributesDragStart = (event, template) => {
    hideAttributeMenu();
    event.dataTransfer.setData('attribute', JSON.stringify(template));
};

const onShapesDragStart = (event, template) => {
    event.dataTransfer.setData('shape', JSON.stringify(template));
};

const hoverTimeout = ref(null);

const handleMenuChange = (e, item) => {
    sidebar.value = false;
    clearTimeout(hoverTimeout.value);
    currentMenu.value = item.slug;
    if (item.showPopover) {
        sidebar.value = true;
    }
};

const handleMenuClick = (e, item) => {
    if (item.slug === 'texts') {
        addText(null);
    } else if (item.slug === 'images') {
        triggerFileUpload();
    } else if (item.slug === 'background') {
        triggerBackgroundUpload();
    } else if (item.slug === 'qr_codes' || item.slug === 'profile') {
        addQRCode();
    }
};

const onSidebarLeave = () => {
    hoverTimeout.value = setTimeout(() => {
        sidebar.value = false;
    }, 100);
};

const onPopupEnter = () => {
    clearTimeout(hoverTimeout.value);
    sidebar.value = true;
};

const onPopupLeave = (e) => {
    console.log('Leave', e.target);
    sidebar.value = false;
};

const handleStageClick = (e) => {
    const clickedNode = e.target;
    const defaults = [
        'top-left',
        'top-right',
        'bottom-left',
        'bottom-right',
        'middle-left',
        'middle-right',
        'bottom-center',
        'top-center',
    ];

    const currentNode = selectedNode.value?.getNode();
    const isNewNode = !selectedNode.value || clickedNode !== currentNode;
    const isNotQuickAction = clickedNode.attrs.type !== 'QUICKACTION';
    const isNotDefault = !defaults.some((prefix) => clickedNode?.attrs?.name?.startsWith(prefix));
    const isDifferentType = clickedNode.className !== currentNode?.className;
    if (isNewNode && isNotQuickAction && isNotDefault && isDifferentType) {
        handleDetach();
    }
};

const getFonts = computed(() => {
    const children = savedObj?.value?.children?.[0]?.children ?? [];
    const fonts = children
        .filter((item) => item.className === 'Text' || item.className === 'ATTRS')
        .map((item) => item.attrs.fontFamily)
        .filter((value, index, self) => value && self.indexOf(value) === index);
    return fonts;
});

const addQRCode = (position = null) => {
    const [image] = useImage(isStudentId.value ? sampleProfile : sampleQR);
    watch(image, (newImage) => {
        if (newImage) {
            qrImage.value = newImage;
        }
    });
    saveState();
};

const addLogo = (position = null) => {
    const [image] = useImage(sampleLogo);
    watch(image, (newImage) => {
        if (newImage) {
            logoImage.value = newImage;
        }
    });
    saveState();
};

onMounted(() => {
    stageRef.value?.getStage().on('mousedown', handleStageClick);
});

onBeforeUnmount(() => {
    stageRef.value?.getStage().off('mousedown', handleStageClick);
});

const handleSaveAs = (values) => {
    if (editMetaVisible.value) {
        handleEditMetaSubmit(values);
        return;
    }
    console.log('values', values, editMetaVisible.value);
    storeData(values);
};

const handleEditMetaSubmit = async (values) => {
    loaderStore.startContextLoading('button');
    await $certificate.executeAction(
        'updateTemplate',
        {
            secureId: props.selectedCertificate.secureId,
            name: values.name,
            certificate_number_formate_id: values.certificate_number_formate_id,
        },
        {
            method: 'put',
            url: `spa/certificates/update-template/${props.selectedCertificate.secureId}`,
            showToast: true,
            reFetch: false,
            onSuccess: () => {
                editMetaVisible.value = false;
                openSavePopup.value = false;
                router.reload();
            },
            onFinish: () => {
                loaderStore.stopContextLoading('button');
            },
        }
    );
};

const handleSaveAndPreview = async (data) => {
    saveStageState(data);
    // if (isNewTemplate.value) {
    //     await $certificate.saveTemplate(payload.value, {
    //         isPreview: true,
    //         handleAdd: (response) => {
    //             $certificate.getPreviewUrl({ id: response.template.id });
    //             openPreviewPopup.value = true;
    //         },
    //     });
    //     isNewTemplate.value = false;
    // } else {
    if (selectedTemplate.value) {
        await $certificate.updateTemplate(
            payload.value,
            {
                isPreview: true,
                handleEdit: (response) => {
                    $certificate.getPreviewUrl({ id: response.template.id });
                    openPreviewPopup.value = true;
                },
            },
            isStudentId.value ? 'student-id' : 'certificate'
        );
        globalHelper.methods.showPopupSuccess('Template updated successfully', 'Success');
    } else {
        globalHelper.methods.showPopupError('Template is not loaded', 'Error');
    }
    // }
    setTimeout(() => {
        openSavePopup.value = false;
    }, 400);
    isSavePreview.value = true;
};
const isIframeLoad = () => {
    if (certificateEmbedeUrl.value != '') {
        isLoading.value = false;
        loaderStore.stopContextLoading('generate');
    }
};

const handleToolbarSave = () => {
    openSavePopup.value = true;
};

const handleActionMouseOver = () => {
    hoveredNode.value = null;
    hoveredTransformerRef.value.detachTransformer();
};

const handleViewAttributes = () => {
    openViewAttributesPopup.value = true;
};

const handleQrTransform = () => {
    const node = qrNode.value?.getNode();
    if (!node) return;
    // Apply scale to dimensions
    const newWidth = node.width() * node.scaleX();
    const newHeight = node.height() * node.scaleY();

    // Update attributes
    node.setAttrs({
        width: newWidth,
        height: newHeight,
        scaleX: 1,
        scaleY: 1,
    });

    saveState();
};

const handleLogoTransform = () => {
    const node = logoNode.value?.getNode();
    if (!node) return;
    // Apply scale to dimensions
    const newWidth = node.width() * node.scaleX();
    const newHeight = node.height() * node.scaleY();

    // Update attributes
    node.setAttrs({
        width: newWidth,
        height: newHeight,
        scaleX: 1,
        scaleY: 1,
    });

    saveState();
};

watch(
    [textElements, imageElements, backgroundImage],
    () => {
        isDataSaved.value = false;
    },
    { deep: true }
);

watch(
    () => customWidth.value,
    (newWidth) => {
        if (newWidth) {
            stageConfig.value.width = newWidth;
        }
    }
);
watch(
    () => certificateStore.getTemplates,
    (newTemplates) => {
        if (newTemplates) {
            selectedTemplate.value = newTemplates.id;
        }
    }
);

watch(
    () => customHeight.value,
    (newHeight) => {
        if (newHeight) {
            stageConfig.value.height = newHeight;
        }
    }
);

watch(
    () => selectedFormat.value,
    (newFormat) => {
        if (newFormat === 'us-letter') {
            stageConfig.value = {
                width: 816,
                height: 1056,
            };
        } else if (newFormat === 'a4') {
            stageConfig.value = {
                width: 707,
                height: 1000,
            };
        } else if (newFormat === 'cr80') {
            stageConfig.value = {
                width: 648,
                height: 408,
            };
        }

        if (newFormat === 'custom') {
            showCustomFormat.value = true;
        } else {
            showCustomFormat.value = false;
        }
    }
);

watch(
    () => selectedOrientation.value,
    (newOrientation) => {
        if (newOrientation === 'horizontal') {
            stageConfig.value = {
                width: stageConfig.value.height,
                height: stageConfig.value.width,
            };
        } else {
            stageConfig.value = {
                width: 700,
                height: 1000,
            };
        }
    }
);

const onEmbedLoad = () => {
    setTimeout(() => {
        loaderStore.stopContextLoading('generate');
    }, 2000);
};

const isLoading = ref(false);

const handleDetach = () => {
    selectedNode.value = null;
    transformerRef.value.detachTransformer();
    if (actionBarRef.value) {
        actionBarRef.value.detachToolbar();
    }
    if (hoveredTransformerRef.value) {
        hoveredNode.value = null;
        hoveredTransformerRef.value.detachTransformer();
    }
};

// Configuration constants
const ELEMENT_TYPES = {
    BACKGROUND: 'BACKGROUND',
    QR: 'QR',
    LOGO: 'LOGO',
};

const CLASS_NAMES = {
    TEXT: 'Text',
    IMAGE: 'Image',
    CIRCLE: 'Circle',
    RECT: 'Rect',
    ELLIPSE: 'Ellipse',
    LINE: 'Line',
};

// Helper functions
const getScale = (isStudentId, width) => {
    return isStudentId && width < 800 ? 0.5 : 1;
};

const extractUniqueFont = (children) => {
    return children
        .filter((item) => item.className === CLASS_NAMES.TEXT)
        .map((item) => item.attrs.fontFamily)
        .filter((value, index, self) => value && self.indexOf(value) === index);
};

const getBackground = (children) => {
    return children.find((item) => item.attrs.type === ELEMENT_TYPES.BACKGROUND);
};

const sortByYPosition = (children) => {
    return children.slice().sort((a, b) => parseFloat(a.attrs.y) - parseFloat(b.attrs.y));
};

// Element renderers
const renderBackgroundImage = (bg) => {
    if (!bg) return '';

    return `<img src="${bg.attrs.name}"
            class="background-image"
            style="width: 100%; height: 100%; position: absolute; top: 0; left: 0;"
            alt="Background">`;
};

const renderTextElement = (item, scale, fontFamily = 'sans-serif') => {
    const {
        x = 0,
        y,
        fontSize = 12,
        fill,
        fontStyle,
        align = 'left',
        textDecoration,
        width,
        text = '',
    } = item.attrs;

    return `<div class="text-item"
            style="position: absolute;
                   font-family: ${fontFamily};
                   left: ${x * scale}px;
                   top: ${y * scale}px;
                   font-size: ${fontSize * scale}px;
                   color: ${fill};
                   font-weight: ${extractFontWeight(fontStyle)};
                   font-style: ${extractFontStyle(fontStyle)};
                   text-align: ${align};
                   text-decoration: ${textDecoration};
                   width: ${width * scale}px;">
            ${text.replace(/\n/g, '<br />')}
          </div>`;
};

const renderImageElement = (item, scale) => {
    const { name, x, y, width, height } = item.attrs;

    return `<img src="${name}"
            class="image-item"
            style="position: absolute;
                   left: ${x * scale}px;
                   top: ${y * scale}px;
                   width: ${width * scale}px;
                   height: ${height * scale}px;"
            alt="Certificate Image">`;
};

const renderCircleElement = (item, scale) => {
    const { x, y, width = 100, height = 100, strokeWidth, stroke, radius, fill } = item.attrs;

    return `<div class="circle-item"
            style="position: absolute;
                   left: ${x * scale}px;
                   top: ${y * scale}px;
                   width: ${width * scale}px;
                   height: ${height * scale}px;
                   border-width: ${strokeWidth * scale}px;
                   border-color: ${stroke};
                   border-radius: ${radius}%;
                   background: ${fill};"></div>`;
};

const renderRectElement = (item, scale) => {
    const { x, y, width, height, strokeWidth, stroke, fill } = item.attrs;

    return `<div class="rect-item"
            style="position: absolute;
                   left: ${x * scale}px;
                   top: ${y * scale}px;
                   width: ${width * scale}px;
                   height: ${height * scale}px;
                   border-width: ${strokeWidth * scale}px;
                   border-color: ${stroke};
                   background: ${fill};"></div>`;
};

const renderEllipseElement = (item, scale) => {
    const { x, y, width = 100, height = 50, strokeWidth, stroke, fill } = item.attrs;

    return `<div class="ellipse-item"
            style="position: absolute;
                   left: ${x * scale}px;
                   top: ${y * scale}px;
                   width: ${width * scale}px;
                   height: ${height * scale}px;
                   border-width: ${strokeWidth * scale}px;
                   border-color: ${stroke};
                   background: ${fill};
                   border-radius: 50%;"></div>`;
};

const renderLineElement = (item, scale) => {
    const { x, y, points, variant, scaleX, scaleY, stroke } = item.attrs;
    const [x1, y1, x2, y2] = points;
    const vertHeight = y2 - y1;
    const hortWidth = x2 - x1;

    const isVertical = variant === 'line-vert';
    const lineWidth = isVertical ? 1 * scale : hortWidth * scaleX * scale;
    const lineHeight = isVertical ? vertHeight * scaleY * scale : 1 * scale;

    return `<div class="line-item"
            style="position: absolute;
                   left: ${x * scale}px;
                   top: ${y * scale}px;
                   width: ${lineWidth}px;
                   height: ${lineHeight}px;
                   background: ${stroke};"></div>`;
};

const renderQRElement = (item, scale, isStudentId, fontFamily) => {
    if (isStudentId) {
        return renderPlaceholderText(
            { ...item, attrs: { ...item.attrs, align: 'center' } },
            scale,
            fontFamily,
            '[profile_picture]'
        );
    }

    const { name, x, y, width, height } = item.attrs;

    return `<div id="qrImage"
            style="position: absolute;
                   left: ${x * scale}px;
                   top: ${y * scale}px;
                   width: ${width * scale}px;
                   height: ${height * scale}px;">
            <img src="${name}"
                 class="qr-item"
                 id="qrItem"
                 style="width: 100%; height: 100%;"
                 alt="Certificate QR Code">
          </div>`;
};

const renderLogoElement = (item, scale, fontFamily) => {
    return renderPlaceholderText(
        { ...item, attrs: { ...item.attrs, align: 'center' } },
        scale,
        fontFamily,
        '[college_logo]'
    );
};

const renderPlaceholderText = (item, scale, fontFamily, placeholder) => {
    const {
        x = 0,
        y,
        fontSize = 12,
        fill,
        fontStyle,
        align = 'left',
        textDecoration,
        width,
    } = item.attrs;

    return `<div class="text-item"
            style="position: absolute;
                   font-family: ${fontFamily};
                   left: ${x * scale}px;
                   top: ${y * scale}px;
                   font-size: ${fontSize * scale}px;
                   color: ${fill};
                   font-weight: ${extractFontWeight(fontStyle)};
                   font-style: ${extractFontStyle(fontStyle)};
                   text-align: ${align};
                   text-decoration: ${textDecoration};
                   width: ${width * scale}px;">
            ${placeholder}
          </div>`;
};

// Main element renderer
const renderElement = (item, scale, isStudentId) => {
    const fontFamily = item.attrs.fontFamily || 'sans-serif';

    switch (item.className) {
        case CLASS_NAMES.TEXT:
            return renderTextElement(item, scale, fontFamily);

        case CLASS_NAMES.IMAGE:
            return renderImageBasedElement(item, scale, isStudentId, fontFamily);

        case CLASS_NAMES.CIRCLE:
            return renderCircleElement(item, scale);

        case CLASS_NAMES.RECT:
            return renderRectElement(item, scale);

        case CLASS_NAMES.ELLIPSE:
            return renderEllipseElement(item, scale);

        case CLASS_NAMES.LINE:
            return renderLineElement(item, scale);

        default:
            return '';
    }
};

const renderImageBasedElement = (item, scale, isStudentId, fontFamily) => {
    const { type, name } = item.attrs;

    if (!name) return '';

    switch (type) {
        case ELEMENT_TYPES.BACKGROUND:
            return ''; // Background is handled separately

        case ELEMENT_TYPES.QR:
            return renderQRElement(item, scale, isStudentId, fontFamily);

        case ELEMENT_TYPES.LOGO:
            return renderLogoElement(item, scale, fontFamily);

        default:
            return renderImageElement(item, scale);
    }
};

// Main function
const generateHTML = () => {
    // Early return if no saved object
    if (!savedObj?.value) {
        console.warn('No saved object found');
        return '';
    }

    const children = savedObj.value.children?.[0]?.children ?? [];
    const scale = getScale(isStudentId.value, savedObj.value.attrs.width);

    const width = savedObj.value.attrs.width * scale;
    const height = savedObj.value.attrs.height * scale;

    const bg = getBackground(children);
    const fonts = extractUniqueFont(children);
    const sortedChildren = sortByYPosition(children);
    const backgroundHTML = renderBackgroundImage(bg);
    const elementsHTML = sortedChildren
        .map((item) => renderElement(item, scale, isStudentId.value))
        .filter(Boolean)
        .join('');

    return `
  <div id="watermark"
       style="position: fixed;
              bottom: 0px;
              left: 0px;
              top: 0px;
              right: 0px;
              width: 100%;
              height: 100%;
              z-index: -1000;">
    ${backgroundHTML}
  </div>
    <div class="certificate-container"
         style="width: ${width}px;
                height: ${height}px;
                position: relative;
                margin: auto;
                overflow: hidden;">
      ${elementsHTML}
    </div>`;
};

watch(
    () => loaderStore.contextLoaders['generate'] && isSavePreview.value,
    (newVal) => {
        if (newVal) {
            isLoading.value = true;
        } else {
            isLoading.value = false;
            openPreviewPopup.value = true;
        }
    }
);

watch(
    () => certificateStore.getTemplates,
    (newVal) => {
        if (newVal) {
            //   $certificate.getPreviewUrl({ id: newVal.id });
        }
    },
    {
        deep: true,
    }
);

watch(
    () => $certificate.state.previewUrl,
    (val) => {
        if (val) {
            certificateEmbedeUrl.value = val;
        }
    }
);

// Attributes

const attributeMenu = ref(null);
const isMenuVisible = ref(null);

const handleAttributeClick = (attrs) => {
    hideAttributeMenu();
    console.log('click', attrs);
    addText(`[${attrs.tag}]`);
};

const showAttributeMenu = (attrs) => {
    isMenuVisible.value = attrs.id;
};
const hideAttributeMenu = () => {
    hideAllPoppers();
    isMenuVisible.value = null;
};
const attributesStore = useCertificateAttributeStore();

onMounted(() => {
    attributesStore.fetchAttributes(isStudentId.value ? 'student-id' : 'certificate');
});

const editMetaVisible = ref(false);
const handleEditMeta = () => {
    editMetaVisible.value = true;
    openSavePopup.value = true;
};
</script>
<style lang=""></style>
