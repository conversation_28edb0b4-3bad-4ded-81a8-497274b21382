@extends('frontend.layouts.frontend')
@section('title', $pagetitle )

@section('content')
@php
$routeGet = explode("/", Route::getCurrentRoute()->uri());
$activeVar = $routeGet[0];
@endphp
<!-- Main content -->
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="custom-header">
                <div class="row">
                    <div class="col-md-offset-11 col-md-1">
                        <span class="pull-right add-btn-block">
                            <a href="{{ route('view-agent-payment-list') }}" data-toggle="tooltip" data-original-title="Go back" >
                                <div class="btn-add"><i class="fa fa-reply"></i></div>
                            </a>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="nav-tabs-custom">
                <ul class="nav nav-tabs">
                    <li class="{{ ($activeVar=='process-commission-agent' ) ? 'icon-active' : '' }}"> 
                        <a href="{{ route('process-commission-agent',array('id'=>$agentId)) }}">Process Commission</a></li>
                    <li class="{{ ($activeVar=='payment-history') ? 'icon-active' : '' }}">
                        <a href="{{ route('spa.manage-users.agents.profile', array('id'=>encryptIt($agentId)))}}#payment">Payment History</a></li>
                    <li class="active {{ ($activeVar=='credit-bonus-allocation') ? 'icon-active'  : '' }}">
                        <a href=" {{ route('credit-bonus-allocation',array('id'=>$agentId))}}">Credit/Bonus Allocation</a></li>
                </ul>
                <div class="tab-content">
                    <div class="active tab-pane padding-30">
                        {{ Form::model(array('method' => 'post'),['class' => 'form-horizontal vertical-add-form','id'=>'creditBonusAllocation']) }}
                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        <div class="row form-horizontal">
                            <div class="col-md-12">
                                <div class="box box-info">
                                    <div class="custom-header">
                                        <h3 class="box-title">Process Commission for Agent : {{ $arrAgentDetail->agency_name }}</h3>
                                        <div style="clear:both"></div>
                                    </div>
                                    <div class="box-body">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label for="full_name" class="col-sm-4 control-label">Type: <span id="" class="required-field">*<div></div></span></label>
                                                <div class="col-sm-5">
                                                    {{ Form::select('type',$crditAllocationType , null, array('class' => 'form-control', 'id' => 'type')) }}
                                                </div>   
                                            </div>
                                            <div class="form-group">
                                                <label for="full_name" class="col-sm-4 control-label">Amount: <span id="" class="required-field">*<div></div></span></label>
                                                <div class="col-sm-5">
                                                    {{ Form::text('amount',  null, array('class' => 'form-control', 'id' => 'amount','placeholder'=>'Enter Amount')) }}
                                                </div>   
                                            </div>
                                            <div class="form-group" id="paymnetMode">
                                                <label for="full_name" class="col-sm-4 control-label">Payment Mode: <span id="" class="required-field">*<div></div></span></label>
                                                <div class="col-sm-5">
                                                    {{ Form::select('payment_mode', $arrPaidMode , null, array('class' => 'form-control', 'id' => 'payment_mode')) }}
                                                </div>   
                                            </div>
                                            <div class="form-group" id="paymentDate">
                                                <label for="full_name" class="col-sm-4 control-label">Payment Date: <span id="" class="required-field">*<div></div></span></label>
                                                <div class="col-sm-5">
                                                    {{ Form::text('payment_date', null, array('class' => 'form-control dateField', 'id' => 'payment_date','placeholder'=>'Enter Payment Date','autocomplete'=>"off")) }}
                                                </div>   
                                            </div>
                                            <div class="form-group">
                                                <label for="full_name" class="col-sm-4 control-label">Comment: <span id="" class="required-field">*<div></div></span></label>
                                                <div class="col-sm-5">
                                                    {{ Form::textarea('comment',  null, array('class' => 'form-control', 'id' => 'comment','rows'=>'2','placeholder'=>'Enter Comment')) }}
                                                </div>   
                                            </div>

                                            <div class="form-group ">
                                                <label class="col-sm-4">&nbsp;</label>
                                                <div class="col-sm-5" >
                                                    <input name="Add" class="btn btn-info" value="Add" type="submit">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--                            <div class="col-md-3">
                                                            <div class="box box-info">
                                                                <div class="custom-header">
                                                                    <h3 class="box-title">Agent Payment</h3>
                                                                    <a class="link-black text-sm pull-right" data-toggle="tooltip" data-original-title="Edit" style="font-size: 24px;" href="http://www.rtomanager.dev/apply-online/31"><i class="fa fa-edit"></i></a>
                                                                    <div style="clear:both"></div>
                                                                </div>
                                                                <div class="box-body">
                                                                    <div id="midsidebar">
                                                                        @include('frontend.agent_payment.agent-payment-module-list')
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>-->
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="custom-header">
                                    <h3 class="box-title">Payment Schedule For Student</h3>
                                </div>
                                <div class="box-body table-responsive no-padding">
                                    <table class="table table-hover table-custom" id="bonusdata">
                                        <thead>
                                            <tr>
                                                <th scope="col">Type</th>
                                                <!--<th scope="col">Std Transaction No</th>-->
                                                <th scope="col">Credit Amount</th>
                                                <th scope="col">Pay Mode</th>
                                                <th scope="col">Credit Used</th>
                                                <th scope="col">Comment</th>
                                                <!--<th scope="col">Action</th>-->
                                            </tr>
                                        </thead>
                                        <tbody id="">
                                            @foreach($arrCreditBonusAllocation as $creditBonus)
                                            <tr>
                                                <td> {{ $creditBonus['crditAllocationType'] }}
                                                    <div class="action-overlay">
                                                        <ul class="icon-actions-set">
                                                            <li>
                                                                <a  class="link-black text-sm viewInfo" data-toggle="tooltip" data-original-title="Credit Used Info" data-id="{{ $creditBonus['id'] }}"  href="javascript:;"><i class="fa fa-book "></i> </a> 
                                                            </li>
                                                            <li>
                                                                <a  class="link-black text-sm editInfo" data-toggle="tooltip" data-original-title="Update Info" data-id="{{ $creditBonus['id'] }}"  href="javascript:;"><i class="fa fa-pencil "></i> </a> 
                                                            </li>
                                                            @if($creditBonus['agentCreditId'] == '')
                                                            <li>
                                                                <span data-toggle="modal" class="delete" data-id="{{ $creditBonus['id'] }}" data-target="#deleteModal"> 
                                                                    <a class="link-black text-sm delete" data-toggle="tooltip" data-original-title="Delete"  href="javascript:;"><i class="fa fa-remove "></i> </a> 
                                                                </span>
                                                            </li>
                                                            @endif
                                                        </ul>
                                                    </div>
                                                </td>
                                                <!--<td> {{ $creditBonus['std_transaction_no'] }} </td>-->
                                                <td>${{ number_format($creditBonus['amount'],2) }} </td>
                                                <td> {{ $creditBonus['arrPaidMode'] }} </td>
                                                <td>${{ number_format($creditBonus['credit_used'],2) }} </td>
                                                <td> {{ $creditBonus['comment'] }} </td>
                                            </tr>
                                            @endforeach
                                            @if(count($arrCreditBonusAllocation)==0)
                                            <tr>
                                                <td colspan="5" style="text-align: center">
                                                    <p style="color:red;">No Record Found</p>
                                                </td>
                                            </tr>
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                                <!-- @if(count($arrCreditBonusAllocation)==0)
                                <div id="noRecords" class="text-center">
                                    <table class="table table-hover table-custom">
                                        <tbody>
                                            <tr><td colspan="12" style="text-align: center">
                                                <p style="color:red;">No Record Found</p>
                                            </td></tr>
                                        </tbody>
                                    </table>
                                </div>
                                @endif -->
                            </div>
                        </div>    
                        <div class="modal fade" id="deleteModal" role="dialog">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                        <h4 class="modal-title">Delete Record</h4>
                                    </div>
                                    <div class="modal-body">
                                        <div class="box box-info">
                                            <div class="box-body">
                                                <p> You want to delete record. Are you sure?</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                                        <button class="btn btn-success yes-sure" type="button">Yes</button>
                                    </div>
                                </div><!-- /.modal-content -->
                            </div><!-- /.modal-dialog -->
                        </div>
                        {{ Form::hidden('agent_id', $agentId, array('class' => 'form-control', 'id' => 'agent_id')) }}
                        {{ Form::close() }}
                        <style>
                            #midsidebar ul{
                                list-style: none;
                                padding: 0 !important;
                            }
                            #midsidebar ul li a{
                                color: #15a3f5;
                                font-size: 14px;
                                line-height: 22px;
                            }
                            #midsidebar ul li.active{
                                background: #e7e7e7;
                            }
                            #midsidebar ul li.active a{
                                padding: 0 0 0 10px !important;
                            }
                            .error {
                                border: 1px solid red !important;
                            }
                        </style>

                        <div class="modal fade" id="creditUsedInfoModel" role="dialog">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                        <h4 class="modal-title">Credit Used Information</h4>
                                    </div>
                                    <div class="modal-body">
                                        <div class="box-body table-responsive no-padding">
                                            <table id="" class="table table-hover table-custom">
                                                <tr>
                                                    <th>Student Id</th>
                                                    <th>Course Info</th>
                                                    <th>Used Trans</th>
                                                    <th>Receipt</th>
                                                    <th>Used Amount</th>
                                                </tr>
                                                <tfoot class="appendBonusData" id="appendBonusData">
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal fade" id="creditUsedEditModel" role="dialog">
                            <div class="modal-dialog">
                                {{ Form::open( array('class' => 'form-horizontal vertical-add-form updateInfo','route' => array('edit-credit-bonus-allocation'),'method' => 'post','id'=>'updateInfo')) }}
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                        <h4 class="modal-title">Credit Used Information</h4>
                                    </div>
                                    <div class="modal-body">
                                        <div class="box-body">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="form-group">
                                                        <label for="type" class="col-sm-4 control-label">Type:<span id="" class="required-field"><div></div></span></label>
                                                        <div class="col-sm-5">  
                                                            {{ Form::select('type', $crditAllocationType ,  null , array('class' => 'form-control edit_type', 'id' => 'edit_type')) }}
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="amount" class="col-sm-4 control-label">Amount:<span id="" class="required-field"><div></div></span></label>
                                                        <div class="col-sm-5">       
                                                            {{ Form::text('amount' ,  null  , array('class' => 'form-control edit_amount', 'id' => 'edit_amount', 'placeholder' => 'Enter amount','disabled')) }}
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="payment_mode" class="col-sm-4 control-label">Payment Mode:<span id="" class="required-field"><div></div></span></label>
                                                        <div class="col-sm-5">  
                                                            {{ Form::select('payment_mode', $arrPaidMode, null , array('class' => 'form-control payment_mode', 'id' => 'edit_payment_mode')) }}
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="payment_date" class="col-sm-4 control-label">Payment Date:<span id="" class="required-field">*<div></div></span></label>
                                                        <div class="col-sm-5">       
                                                            {{ Form::text('payment_date', null, array('class' => 'form-control dateField', 'id' => 'edit_payment_date','placeholder'=>'Enter Payment Date')) }}
                                                            {{ Form::hidden('edit_id', null, array('class' => 'form-control', 'id' => 'edit_id','placeholder'=>'Enter Payment Date')) }}
                                                            {{ Form::hidden('agent_id', null, array('class' => 'form-control', 'id' => 'edit_agent_id','placeholder'=>'')) }}
                                                        </div>
                                                    </div>
                                                    <div class="form-group margin-minus-15">
                                                        <label for="comment" class="col-sm-4 control-label">Comment:<span id="" class="required-field">*<div></div></span></label>
                                                        <div class="col-sm-5">       
                                                            {{ Form::textarea('comment',  null, array('class' => 'form-control', 'id' => 'edit_comment','placeholder'=>'Enter Comment')) }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="submit" class="btn disable btn-info">Update</button>
                                        <button data-dismiss="modal" class="btn btn-danger" type="button">Cancel</button>
                                    </div>
                                </div>
                                {{ Form::close() }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@endsection