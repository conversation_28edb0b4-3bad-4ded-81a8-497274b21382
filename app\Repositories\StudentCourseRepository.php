<?php

namespace App\Repositories;

use App\Helpers\Helpers;
use App\Model\v2\CampusVenue;
use App\Model\v2\CourseTraining;
use App\Model\v2\OfferUpfrontFeeSchedule;
use App\Model\v2\ResultGrade;
use App\Model\v2\SetupProviderFacility;
use App\Model\v2\Student;
use App\Model\v2\StudentCertificateRegister;
use App\Model\v2\StudentCommunicationLog;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentDeferCourse;
use App\Model\v2\StudentOfferDocuments;
use App\Model\v2\StudentSubjectEnrolment;
use App\Model\v2\StudentUnitEnrollment;
use App\Traits\CommonTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Support\Services\UploadService;

class StudentCourseRepository extends CommonRepository
{
    use CommonTrait;

    protected $model;

    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    public function getAdditionalServiceListData($request, $countOnly = false)
    {
        // $collegeId = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $whereArr = [
            'rsasr.student_id' => $request->student_id,
            'rsasr.college_id' => $request->user()->college_id,
        ];

        $columnArr = [
            'rssn.services_name',
            'rssc.category_name',
            'rss.facility_name as facilityName',
            'rsp.company_name as providerName',
            'rsasr.service_start_date',
            'rsasr.student_price',
            'rsasr.comment as additionalComment',
            'rsasr.id as id',
        ];

        $query = StudentSubjectEnrolment::from('rto_student_additional_service_request as rsasr')
            ->join('rto_services_setup as rss', 'rss.id', '=', 'rsasr.facility_id')
            ->join('rto_service_provider_facilities_setup as rspfs', 'rspfs.facility_id', '=', 'rsasr.facility_id')
            ->leftjoin('rto_setup_services_category as rssc', 'rssc.id', '=', 'rsasr.category_id')
            ->leftjoin('rto_setup_services_name as rssn', 'rssn.id', '=', 'rsasr.service_name_id')
            ->leftjoin('rto_setup_provider as rsp', 'rsp.id', '=', 'rsasr.service_provider_id')
            ->where($whereArr)
            ->select($columnArr);

        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPaginationV2($query, $post, $countOnly);

        return $result;
    }

    public function getCoeHistoryListData($request, $countOnly = false)
    {
        $collegeId = $request->college_id;
        $studentCourseId = $request->student_course_id;

        $post = ($request->input()) ? $request->input() : [];
        $filePath = Config::get('constants.uploadFilePath.StudentCOE');
        $destinationPath = Helpers::changeRootPath($filePath, $post['student_id']);

        $columnArr1 = [
            DB::raw("CONCAT(rto_courses.course_code,':',rto_courses.course_name) as course_name"),
            DB::raw("CONCAT(DATE_FORMAT(rsc.start_date, '%d %b %Y'),' - ',DATE_FORMAT(rsc.finish_date, '%d %b %Y')) as study_period"),
            'rsc.coe_name',
            'rsc.coe_image',
            'rsc.updated_at',
            'rto_users.name as updated_by',
        ];
        $query1 = StudentCourses::from('rto_student_courses as rsc')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rsc.course_id')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'rsc.updated_by')
            ->where(['rsc.id' => $studentCourseId])
            ->select($columnArr1);

        $whereArr2 = [
            'rsd.college_id' => $collegeId,
            'rsd.student_id' => $request->student_id,
            'rsd.student_course_id' => $studentCourseId,
        ];
        $columnArr2 = [
            DB::raw("CONCAT(rto_courses.course_code,':',rto_courses.course_name) as course_name"),
            // DB::raw("CONCAT(DATE_FORMAT(rsc.start_date, '%d %b %Y'),' - ',DATE_FORMAT(rsd.new_course_end_date, '%d %b %Y')) as study_period"),
            DB::raw(" CONCAT( DATE_FORMAT(rsc.start_date, '%d %b %Y'), ' - ', DATE_FORMAT( IF(rsd.new_course_end_date IS NOT NULL AND rsd.new_course_end_date != '', rsd.new_course_end_date, rsd.original_course_end_date), '%d %b %Y' ) ) as study_period "),
            'rsd.new_coe_code as coe_name',
            'rsd.new_coe_file as coe_image',
            'rsd.updated_at',
            'rto_users.name as updated_by',
        ];
        $query2 = StudentDeferCourse::from('rto_student_defer as rsd')
            ->leftjoin('rto_student_courses as rsc', 'rsc.id', '=', 'rsd.student_course_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rsd.course_id')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'rsd.updated_by')
            ->where($whereArr2)
            ->where(function ($qry) {
                $qry->whereNotNull('rsd.new_coe_file')->orWhereNotNull('rsd.new_coe_code');
            })
            ->select($columnArr2)
            ->groupBy('rsd.id')
            ->orderBy('rsd.id', 'DESC');

        $unionQuery = $query1->unionAll($query2);

        $this->gridDataSorting($unionQuery, $post);

        $result = $this->gridDataPaginationV2($unionQuery, $post, $countOnly);

        if (! $countOnly) {
            foreach ($result['data'] as $k => $row) {
                $downloadURL = null;
                if (! empty($row['coe_image'])) {
                    $existingFilePath = UploadService::exists($destinationPath['view'].$row['coe_image']);
                    if ($existingFilePath) {
                        $downloadURL = UploadService::download($destinationPath['view'].$row['coe_image']);
                    }
                }
                $result['data'][$k]['download_url'] = $downloadURL;
            }
        }

        return $result;
    }

    /*public function getCoeHistoryListDataV2($request, $countOnly = false)
    {
        $post = ($request->input()) ? $request->input() : [];
        $filePath = Config::get('constants.uploadFilePath.StudentCOE');
        $destinationPath = Helpers::changeRootPath($filePath, $post['student_id']);

        $query = StudentCourses::from('rto_student_courses as rsc')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rsc.course_id')
            ->leftjoin('rto_users as rsc_users', 'rsc_users.id', '=', 'rsc.updated_by')
            ->leftjoin('rto_student_defer as rsd', function($join) {
                $join->on('rsd.student_course_id', '=', 'rsc.id')
                     ->where(function($query) {
                         $query->whereNotNull('rsd.new_coe_file')->orWhereNotNull('rsd.new_coe_code');
                     });
            })
            ->leftjoin('rto_users as rsd_users', 'rsd_users.id', '=', 'rsd.updated_by')
            ->where([
                'rsc.student_id' => $request->student_id,
                'rsc.id' => $request->student_course_id,
            ])
            ->select([
                DB::raw("CONCAT(rto_courses.course_code,':',rto_courses.course_name) as course_name"),
                DB::raw("CASE
                    WHEN rsd.id IS NOT NULL AND rsd.new_course_end_date IS NOT NULL THEN CONCAT(DATE_FORMAT(rsc.start_date, '%d %b %Y'),' - ',DATE_FORMAT(rsd.new_course_end_date, '%d %b %Y'))
                    WHEN rsd.id IS NOT NULL AND rsd.new_course_end_date IS NULL THEN CONCAT(DATE_FORMAT(rsc.start_date, '%d %b %Y'),' - ',DATE_FORMAT(rsd.original_course_end_date, '%d %b %Y'))
                    ELSE CONCAT(DATE_FORMAT(rsc.start_date, '%d %b %Y'),' - ',DATE_FORMAT(rsc.finish_date, '%d %b %Y'))
                END as study_period"),
                DB::raw("CASE WHEN rsd.id IS NOT NULL THEN rsd.new_coe_code ELSE rsc.coe_name END as coe_name"),
                DB::raw("CASE WHEN rsd.id IS NOT NULL THEN rsd.new_coe_file ELSE rsc.coe_image END as coe_image"),
                DB::raw("CASE WHEN rsd.id IS NOT NULL THEN rsd.updated_at ELSE rsc.updated_at END as updated_at"),
                DB::raw("CASE WHEN rsd.id IS NOT NULL THEN rsd_users.name ELSE rsc_users.name END as updated_by"),
                'rsd.id as defer_id'
            ]);

        //$this->gridDataSorting($query, $post);

        $result = $this->gridDataPaginationV2($query, $post, $countOnly);

        if (! $countOnly) {
            foreach ($result['data'] as $k => $row) {
                //$result['data'][$k]['download_url'] = UploadService::download($destinationPath['view'].$row['coe_image']);
                $downloadURL = null;
                if (! empty($row['coe_image'])) {
                    $existingFilePath = UploadService::exists($destinationPath['view'].$row['coe_image']);
                    if ($existingFilePath) {
                        $downloadURL = UploadService::download($destinationPath['view'].$row['coe_image']);
                    }
                }
                $result['data'][$k]['download_url'] = $downloadURL;
            }
        }
        return $result;
    }*/

    public function getFeeScheduleListData($request, $countOnly = false)
    {
        // $collegeId = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            DB::raw("CONCAT(roufs.frequency,' ',roufs.duration) as fee_duration"),
            'roufs.*',
            'rto_users.name as update_by',
        ];
        $whereArr = [
            'roufs.college_id' => $request->college_id,
            'roufs.student_id' => $request->student_id,
            'roufs.student_course_id' => $request->student_course_id,
        ];
        $query = OfferUpfrontFeeSchedule::from('rto_offer_upfront_fee_schedule as roufs')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'roufs.updated_by')
            ->where($whereArr)
            ->select($columnArr);

        $this->gridDataSorting($query, $post);

        return $this->gridDataPaginationV2($query, $post, $countOnly);
    }

    public function getServiceProviderData($request)
    {
        $whereArr = [
            'rspfs.college_id' => $request->college_id,
            'rspfs.category_id' => $request->category_id,
            'rspfs.is_active' => '1',
        ];

        return SetupProviderFacility::from('rto_service_provider_facilities_setup as rspfs')
            ->leftjoin('rto_setup_provider as rsp', 'rsp.id', '=', 'rspfs.service_provider_id')
            ->where($whereArr)
            ->get(['rsp.id as Id', 'rsp.company_name as Name'])
            ->toArray();

    }

    public function getTrainingPlanData($primaryId)
    {

        $arrTrainingStatus = Config::get('constants.arrTrainingStatus');
        $arrContractType = Config::get('constants.arrContractType');
        $arrStateType = Config::get('constants.arrStateList');
        $arrFundingSource = Config::get('constants.arrFundingSource');

        $columnArr = [
            'rstp.id',
            'rs.generated_stud_id',
            DB::raw('concat(rs.name_title, rs.first_name, " ", rs.family_name) as student_name'),
            DB::raw('concat(rv.venue_code," ",rv.venue_name ) as venue_code_text'),
            'rc.course_code',
            'rc.course_name',
            'rsc.course_attempt',
            'rcc.contract_code',
            're.employer_name',
            'rcs.course_site_name',
            'rstp.*',
        ];

        $res = CourseTraining::from('rto_course_training_plan as rstp')
            ->leftjoin('rto_employer as re', 're.id', '=', 'rstp.employer')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rstp.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rstp.course_id')
            ->leftjoin('rto_contract_code as rcc', 'rcc.id', '=', 'rstp.contract_id')
            ->leftjoin('rto_venue as rv', 'rv.id', '=', 'rstp.venue_code')
            ->leftjoin('rto_course_site as rcs', 'rcs.id', '=', 'rstp.course_site_id')
            ->leftJoin('rto_student_courses as rsc', function ($join) {
                $join->on('rsc.student_id', '=', 'rstp.student_id');
                $join->on('rsc.course_id', '=', 'rstp.course_id');
            })
            ->where('rstp.id', $primaryId)
            ->select($columnArr)->get()->first();

        if (isset($res)) {
            $res->stateText = (! array_key_exists('0'.$res->state, $arrStateType) || empty($res->state)) ? '' : $arrStateType['0'.$res->state];
            $res->statusText = $arrTrainingStatus[$res->status];
            $res->contractTypeText = $arrContractType[$res->contract_type];
            $res->fundingSourceText = $arrFundingSource[$res->funding_source];
            $res->contractDateText = date('d-M-Y', strtotime($res->contract_date));
            $res->employer_name = $res->employer_name ?? '-';
            $res->course_site_name = $res->course_site_name ?? '-';
        }

        return $res;
    }

    public function getStudentCourseData($collegeId, $studentId, $studentCourseId)
    {

        $whereArr = [
            'rs.college_id' => $collegeId,
            'rs.id' => $studentId,
            'rsc.id' => $studentCourseId,
        ];
        $columnArr = [
            'rs.id as studentId',
            'rsc.id as selectedStudCourseID',
            DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as student_name"),
            DB::raw("CONCAT(rc.course_code,' - ',rc.course_name) as course_name"),
            'rs.generated_stud_id',
            'rs.profile_picture',
            'rsc.status',
            'rsc.start_date',
            'rsc.finish_date',
            'campus.name as campus_name',
        ];

        $res = Student::alias('rto_students as rs')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_campus as campus', 'campus.id', '=', 'rsc.campus_id')
            ->where($whereArr)
            ->select($columnArr)->get()
            ->first();

        if (isset($res)) {
            $res->profile_picture = $this->getStudentProfilePicPath($studentId, $res->profile_picture);
            $res->status_color = $this->getCourseStatusColor($res->status);
            $res->start_date = date('d-m-Y', strtotime($res->start_date));
            $res->finish_date = date('d-m-Y', strtotime($res->finish_date));
            // $res->duration = date('d/m/Y', strtotime($res->start_date)) . ' - ' . date('d/m/Y', strtotime($res->finish_date));
            $res->duration = Helpers::convertDateToReadableFormat($res->start_date).' - '.Helpers::convertDateToReadableFormat($res->finish_date);
        }

        return $res;
    }

    public function getStudentTrainingPlanListData($request, $courseId)
    {
        $arrTrainingStatus = Config::get('constants.arrTrainingStatus');
        $arrContractType = Config::get('constants.arrContractType');

        $whereArr = [
            'rstp.college_id' => $request->college_id,
            'rstp.student_id' => $request->student_id,
            'rstp.course_id' => $courseId,
        ];
        $columnArr = [
            'rstp.id',
            'rstp.student_id',
            'rstp.employer',
            'rstp.training_contract_id',
            'rstp.booking_id',
            'rstp.contract_date',
            'rstp.supervisor',
            'rstp.status',
            'rstp.contract_type',
            're.employer_name',
            'rstp.course_id',
            'rsc.id as student_course_id',
        ];

        $query = CourseTraining::from('rto_course_training_plan as rstp')
            ->leftjoin('rto_employer as re', 're.id', '=', 'rstp.employer')
            ->leftJoin('rto_student_courses as rsc', function ($join) {  // TODO:: temporary solution
                $join->on('rsc.student_id', '=', 'rstp.student_id');
                $join->on('rsc.course_id', '=', 'rstp.course_id');
            })
            ->where($whereArr)
            ->select($columnArr);
        $post = ($request->input()) ? $request->input() : [];

        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPaginationV2($query, $post, false);

        foreach ($result['data'] as $k => $row) {

            $result['data'][$k]['status'] = $arrTrainingStatus[$row['status']];
            $result['data'][$k]['contract_type'] = $arrContractType[$row['contract_type']];
            $result['data'][$k]['contract_date'] = date('Y-m-d', strtotime($row['contract_date']));
            $result['data'][$k]['employer_name'] = ($row['employer_name'] != '') ? $row['employer_name'] : '-';
        }

        return $result;

        // return $courseTrainingArr;
    }

    public function getCertificateIssueListData($request, $countOnly = false)
    {
        // $collegeId = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $whereArr = [
            'rscr.college_id' => $request->college_id,
            'rscr.student_id' => $request->student_id,
            'rsc.id' => $request->student_course_id,
        ];

        $columnArr = [
            'rscr.id',
            'rscr.certificate_no',
            'rscr.manually_certificate_no',
            'rscr.is_file_name',
            'rscr.certificate_type',
            'rscr.certificate_name',
            DB::raw("CONCAT(rto_courses.course_code, ':' ,rto_courses.course_name) as course_name"),
            DB::raw("DATE_FORMAT(rscr.date_generated, '%d-%m-%Y') as generated_date"),
            'rto_users.name as generated_by',
        ];
        $query = StudentCertificateRegister::from('rto_student_certificate_register as rscr')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'rscr.created_by')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rscr.course_id')
            ->leftJoin('rto_student_courses as rsc', function ($join) {
                $join->on('rsc.student_id', '=', 'rscr.student_id');
                $join->on('rsc.course_id', '=', 'rscr.course_id');
            })
            ->where($whereArr)
            ->select($columnArr);

        $this->gridDataSorting($query, $post);
        $resArr = Config::get('constants.certificateType');
        $certificateDatas = $this->gridDataPaginationV2($query, $post, $countOnly);

        if (is_array($certificateDatas)) {
            foreach ($certificateDatas['data'] as &$certificateData) {
                $certificateData['certificate_name'] = $certificateData['certificate_name'] ?? $resArr[$certificateData['certificate_type']] ?? null;
            }
            unset($certificateData);
        }

        return $certificateDatas;
        // return $this->gridDataPagination($query, $post, $countOnly);
    }

    public function getEnrollSubjectListData($request, $countOnly = false)
    {
        // $collegeId = Auth::user()->college_id;
        // $finalOutcomeArr = Config::get('constants.arrSelectFinalOutcome');
        $post = ($request->input()) ? $request->input() : [];
        $whereArr = [
            'rsse.college_id' => $request->college_id,
            'rsse.student_id' => $request->student_id,
            'rsc.id' => $request->student_course_id,
        ];

        $columnArr = [
            DB::raw("CONCAT(DATE_FORMAT(rsse.activity_start_date, '%d %b %Y'),' - ',DATE_FORMAT(rsse.activity_finish_date, '%d %b %Y')) as activity_date"),
            'rsse.final_outcome as compentancy',
            'rsse.batch as batch',
            'rsse.id',
            'rsse.subject_attempt',
            'rto_subject_unit.unit_code',
            'rto_subject_unit.unit_name',
            'rsse.created_at',
        ];

        $query = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
            // ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rsse.semester_id')
            ->leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rsse.unit_id')
            // ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rsse.subject_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rsse.course_id')
            ->leftjoin('rto_student_courses as rsc', 'rsc.course_id', '=', 'rsse.course_id')
            // ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rsc.campus_id')
            ->where($whereArr)
            ->withStudentCourseId($request->student_course_id)
            ->select($columnArr)
            ->orderBy('rsse.created_at', 'DESC');

        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPaginationV2($query, $post, $countOnly);

        if (! $countOnly) {
            foreach ($result['data'] as $k => $row) {
                $result['data'][$k]['batch'] = (! empty($row['batch'])) ? $row['batch'] : 'No Batch';
            }
        }

        return $result;
    }

    public function getEnrollVenueLocation($request)
    {
        $courseId = StudentCourses::find($request->student_course_id)->course_id;

        return CampusVenue::from('rto_venue as rv')
            ->join('rto_student_courses as rsc', 'rsc.campus_id', '=', 'rv.campus_id')
            ->where([
                'rv.college_id' => $request->college_id,
                'rsc.course_id' => $courseId,
            ])
            ->select([
                'rv.id as Id',
                DB::raw("CONCAT(rv.state,' : ',rv.venue_code, ' - ', rv.venue_name) as Name"),
            ])
            ->groupBy('rv.venue_name')
            ->get()
            ->toArray();
    }

    public function studentSubjectEnrolment($whereArr, $subjectIds)
    {
        return StudentSubjectEnrolment::where($whereArr)->whereIn('subject_id', $subjectIds)->count();
    }

    public function studentUnitEnrolment($whereArr, $unitIds, $isHigherEd)
    {
        if ($isHigherEd) {

            $arrSelectMarksOutcome = ResultGrade::getFailedGradeResult($whereArr['college_id']);
            $filedResultArray = $arrSelectMarksOutcome->pluck('id')->toArray();

            return StudentSubjectEnrolment::whereNotIn('mark_outcome', $filedResultArray)->where($whereArr)->whereIn('unit_id', $unitIds)->count();
        } else {
            return StudentSubjectEnrolment::where('final_outcome', '!=', 'NYC')->where($whereArr)->whereIn('unit_id', $unitIds)->count();
        }
    }

    public function saveStudentSubjectEnrollData($data, $subjectId, $unitId, $attempt = 0)
    {
        $loginData = Auth::user();
        /* Check If course type is higherEd then mark_outcome otherwise final_outcome */

        $dataArr = [
            'college_id' => $loginData->college_id,
            'student_id' => $data['student_id'],
            'student_course_id' => $data['student_course_id'],
            'course_id' => $data['course_id'],
            'semester_id' => $data['semester_id'],
            'term' => $data['term'],
            'enroll_type' => $data['type_of_enrollment'],
            'course_stage' => $data['course_stage'],
            'subject_id' => $subjectId,
            'unit_id' => $unitId,
            'subject_attempt' => ++$attempt,
            'batch' => null,
            'activity_start_date' => date('Y-m-d', strtotime($data['activity_start_date'])),
            'activity_finish_date' => date('Y-m-d', strtotime($data['activity_finish_date'])),
            'study_reason' => $data['study_reason'],
            'vanue_location' => $data['venue_location'],
            'funding_source_state' => $data['funding_source_state'],
            'funding_source_nat' => $data['funding_source_nat'],
            'delivery_mode' => $data['delivery_mode'],
            'outcome_identifier' => $data['outcome_identifier'],
            'final_outcome' => $data['final_outcome'],
            'mark_outcome' => (isset($data['mark_outcome'])) ? $data['mark_outcome'] : null,
            'grade' => (isset($data['grade'])) ? $data['grade'] : null,
            'vet_in_school' => '',
            'created_by' => $loginData->id,
            'updated_by' => $loginData->id,
        ];

        return StudentSubjectEnrolment::create($dataArr);
    }

    public function saveStudentUnitEnrollData($data, $objSubjectEnrolment, $unitRow)
    {
        $loginData = Auth::user();
        $dataArr = [
            'student_subject_enrollment_id' => $objSubjectEnrolment->id,
            'college_id' => $loginData->college_id,
            'unit_id' => $unitRow['id'],
            'compentency' => $data['competency'],
            'schedule_hours' => $unitRow['nominal_hours'],
            'delivery_mode' => $data['delivery_mode'],
            'tution_fee' => $unitRow['tution_fees'],
            'attended_hour' => 0,
            'study_from' => $objSubjectEnrolment->activity_start_date,
            'study_to' => $objSubjectEnrolment->activity_finish_date,
            'created_by' => $loginData->id,
            'updated_by' => $loginData->id,
        ];

        return StudentUnitEnrollment::create($dataArr);
    }

    public function getStudentCourseList($selectedDataArr)
    {
        // Retrieve and process the student course list
        // Use the $selectedDataArr to fetch the data from the database or any other source
        // Return the processed data
    }

    public function getFeeScheduleDetails($request, $countOnly = false)
    {

        $post = ($request->input()) ? $request->input() : [];

        $query = StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_offer_upfront_fee_schedule as roufs', 'roufs.course_id', '=', 'rsc.course_id')
            ->where(['rsc.id' => $request->student_course_id])
            ->select('rsc.*', DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as full_name"));

        $this->gridDataSorting($query, $post);

        return $this->gridDataPagination($query, $post, $countOnly);
    }

    public function getUploadedOfferChecklistDocumentsData($request)
    {
        $whereArr = [
            'rodc.college_id' => $request->college_id,
            'rsod.rto_student_id' => $request->student_id,
            'rsod.rto_offer_document_checklist_id' => $request->uploadid,
        ];
        $columnArr = [
            'ru.name',
            'rodc.document_name',
            'rcm.folder_or_file',
            'rcm.size',
            'rodc.college_id',
            'rsod.*',
        ];

        return StudentOfferDocuments::from('rto_student_offer_documents as rsod')
            ->leftJoin('rto_users as ru', 'ru.id', '=', 'rsod.last_checked_by')
            ->leftJoin('rto_offer_document_checklist as rodc', 'rodc.id', '=', 'rsod.rto_offer_document_checklist_id')
            ->leftJoin('rto_college_materials as rcm', 'rcm.id', '=', 'rsod.document_material_id')
            ->where($whereArr)
            ->orderBy('rsod.id', 'DESC')
            ->limit(1)
            ->get($columnArr)
            ->toArray();
    }

    public function getAppliedStudentCourse($studentId, $studCourseId)
    {
        return StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_campus as cam', 'cam.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->where([
                'rsc.student_id' => $studentId,
                'rsc.id' => $studCourseId,
            ])
            ->select([
                'cam.name as campus_name',
                'rc.cricos_code',
                'rc.course_code',
                'rc.work_placement_hour',
                'rc.work_placement',
                'rc.course_name',
                'rsc.*',
                'ra.agency_name',
                'ra.office_address',
                'ra.office_city',
                'ra.office_state',
                'ra.office_postcode',
            ])
            ->get()->first();
    }

    /* Below functions are not re-structured */
    public function getStudentUpfrontFeeSchedule($selectedCourseId)
    {
        return StudentCourses::join('rto_courses as rc', 'rc.id', '=', 'rto_student_courses.course_id')
            ->where('rto_student_courses.id', $selectedCourseId)
            ->select('rc.course_code', 'rc.course_name', 'rto_student_courses.*')
            ->first();
    }

    public function deleteOfferUpfrontFeeSchedule($collegeId, $selectedCourseId)
    {
        OfferUpfrontFeeSchedule::where('college_id', $collegeId)
            ->where('student_course_id', $selectedCourseId)
            ->delete();
    }

    public function saveUpfrontFeeSchedules($upfrontFeeSchedules)
    {
        OfferUpfrontFeeSchedule::insert($upfrontFeeSchedules);
    }

    public function getDocumentTypes() {}

    public function getStudentType($studentId) {}

    public function getOfferDocumentNames($collegeId, $studentType)
    {
        // return OfferDocumentChecklist::where('college_id', '=', $collegeId)->where('student_origin', '=', $studentType)->where('is_active', '=', 1)->get();
    }

    public function getStudentDocument($studentId)
    {
        return StudentOfferDocuments::from('rto_student_offer_documents as rsod')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rsod.last_checked_by')
            ->where('rsod.rto_student_id', '=', $studentId)
            ->get(['ru.name', 'rsod.*']);
    }

    public function getOfferCommunicationLogListData($postData, $countOnly = false)
    {
        $query = StudentCommunicationLog::leftjoin('rto_users', 'rto_users.id', '=', 'rto_student_communication.comment_by')
            ->leftjoin('rto_setup_section', 'rto_student_communication.status', '=', 'rto_setup_section.id')
            ->leftjoin('rto_setup_section as a2', 'rto_student_communication.type', '=', 'a2.id')
            ->where(['rto_student_communication.college_id' => $postData['college_id'], 'rto_student_communication.student_id' => $postData['student_id'], 'rto_student_communication.student_course_id' => $postData['student_course_id'], 'rto_student_communication.log_type' => 'offer'])
            ->orderBy('rto_student_communication.id', 'DESC')
            ->select(DB::raw(' IF(rto_student_communication.visiblity = 1, "enable", "disable") as visiblity_name'), 'rto_users.id as user_id', 'rto_users.username as username', 'rto_student_communication.*', 'rto_setup_section.value as status_name', 'a2.value as type_name');
        $this->gridDataSorting($query, $postData);
        $result = $this->gridDataPaginationV2($query, $postData, $countOnly);
        foreach ($result['data'] as $k => $row) {

            $result['data'][$k]['created_at'] = date('d-m-Y h:i:s A', strtotime($row['created_at']));
        }

        return $result;
    }

    public function delete($id)
    {
        return $this->model->destroy($id);
    }
}
