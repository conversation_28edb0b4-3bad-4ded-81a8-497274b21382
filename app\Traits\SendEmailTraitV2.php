<?php

namespace App\Traits;

use App\DTO\studentProfile\SendQueueEmailPayload;
use App\Helpers\Helpers;
use App\Model\SendMail;
use App\Model\v2\SmtpSetup;
use App\Model\v2\Staff;
use App\Model\v2\Student;
use App\Repositories\StudentProfileCommonRepository;
use Illuminate\Http\Request;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Mail;

trait SendEmailTraitV2
{
    use CommonTrait;

    protected $studentProfileCommonRepository;

    public function __construct(
        StudentProfileCommonRepository $studentProfileCommonRepository
    ) {
        $this->studentProfileCommonRepository = $studentProfileCommonRepository;
    }

    public function setStudentProfileCommonRepository(StudentProfileCommonRepository $studentProfileCommonRepository)
    {
        $this->studentProfileCommonRepository = $studentProfileCommonRepository;
    }

    public function getCourseType()
    {
        return 'course';
    }

    public function getGenericType()
    {
        return 'generic';
    }

    public function sendMailToStudentTrait(SendQueueEmailPayload $dto)
    {
        $payload = $dto->toArray();
        // Retrieve input data from the request
        $studentIDs = (! is_array($payload['student_id'])) ? explode(',', $payload['student_id']) : $payload['student_id'];
        $courseId = ($payload['course_id']) ? ((! is_array($payload['course_id'])) ? explode(',', $payload['course_id']) : $payload['course_id']) : null;
        // $courseId = $payload['course_id'];
        $studentCourseId = $payload['student_course_id'];
        $emailType = ($payload['email_type']) ? $payload['email_type'] : $this->getCourseType();
        // Retrieve student course information based on course or student course ID

        // dd($emailType);
        if (! empty($studentCourseId)) {
            foreach ($studentIDs as $key => $studentID) {
                $studentArrInfo[] = Student::with(['studentCourses' => function ($query) use ($courseId, $key) {
                    $query->where('course_id', $courseId[$key]);
                }])->where('id', $studentID)->first();
            }
            // $studentArrInfo = $this->studentProfileCommonRepository->getStudentCoursesById($studentCourseId, $studentIDs);
        } else {
            $studentArrInfo = Student::with(['studentCourses' => function ($query) use ($courseId) {
                if (! empty($courseId)) {
                    if (is_array($courseId)) {
                        $query->whereIn('course_id', $courseId);
                    } else {
                        $query->where('course_id', $courseId);
                    }
                }
            }])->whereIn('id', $studentIDs)->get();
        }
        [$attachment, $attachmentLogData, $existAttachment, $existAttachmentLogData] = $this->getAttachmentsData($payload);

        $sendCount = 0;
        $notSendCount = 0;
        $failReason = '';
        $checkMailListener = false;
        $escapeCourseCheck = true;
        $failedStudentId = [];
        $failedStudentIdWithReason = [];
        $successStudentIdWithReason = [];
        // dd($studentArrInfo);
        foreach ($studentArrInfo as $studRow) {

            $studentName = $studRow->first_name.' '.$studRow->family_name;

            $flag = false;
            $rowStudentId = $studRow->id;
            $rowCourseId = '';

            // Check If selected Course Found or not
            if (((count($studRow->studentCourses) > 0) && ($emailType == $this->getCourseType())) || ($emailType == $this->getGenericType())) {
                // if ($courseId == $studRow->studentCourses[0]->course_id || $studentCourseId == $studRow->studentCourses[0]->id || $escapeCourseCheck) {

                if ($emailType == $this->getCourseType()) {
                    $rowCourseId = $studRow->studentCourses[0]->course_id;
                }

                if (! $checkMailListener) {
                    $failReason = $this->checkTestMail($payload);
                    $checkMailListener = true;
                }

                if ($checkMailListener && empty($failReason)) {
                    $mailData = $this->prepareStudentMailData($rowStudentId, $rowCourseId, $studRow, $payload, $existAttachment, $attachment, $emailType);

                    $mailData['attachFile'] = array_merge($payload['attachment_file'], $existAttachment);
                    $mailData['attachmentLogData'] = array_merge($attachmentLogData, $attachmentLogData);
                    $sentEmail = $this->sendEmailEvent($mailData, new Request($payload), $studentName);

                    if ($sentEmail['status'] == 'fail') {
                        $failedStudentIdWithReason[] = [
                            'type' => 'Email Not Valid',
                            'reason' => $sentEmail['message'],
                        ];
                        $failedStudentId[] = $rowStudentId;
                    } else {
                        $successStudentIdWithReason[] = $studentName;
                    }
                    $sendCount++;
                    $flag = true;
                } else {
                    $notSendCount++;
                    $failedStudentId[] = $rowStudentId;
                    $failedStudentIdWithReason[] = [
                        'type' => 'mailprovidernotworking',
                        'reason' => $failReason,
                    ];
                    $emailStatusData['data']['failData'][] = $this->getFailDataArr($studRow, $failReason);
                    $flag = true;
                }

                // }
            }
            // dd($flag);
            if (! $flag) {
                $notSendCount++;
                $failedStudentId[] = $rowStudentId;
                $failedStudentIdWithReason[] = [
                    'type' => 'coursenotfound',
                    'reason' => "Course not found for $studRow->first_name $studRow->family_name",
                ];
                $emailStatusData['data']['failData'][] = $this->getFailDataArr($studRow, "Course not found for $studRow->first_name $studRow->family_name");
            }
        }

        return [
            'failed' => $failedStudentId,
            'failed_student_id_with_reason' => $failedStudentIdWithReason,
            'successStudentIdWithReason' => $successStudentIdWithReason,
        ];
    }

    public function sendMailForStudentOrientationTrait($payload)
    {
        $studentCourseIDs = explode(',', $payload['student_course_id']);
        $studentCourseInfo = $this->studentCoursesRepository->with(['student', 'course', 'agent'])->whereIn('id', $studentCourseIDs)->get();

        return $this->sendMailForStudentOrientationAndCommunication($payload, $studentCourseInfo, 'Student', 'Orientation');
    }

    public function sendMailForStudentCommunicationTrait($payload)
    {
        $studentCourseIDs = explode(',', $payload['student_course_id']);
        $studentCourseInfo = $this->studentCoursesRepository->with(['student'])->whereIn('id', $studentCourseIDs)->get();

        return $this->sendMailForStudentOrientationAndCommunication($payload, $studentCourseInfo, 'Student', 'Communication');
    }

    private function sendMailForStudentOrientationAndCommunication($payload, $resData, $userRole = 'Student', $module = '')
    {
        [$attachment, $attachmentLogData, $existAttachment, $existAttachmentLogData] = $this->getAttachmentsData($payload);

        $contentData['attachment'] = $attachment;
        $contentData['attachmentLog'] = $attachmentLogData;
        $contentData['email_subject'] = ($payload['email_subject'] != '') ? $payload['email_subject'] : "$userRole $module";

        $failReason = $this->checkTestMail($payload);
        if ($failReason) {
            return ['success_msg' => '', 'fail_msg' => $failReason, 'statusData' => []];
        }

        $sendCount = 0;
        $notSendCount = 0;
        $sendStatus = [];
        foreach ($resData as $res) {
            $studRow = $res->student;
            $studentName = $studRow['first_name'].' '.$studRow['family_name'];
            $mailData = $this->prepareStudentMailData($res->student_id, $res->course_id, $studRow, $payload, $existAttachment, $attachment);
            $mailData['attachmentLogData'] = array_merge($attachmentLogData, $existAttachmentLogData);

            try {
                $this->sendEmailEvent($mailData, $payload, $studentName);
                $sendCount++;
                $sendStatus[] = $this->getSendDataArr($studRow, 'Success', 'Send Successfully');
                if ($module == 'Orientation') {
                    $this->studentCoursesRepository->update(['is_orientation_email_send' => 1], $res->id);
                }
            } catch (\Exception $e) {
                $notSendCount++;
                $sendStatus[] = $this->getSendDataArr($studRow, 'Fail', $e->getMessage());
            }
        }

        return [
            'success_msg' => 'Email sent successfully for '.$sendCount.(($sendCount > 1) ? ' Student(s)' : ' student'),
            'fail_msg' => ($notSendCount == 0) ? '' : 'Email sent fail for '.$notSendCount.(($notSendCount > 1) ? ' Student(s)' : ' student'),
            'statusData' => $sendStatus,
        ];
    }

    public function sendMailForStaffCommunicationTrait($payload)
    {
        $staffIDs = explode(',', $payload['staff_id']);
        $staffInfo = Staff::whereIn('id', $staffIDs)->get();

        $emailCC = ($payload['email_cc'] != '') ? explode(',', $payload['email_cc']) : [];
        $emailBCC = ($payload['email_bcc'] != '') ? explode(',', $payload['email_bcc']) : [];
        $replyToEmail = ($payload['reply_to_email'] != '') ? $payload['reply_to_email'] : '';

        [$attachment, $attachmentLogData, $existAttachment, $existAttachmentLogData] = $this->getAttachmentsData($payload);

        $contentData['attachment'] = $attachment;
        $contentData['attachmentLog'] = $attachmentLogData;
        $contentData['email_subject'] = ($payload['email_subject'] != '') ? $payload['email_subject'] : 'Student Orientation';

        $sendCount = 0;
        $notSendCount = 0;
        $sendStatus = [];
        $emailStatusData = [];
        $checkMailListener = false;

        foreach ($staffInfo as $staff) {
            $flag = false;
            $failReason = '';
            if (! $checkMailListener) {
                $failReason = $this->checkTestMail($payload);
                $checkMailListener = true;
            }
            if ($checkMailListener && empty($failReason)) {
                [$emailSubject, $emailContent] = $this->getStaffMailContent($staff->id, $payload['email_subject'], $payload['email_content']);
                $mailData = [
                    'id' => $staff->id,
                    'to' => $staff->email,
                    'from' => $payload['email_from'],
                    'cc' => $emailCC,
                    'bcc' => $emailBCC,
                    'replyTo' => $replyToEmail,
                    'subject' => $emailSubject,
                    'attachFile' => array_merge($existAttachment, $attachment),
                    'body' => $emailContent,
                    'attachmentLogData' => array_merge($attachmentLogData, $existAttachmentLogData),
                ];
                $recipientName = $staff->first_name.' '.$staff->last_name;
                $this->sendEmailEvent($mailData, $payload, $recipientName);
                $flag = true;
            }

            if ($flag) {
                $sendStatus[] = $this->getSendDataArr($staff, $flag, 'Send Successfully');
                $sendCount++;
            } else {
                $notSendCount++;
                $sendStatus[] = $this->getSendDataArr($staff, $flag, $failReason);
            }
        }

        $emailStatusData['success_msg'] = 'Email sent successfully for '.$sendCount.(($sendCount > 1) ? ' Staff(s)' : ' Staff');
        $emailStatusData['fail_msg'] = ($notSendCount == 0) ? '' : 'Email sent fail for '.$notSendCount.(($notSendCount > 1) ? ' Staff(s)' : ' Staff');
        $emailStatusData['statusData'] = $sendStatus;

        return $emailStatusData;
    }

    private function getSendDataArr($studRow, $status, $description)
    {
        return [
            'id' => $studRow['id'],
            'name' => $studRow['first_name'].' '.$studRow['family_name'],
            'profile_pic' => $this->getStudentProfilePicPath($studRow['id'], $studRow['profile_picture'], 'small'),
            'status' => ($status) ? 'Success' : 'Fail',
            'description' => $description,
        ];
    }

    private function getAttachmentsData($payload)
    {

        // Retrieve existing attachments
        $emailExistAttachmentIdArr = ($payload['existing_attachment_id'] != '') ? explode(',', $payload['existing_attachment_id']) : [];

        [$existAttachment, $existAttachmentLogData] = $this->retrieveExistingAttachments($emailExistAttachmentIdArr, $payload['email_template_id'], $payload['college_id']);

        // Retrieve attachment files
        $attachment = $attachmentLogData = [];
        if ($payload['attachment_file']) {
            [$attachment, $attachmentLogData] = $this->mailAttachment($payload, true);
        }

        return [$attachment, $attachmentLogData, $existAttachment, $existAttachmentLogData];
    }

    private function checkTestMail($payload)
    {
        $tempMailData = Config::get('constants.testMailData');
        $tempMailData['college_id'] = $payload['college_id'];
        $res = $this->sendSmtpTestMail($tempMailData);
        if ($res['status']) {
            return '';
        } else {
            return $res['message'];
        }
    }

    private function retrieveExistingAttachments($emailExistAttachmentIdArr, $emailTemplateId, $collegeId)
    {
        $existAttachment = [];
        $existAttachmentLogData = [];
        if (count($emailExistAttachmentIdArr) > 0) {
            $existFilePath = Config::get('constants.uploadFilePath.Templates');

            $existDestinationPath = Helpers::changeRootPath($existFilePath, $emailTemplateId, $collegeId);
            // dd($existDestinationPath);
            $docList = $this->studentProfileCommonRepository->emailTemplateDocModel($emailExistAttachmentIdArr);
            foreach ($docList as $doc) {
                $existAttachment[] = $existDestinationPath['view'].$doc['file'];
                $existAttachmentLogData[$doc['file']] = $existDestinationPath['view'].$doc['file'];
            }
        }

        return [$existAttachment, $existAttachmentLogData];
    }

    private function prepareStudentMailData($studentId, $courseId, $studRow, $payload, $existAttachment, $attachment, $emailType = 'course')
    {
        $emailCC = ($payload['email_cc'] != '') ? explode(',', $payload['email_cc']) : [];
        $emailBCC = ($payload['email_bcc'] != '') ? explode(',', $payload['email_bcc']) : [];
        $replyToEmail = ($payload['reply_to_email'] != '') ? $payload['reply_to_email'] : '';

        [$convertedSubject, $convertedData] = $this->getStudentMailContent($studentId, $courseId, $payload['email_content'], $payload['email_subject'], $emailType);

        return [
            'id' => $studentId,
            'to' => $studRow->email,
            'from' => $payload['email_from'],
            'cc' => $emailCC,
            'bcc' => $emailBCC,
            'replyTo' => $replyToEmail,
            'subject' => $convertedSubject,
            'body' => $convertedData,
            'attachFile' => array_merge($existAttachment, $attachment),
        ];
    }

    private function getStudentMailContent($studentId, $courseId, $content, $subject, $emailType)
    {
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);
        if ($emailType == $this->getCourseType()) {
            $arrStudentCourse = $this->studentProfileCommonRepository->getStudentCoursesEmailContent($studentId, $courseId);
        } else {
            $arrStudentCourse = $this->studentProfileCommonRepository->getStudentEmailContent($studentId);
        }

        $arrStudentEnrolledCourse = $this->studentProfileCommonRepository->getArrayStudentEnrolledCourseName($studentId);
        $arrStudentOfferedCourse = $this->studentProfileCommonRepository->getArrayStudentOfferedCourseName($studentId);
        if (! empty($arrStudentCourse)) {
            $row = $arrStudentCourse[0];
            $domain = url('/');
            $basePath = $destinationPath['view'];
            $usePublicImages = $row['allow_public_images'] ?? true;
            $college_logo_url = $this->getUploadedFileUrl($basePath.$row['college_logo'], $usePublicImages);
            $college_signature_url = $this->getUploadedFileUrl($basePath.$row['college_signature'], $usePublicImages);
            $dean_signature_url = $this->getUploadedFileUrl($basePath.$row['dean_signature'], $usePublicImages);
            $admission_manager_signature_url = $this->getUploadedFileUrl($basePath.$row['admission_manager_signature'], $usePublicImages);
            $student_support_signature_url = $this->getUploadedFileUrl($basePath.$row['student_support_signature'], $usePublicImages);

            $college_logo = '<img src="'.$college_logo_url.'" alt="College Logo" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $college_signature = '<img src="'.$college_signature_url.'" alt="College Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $dean_signature = '<img src="'.$dean_signature_url.'" alt="Dean/CEO Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $admission_manager_signature = '<img src="'.$admission_manager_signature_url.'" alt="Admission Manager Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $student_support_signature = '<img src="'.$student_support_signature_url.'" alt="Student Support Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';

            $enrolledCourseList = '';
            if (! empty($arrStudentEnrolledCourse)) {
                $enrolledCourseList = '<ul>';
                foreach ($arrStudentEnrolledCourse as $value) {
                    $enrolledCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
                }
                $enrolledCourseList .= '</ul>';
            }
            $offeredCourseList = '';
            if (! empty($arrStudentOfferedCourse)) {
                $offeredCourseList = '<ul>';
                foreach ($arrStudentOfferedCourse as $value) {
                    $offeredCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
                }
                $offeredCourseList .= '</ul>';
            }

            $imgArr = [
                'college_logo' => $college_logo,
                'college_signature' => $college_signature,
                'dean_signature' => $dean_signature,
                'admission_manager_signature' => $admission_manager_signature,
                'student_support_signature' => $student_support_signature,
            ];
            $dataArr = $this->dataBindForStudent($row, $enrolledCourseList, $offeredCourseList, $imgArr);
            foreach ($dataArr as $key => $value) {
                // TODO::GNG-5159 (Use regex to replace only placeholders that are not inside HTML attributes)
                $pattern = '/(?<!data-mention=")'.preg_quote($key, '/').'(?!")(?![^<]*>)/';
                $content = preg_replace($pattern, $value, $content);
                $subject = str_replace("$key", $value, $subject);
            }

            return [$subject, $content];
        } else {
            return false;
        }
        // return $student;
    }

    private function getStaffMailContent($staffId, $emailSubject, $emailContent)
    {
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);

        $arrStaffData = (new Staff)->getStaffEmailContent($staffId);

        if (! empty($arrStaffData)) {
            $row = $arrStaffData[0];
            // $domain = env('APP_URL');
            $domain = url('/');

            $basePath = $destinationPath['view'];
            $usePublicImages = $row['allow_public_images'] ?? true;
            $college_logo_url = $this->getUploadedFileUrl($basePath.$row['college_logo'], $usePublicImages);
            $college_signature_url = $this->getUploadedFileUrl($basePath.$row['college_signature'], $usePublicImages);
            $dean_signature_url = $this->getUploadedFileUrl($basePath.$row['dean_signature'], $usePublicImages);
            $admission_manager_signature_url = $this->getUploadedFileUrl($basePath.$row['admission_manager_signature'], $usePublicImages);
            $student_support_signature_url = $this->getUploadedFileUrl($basePath.$row['student_support_signature'], $usePublicImages);

            $college_logo = '<img src="'.$college_logo_url.'" alt="College Logo" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $college_signature = '<img src="'.$college_signature_url.'" alt="College Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $dean_signature = '<img src="'.$dean_signature_url.'" alt="Dean/CEO Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $admission_manager_signature = '<img src="'.$admission_manager_signature_url.'" alt="Admission Manager Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $student_support_signature = '<img src="'.$student_support_signature_url.'" alt="Student Support Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';

            $dataArr = [
                '{AlterEmail1}' => '',
                '{AlterEmail2}' => '',
                //  "{CollegeEmail}"        => $row['coe_name'],
                '{CollegeLogo}' => $college_logo,
                '{CollegeEmail}' => $row['college_email'],
                '{Country}' => $row['country_name'],
                '{CountryBirth}' => '',
                '{CurrentDate}' => date('d-m-Y'),
                '{DoB}' => '',
                '{DOB}' => '',
                '{DoB Without Stroke}' => '******************',
                '{Email}' => $row['staff_email'],
                '{ExpDate}' => '',
                '{Fax}' => $row['fax'],
                '{StudentId}' => '',
                '{FirstName}' => $row['first_name'],
                '{MiddleName}' => '',
                '{LastName}' => $row['last_name'],
                '{Gender}' => '',
                '{Mobile}' => $row['mobile'],
                '{Nationality}' => $row['nationality'],
                '{NickName}' => '',
                '{PassportNo}' => '',
                '{Phone}' => $row['phone'],
                '{Postcode}' => $row['postcode'],
                '{State}' => $row['state'],
                '{StreetAddress}' => $row['address'],
                '{StreetNumber}' => '',
                '{UnitDetail}' => '',
                '{BuildingName}' => '',
                '{Suburb}' => $row['city_town'],
                '{Title}' => $row['name_title'],
                '{UserName}' => $row['staff_name'],
                '{VisaType}' => '',
                '{CourseCode}' => '',
                '{CourseName}' => '',
                '{CollegeRtoCode}' => $row['RTO_code'],
                '{CollegeCircosCode}' => $row['CRICOS_code'],
                '{CollegeLegalName}' => $row['legal_name'],
                '{CollegeName}' => $row['entity_name'],
                '{CollegeSignature}' => $college_signature,
                '{DeanName}' => $row['dean_name'],
                '{DeanSignature}' => $dean_signature,
                '{AdmissionManagerSignature}' => $admission_manager_signature,
                '{StudentSupportSignature}' => $student_support_signature,
                '{CollegeContactPerson}' => $row['contact_person'],
                '{CollegeContactPhone}' => $row['contact_phone'],
                '{CollegeURL}' => $row['college_url'],
                '{CollegeABN}' => $row['college_ABN'],
                '{CollegeFax}' => $row['fax'],
                '{CourseType}' => '',
                '{Campus}' => '',
                '{StudentType}' => '',
                '{TeacherFirstName}' => $row['teacher_first_name'],
                '{TeacherLastName}' => $row['teacher_last_name'],
                '{TeacherEmail}' => $row['teacher_email'],
                '{TeacherMobile}' => $row['teacher_mobile'],
                '{AgencyName}' => '',
                '{AgentName}' => '',
                '{AgentEmail}' => '',
                '{AgentTelephone}' => '',
                '{EnrolledCourseList}' => '',
                '{OfferedCourseList}' => '',
                '{CourseStartDate}' => '',
                '{CourseEndDate}' => '',
                '{CourseDuration}' => '',
                '{StudentContactEmail}' => '',
                '{StudentAlternateEmail}' => '',
                '{StudentEmergencyEmail}' => '',
            ];

            foreach ($dataArr as $key => $value) {
                $emailContent = str_replace("$key", $value, $emailContent);
                $emailSubject = str_replace("$key", $value, $emailSubject);
            }

            return [$emailSubject, $emailContent];
        } else {
            return false;
        }
    }

    private function mailAttachment($payload, $viewFlag = false)
    {

        $emailAttachments = $payload['attachment_file'];
        $result = array_combine(
            array_map(fn ($fileUrl) => pathinfo($fileUrl, PATHINFO_BASENAME), $emailAttachments),
            $emailAttachments
        );

        return [$emailAttachments, $result];

        $filePath = Config::get('constants.uploadFilePath.TempMailAttachment');
        $destinationPath = Helpers::changeRootPath($filePath);

        $savedFileName = [];
        $mailFileName = [];
        $counts = 0;
        $imageGet = 0;
        foreach ($emailAttachments as $emailAttachment) {
            $originalName = $emailAttachment->getClientOriginalName();
            $filename = date('YmdHsi').'-'.$originalName;
            $savedFileName[] = $filename;
            if ($imageGet == 0) {
                if (! is_dir($destinationPath['default'])) {
                    File::makeDirectory($destinationPath['default'], 0777, true, true);
                }
                $emailAttachment->move($destinationPath['default'], $filename);
                $mailFileName[] = $destinationPath['view'].$savedFileName[$counts];
                if ($viewFlag) {
                    $mailLogFileName[$originalName] = $destinationPath['view'].$savedFileName[$counts];
                }
                $counts++;
            }
        }

        if ($viewFlag) {
            return [$mailFileName, $mailLogFileName];
        }

        return $mailFileName;
    }

    private function getFailDataArr($studRow, $reason)
    {
        return [
            'id' => $studRow->id,
            'name' => $studRow->first_name.' '.$studRow->family_name,
            'profile_pic' => $this->getStudentProfilePicPath($studRow->id, $studRow->profile_picture, 'small'),
            'reason' => $reason,
        ];
    }

    private function sendSmtpTestMail($mailData)
    {

        $collegeId = $mailData['college_id'];
        $mail = SmtpSetup::getSMTPDetail();
        if (($mail) && ($mail->status)) {
            $this->setSMTP();
        }
        $fromEmail = $mail && $mail->status ? $mail->email : config('mail.from.address');
        $fromEmailName = $mail && $mail->status ? $mail->name : config('mail.from.name');
        $mailData['from'] = $fromEmail;
        $mailData['fromName'] = $fromEmailName;
        $page = $mailData['page'] ?? [];
        $data = $mailData['data'] ?? [];
        try {
            ini_set('memory_limit', '1024M');
            ini_set('max_execution_time', 180); // 3 minutes

            Mail::send($page, $data, function ($message) use ($mailData) {
                $message->from(($mailData['from'] = ! '' ? $mailData['from'] : '<EMAIL>'), $mailData['fromName']);
                $message->to($mailData['to']);
                $message->subject($mailData['subject']);
                $message->html($mailData['body']);
            });
            $result = '';
        } catch (\Exception  $exception) {
            $errorMessage = $exception->getMessage();
            $errorCode = $exception->getCode();
            $contentText = (isset($mailData['data']['content'])) ? $mailData['data']['content'] : (isset($mailData['body']) ? $mailData['body'] : '');
            $arrFailMail = [
                'sender' => ($mailData['from'] != '') ? $mailData['from'] : '<EMAIL>',
                'receiver' => $mailData['to'],
                'subject' => $mailData['subject'],
                'content' => $contentText,
                'error_message' => $errorMessage,
                'error_code' => $errorCode,
            ];
            //    $objFailedEmail=new FailedEmails();
            //    $objFailedEmail->saveFailedEmails($collegeId,$arrFailMail);
            // dd($mailData);
            $this->studentProfileCommonRepository->saveFailedEmails($collegeId, $arrFailMail);

            return ['status' => false, 'message' => $errorMessage];
        }
        if ($result == '') {
            return ['status' => true, 'message' => 'Email sent successfully'];
        } else {
            return ['status' => false, 'message' => 'Email is not send successfully.'];
        }
    }

    private function dataBindForStudent($row, $enrolledCourseList, $offeredCourseList, $imgArr)
    {
        return [

            '{AlterEmail1}' => $row['emergency_email'],
            '{AlterEmail2}' => $row['emergency_email'],
            '{CollegeLogo}' => $imgArr['college_logo'],
            '{CollegeEmail}' => $row['college_email'],
            '{Country}' => $row['country_name'],
            '{CountryBirth}' => $row['birth_country'],
            // "{CurrentDate}"         => date('d-m-Y'),
            '{CurrentDate}' => $this->getCurrentDateTimeWithTimeZone($row['college_timezone'], 'd-m-Y'), // TODO::GN-2333
            '{DoB}' => date('d-m-Y', strtotime($row['birth_date'])),
            '{DOB}' => date('d-m-Y', strtotime($row['birth_date'])),
            '{DoB Without Stroke}' => '******************',
            '{Email}' => $row['student_email'],
            '{ExpDate}' => date('d-m-Y', strtotime($row['visa_expiry_date'])),
            '{Fax}' => $row['fax'],
            '{Student ID}' => $row['generated_stud_id'],
            '{Student Name}' => $row['first_name'].' '.$row['family_name'],
            '{StudentId}' => $row['generated_stud_id'],
            '{FirstName}' => $row['first_name'],
            '{MiddleName}' => $row['middel_name'],
            '{LastName}' => $row['family_name'],
            '{Gender}' => $row['gender'],
            '{Mobile}' => $row['current_mobile_phone'],
            '{Nationality}' => $row['nationality'],
            '{NickName}' => $row['nickname'],
            '{PassportNo}' => $row['passport_no'],
            '{Phone}' => $row['current_mobile_phone'],
            '{Postcode}' => $row['current_postcode'],
            '{State}' => $row['current_state'],
            '{StreetAddress}' => $row['current_street_name'],
            '{StreetNumber}' => $row['current_street_no'],
            '{UnitDetail}' => $row['current_unit_detail'],
            '{BuildingName}' => $row['current_building_name'],
            '{Suburb}' => $row['current_city'],
            '{Title}' => $row['name_title'],
            '{UserName}' => $row['generated_stud_id'],
            '{VisaType}' => $row['visa_type'],
            '{CourseCode}' => $row['course_code'] ?? '',
            '{CourseName}' => $row['course_name'] ?? '',
            '{CollegeRtoCode}' => $row['RTO_code'],
            '{CollegeCircosCode}' => $row['CRICOS_code'],
            '{CollegeLegalName}' => $row['legal_name'],
            '{CollegeName}' => $row['entity_name'],
            '{CollegeSignature}' => $imgArr['college_signature'],
            '{DeanName}' => $row['dean_name'],
            '{DeanSignature}' => $imgArr['dean_signature'],
            '{AdmissionManagerSignature}' => $imgArr['admission_manager_signature'],
            '{StudentSupportSignature}' => $imgArr['student_support_signature'],
            '{CollegeContactPerson}' => $row['contact_person'],
            '{CollegeContactPhone}' => $row['contact_phone'],
            '{CollegeURL}' => $row['college_url'],
            '{CollegeABN}' => $row['college_ABN'],
            '{CollegeFax}' => $row['fax'],
            '{CourseType}' => $row['course_type'] ?? '',
            '{Campus}' => $row['campus_name'] ?? '',
            '{StudentType}' => $row['student_type'],
            '{TeacherFirstName}' => $row['teacher_first_name'],
            '{TeacherLastName}' => $row['teacher_last_name'],
            '{TeacherEmail}' => $row['teacher_email'],
            '{TeacherMobile}' => $row['teacher_mobile'],
            '{AgencyName}' => $row['agency_name'] ?? '',
            '{AgentName}' => $row['agent_name'] ?? '',
            '{AgentEmail}' => $row['agent_email'] ?? '',
            '{AgentTelephone}' => $row['agent_telephone'] ?? '',
            '{EnrolledCourseList}' => $enrolledCourseList,
            '{OfferedCourseList}' => $offeredCourseList,
            '{CourseStartDate}' => (isset($row['start_date'])) ? date('d-m-Y', strtotime($row['start_date'])) : '',
            '{CourseEndDate}' => (isset($row['finish_date'])) ? date('d-m-Y', strtotime($row['finish_date'])) : '',
            '{CourseDuration}' => (isset($row['total_weeks'])) ? $row['total_weeks'].' Weeks' : '',
            '{StudentContactEmail}' => $row['personalEmail'],
            '{StudentAlternateEmail}' => $row['AlternateEmail'],
            '{StudentEmergencyEmail}' => $row['emergency_email'],
        ];
    }

    private function sendEmailEvent($mailData, $requestData, $recipientName)
    {
        if (empty($mailData['to'])) {
            return ['status' => 'fail', 'message' => "Email not found for $recipientName."];
        }

        // $mailData = $event->studentMailData;
        // $requestData = $event->requestData;

        $mailData['college_id'] = $requestData['college_id'];
        $mailStatus = (new SendMail)->sendSmtpMailQueue($mailData);

        if ($mailStatus['status'] == 'success') {
            if ((isset($requestData['is_attendance_warning']) && (! empty($requestData['is_attendance_warning'])))) {
                $this->communicationLog->saveWarningEmailLog($requestData, $mailData['id'], $mailData['body'], $mailData['to'], $mailData['from']);
            } elseif (isset($requestData['offer_comm_log']) && $requestData['offer_comm_log'] == 'on') {
                $requestData = (object) $requestData;
                $this->communicationLog->saveCommunicationLog($requestData, $mailData['id'], $mailData['body'], $mailData['to'], $mailData['from'], $mailData['attachmentLogData'], $mailData);
            }

            if (isset($requestData['staff_id']) && ! empty($requestData['staff_id'])) {
                $this->communicationLog->saveStaffCommunicationLog($requestData, $mailData['id'], $mailData['body'], $mailData['to'], $mailData['from'], $mailData['attachmentLogData'], $mailData);
            }
            // return $mailStatus;
        }

        return $mailStatus;
        // return event(new \App\Events\SendStudentMailEvent($mailData, $payload));
        // $event = event(new \App\Events\SendStudentMailEvent($mailData, $request));
        // return optional($event[0])->success ?? false;
    }

    private function setSMTP()
    {
        $config = SmtpSetup::where('status', 1)->first(); // Adjust the query to suit your needs
        if ($config) {
            // Set mail configuration
            info('SMTP Config ', [tenant('id'), Config::get('mail.mailers.smtp'), $config]);
            Config::set('mail.mailers.smtp.transport', 'smtp');
            Config::set('mail.mailers.smtp.host', $config->host);
            Config::set('mail.mailers.smtp.port', $config->port);
            Config::set('mail.mailers.smtp.username', $config->username);
            Config::set('mail.mailers.smtp.password', $config->password);
            Config::set('mail.mailers.smtp.encryption', $config->encryption);
            Config::set('mail.from.address', $config->email);
            Config::set('mail.from.name', $config->name);
        }
        info('SMTP Config after set', [tenant('id'), Config::get('mail.mailers.smtp'), $config]);

    }

    private function testSMTPEmail($mailData)
    {
        $mail = SmtpSetup::getSMTPDetail();
        if (($mail) && ($mail->status)) {
            $this->setSMTP();
            Mail::forgetMailers();
        }
        $fromEmail = $mail && $mail->status ? $mail->email : config('mail.from.address');
        $fromEmailName = $mail && $mail->status ? $mail->name : config('mail.from.name');
        $mailData['from'] = $fromEmail;
        $mailData['fromName'] = $fromEmailName;
        $page = $mailData['page'] ?? [];
        $data = $mailData['data'] ?? [];
        try {
            ini_set('memory_limit', '1024M');
            ini_set('max_execution_time', 180); // 3 minutes
            Mail::send($page, $data, function ($message) use ($mailData) {
                $message->from(($mailData['from'] = ! '' ? $mailData['from'] : '<EMAIL>'), $mailData['fromName']);
                $message->to($mailData['to']);
                $message->subject($mailData['subject']);
                $message->html($mailData['body']);
            });
            $result = '';
        } catch (\Exception  $exception) {
            $errorMessage = $exception->getMessage();

            return ['status' => false, 'message' => $errorMessage];
        }
        if ($result == '') {
            return ['status' => true, 'message' => 'Email sent successfully'];
        } else {
            return ['status' => false, 'message' => 'Email is not send successfully.'];
        }
    }
}
