import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref } from 'vue';
import apiClient from '@spa/services/api.client.js';
import useConfirm from '@spa/services/useConfirm';

export const useStudentAgentCommissionStore = defineStore('useStudentAgentCommissionStore', () => {
    const storeUrl = ref('v2/tenant/student-agent-commission');
    const commonStoreProps = useCommonStore(storeUrl.value);
    const confirm = useConfirm();

    const approveModalVisible = ref(false);
    const approveModalItem = ref(null);
    const approveModalLoading = ref(false);

    // Bulk approve modal state
    const bulkApproveModalVisible = ref(false);
    const bulkApproveModalItems = ref([]);
    const bulkApproveModalLoading = ref(false);

    const processModalVisible = ref(false);
    const processModalItem = ref(null);
    const processModalLoading = ref(false);

    const approveCommission = async (item, remarks = '') => {
        approveModalLoading.value = true;
        try {
            const response = await apiClient.post('/api/approve-agent-commission', {
                id: item.id,
                remarks: remarks,
                approveType: 'single',
            });
            if (response.status === 'success') {
                commonStoreProps.fetchPaged();
                closeApproveModal();
                return { success: true, message: response.message };
            } else {
                return { success: false, message: response.message };
            }
        } catch (error) {
            const errorMessage = error.response?.data?.message || 'Failed to approve commission';
            return { success: false, message: errorMessage };
        } finally {
            approveModalLoading.value = false;
        }
    };

    const confirmApprove = (item) => {
        approveModalItem.value = item;
        approveModalVisible.value = true;
    };

    const disapproveCommission = async (item) => {
        try {
            const response = await apiClient.post('/api/disapprove-agent-commission', {
                id: item.id,
            });

            if (response.status === 'success') {
                commonStoreProps.fetchPaged();
                return { success: true, message: response.message };
            } else {
                return { success: false, message: response.message };
            }
        } catch (error) {
            const errorMessage = error.response?.data?.message || 'Failed to disapprove commission';
            return { success: false, message: errorMessage };
        }
    };

    const confirmDisapprove = (item) => {
        confirm.require({
            message: 'Are you sure you want to disapprove this commission?',
            header: 'Disapprove Commission',
            icon: 'pi pi-exclamation-triangle',
            variant: 'danger',
            acceptLabel: 'Disapprove',
            rejectLabel: 'Cancel',
            width: 500,
            accept: async () => {
                await disapproveCommission(item);
            },
            reject: () => {
                // User cancelled - do nothing
            },
            onHide: () => {
                // Dialog closed - do nothing
            },
        });
    };

    const processCommission = async (item, paymentData) => {
        processModalLoading.value = true;
        try {
            const response = await apiClient.post('/api/process-agent-commission', {
                id: item.id,
                mode: paymentData.mode,
                commission_paid: paymentData.commission_paid,
                paid_date: paymentData.paid_date,
                remarks: paymentData.remarks,
                is_process: paymentData.is_process,
            });

            if (response.status === 'success') {
                commonStoreProps.fetchPaged();
                closeProcessModal();
                return { success: true, message: response.message };
            } else {
                return { success: false, message: response.message };
            }
        } catch (error) {
            const errorMessage =
                error.response?.data?.message || 'Failed to process commission payment';
            return { success: false, message: errorMessage };
        } finally {
            processModalLoading.value = false;
        }
    };

    const confirmProcess = (item) => {
        processModalItem.value = item;
        processModalVisible.value = true;
    };

    const closeProcessModal = () => {
        processModalVisible.value = false;
        processModalItem.value = null;
        processModalLoading.value = false;
    };

    const closeApproveModal = () => {
        approveModalVisible.value = false;
        approveModalItem.value = null;
        approveModalLoading.value = false;
    };

    // Bulk approve commission functionality
    const bulkApproveCommission = async (items, remarks = '') => {
        bulkApproveModalLoading.value = true;
        try {
            const response = await apiClient.post('/api/approve-agent-commission', {
                ids: items.map((item) => item.id),
                remarks: remarks,
                approveType: 'bulk',
            });
            if (response.status === 'success') {
                commonStoreProps.fetchPaged();
                closeBulkApproveModal();
                return { success: true, message: response.message, data: response.data };
            } else {
                return { success: false, message: response.message };
            }
        } catch (error) {
            const errorMessage = error.response?.data?.message || 'Failed to approve commissions';
            return { success: false, message: errorMessage };
        } finally {
            bulkApproveModalLoading.value = false;
        }
    };

    const confirmBulkApprove = (items) => {
        bulkApproveModalItems.value = items;
        bulkApproveModalVisible.value = true;
    };

    const closeBulkApproveModal = () => {
        bulkApproveModalVisible.value = false;
        bulkApproveModalItems.value = [];
        bulkApproveModalLoading.value = false;
    };

    return {
        ...commonStoreProps,
        approveCommission,
        confirmApprove,
        closeApproveModal,
        disapproveCommission,
        confirmDisapprove,
        processCommission,
        confirmProcess,
        closeProcessModal,

        // Bulk approve functions
        bulkApproveCommission,
        confirmBulkApprove,
        closeBulkApproveModal,

        // Modal state
        approveModalVisible,
        approveModalItem,
        approveModalLoading,
        processModalVisible,
        processModalItem,
        processModalLoading,

        // Bulk approve modal state
        bulkApproveModalVisible,
        bulkApproveModalItems,
        bulkApproveModalLoading,
    };
});
