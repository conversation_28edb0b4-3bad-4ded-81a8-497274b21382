<template>
    <div :class="rootClass">
        <div class="flex flex-col items-center justify-start space-y-4">
            <template v-if="$slots.icon || iconName">
                <slot name="icon"></slot>
            </template>
            <component :is="getComponent(iconName)" :class="iconClass" v-else />
            <template v-if="$slots.content">
                <slot name="content"></slot>
            </template>
            <div class="space-y-2" v-else>
                <div :class="titleClass" v-html="title"></div>
                <div :class="subtitleClass">
                    {{ subtitle }}
                </div>
            </div>
        </div>
        <slot name="button"></slot>
    </div>
</template>
<script>
import { SubTitle } from 'chart.js';
import { twMerge } from 'tailwind-merge';
import IconMaps from '@spa/plugins/icons-maps.js';

export default {
    props: {
        title: {
            type: String,
            default: 'No data found',
        },
        subtitle: {
            type: String,
            default: 'No data were found matching your search',
        },
        iconName: {
            type: String,
            default: '',
        },
        pt: {
            type: Object,
            default: {},
        },
    },
    computed: {
        rootClass() {
            return twMerge(
                'flex w-full flex-col items-center justify-center space-y-6 rounded-lg bg-gray-50 py-8',
                this.pt.root
            );
        },
        iconClass() {
            return twMerge('text-primary-blue-500 w-14 h-14', this.pt.icon);
        },
        titleClass() {
            return twMerge('text-center text-base font-medium text-gray-700', this.pt.title);
        },
        subtitleClass() {
            return twMerge('text-center text-sm text-gray-500', this.pt.subtitle);
        },
    },
    methods: {
        getComponent(icon) {
            return IconMaps[icon] || null;
        },
    },
};
</script>
<style lang=""></style>
