<template>
    <div class="relative w-full">
        <div class="absolute left-3 top-1/2 z-[1] -translate-y-1/2">
            <icon-search class="h-4 w-4 text-gray-400" />
        </div>
        <AutoComplete
            :style="inputStyle"
            class="pl-6"
            :data-items="studentsList"
            v-model="vModel"
            @input="triggerSearch"
            :loading="loading"
            @change="changeStudent"
            @select="selectStudent"
            :text-field="'full_name'"
            :data-item-key="'id'"
            :valueField="'id'"
            :item-render="'myTemplate'"
            :value-render="'valueRender'"
            :placeholder="'Search for student'"
            :footer="'myFooter'"
            :popup-settings="popupSettings"
        >
            <template v-slot:valueRender="{ props }">
                <span :class="props.class" :id="props.id">
                    <span class="truncate"> Value {{ props.value }} </span>
                </span>
            </template>
            <template v-slot:myTemplate="{ props }">
                <li :class="props.class" @click="(ev) => props.onClick(ev, props)">
                    <student-info
                        :student="props.dataItem"
                        :isLinkable="false"
                        :showid="true"
                        :pt="{ root: 'w-6 h-6', wrapper: 'gap-2' }"
                    />
                </li>
            </template>
            <template v-slot:myFooter="{ props }">
                <div v-if="hasMoreResults > 0" class="p-2 text-right">
                    ...and {{ hasMoreResults }} others
                </div>
            </template>
        </AutoComplete>
    </div>
</template>
<script>
import { ref, computed, watch } from 'vue';
import axios from 'axios';
import { AutoComplete } from '@progress/kendo-vue-dropdowns';
import StudentInfoDisplay from '@spa/components/StudentInfoDisplay.vue';
import { IconSearch24Regular } from '@iconify-prerendered/vue-fluent';
import { twMerge } from 'tailwind-merge';

export default {
    props: {
        mode: { type: String, default: '' },
        style: {
            type: Object,
            default: () => ({}),
        },
        student: { type: Object, default: () => null },
        modelValue: { type: [String, Number], default: null },
        pt: {
            type: Object,
            default: () => ({}),
        },
        valuePrimitive: {
            type: Boolean,
            default: true,
        },
    },
    components: {
        AutoComplete,
        'student-info': StudentInfoDisplay,
        'icon-search': IconSearch24Regular,
    },
    data() {
        return {
            cancelSource: axios.CancelToken.source(),
            loading: false,
            studentsList: [],
            totalStudentsList: 0,
            currentStudent: '',
            internalValue: '',
            opened: false,
            hasMoreResults: 0,
            popupSettings: {
                className: 'tw-fixed-width',
                animate: false,
                offset: { left: 150, top: 50 },
            },
        };
    },
    created() {
        // Initialize the debounced function with a delay of 200ms
        this.debouncedSearch = this.debounce(this.searchStudents, 200);
        // Set initial value from student prop
        this.internalValue = this.student?.full_name || '';
    },

    computed: {
        vModel: {
            get() {
                return this.internalValue;
            },
            set(value) {
                this.internalValue = value;
                this.$emit('update:modelValue', value);
            },
        },
        inputStyle() {
            return {
                ...this.style,
                paddingLeft: '1.5rem',
            };
        },
        rootClass() {
            return twMerge('relative w-full', this.pt.root);
        },
    },
    methods: {
        debounce(func, delay) {
            let timeoutId;
            return function (...args) {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    func.apply(this, args);
                }, delay);
            };
        },
        searchStudents(event) {
            const searchTerm = event.target.value;
            this.loading = true;
            this.totalStudentsList = this.hasMoreResults = 0;
            if (searchTerm && searchTerm.length >= 1) {
                $http
                    .post(this.route('spa.search.students'), {
                        search: searchTerm,
                    })
                    .then((resp) => {
                        this.loading = false;
                        if (resp['success']) {
                            this.studentsList = resp.data || [];
                            this.totalStudentsList = resp.total || 0;
                            this.hasMoreResults = resp.more || 0;
                        }
                    });
            } else {
                this.studentsList = [];
                this.opened = false;
                this.loading = false;
            }
        },
        triggerSearch(event) {
            this.debouncedSearch(event, 300);
        },
        changeStudent(e) {
            const selectedItem = e.item || null;
            if (selectedItem) {
                this.$emit('updated', selectedItem);
            }
            return;
        },

        selectStudent(e) {
            const selectedItem = e.item || null;
            if (selectedItem) {
                this.$emit('select', selectedItem);
            }
            return;
        },
    },
    watch: {
        student: {
            handler(newValue) {
                // Update internal value when student prop changes externally
                // If student is null or undefined, clear the input
                if (!newValue) {
                    this.internalValue = '';
                    this.currentStudent = '';
                } else {
                    this.internalValue = newValue.full_name || '';
                    this.currentStudent = newValue.full_name || '';
                }
            },
            deep: true,
            immediate: true,
        },
        modelValue: {
            handler(newValue) {
                // If modelValue (studentId) is null or empty, clear the input
                if (!newValue) {
                    this.internalValue = '';
                    this.currentStudent = '';
                }
            },
            immediate: true,
        },
    },
};
</script>
