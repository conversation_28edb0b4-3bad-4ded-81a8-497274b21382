<?php

namespace App\Exports\TCSI;

use App\Model\v2\Student;
use App\Model\v2\StudentCourses;
use App\Model\v2\TcsiStudentDisability;
use App\Traits\ExportDataTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB as FacadesDB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class TCSIStudent implements FromCollection, ShouldAutoSize, WithHeadings
{
    use ExportDataTrait;

    protected $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->mergeCells('A1:S1'); // Adjust ranges as needed
        $sheet->getStyle('A1:S1')->getFont()->setBold(true);
        // Other styling and merging as needed
    }

    public function headings(): array
    {
        return [
            '0' => 'Information Type',
            '1' => 'E313 Student Identification Code',
            '2' => 'E314 Date of Birth',
            '3' => 'E402 Student Family Name',
            '4' => 'E403 Student Given Name first',
            '5' => 'E404 Student Given Name others',
            '6' => 'E410 Residential Address Street',
            '7' => 'E469 Residential Address Suburb',
            '8' => 'E470 Residential Address State',
            '9' => 'E658 Residential Address Country Code',
            '10' => 'E320 Residential Address Postcode',
            '11' => 'E584 Unique Student Identifier',
            '12' => 'E315 Gender Code',
            '13' => 'E316 Aboriginal And Torres Strait Islander Code',
            '14' => 'E346 Country of Birth Code',
            '15' => 'E347 Year of Arrival In Australia',
            '16' => 'E348 Language Spoken At Home',
            '17' => 'E572 Year Left School',
            '18' => 'E612 Level Left School',
            '19' => 'E573 Highest Attainment Code Parent 1',
            '20' => 'E574 Highest Attainment Code Parent 2',
            '21' => 'E661 Term Address Country Code',
            '22' => 'E319 Term Address Postcode',
            '23' => 'E615 Disability Code',
            '24' => 'E609 Disability Effective From Date',
            '25' => 'E610 Disability Effective To Date',
            '26' => 'E358 Citizen Resident Code',
            '27' => 'E609 Citizenship Effective From Date',
            // '28' => 'E416 Tax File Number',
            // '29' => 'E488 CHESSN'
        ];
    }

    public function collection()
    {
        $collegeId = $this->request->college_id;
        $reportingYear = $this->request->reporting_year;
        $includeDeferred = filter_var($this->request->input('include_deferred', false), FILTER_VALIDATE_BOOLEAN);
        $includeCancelled = filter_var($this->request->input('include_cancelled', false), FILTER_VALIDATE_BOOLEAN);
        $includeSuspended = filter_var($this->request->input('include_suspended', false), FILTER_VALIDATE_BOOLEAN);
        // Backward compatibility: legacy combined flag
        $legacyIncludeBoth = filter_var($this->request->input('include_deferred_cancelled', false), FILTER_VALIDATE_BOOLEAN);
        if ($legacyIncludeBoth) {
            $includeDeferred = true;
            $includeCancelled = true;
        }

        $columnArr = [
            'rs.id',
            // DB::raw('(CASE WHEN (rs.id > 0) THEN "Student" END) as information_type'),
            'rs.generated_stud_id',
            'rs.DOB as dob',
            'rs.family_name',
            'rs.first_name',
            'rs.middel_name',
            'rs.student_type',
            FacadesDB::raw('CONCAT(rs.current_street_no," ", rs.current_street_name) AS current_street'),
            'rs.current_city',
            'rs.current_state',
            'rc1.absvalue as country_code',
            'rc1.absvalue as primary_country_code',
            'rs.current_postcode',
            'rs.current_postcode as primary_postcode',
            'rs.USI as usi',
            FacadesDB::raw('(CASE WHEN rs.gender = "Male" THEN "M" WHEN rs.gender = "Female" THEN "F" ELSE "O" END) AS gender_code'),
            'rsd.is_aus_aboriginal_or_islander as aboriginal_code',
            'rc2.absvalue as birth_country_code',
            'rstd.year_of_arrival_in_australia as year_of_arrival',
            FacadesDB::raw('(CASE WHEN rsd.is_english_main_lang = "1" THEN "0001" ELSE rsd.main_lang END) AS language_code1'),
            'rl.abs_value AS language_code',
            'rstd.level_left_school',
            'rstd.year_of_left_school',
            'rstd.attended_year_12',
            'rstd.education_of_parent1',
            'rstd.education_of_parent2',
            'rstd.citizenship_code',
            'rstd.citizenship_effective_from_date as citizenship_from',
            // 'rstd.tax_file_number',
            // 'rstd.chessn_number',
            // 'rsd.completed_school_year',
            // 'rsd.highest_completed_school_level as school_level',
            // 'rsd.area_of_disability',
            FacadesDB::raw('(CASE WHEN (rs.id > 0) THEN "NA" END) as not_clear_fields'),
        ];

        $conditions = [
            'rs.college_id' => $collegeId,
            'rs.is_student' => Student::ACTIVE_STUDENT,
        ];

        $validStatuses = [
            StudentCourses::STATUS_CURRENT,
            StudentCourses::STATUS_WITHDRAWN,
        ];

        // Include selected additional statuses
        if ($includeCancelled) {
            $validStatuses[] = StudentCourses::STATUS_CANCELLED;
        }
        if ($includeDeferred) {
            $validStatuses[] = StudentCourses::STATUS_DEFERRED;
        }
        if ($includeSuspended) {
            $validStatuses[] = StudentCourses::STATUS_SUSPENDED;
        }

        $resultData = Student::alias('rto_students as rs')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rs.id')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->leftjoin('rto_student_tcsi_details as rstd', 'rstd.student_id', '=', 'rs.id')
            ->leftjoin('rto_country as rc1', 'rc1.id', '=', 'rs.current_country')
            ->leftjoin('rto_country as rc2', 'rc2.id', '=', 'rs.birth_country')
            ->join('rto_language as rl', 'rsd.main_lang', '=', 'rl.id')
            ->where($conditions)
            ->whereIn('rsc.status', $validStatuses)
            // ->where(function ($query) use ($reportingYear) {
            //     $query->whereYear('rsc.start_date', '<=', $reportingYear)
            //         ->whereYear('rsc.start_date', '>=', $reportingYear);
            // })
            ->whereYear('rsc.start_date', $reportingYear)
            ->select($columnArr)
            ->groupBy('rs.id')
            ->get()
            ->toArray();

        $rows = [];

        foreach ($resultData as $res) {

            $columns1 = [];
            $columns2 = [];
            $columns3 = [];

            $columns1[] = [
                'information_type' => 'Student',
                'generated_stud_id' => $res['generated_stud_id'],
                'dob' => $res['dob'],
                'family_name' => $res['family_name'],
                'first_name' => $res['first_name'],
                'middel_name' => ($res['middel_name'] != '') ? $res['middel_name'] : '9999',
                'current_street' => $res['current_street'],
                'current_city' => $res['current_city'],
                'current_state' => $res['current_state'],
                'country_code' => $res['country_code'],
                'current_postcode' => $res['current_postcode'],
                'usi' => $res['usi'],
                'gender_code' => $res['gender_code'],
                'aboriginal_code' => $res['aboriginal_code'],
                'birth_country_code' => $res['birth_country_code'],
                'year_of_arrival' => $res['year_of_arrival'],
                'language_code' => $res['language_code'],
                'year_of_left_school' => $res['year_of_left_school'],
                'level_left_school' => $res['level_left_school'],
                'education_of_parent1' => $res['education_of_parent1'],
                'education_of_parent2' => $res['education_of_parent2'],
                'primary_country_code' => $res['primary_country_code'],
                'primary_postcode' => $res['primary_postcode'],
                'disability_code' => '',
                'disability_start' => '',
                'disability_end' => '',
                'citizenship_code' => '',
                'citizenship_from' => '',
                // "tax_file_number"       => '',
                // "chessn_number"         => '',
            ];

            if ($res['citizenship_code'] != '') {
                $columns2[] = [
                    'information_type' => 'Citizenship',
                    'generated_stud_id' => $res['generated_stud_id'],
                    'dob' => '',
                    'family_name' => '',
                    'first_name' => '',
                    'middel_name' => '',
                    'current_street' => '',
                    'current_city' => '',
                    'current_state' => '',
                    'country_code' => '',
                    'current_postcode' => '',
                    'usi' => '',
                    'gender_code' => '',
                    'aboriginal_code' => '',
                    'birth_country_code' => '',
                    'year_of_arrival' => '',
                    'language_code' => '',
                    'not_clear_fields' => '',
                    'level_left_school' => '',
                    'education_of_parent1' => '',
                    'education_of_parent2' => '',
                    'primary_country_code' => '',
                    'primary_postcode' => '',
                    'disability_code' => '',
                    'disability_start' => '',
                    'disability_end' => '',
                    'citizenship_code' => $res['citizenship_code'],
                    'citizenship_from' => $this->checkDate($res['citizenship_from']),
                    // "tax_file_number"       => '',
                    // "chessn_number"         => '',
                ];
            }

            $whereDisArr = [
                'college_id' => $collegeId,
                'student_id' => $res['id'],
            ];

            $disArr = TcsiStudentDisability::where($whereDisArr)->get()->toArray();

            if (! empty($disArr)) {
                $columns3[] = [
                    'information_type' => 'Disability',
                    'generated_stud_id' => $res['generated_stud_id'],
                    'dob' => '',
                    'family_name' => '',
                    'first_name' => '',
                    'middel_name' => '',
                    'current_street' => '',
                    'current_city' => '',
                    'current_state' => '',
                    'country_code' => '',
                    'current_postcode' => '',
                    'usi' => '',
                    'gender_code' => '',
                    'aboriginal_code' => '',
                    'birth_country_code' => '',
                    'year_of_arrival' => '',
                    'language_code' => '',
                    'not_clear_fields' => '',
                    'level_left_school' => '',
                    'education_of_parent1' => '',
                    'education_of_parent2' => '',
                    'primary_country_code' => '',
                    'primary_postcode' => '',
                    'disability_code' => $disArr[0]['disability_code'],
                    'disability_start' => $disArr[0]['start_date'],
                    'disability_end' => $disArr[0]['end_date'],
                    'citizenship_code' => '',
                    'citizenship_from' => '',
                    // "tax_file_number"       => '',
                    // "chessn_number"         => '',
                ];
            }

            $combinedRow = array_merge($columns1, $columns2, $columns3);

            $rows[] = $combinedRow;
        }

        return collect($rows);
    }
}
