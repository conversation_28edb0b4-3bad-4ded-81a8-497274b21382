<template>
    <div
        class="flex items-center gap-2 font-medium text-primary-blue-500"
        v-if="getCompletedPercentageValue != 100"
    >
        <!-- <Tooltip
            :content="'tooltipTemplate'"
            :anchor-element="'target'"
            :tooltipStyle="{ 'background-color': '#fff' }"
            :tooltipClassName="'tw-tooltip'"
            :showCallout="false"
            :position="'auto'"
            :openDelay="100"
        >
            <div
                :title="'title'"
                :class="'flex w-full cursor-pointer justify-end space-x-2 text-start text-red-500'"
            >
                <span class="mr-2"><icon :name="'info'" :fill="'#ef4444'" /></span>
                {{ getCompletedPercentage }}
            </div>
            <template #tooltipTemplate="{ props }">
                <div class="subjectUnits">
                    <div
                        v-for="(message, index) in getProgressStatusMessages"
                        :class="{
                            'border-b': index !== getProgressStatusMessages.length - 1,
                        }"
                        class="py-2 text-xs text-gray-500"
                        :key="index"
                    >
                        {{ message }}
                    </div>
                </div>
            </template>
        </Tooltip> -->
        <div
            :title="`Course is ${getCompletedPercentage}.`"
            :class="'flex w-full cursor-pointer justify-end space-x-2 text-start text-red-500'"
        >
            {{ getCompletedPercentage }}
        </div>
        <Popover :open="openInfo" @toggle="openInfo = !openInfo" @close="openInfo = false">
            <template #picker>
                <span><icon :name="'info'" :fill="'#ef4444'" /></span>
            </template>
            <template #popup>
                <ul
                    class="subjectUnits max-h-[600px] max-w-96 list-outside list-disc space-y-3 overflow-y-auto py-4 pe-4 ps-7"
                >
                    <li
                        v-for="(message, index) in getProgressStatusMessages"
                        class="text-sm leading-5 text-gray-600"
                        :key="index"
                    >
                        {{ message }}
                    </li>
                </ul>
            </template>
        </Popover>
    </div>
    <div class="px-2 font-medium text-primary-blue-500" v-else>
        {{ getCompletedPercentage }}
    </div>
</template>
<script>
import { Tooltip } from '@progress/kendo-vue-tooltip';
import Popover from '@spa/components/Popover/Popover.vue';

export default {
    props: {
        steps: { type: Object, default: {} },
    },
    data() {
        return {
            openInfo: false,
        };
    },
    components: {
        Tooltip,
        Popover,
    },
    computed: {
        getCompletedPercentageValue() {
            const completedSteps =
                this.steps?.order?.filter((step) => step.status || step.ignore == true).length || 0;
            let totalSteps = parseInt(this.steps?.tabs || 0);
            if (isNaN(totalSteps)) totalSteps = 0;
            if (totalSteps > 0) return (completedSteps / totalSteps) * 100;
            return 0;
        },
        getCompletedPercentage() {
            const completionPercentage = this.getCompletedPercentageValue;
            return `${completionPercentage.toFixed(2)}% Complete`;
        },
        getProgressStatusMessages() {
            const isCompleted = this.getCompletedPercentageValue;
            if (isCompleted != 100) {
                //get the error logs
                const fieldValues = this.steps?.order?.reduce((acc, item) => {
                    if (!item.ignore) {
                        if (Array.isArray(item.field)) {
                            return acc.concat(item.field);
                        } else {
                            acc.push(item.field);
                        }
                    }
                    return acc;
                }, []);
                return fieldValues;
            }
            return false;
        },
    },
};
</script>
