<?php

namespace App\Exports;

use App\Model\v2\CourseType;
use App\Model\v2\Student;
use App\Services\ScoutFilterService;
use App\Traits\ExportDataTrait;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Meilisearch\Endpoints\Indexes;

class ManageStudentScoutExport implements FromCollection, WithHeadings
{
    use ExportDataTrait;

    protected $request;

    protected $higherEdCourseTypes;

    protected $collegeHasHigherEd;

    /**
     * @return \Illuminate\Support\Collection
     */
    public function __construct(Request $request)
    {
        ini_set('memory_limit', '-1');
        $this->request = $request;
        $this->higherEdCourseTypes = $this->getHigherEdCourseTypes();
        $this->collegeHasHigherEd = ! empty($this->higherEdCourseTypes);
    }

    public $form = [
        'keyword' => '',
        'courses' => [],
        'student_type' => [],
        'status' => [],
        'origin' => [],
        'batches' => [],
        'teachers' => [],
        'intake_dates' => [],
        'nationality' => [],
        'course_type' => [],
        'sort_by' => '',
        'sort_direction' => 'asc',

    ];

    public function headings(): array
    {

        $data = [
            1 => 'Student ID',
            2 => 'Title',
            3 => 'First Name',
            4 => 'Last Name',
            5 => 'Email',
            6 => 'Date of Birth',
            7 => 'Gender',
            8 => 'Mobile',
            9 => 'Student Type',
            10 => 'Passport Number',
            11 => 'USI',
            12 => 'Recruitment Agent Name',
            13 => 'Agent ID',
            14 => 'Agent Code',
            15 => 'Agent Email',
            16 => 'Agent Phone Number',
            17 => 'Course Student is enrolled',
            18 => 'Australian Residential Address',
            19 => 'Emergency Contact Details',
            20 => 'Intake Name',
            21 => 'Course Start Date',
            22 => 'Course End Date',
        ];

        // Only add Course Status column if college has HigherEd courses
        if ($this->collegeHasHigherEd) {
            $data[23] = 'Course Status';
            $data[24] = 'Campus';
            $data[25] = 'Nationality';
            $data[26] = 'Galaxy Portal Login Status';
        } else {
            $data[23] = 'Campus';
            $data[24] = 'Nationality';
            $data[25] = 'Galaxy Portal Login Status';
        }

        if ($this->request->has('status') && count((array) $this->request->input('status')) === 1 && $this->collegeHasHigherEd) {
            $data[count($data) + 1] = 'Course Status';
        }

        return $data;
    }

    public function collection()
    {
        $request = $this->request;
        $this->form = [
            'keyword' => ! empty($request->input('searchText')) ? $request->input('searchText') : '',
            'courses' => ! empty($request->input('course_id')) ? $request->input('course_id') : [],
            'campuses' => ! empty($request->input('campus_id')) ? $request->input('campus_id') : [],
            'student_type' => ! empty($request->input('student_type')) ? $request->input('student_type') : [],
            'status' => ! empty($request->input('status')) ? $request->input('status') : [],
            'origin' => [],
            'batches' => ! empty($request->input('batch')) ? $request->input('batch') : [],
            'teachers' => ! empty($request->input('teacher')) ? $request->input('teacher') : [],
            'intake_dates' => ! empty($request->input('student_intake')) ? $request->input('student_intake') : [],
            'nationality' => ! empty($request->input('nationality')) ? $request->input('nationality') : [],
            'course_type' => ! empty($request->input('course_type')) ? $request->input('course_type') : [],
            'sort_by' => ! empty($request->input('sort')) ? $request->input('sort')[0]['field'] : 'courses.created_at',
            // 'sort_by'       => !empty($request->input('sort')) ? $request->input('sort')[0]['field'] : 'rto_students.created_at',
            'sort_direction' => ! empty($request->input('sort')) ? $request->input('sort')[0]['dir'] : 'desc',

        ];

        $searchKeyword = $this->form['keyword'];
        if (strlen($searchKeyword) < 3) {
            $searchKeyword = '';
        }

        $hasFilters = collect($this->form)
            ->except(['keyword', 'sort_by', 'sort_direction'])
            ->filter(fn ($val) => ! empty($val))
            ->isNotEmpty()
            || ! empty($searchKeyword);

        // Create the builder based on whether filters are applied
        if ($hasFilters) {
            $scoutBuilder = Student::search($this->form['keyword'], function (Indexes $searchEngine, string $query, array $options) {
                $options['matchingStrategy'] = 'all';
                $options['limit'] = 10000; // Increase the limit
                $options['offset'] = 0;    // Start from beginning

                return $searchEngine->search($query, $options);
            });
        } else {
            $scoutBuilder = Student::query();
        }

        // Apply filters
        if (count($this->form['courses'])) {
            $scoutBuilder->whereIn('courses.course.id', $this->form['courses']);
        }

        if (count($this->form['course_type'])) {
            $scoutBuilder->whereIn('courses.course.course_type_id	', $this->form['course_type']);
        }

        if (count($this->form['campuses'])) {
            $scoutBuilder->whereIn('courses.campus_id', $this->form['campuses']);
        }

        if (count($this->form['student_type'])) {
            $scoutBuilder->whereIn('student_type', $this->form['student_type']);
        }

        if (count($this->form['status'])) {
            $scoutBuilder->whereIn('courses.status', $this->form['status']);
        }

        if (count($this->form['nationality'])) {
            $scoutBuilder->whereIn('nationality', $this->form['nationality']);
        }

        if (count($this->form['teachers'])) {
            $scoutBuilder->whereIn('enrollments.teacher_id', $this->form['teachers']);
        }

        if (count($this->form['intake_dates'])) {
            $scoutBuilder->whereIn('courses.intake_date', $this->form['intake_dates']);
        }

        if (count($this->form['batches'])) {
            $scoutBuilder->whereIn('enrollments.batch', $this->form['batches']);
        }

        if (@$this->form['sort_by']) {
            $scoutBuilder->orderBy($this->form['sort_by'], $this->form['sort_direction']);
        }

        if ($hasFilters) {
            ScoutFilterService::FilterStudentForList($scoutBuilder, $this->form);
        }

        $scoutBuilder->where('is_student', 1);

        // FIXED: Handle the different builder types properly
        if ($hasFilters) {
            // For Scout builder, use ->query() method
            $results = $scoutBuilder->query(function (Builder $builder) {
                return $this->buildQuery($builder);
            })->take(10000)->get();
        } else {
            // For Eloquent builder, directly call the query building method
            $results = $this->buildQuery($scoutBuilder)->get();
        }

        $manageStudent = $results->map(function ($item) {

            $data = $item->toArray();
            if (count($this->form['status']) == 1) {
                $courseNames = collect($data['student_courses'])
                    ->filter(function ($studentCourses) {
                        return isset($studentCourses['status']) && $studentCourses['status'] == $this->form['status'][0];
                    })->pluck('course')->map(function ($course) {

                        return $course['course_code'].' - '.$course['course_name'];
                    })->unique()->implode('@@');
            } else {
                $courseNames = collect($data['student_courses'])
                    ->map(function ($studentCourses) {
                        $course = $studentCourses['course'];
                        $status = $studentCourses['status'] ?? '';

                        return $course['course_code'].' - '.$course['course_name'].' [ '.$status.' ]';
                    })
                    ->unique()
                    ->implode('@@');
            }

            $data['course_list'] = $courseNames;
            $data['currentCampus'] = $data['student_courses'][0]['currentCampus'];
            $data['CourseStartDate'] = $data['student_courses'][0]['start_date'] ?? '';
            $data['CourseEndDate'] = $data['student_courses'][0]['finish_date'] ?? '';
            $data['intake_date'] = $data['student_courses'][0]['intake_date'] ?? '';

            // Only show courseStatus for HigherEd course types if college has HigherEd courses
            if ($this->collegeHasHigherEd) {
                $courseTypeId = $data['student_courses'][0]['course_type_id'] ?? null;
                $isHigherEd = $courseTypeId && in_array($courseTypeId, $this->higherEdCourseTypes);
                $data['courseStatus'] = $isHigherEd ? $data['student_courses'][0]['status'] : '';
            } else {
                $data['courseStatus'] = null; // Don't include courseStatus for non-HigherEd colleges
            }

            return $data;
        });

        if (count($manageStudent) > 0) {
            foreach ($manageStudent as $key => $student) {

                $columnData = [
                    // 0 => $student['id'],
                    1 => $student['generated_stud_id'],
                    2 => $student['name_title'],
                    3 => $student['first_name'],
                    4 => $student['family_name'],
                    5 => $student['email'],
                    6 => $this->checkDate($student['DOB'], 'd-m-Y', ''),
                    7 => $student['gender'],
                    8 => $student['current_mobile_phone'],
                    9 => $student['student_type'],
                    10 => $student['passport_no'],
                    11 => $student['USI'],
                    12 => $student['agency_name'],
                    13 => $student['agency_id'],
                    14 => $student['agency_code'],
                    15 => $student['agency_email'],
                    16 => $student['agency_contact'],
                    17 => $student['course_list'],
                    18 => $student['address'],
                    19 => $student['emergency_phone'],
                    20 => $this->checkDate($student['intake_date'], 'd-m-Y', ''),
                    21 => $this->checkDate($student['CourseStartDate'], 'd-m-Y', ''),
                    22 => $this->checkDate($student['CourseEndDate'], 'd-m-Y', ''),
                ];

                // Add columns based on whether college has HigherEd courses
                if ($this->collegeHasHigherEd) {
                    $columnData[23] = $student['courseStatus'];
                    $columnData[24] = $student['currentCampus'];
                    $columnData[25] = $student['nationality'];
                    $columnData[26] = $student['galaxyStatus'];
                } else {
                    $columnData[23] = $student['currentCampus'];
                    $columnData[24] = $student['nationality'];
                    $columnData[25] = $student['galaxyStatus'];
                }

                $columns[] = $columnData;
                if (! empty($this->form['status'] && count($this->form['status']) == 1) && $this->collegeHasHigherEd) {
                    // Only add courseStatus column if it's a HigherEd course and has a value
                    if (! empty($student['courseStatus'])) {
                        $nextIndex = count($columnData) + 1;
                        $columns[$key][$nextIndex] = $student['courseStatus']; // Appends as the next available index
                    }
                }
            }
        }

        return collect($columns);
    }

    // Get all HigherEd course type IDs for current college once to avoid repeated database calls
    private function getHigherEdCourseTypes()
    {
        $arrSpecialCourseTypeName = Config::get('constants.arrSpecialCourseTypeName');
        $collegeId = Auth::user()->college_id;

        return CourseType::whereIn('title', $arrSpecialCourseTypeName)
            ->where('status', 1)
            ->whereIn('college_id', [0, $collegeId]) // Include global (0) and college-specific course types
            ->pluck('id')
            ->toArray();
    }

    // Extract the query building logic into a separate method
    private function buildQuery(Builder $builder)
    {
        return $builder
            ->select(
                'rto_students.id',
                'rto_students.generated_stud_id',
                'rto_students.name_title',
                'rto_students.first_name',
                'rto_students.family_name',
                'rto_students.email',
                'rto_students.DOB',
                'rto_students.gender',
                'rto_students.current_mobile_phone',
                'rto_students.student_type',
                'rto_students.passport_no',
                'rto_students.USI',
                'nationalityCountry.nationality',
                'ra.agency_name',
                'ra.id as agency_id',
                'ra.agent_code as agency_code',
                'ra.primary_email as agency_email',
                DB::raw(
                    "CONCAT_WS('/',
                            NULLIF(ra.telephone, ''),
                            NULLIF(ra.mobile1, ''),
                            NULLIF(ra.mobile2, '')
                        ) as agency_contact"
                ),
                DB::raw("CASE WHEN ru.last_login IS NOT NULL THEN 'Active' ELSE 'Inactive' END as galaxyStatus"),
                'rsd.emergency_phone',
                DB::raw(
                    "CONCAT_WS(', ',
                            NULLIF(rto_students.current_street_no, ''),
                            NULLIF(rto_students.current_street_name, ''),
                            NULLIF(rto_students.current_city, ''),
                            NULLIF(rto_students.current_state, ''),
                            NULLIF(rto_students.current_postcode, ''),
                            NULLIF(country.name, '')
                        ) as address"
                ),
                'courses.created_at'
            )
            ->leftjoin('rto_users as ru', 'ru.username', '=', 'rto_students.generated_stud_id')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_courses as courses', 'courses.id', '=', 'rsc.course_id')
            ->leftjoin('rto_country as nationalityCountry', 'rto_students.nationality', '=', 'nationalityCountry.id')
            ->leftjoin('rto_campus as campus', 'campus.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_country as country', 'country.id', '=', 'rto_students.current_country')
            ->groupBy('rto_students.id')
            ->with([
                'studentCourses' => function ($q) {
                    $courses = ! empty($this->form['courses']) ? $this->form['courses'] : null;
                    $statusFilters = ! empty($this->form['status']) ? $this->form['status'] : null;
                    $prioritySortQuery = generateOrderByCondition('rto_student_courses.course_id', $courses);
                    $sortDataQuery = generateStatusFilterConditions($statusFilters);
                    $q = $q
                        ->leftjoin('rto_campus as campus', 'campus.id', '=', 'rto_student_courses.campus_id')
                        ->with(['course'])
                        ->select(
                            'rto_student_courses.id',
                            'rto_student_courses.student_id',
                            'rto_student_courses.course_id',
                            'rto_student_courses.course_type_id',
                            'rto_student_courses.status',
                            'rto_student_courses.start_date',
                            'rto_student_courses.finish_date',
                            'rto_student_courses.intake_date',
                            'campus.name as currentCampus',
                            DB::raw('('.$sortDataQuery.') as ordertext'),
                        );
                    if ($prioritySortQuery) {
                        $q->orderBy(DB::raw($prioritySortQuery));
                    }
                    $q->orderBy(DB::raw('('.$sortDataQuery.')'), 'ASC');

                    return $q;
                },
            ]);
    }
}
