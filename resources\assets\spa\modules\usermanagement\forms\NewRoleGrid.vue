<template>
    <div class="tw-permission-grid w-full space-y-4 pr-4">
        <div class="relative w-full">
            <span class="absolute left-3 top-1/2 -translate-y-1/2">
                <icon :name="'lens'" width="16" height="16" />
            </span>
            <input
                type="text"
                id="searchKeyword"
                ref="searchKeyword"
                v-model.lazy="searchkeyword"
                v-debounce="300"
                class="tw-input-text h-[2.32rem] pl-8"
                placeholder="Search"
                autocomplete="off"
                @focus="searchingByKeyword = true"
                @blur="searchingByKeyword = false"
            />
        </div>
        <div v-if="Object.keys(getPermissions).length > 0">
            <div
                class="w-full rounded-lg border border-gray-200 bg-white shadow-sm"
                v-for="(permissionsList, moduleName, index) in getPermissions"
            >
                <div
                    class="flex flex-wrap rounded-t-lg border-b border-gray-200 bg-gray-200 p-2 text-center text-sm font-medium text-gray-500"
                    :class="{ 'border-b-0': !openModules[moduleName] }"
                >
                    <div class="flex w-full justify-between space-x-4">
                        <div class="flex space-x-2">
                            <span
                                :class="openModules[moduleName] ? 'rotate-180' : ''"
                                class="transition-transform"
                                @click="openModules[moduleName] = !openModules[moduleName]"
                            >
                                <icon name="arrow-down" />
                            </span>
                            <div class="student">{{ moduleName }}</div>
                        </div>
                        <div class="flex space-x-4" v-if="permissionsList.length > 1">
                            <div
                                class="cursor-pointer text-primary-blue-500"
                                @click="handleGroupPermission(roles, permissionsList, true)"
                            >
                                Select All
                            </div>
                            <div
                                class="cursor-pointer text-primary-blue-500"
                                @click="handleGroupPermission(roles, permissionsList, false)"
                            >
                                Deselect All
                            </div>
                        </div>
                    </div>
                </div>
                <div class="rounded-lg bg-white p-0" :hidden="!openModules[moduleName]">
                    <table class="relative w-full bg-white">
                        <template
                            v-for="permission in permissionsList"
                            :key="permission.id"
                            v-show="!openModules[moduleName]"
                        >
                            <tr class="h-12">
                                <td class="sticky left-0 z-10 border-b bg-white p-2 text-sm">
                                    <div class="flex space-x-2" v-if="roles.group_permission">
                                        <input
                                            type="checkbox"
                                            v-model="roles.group_permission[permission.name].v"
                                            @change="
                                                handlePermissionChange($event, permission, roles)
                                            "
                                            class="k-checkbox k-checkbox-lg tw-checkbox"
                                        />
                                        <div>
                                            {{ permission.label }}
                                        </div>
                                    </div>
                                </td>
                                <!-- <td
                                        v-for="role in roles.slice(1).filter((role) => role.visible)"
                                        :key="role.field"
                                        class="border-b p-2 text-center"
                                    >
                                        <input
                                            type="checkbox"
                                            v-model="role.group_permission[permission.name].v"
                                            @change="handlePermissionChange($event, permission, role)"
                                            class="k-checkbox k-checkbox-lg tw-checkbox tw-checkbox__default-cross"
                                        />
                                    </td> -->
                            </tr>
                        </template>
                    </table>
                </div>
            </div>
        </div>
        <NoData :message="getNoDataFoundMessage" v-else>
            <template #action>
                <!-- <PrimaryButton @click="openAddCourseModal">
                    <icon :name="'add'" :width="16" :height="16" :fill="'#FFF'" />
                    <div class="ml-2 capitalize">Add Permissions</div>
                </PrimaryButton> -->
            </template>
        </NoData>
    </div>
</template>
<script>
import { mapState } from 'pinia';
import { usersStore } from '@spa/stores/modules/userManagement';
import NoData from '@spa/modules/usermanagement/NoData.vue';
export default {
    props: {
        save: { type: String, default: 'onchange' },
        openstate: { type: Boolean, default: true },
        newrolename: { type: String, default: '[Role_Name]' },
    },
    components: {
        NoData,
    },
    data() {
        return {
            openModules: {},
            newRole: [],
            highlightChanges: true,
            searchkeyword: '',
        };
    },
    mounted() {
        const permissionsModules = Object.keys(this.permissions);
        if (!this.openstate) {
            const firstModule = permissionsModules[0];
            if (firstModule) this.openModules[firstModule] = true;
        } else if (this.openstate) {
            permissionsModules.forEach((item) => {
                this.openModules[item] = true;
            });
        }
        this.newRole = this.getDummyRole || [];
    },
    computed: {
        ...mapState(usersStore, [
            'getUserRoles',
            'getSelectedRoles',
            'getDummyRole',
            'getEditableRoles',
            'permissions',
            'updatePermissionsForRole',
            'trackPermissionsForRole',
        ]),
        getPermissions() {
            if (this.searchkeyword != '') {
                const result = {};
                for (const moduleName in this.permissions) {
                    const permissions = this.permissions[moduleName];
                    const matched = permissions.filter((permission) =>
                        permission.label.toLowerCase().includes(this.searchkeyword.toLowerCase())
                    );
                    if (matched.length > 0) {
                        result[moduleName] = matched;
                    }
                }
                return result;
            } else {
                return this.permissions;
            }
        },
        isInFormMode() {
            return this.mode == 'edit';
        },
        roles() {
            return this.newRole[1] || [];
        },
        defaultOpen() {
            return this.openstate == true && this.firstLoad == true;
        },
        hasUpdatedPermissions() {
            let changedPermissions = 0;
            for (const role of this.getUserRoles) {
                if (!role.visible || !role.group_permission) continue;
                for (const key in role.group_permission) {
                    if (role.group_permission[key].o !== role.group_permission[key].v) {
                        changedPermissions++; // Permission changed
                    }
                }
            }
            return changedPermissions; // No permission changes found
        },
        getNoDataFoundMessage() {
            if (this.searchkeyword != '') {
                return 'There are no permissions set for this user group. Use different search term.';
            } else {
                return 'There are no permissions set for this user group. Please contact your Administrator.';
            }
        },
    },
    methods: {
        handleGroupPermission(role, permissionsList, checked) {
            if (!role.group_permission) return;
            permissionsList.forEach(function (permission) {
                role.group_permission[permission.name].v = checked || false;
            });
            if (this.save == 'onchange') {
                this.updatePermissionsForRole(role, permissionsList, checked);
            } else {
                const permissionsList = this.newRole[1]?.group_permission || {};
                this.$emit('permissionchanged', permissionsList);
            }
        },
        isAllPermissionsGiven(role, permissionsList) {
            const permissionNames = permissionsList.map((permission) => permission.name);
            return (
                Object.entries(role.group_permission).filter(
                    ([key, value]) => permissionNames.includes(key) && value.v === false
                ).length == 0
            );
        },
        hasChangedPermissions(role, permissionsList) {
            const permissionNames = permissionsList.map((permission) => permission.name);
            return Object.entries(role.group_permission).some(([key, value]) => {
                return permissionNames.includes(key) && value?.c === true;
            });
        },
        handlePermissionChange(e, permission, role) {
            role.group_permission[permission.name].c =
                role.group_permission[permission.name].v !=
                role.group_permission[permission.name].o;
            if (this.save == 'onchange') {
                role.group_permission[permission.name].c =
                    role.group_permission[permission.name].v !=
                    role.group_permission[permission.name].o;
                //this.updatePermissionsForRole(role, permission, e.target.checked);
            } else {
                const permissionsList = this.newRole[1]?.group_permission || {};
                this.$emit('permissionchanged', permissionsList);
            }
        },
        getRoleTitle(index, roletitle) {
            if (index > 0 && this.mode == 'edit') {
                return this.newrolename || 'ROLE_NAME';
            }
            return roletitle;
        },
    },
};
</script>
<style lang=""></style>
