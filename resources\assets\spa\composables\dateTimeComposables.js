import dayjs from 'dayjs';

export const convertJsDateTimeFormat = (dateString, format = 'DD-MM-YYYY hh:mm A') => {
    return dayjs(dateString).format(format);
};

export const convertJsDateFormat = (dateString, format = 'DD-MM-YYYY') => {
    return dayjs(dateString).format(format);
};

export const convertJsTimeFormat = (dateString, format = 'hh:mm A') => {
    return dayjs(dateString).format(format);
};

export const convertJsTimeFormat24 = (dateString, format = 'HH:mm') => {
    return dayjs(dateString).format(format);
};

export const convertJsTimeFormat12 = (dateString, format = 'hh:mm A') => {
    return dayjs(dateString).format(format);
};

export const convertJsTimeFormat12NoSec = (dateString, format = 'hh:mm A') => {
    return dayjs(dateString).format(format);
};

export const convertJsTimeFormat24NoSec = (dateString, format = 'HH:mm') => {
    return dayjs(dateString).format(format);
};

export const convertJsTimeFormat12NoSecNoSpace = (dateString, format = 'hh:mmA') => {
    return dayjs(dateString).format(format);
};
