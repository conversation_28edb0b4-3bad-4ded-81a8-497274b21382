<template>
    <form-element>
        <fieldset class="k-form-fieldset space-y-6">
            <div class="space-y-6 pb-8">
                <div v-if="selectedCampuses.length > 0">
                    <div class="mb-2 grid grid-cols-1 gap-x-6 gap-y-1">
                        <field
                            :id="'intake_name'"
                            :name="'intake_name'"
                            :component="'myTemplate'"
                            :label="'Intake Name'"
                            :placeholder="'Enter Intake Name'"
                            :validator="requiredtrue"
                            :indicaterequired="true"
                        >
                            <template v-slot:myTemplate="{ props }">
                                <forminput
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                    <div>
                        <div class="mb-4 font-medium text-gray-700">
                            Select Campues In Which This Intake Is Applied
                        </div>
                        <div class="mb-2 grid grid-cols-3 gap-x-6 gap-y-2">
                            <div
                                class="course-checkbox flex justify-start"
                                v-for="(campus, index) in selectedCampuses"
                                :key="index"
                            >
                                <field
                                    :id="'campus_' + campus.id"
                                    :name="'campus_' + campus.id"
                                    :component="'myTemplate'"
                                    :label="campus.text"
                                    :validator="requiredCampus"
                                    v-model="campus.selected"
                                    :label-placement="'after'"
                                >
                                    <template v-slot:myTemplate="{ props }">
                                        <checkbox
                                            v-bind="props"
                                            @change="props.onChange"
                                            @blur="props.onBlur"
                                            @focus="props.onFocus"
                                        />
                                    </template>
                                </field>
                            </div>
                        </div>
                    </div>
                    <div class="rounded-md border border-gray-200 p-4">
                        <div class="mb-2 space-y-2">
                            <div class="font-semibold capitalize text-gray-700">
                                Provide class timings for this intake
                            </div>
                            <div>
                                Provide the date and time the class will be taking place on. Give
                                timing for one single class (Particularly the first instance of the
                                class).
                            </div>
                        </div>
                        <div class="mb-2 grid grid-cols-2 gap-x-6 gap-y-1">
                            <field
                                :id="'class_start_date'"
                                :name="'class_start_date'"
                                :component="'classStartDateTemplate'"
                                :label="'One Particular Class Will Start On'"
                                :placeholder="'Enter Class Start Date'"
                                :validator="requiredDate"
                                :indicaterequired="true"
                                @blur="setClassEndDate"
                            >
                                <template v-slot:classStartDateTemplate="{ props }">
                                    <formdatepicker
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                            <field
                                :id="'class_end_date'"
                                :name="'class_end_date'"
                                :component="'classEndDateTemplate'"
                                :label="'That Particular Class Ends On'"
                                :placeholder="'Enter Class End Date'"
                                :validator="checkClassEndDate"
                                v-if="showEndDate"
                                :indicaterequired="true"
                            >
                                <template v-slot:classEndDateTemplate="{ props }">
                                    <formdatepicker
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                        </div>
                        <div class="mb-2 grid grid-cols-2 gap-x-6 gap-y-1">
                            <div class="mb-2 grid grid-cols-2 gap-x-6 gap-y-1">
                                <field
                                    :id="'class_start_hour'"
                                    :name="'class_start_hour'"
                                    :component="'classStartHrTemplate'"
                                    :label="'Class Start Hour'"
                                    :placeholder="'Enter Class Start Hour'"
                                    :text-field="'name'"
                                    :data-item-key="'id'"
                                    :valueField="'id'"
                                    :valuePrimitive="true"
                                    :dataItems="hours"
                                    :validator="requiredtrue"
                                    :indicaterequired="true"
                                >
                                    <template v-slot:classStartHrTemplate="{ props }">
                                        <formdropdown
                                            v-bind="props"
                                            @change="props.onChange"
                                            @blur="props.onBlur"
                                            @focus="props.onFocus"
                                        />
                                    </template>
                                </field>
                                <field
                                    :id="'class_start_minute'"
                                    :name="'class_start_minute'"
                                    :component="'classStartMinTemplate'"
                                    :label="'Class Start Minute'"
                                    :placeholder="'Enter Class Start Minute'"
                                    :text-field="'name'"
                                    :data-item-key="'id'"
                                    :valueField="'id'"
                                    :valuePrimitive="true"
                                    :dataItems="minutes"
                                    :indicaterequired="true"
                                >
                                    <template v-slot:classStartMinTemplate="{ props }">
                                        <formdropdown
                                            v-bind="props"
                                            @change="props.onChange"
                                            @blur="props.onBlur"
                                            @focus="props.onFocus"
                                        />
                                    </template>
                                </field>
                            </div>
                            <div class="mb-2 grid grid-cols-2 gap-x-6 gap-y-1">
                                <field
                                    :id="'class_end_hour'"
                                    :name="'class_end_hour'"
                                    :component="'classEndHourTemplate'"
                                    :label="'Class End Hour'"
                                    :placeholder="'Enter Class End Hour'"
                                    :text-field="'name'"
                                    :data-item-key="'id'"
                                    :valueField="'id'"
                                    :valuePrimitive="true"
                                    :dataItems="hours"
                                    :validator="requiredtrue"
                                    :indicaterequired="true"
                                >
                                    <template v-slot:classEndHourTemplate="{ props }">
                                        <formdropdown
                                            v-bind="props"
                                            @change="props.onChange"
                                            @blur="props.onBlur"
                                            @focus="props.onFocus"
                                        />
                                    </template>
                                </field>
                                <field
                                    :id="'class_end_minute'"
                                    :name="'class_end_minute'"
                                    :component="'classEndMinTemplate'"
                                    :label="'Class End Minute'"
                                    :placeholder="'Enter Class End Minute'"
                                    :text-field="'name'"
                                    :data-item-key="'id'"
                                    :valueField="'id'"
                                    :valuePrimitive="true"
                                    :dataItems="minutes"
                                    :indicaterequired="true"
                                >
                                    <template v-slot:classEndMinTemplate="{ props }">
                                        <formdropdown
                                            v-bind="props"
                                            @change="props.onChange"
                                            @blur="props.onBlur"
                                            @focus="props.onFocus"
                                        />
                                    </template>
                                </field>
                            </div>
                        </div>
                    </div>
                    <div class="rounded-md border border-gray-200 p-4">
                        <div class="mb-2 space-y-2">
                            <div class="font-semibold capitalize text-gray-700">
                                Provide details for the intake
                            </div>
                            <div class="font-semibold italic text-gray-700">
                                Intake is a date frame in which the enrolment will be open for the
                                students to apply to this course using this intake.
                            </div>
                            <div>
                                So, Intake date frame must be before the class start time and should
                                not overlap with the class start time.
                                <span class="font-md italic text-orange-400"
                                    >If the intake time overlaps the class start time, the system
                                    might accept the applications even after the course has already
                                    started.</span
                                >
                            </div>
                        </div>
                        <div class="mb-2 grid grid-cols-2 gap-x-6 gap-y-1">
                            <field
                                :id="'intake_start'"
                                :name="'intake_start'"
                                :component="'intakeStartTemplate'"
                                :label="'Intake Start'"
                                :placeholder="'Enter Intake Start'"
                                :validator="requiredDate"
                                :indicaterequired="true"
                                :hint="'Student can start to apply to this course from this date.'"
                            >
                                <template v-slot:intakeStartTemplate="{ props }">
                                    <formdatepicker
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                            <field
                                :id="'intake_end'"
                                :name="'intake_end'"
                                :component="'intakeEndTemplate'"
                                :label="'Intake End'"
                                :placeholder="'Enter Intake End'"
                                :validator="checkIntakeEnd"
                                :indicaterequired="true"
                                :hint="'No more applications will be acceted after mid-night on this date.'"
                            >
                                <template v-slot:intakeEndTemplate="{ props }">
                                    <formdatepicker
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                        </div>
                        <div class="mb-2 grid grid-cols-2 gap-x-6 gap-y-1">
                            <field
                                :id="'intake_receiver'"
                                :name="'intake_receiver'"
                                :label="'Intake Receiver'"
                                :component="'courseGeneral'"
                                :text-field="'text'"
                                :data-item-key="'id'"
                                :valueField="'id'"
                                :valuePrimitive="true"
                                :default-item="defaultTarget"
                                :data-items="deliveryTargetData"
                                :validator="requiredtrue"
                                :indicaterequired="true"
                                :hint="'Type of student this intake can accept application from.'"
                            >
                                <template v-slot:courseGeneral="{ props }">
                                    <formdropdown
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                            <field
                                :id="'intake_seats'"
                                :name="'intake_seats'"
                                :component="'myTemplate'"
                                :label="'Intake Seats'"
                                :placeholder="'Enter Intake Seats'"
                                :validator="requiredtrue"
                                :indicaterequired="true"
                                :step="1"
                                :spinners="1"
                                :min="0"
                                max="500"
                                :hint="'Number of students an intake can enrol.'"
                            >
                                <template v-slot:myTemplate="{ props }">
                                    <numerictextbox
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                            <template v-if="!isHigherEd">
                                <field
                                    :id="'intake_delivery_mode'"
                                    :name="'intake_delivery_mode'"
                                    :label="'Enter Delivery Mode'"
                                    :component="'courseDeliveryMode'"
                                    :text-field="'text'"
                                    :data-item-key="'id'"
                                    :valueField="'id'"
                                    :valuePrimitive="true"
                                    :default-item="defaultDeliveryMode"
                                    :data-items="courseDeliveryModes"
                                    :validator="required"
                                    :disabled="isDeliveryModeDisabled"
                                >
                                    <template v-slot:courseDeliveryMode="{ props }">
                                        <formdropdown
                                            v-bind="props"
                                            @change="props.onChange"
                                            @blur="props.onBlur"
                                            @focus="props.onFocus"
                                        />
                                    </template>
                                </field>
                            </template>
                            <field
                                :id="'restrict_enrollments_to_seats'"
                                :name="'restrict_enrollments_to_seats'"
                                :component="'switchTemplate'"
                                :label="'Is Seat Fixed'"
                                :hint="'Turn this on if this intake will have fixed number of students to enrol.'"
                            >
                                <template v-slot:switchTemplate="{ props }">
                                    <formswitch
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                        </div>
                    </div>
                    <div class="rounded-md border border-gray-200 p-4">
                        <div class="mb-2 space-y-2">
                            <div class="font-semibold text-gray-700">Fees Section</div>
                        </div>
                        <div class="grid grid-cols-2 gap-x-6 gap-y-4" v-if="isShortCourse">
                            <div v-if="showFaceToFaceFee">
                                <field
                                    :id="'facetoface_fee'"
                                    :name="'facetoface_fee'"
                                    :label="'Tuition Fee For Face-To-Face Delivery Mode'"
                                    :component="'hoursFees'"
                                    :step="counterStep"
                                    :spinners="showSpinnersInNumbers"
                                    :placeholder="'Tuition Fee For Face-To-Face Delivery Mode'"
                                    :validator="checkFaceToFaceFeeRequired"
                                    :format="'c2'"
                                    :disabled="isFaceToFaceFeesDisabled"
                                >
                                    <template v-slot:hoursFees="{ props }">
                                        <numerictextbox
                                            v-bind="props"
                                            @change="props.onChange"
                                            @blur="props.onBlur"
                                            @focus="props.onFocus"
                                        />
                                    </template>
                                </field>
                            </div>
                            <div v-if="showOnlineFee">
                                <field
                                    :id="'online_fee'"
                                    :name="'online_fee'"
                                    :label="'Tuition Fee For Online Delivery Mode'"
                                    :component="'hoursFees'"
                                    :step="counterStep"
                                    :spinners="showSpinnersInNumbers"
                                    :placeholder="'Tuition Fee For Online Delivery Mode'"
                                    :validator="checkOnlineFeeRequired"
                                    :format="'c2'"
                                    :disabled="isOnlineFeesDisabled"
                                >
                                    <template v-slot:hoursFees="{ props }">
                                        <numerictextbox
                                            v-bind="props"
                                            @change="props.onChange"
                                            @blur="props.onBlur"
                                            @focus="props.onFocus"
                                        />
                                    </template>
                                </field>
                            </div>
                            <div v-if="showBlendedFee">
                                <field
                                    :id="'course_fee'"
                                    :name="'course_fee'"
                                    :label="'Blended Tuition Fee'"
                                    :component="'hoursFees'"
                                    :step="5"
                                    :spinners="1"
                                    :placeholder="'Enter International Tuition Fee'"
                                    :validator="checkBlendedFeeRequired"
                                    :format="'c2'"
                                    :min="0"
                                    :indicaterequired="true"
                                    :disabled="isBlendedFeesDisabled"
                                >
                                    <template v-slot:hoursFees="{ props }">
                                        <numerictextbox
                                            v-bind="props"
                                            @change="props.onChange"
                                            @blur="props.onBlur"
                                            @focus="props.onFocus"
                                        />
                                    </template>
                                </field>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-x-6 gap-y-4" v-else>
                            <div v-if="showInternationalFee">
                                <field
                                    :id="'course_fee'"
                                    :name="'course_fee'"
                                    :label="'International Tuition Fee'"
                                    :component="'hoursFees'"
                                    :step="5"
                                    :spinners="1"
                                    :placeholder="'Enter International Tuition Fee'"
                                    :validator="checkInternationalFeeRequired"
                                    :format="'c2'"
                                    :min="0"
                                    :indicaterequired="true"
                                    :disabled="isInternationalFeesDisabled"
                                >
                                    <template v-slot:hoursFees="{ props }">
                                        <numerictextbox
                                            v-bind="props"
                                            @change="props.onChange"
                                            @blur="props.onBlur"
                                            @focus="props.onFocus"
                                        />
                                    </template>
                                </field>
                            </div>
                            <div v-if="showDomesticFee">
                                <field
                                    :id="'course_domestic_fee'"
                                    :name="'course_domestic_fee'"
                                    :label="'Domestic Tuition Fee'"
                                    :component="'hoursFees'"
                                    :step="5"
                                    :spinners="1"
                                    :placeholder="'Enter Domestic Tuition Fee'"
                                    :validator="checkDomesticFeeRequired"
                                    :format="'c2'"
                                    :min="0"
                                    :indicaterequired="true"
                                    :disabled="isDomesticFeesDisabled"
                                >
                                    <template v-slot:hoursFees="{ props }">
                                        <numerictextbox
                                            v-bind="props"
                                            @change="props.onChange"
                                            @blur="props.onBlur"
                                            @focus="props.onFocus"
                                        />
                                    </template>
                                </field>
                            </div>
                            <div v-if="showOffshoreFee">
                                <field
                                    :id="'international_offshore_fee'"
                                    :name="'international_offshore_fee'"
                                    :label="'International Offshore Tuition Fee'"
                                    :component="'hoursFees'"
                                    :step="counterStep"
                                    :spinners="showSpinnersInNumbers"
                                    :placeholder="'Enter Tuition Fee For International Offshore Students'"
                                    :validator="checkOffshoreFeeRequired"
                                    :format="'c2'"
                                    :disabled="isInternationalOffShoreDisabled"
                                >
                                    <template v-slot:hoursFees="{ props }">
                                        <numerictextbox
                                            v-bind="props"
                                            @change="props.onChange"
                                            @blur="props.onBlur"
                                            @focus="props.onFocus"
                                        />
                                    </template>
                                </field>
                            </div>
                            <div v-if="showOnshoreFee">
                                <field
                                    :id="'international_onshore_fee'"
                                    :name="'international_onshore_fee'"
                                    :label="'International Onshore Tuition Fee'"
                                    :component="'hoursFees'"
                                    :step="counterStep"
                                    :spinners="showSpinnersInNumbers"
                                    :placeholder="'Enter Tuition Fee For International Onshore Students'"
                                    :validator="checkOnshoreFeeRequired"
                                    :format="'c2'"
                                    :disabled="isInternationalOnShoreDisabled"
                                >
                                    <template v-slot:hoursFees="{ props }">
                                        <numerictextbox
                                            v-bind="props"
                                            @change="props.onChange"
                                            @blur="props.onBlur"
                                            @focus="props.onFocus"
                                        />
                                    </template>
                                </field>
                            </div>
                        </div>
                    </div>
                    <div class="rounded-md border border-gray-200 p-4" v-if="!isInEditMode">
                        <div class="mb-2 space-y-2">
                            <div class="font-semibold capitalize text-gray-700">
                                Will there be classes for same slot of time across multiple days? If
                                yes then chosse the recurring pattern
                            </div>
                            <div>Provide the date and time the class will be taking place on.</div>
                        </div>
                        <div class="mb-2 grid grid-cols-2 gap-x-6 gap-y-1">
                            <field
                                :id="'recurring_class'"
                                :name="'recurring_class'"
                                :component="'myTemplate'"
                                :label="'Recurring Class'"
                                :placeholder="'Enter Recurring Class'"
                                :validator="requiredtrue"
                                :dataItems="recurringIntervals"
                                :text-field="'name'"
                                :data-item-key="'id'"
                                :valueField="'id'"
                                :hint="'Choose how often the class should repeat. For example, selecting `Weekly` will create a new class with the same name and time on the same day each week.'"
                                :valuePrimitive="true"
                            >
                                <template v-slot:myTemplate="{ props }">
                                    <formdropdown
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                            <field
                                :id="'recurring_end_date'"
                                :name="'recurring_end_date'"
                                :component="'myTemplate'"
                                :label="'Recurring End Date'"
                                :placeholder="'Enter Recurring End Date'"
                                :hint="'Provide the date on which the last class will be start. No intakes and classes will be created beyond this date.'"
                                :validator="checkRecurringEndDate"
                                v-if="isClassRecurring"
                            >
                                <template v-slot:myTemplate="{ props }">
                                    <formdatepicker
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                        </div>
                        <div v-if="isClassDaily">
                            <div class="mb-4 font-medium capitalize text-gray-700">
                                Select the week days this class will re-occur.
                            </div>
                            <div class="mb-2 grid grid-cols-4 gap-x-6 gap-y-2">
                                <div
                                    class="course-checkbox flex justify-start"
                                    v-for="(day, index) in weekdays"
                                    :key="index"
                                >
                                    <field
                                        :id="'daily' + day.id"
                                        :name="'daily' + day.id"
                                        :component="'myTemplate'"
                                        :label="day.text"
                                        v-model="day.selected"
                                        :label-placement="'after'"
                                    >
                                        <template v-slot:myTemplate="{ props }">
                                            <checkbox
                                                v-bind="props"
                                                @change="props.onChange"
                                                @blur="props.onBlur"
                                                @focus="props.onFocus"
                                            />
                                        </template>
                                    </field>
                                </div>
                            </div>
                        </div>
                        <div v-if="isClassRecurring" class="mb-2 space-y-2">
                            <div class="font-md">
                                Note* this class will start from {{ startDateText }} to
                                {{ endDateText }} and will repeat {{ repeatText }} till
                                {{ repeatUntilDateText }}
                            </div>
                        </div>
                        <div class="mb-2 grid grid-cols-1 gap-x-6 gap-y-1" v-if="isClassRecurring">
                            <field
                                :id="'same_intakes_for_all_repeats'"
                                :name="'same_intakes_for_all_repeats'"
                                :component="'switchTemplate'"
                                :label="'Intake start date remains same for all classes?'"
                                :orientation="'horizontal'"
                                :valueLabel="false"
                                :dynamicLabel="['Yes', 'No']"
                                :labelPosition="'end'"
                            >
                                <template v-slot:switchTemplate="{ props }">
                                    <formswitch
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                    <div class="font-xstext-xs font-norma text-gray-500">
                                        {{ getRecurringSwitchHint }}
                                    </div>
                                </template>
                            </field>
                        </div>
                    </div>
                    <div class="mb-2 grid grid-cols-2 gap-x-6 gap-y-1">
                        <field
                            :id="'intake_status'"
                            :name="'intake_status'"
                            :label="'Status'"
                            :data-items="intakeStatusData"
                            :layout="'horizontal'"
                            :component="'myTemplate'"
                        >
                            <template v-slot:myTemplate="{ props }">
                                <formradiogroup
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                </div>
                <div v-else>
                    <div
                        role="alert"
                        aria-live="polite"
                        class="rounded-lg border border-gray-200 bg-white p-4 shadow-sm"
                    >
                        <div class="flex items-start gap-4">
                            <div class="min-w-0 flex-1">
                                <p class="font-semibold text-gray-900">
                                    Intakes cannot be added yet
                                </p>
                                <p class="mt-1 text-sm text-gray-600">
                                    Please complete the required details in the
                                    <strong>Hours &amp; Fees</strong> section before creating
                                    intakes.
                                </p>
                                <ul class="ml-4 mt-3 list-disc text-sm text-gray-600">
                                    <li>Select one or more campuses.</li>
                                    <li>Specify the delivery mode.</li>
                                    <li>Enter the applicable fees.</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <dialog-actions-bar v-if="selectedCampuses.length > 0">
            <div class="flex w-full justify-end gap-2">
                <div class="mr-2">
                    <SecondaryButton @click="cancelProcess" :class="'hover:cursor-pointer'">
                        <div class="ml-2">Cancel</div>
                    </SecondaryButton>
                </div>
                <div class="mr-2">
                    <Button
                        :variant="'primary'"
                        :type="'submit'"
                        :size="'base'"
                        :disabled="!kendoForm.allowSubmit"
                        @click="checkFormErrors"
                    >
                        <ContextLoader
                            :context="'buttonLoading'"
                            template="spin"
                            :loadingText="'Saving Intake'"
                            :pt="{
                                root: 'flex items-center justify-center gap-1',
                            }"
                            v-if="saving"
                        />
                        <span v-else>Save Intake</span>
                    </Button>
                </div>
            </div>
        </dialog-actions-bar>
    </form-element>
</template>

<script>
import { Field, FormElement } from '@progress/kendo-vue-form';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
import FormCheckboxInline from '@spa/components/KendoInputs/FormCheckboxInline.vue';
import FormNumericInput from '@spa/components/KendoInputs/FormNumericInput.vue';
import FormNumericTextbox from '@spa/components/KendoInputs/FormNumericTextBox.vue';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
import FormDateTimePicker from '@spa/components/KendoInputs/FormDateTimePicker.vue';
import FormTimePicker from '@spa/components/KendoInputs/FormTimePicker.vue';
import FormRadioGroup from '@spa/components/KendoInputs/FormRadioGroup.vue';
import FormSwitch from '@spa/components/KendoInputs/FormSwitch.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import Button from '@spa/components/Buttons/Button';
import PrimaryButton from '@spa/components/Buttons/PrimaryButton.vue';
import SecondaryButton from '@spa/components/Buttons/SecondaryButton.vue';
import { mapState } from 'pinia';
import { useCoursesStore } from '@spa/stores/modules/courses';
import NavigationButtons from '@spa/pages/courses/includes/NavigationButtons.vue';
import FileUploader from '@spa/components/Uploader/FileUploader.vue';
import { Upload } from '@progress/kendo-vue-upload';
import { DialogActionsBar } from '@progress/kendo-vue-dialogs';
import { Checkbox } from '@progress/kendo-vue-inputs';
import ContextLoader from '@spa/components/Loader/ContextLoader.vue';

import {
    requiredtrue,
    requiredCode,
    validDate,
    requiredDate,
    validEndDate,
    requiredmonetoryvalue,
} from '@spa/services/validators/kendoCommonValidator.js';
import { end } from '@popperjs/core';

export default {
    components: {
        field: Field,
        'form-element': FormElement,
        forminput: FormInput,
        formtextarea: FormTextArea,
        formcheckbox: FormCheckboxInline,
        checkbox: Checkbox,
        numericinput: FormNumericInput,
        numerictextbox: FormNumericTextbox,
        formradiogroup: FormRadioGroup,
        formswitch: FormSwitch,
        formdropdown: FormDropDown,
        formdatepicker: FormDatePicker,
        formtimepicker: FormTimePicker,
        formdatetimepicker: FormDateTimePicker,
        Button,
        navigationbuttons: NavigationButtons,
        'file-upload': FileUploader,
        upload: Upload,
        'dialog-actions-bar': DialogActionsBar,
        PrimaryButton,
        SecondaryButton,
        ContextLoader,
    },
    props: {
        saving: { type: Boolean, default: false },
        courseDuration: { type: Object, default: 0 },
        durationType: { type: Object, default: {} },
    },
    inject: {
        kendoForm: { default: {} },
    },
    mounted() {
        this.getCampusesData();
        const selectedDays = this.weekdays
            .filter((day) => day.selected)
            .map((day) => day.id)
            .join(',');
        this.kendoForm.onChange('repeats_on', { value: selectedDays });
    },
    computed: {
        ...mapState(useCoursesStore, [
            'course',
            'formInits',
            'currentposition',
            'operationMode',
            'formInits',
            'faculties',
        ]),
        hours() {
            const hours = [];
            for (let i = 5; i < 20; i++) {
                hours.push({ id: i, name: i.toString().padStart(2, '0') });
            }
            return hours;
        },
        minutes() {
            const minutes = [];
            for (let i = 0; i < 60; i = i + 5) {
                minutes.push({ id: i, name: i.toString().padStart(2, '0') });
            }
            return minutes;
        },
        recurringIntervals() {
            return [
                { id: 'non-recurring', name: 'Non-Recurring' },
                { id: 'daily', name: 'Daily' },
                { id: 'weekly', name: 'Weekly' },
                { id: 'monthly', name: 'Monthly' },
            ];
        },
        deliveryTargetData: function () {
            return this.formInits?.delivery_targets;
        },
        getDeliveryTarget() {
            return this.kendoForm.valueGetter('intake_receiver');
        },
        isShortCourse() {
            return this.isCourseShortCourse(this.course?.course_type_id);
        },
        showDomesticFee: function () {
            return this.getDeliveryTarget == 'Both' || this.getDeliveryTarget == 'Domestic';
        },
        showInternationalFee: function () {
            return this.getDeliveryTarget == 'Both' || this.getDeliveryTarget == 'International';
        },
        showOffshoreFee() {
            return !this.showInternationalFee && this.getDeliveryTarget == 'InternationalOffShore';
        },
        showOnshoreFee() {
            return !this.showInternationalFee && this.getDeliveryTarget == 'InternationalOnshore';
        },
        showFaceToFaceFee() {
            const mode = this.kendoForm.valueGetter('intake_delivery_mode') || '';
            if (!this.isShortCourse) {
                return false;
            }
            return mode == 'facetoface';
        },
        showOnlineFee() {
            const mode = this.kendoForm.valueGetter('intake_delivery_mode') || '';
            if (!this.isShortCourse) {
                return false;
            }
            return mode == 'online';
        },
        showBlendedFee() {
            const mode = this.kendoForm.valueGetter('intake_delivery_mode') || '';
            if (!this.isShortCourse) {
                return false;
            }
            return mode == '' || mode == 'hybrid';
        },
        isClassDaily() {
            return (
                this.isClassRecurring && this.kendoForm.valueGetter('recurring_class') === 'daily'
            );
        },
        isClassRecurring() {
            const recurringClass = this.kendoForm.valueGetter('recurring_class');
            return recurringClass !== 'non-recurring';
        },
        isInEditMode() {
            return this.kendoForm.valueGetter('id') !== null;
        },
        startDateText() {
            let startDate = this.kendoForm.valueGetter('class_start_date') || '';
            startDate = new Date(startDate);
            if (isNaN(startDate.getTime())) {
                return 'N/A';
            }
            startDate = startDate.toISOString().slice(0, 10);
            const class_start_hour = (this.kendoForm.valueGetter('class_start_hour') || '0')
                .toString()
                .padStart(2, '0');
            const class_start_minute = (this.kendoForm.valueGetter('class_start_minute') || '0')
                .toString()
                .padStart(2, '0');
            const datetime = `${startDate} ${class_start_hour}:${class_start_minute}`;
            return this.convertDateSring(datetime);
        },
        endDateText() {
            let endDate = this.kendoForm.valueGetter('class_end_date') || '';
            endDate = new Date(endDate);
            if (isNaN(endDate.getTime())) {
                return 'N/A';
            }
            endDate = endDate.toISOString().slice(0, 10);
            const class_end_hour = (this.kendoForm.valueGetter('class_end_hour') || '0')
                .toString()
                .padStart(2, '0');
            const class_end_minute = (this.kendoForm.valueGetter('class_end_minute') || '0')
                .toString()
                .padStart(2, '0');
            const datetime = `${endDate} ${class_end_hour}:${class_end_minute}`;
            return this.convertDateSring(datetime);
        },
        repeatText() {
            return this.kendoForm.valueGetter('recurring_class');
        },
        repeatUntilDateText() {
            let endDate = this.kendoForm.valueGetter('recurring_end_date') || '';
            endDate = new Date(endDate);
            if (isNaN(endDate.getTime())) {
                return 'N/A';
            }
            return endDate.toISOString().slice(0, 10);
        },
        getRecurringSwitchHint() {
            const sameIntakeDate = this.kendoForm.valueGetter('same_intakes_for_all_repeats');
            if (sameIntakeDate === true) {
                return 'As this is turned on, all the intakes going to be created will have a same intake start date regardless of class start date, only the intake end date will be adjusted.';
            } else {
                return 'As this is turned off, Intake start date will be adjusted according to the class start date to maintain the same number of intake days for each class.';
            }
        },
        courseDeliveryModes: function () {
            return this.formInits?.delivery_mode;
        },
        isHigherEd: function () {
            return this.isCourseHigherEd(this.course?.course_type_id);
        },
        isDeliveryModeDisabled: function () {
            console.log(this.course);
            return true;
        },
        isFaceToFaceFeesDisabled: function () {
            return true;
        },
        isOnlineFeesDisabled: function () {
            return true;
        },
        isBlendedFeesDisabled: function () {
            return true;
        },
        isInternationalFeesDisabled: function () {
            return true;
        },
        isDomesticFeesDisabled: function () {
            return true;
        },
        isInternationalOffShoreDisabled: function () {
            return true;
        },
        isInternationalOnShoreDisabled: function () {
            return true;
        },
    },
    data: function () {
        return {
            dataitem: [],
            intakeStatusData: [
                { value: 1, label: 'Active' },
                { value: 0, label: 'Inactive' },
            ],
            defaultTarget: {
                text: 'Select Delivery Target ...',
                id: '',
            },
            defaultDeliveryMode: {
                id: null,
                text: 'Select Delivery Mode',
            },
            selectedCampuses: [],
            showEndDate: true,
            weekdays: [
                { id: 1, text: 'Monday', selected: true },
                { id: 2, text: 'Tuesday', selected: true },
                { id: 3, text: 'Wednesday', selected: true },
                { id: 4, text: 'Thursday', selected: true },
                { id: 5, text: 'Friday', selected: true },
                { id: 6, text: 'Saturday', selected: false },
                { id: 7, text: 'Sunday', selected: false },
            ],
        };
    },
    methods: {
        requiredtrue,
        requiredCode,
        validDate,
        requiredDate,
        validEndDate,
        requiredmonetoryvalue,
        getCampusesData: function () {
            const facultiesData = this.faculties?.campus_list || '';
            const facultiesArr = facultiesData.split(',');
            const selectedCampuses = this.formInits?.campuses.filter((campus) =>
                facultiesArr.includes(String(campus.id))
            );
            const appliedCampuses = this.kendoForm.valueGetter('campuses') || [];
            this.selectedCampuses = selectedCampuses.map((campus) => ({
                text: campus.text,
                id: campus.id,
                selected:
                    this.isInEditMode || appliedCampuses.length > 0
                        ? appliedCampuses.includes(campus.id)
                        : true,
            }));
        },
        cancelProcess() {
            this.$emit('cancel');
        },
        requiredCampus() {
            const selectedCampuses = this.selectedCampuses.filter((campus) => campus.selected);
            if (selectedCampuses.length === 0) {
                return 'Please select at least one campus.';
            }
            return '';
        },
        convertDateSring(value) {
            //convert the string to yyyy-mm-dd format
            if (value instanceof Date) return value;
            const parts = value ? value.split('-') : '';
            let dateString = '';
            if (parts.length === 3) {
                dateString = `${parts[0]}-${parts[1]}-${parts[2]}`;
            }
            return dateString;
        },
        checkIntakeEnd(value) {
            const start = this.kendoForm.valueGetter('intake_start') || '';
            const startDate = this.convertDateSring(start);
            const endDate = this.convertDateSring(value);
            return this.validEndDate(
                endDate,
                startDate,
                0,
                'Intake end date must be greater than intake start date.'
            );
        },
        checkClassEndDate(value) {
            const start = this.kendoForm.valueGetter('class_start_date') || '';
            return this.validEndDate(
                this.convertDateSring(value),
                this.convertDateSring(start),
                0,
                'Class end date must be greater than class start date.'
            );
        },
        setClassEndDate(e) {
            this.showEndDate = false;
            const type = this.durationType?.id || 1;
            const duration = this.duration || 1;
            const dateValue = this.kendoForm.valueGetter('class_start_date');
            const selectedDate = new Date(dateValue);
            if (isNaN(selectedDate.getTime())) {
                return;
            }
            let nextDate = new Date(selectedDate);

            if (type == 2) {
                // Week
                nextDate.setDate(nextDate.getDate() + 7);
            } else if (type == 3) {
                // Month
                nextDate.setMonth(nextDate.getMonth() + duration);
            } else if (type == 4) {
                // Year
                nextDate.setFullYear(nextDate.getFullYear() + duration);
            } else {
                // Day (or fallback)
                nextDate.setDate(nextDate.getDate() + duration);
            }
            nextDate.setDate(nextDate.getDate() - 1);
            this.kendoForm.onChange('class_end_date', { value: nextDate });
            setTimeout(() => {
                this.showEndDate = true;
            }, 1);
        },
        checkRecurringEndDate(value) {
            const start = this.kendoForm.valueGetter('class_start_date') || '';
            if (this.isClassRecurring) {
                return this.validEndDate(
                    this.convertDateSring(value),
                    this.convertDateSring(start),
                    1,
                    'Recurring end date must be greater than class start date.'
                );
            }
            return '';
        },
        checkInternationalFeeRequired(value) {
            const visible = this.showInternationalFee;
            if (visible) {
                return this.requiredmonetoryvalue(value);
            }
        },
        checkDomesticFeeRequired(value) {
            const visible = this.showDomesticFee;
            if (visible) {
                return this.requiredmonetoryvalue(value);
            }
        },
        checkFormErrors() {
            if (!this.kendoForm.valid && this.kendoForm.visited) {
                const firstElement = document.querySelector('.k-text-error');
                if (firstElement) {
                    firstElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start', // aligns top of element with top of viewport
                        inline: 'nearest',
                    });
                }
            }
            // Add offset after scrolling
        },
        checkFaceToFaceFeeRequired(value) {
            if (this.showFaceToFaceFee) {
                return this.requiredmonetoryvalue(value);
            }
            return '';
        },
        checkOnlineFeeRequired(value) {
            if (this.showOnlineFee) {
                return this.requiredmonetoryvalue(value);
            }
            return '';
        },
        checkBlendedFeeRequired(value) {
            if (this.showBlendedFee) {
                return this.requiredmonetoryvalue(value);
            }
            return '';
        },
    },
    watch: {
        formInits: {
            handler(newVal) {
                this.getCampusesData();
            },
            deep: true,
        },
        selectedCampuses: {
            handler(newVal) {
                const selectedValues = newVal
                    .filter((campus) => campus.selected)
                    .map((campus) => campus.id)
                    .join(',');
                this.kendoForm.onChange('campuses', { value: selectedValues });
            },
            deep: true,
        },
        weekdays: {
            handler(newVal) {
                const selectedDays = newVal
                    .filter((day) => day.selected)
                    .map((day) => day.id)
                    .join(',');
                this.kendoForm.onChange('repeats_on', { value: selectedDays });
            },
            deep: true,
        },
    },
};
</script>
