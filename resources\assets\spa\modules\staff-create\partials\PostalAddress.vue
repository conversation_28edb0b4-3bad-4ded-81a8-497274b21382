<script setup>
import { computed } from 'vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormRadioGroup from '@spa/components/KendoInputs/FormRadioGroup.vue';
import CountrySelect from '@spa/modules/country/CountrySelect.vue';
import { getValidationMessage } from '@spa/composables/formComposables.js';

const props = defineProps({
    userType: {},
    modelValue: {},
});
const userType = computed(() => {
    return props.userType ?? route().params.user_type;
});
const emit = defineEmits(['update:modelValue']);
const formData = computed({
    get() {
        return props.modelValue || {};
    },
    set(value) {
        emit('update:modelValue', value);
    },
});
</script>
<template>
    <div>
        <h3 class="mb-6 text-base font-semibold text-gray-800">Postal Address</h3>

        <div class="mb-6">
            <FormInput
                v-model="formData.postal_address"
                name="postal_address"
                label="Address"
                placeholder="Search Address"
                :required="true"
                v-bind="getValidationMessage(formData, 'postal_address')"
            />
        </div>

        <div class="mb-6">
            <CountrySelect
                v-model="formData.postal_country"
                name="postal_country"
                label="Country"
                :required="true"
                v-bind="getValidationMessage(formData, 'postal_country')"
            />
        </div>

        <div class="mb-6">
            <FormInput
                v-model="formData.postal_city"
                name="postal_city"
                label="City/Town/Suburb"
                placeholder="Add address"
                :required="true"
                v-bind="getValidationMessage(formData, 'postal_city')"
            />
        </div>

        <div class="mb-6">
            <FormInput
                v-model="formData.postal_state"
                name="postal_state"
                label="State/Province"
                placeholder="Add address"
                :required="true"
                v-bind="getValidationMessage(formData, 'postal_state')"
            />
        </div>

        <div class="mb-6">
            <FormInput
                v-model="formData.postal_postcode"
                name="postal_postcode"
                label="Postcode"
                placeholder="Add address"
                v-bind="getValidationMessage(formData, 'postal_postcode')"
            />
        </div>

        <div class="mb-6">
            <FormInput
                v-model="formData.postal_abn"
                name="postal_abn"
                label="ABN"
                placeholder="Add address"
                v-bind="getValidationMessage(formData, 'postal_abn')"
            />
        </div>
    </div>
</template>

<style scoped></style>
