<?php

namespace GalaxyAPI\Traits;

use Illuminate\Database\Eloquent\SoftDeletes;

trait ArchiveModelTrait
{
    use SoftDeletes;

    public function scopeFilterWithArchived($query, $value)
    {
        if ($value === 1) {
            return $query->withTrashed();
        }

        return $query;
    }

    public function scopeFilterOnlyArchived($query, $value)
    {
        if ($value === true) {
            return $query->onlyTrashed();
        }

        return $query;
    }

    public function scopeAlias($query, $alias)
    {
        //        dd($alias);
        $parts = explode('as', $alias);
        $this->alias = trim(end($parts));

        return $query->from(dbRawL10("{$this->getTable()} as {$this->alias}"));
    }

    public function getQualifiedDeletedAtColumn()
    {
        $alias = $this->alias ?? $this->getTable();

        return dbRawL10($alias.'.'.$this->getDeletedAtColumn());
    }
}
