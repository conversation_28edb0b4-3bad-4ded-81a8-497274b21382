<template>
    <div :class="rootClass">
        <fieldwrapper>
            <klabel
                :class="'k-form-label'"
                :editor-id="id"
                :editor-valid="valid"
                :disabled="disabled"
                :optional="optional"
                class="mb-1 font-medium leading-5 text-gray-700"
            >
                {{ label }}
            </klabel>
            <div class="k-form-field">
                <datetimepicker
                    :valid="valid"
                    :disabled="disabled"
                    :focusedDate="focusedDate"
                    :format="format"
                    :formatPlaceholder="formatPlaceholder"
                    :label="label"
                    :max="max"
                    :maxTime="maxTime"
                    :min="min"
                    :minTime="minTime"
                    :placeholder="placeholder"
                    :class="computedDatePickerClass"
                    :defaultValue="getDefaultValue"
                    :value="internalValue"
                    @input="handleInput"
                    @change="handleChange"
                    @blur="handleBlur"
                    @focus="handleFocus"
                    @iconclick="handleIconClick"
                    :popupSettings="popupOptions"
                    :title="title"
                    :weekNumber="weekNumber"
                    :width="width"
                    :cancelButton="cancelButton"
                />
                <error v-if="showValidationMessage">
                    {{ validationMessage }}
                </error>
                <hint v-else-if="showHint">{{ hint }}</hint>
            </div>
        </fieldwrapper>
    </div>
</template>

<script>
import { DateTimePicker } from '@progress/kendo-vue-dateinputs';
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { twMerge } from 'tailwind-merge';

export default {
    props: {
        size: String,
        valid: {
            type: Boolean,
            default: true,
        },
        id: String,
        cancelButton: {
            type: Boolean,
            default: true,
        },
        defaultShow: {
            type: Boolean,
            default: false,
        },
        defaultValue: {
            type: [null, Date],
            default: null,
        },
        disabled: Boolean,
        focusedDate: {
            type: Date,
            default: () => new Date('1900-01-01'),
        },
        format: {
            type: String,
            default: 'dd MMM yyyy hh:mm a',
        },
        formatPlaceholder: String,
        label: String,
        min: {
            type: Date,
            default: () => new Date('1900-01-01'),
        },
        max: {
            type: Date,
            default: () => new Date('9999-12-31'),
        },
        maxTime: {
            type: Date,
            default: () => new Date('9999-12-31'),
        },
        minTime: {
            type: Date,
            default: () => new Date('1900-01-01'),
        },
        title: String,
        weekNumber: Boolean,
        modelValue: {
            type: [Date, String, null],
            default: null,
        },
        width: Number,
        pt: {
            type: Object,
            default: () => ({}),
        },
        // Form validation props
        touched: {
            type: Boolean,
            default: false,
        },
        validationMessage: {
            type: String,
            default: '',
        },
        hint: {
            type: String,
            default: '',
        },
        optional: {
            type: Boolean,
            default: false,
        },
        placeholder: {
            type: String,
            default: '',
        },
        popupOptions: {
            type: Object,
            default: () => ({}),
        },
    },
    components: {
        fieldwrapper: FieldWrapper,
        error: Error,
        hint: Hint,
        klabel: Label,
        datetimepicker: DateTimePicker,
    },
    emits: ['update:modelValue', 'input', 'change', 'blur', 'focus', 'iconclick'],
    computed: {
        internalValue: {
            get() {
                return this.modelValue;
            },
            set(value) {
                this.$emit('update:modelValue', value);
            },
        },
        showValidationMessage() {
            return this.touched && this.validationMessage;
        },
        showHint() {
            return !this.showValidationMessage && this.hint;
        },
        hintId() {
            return this.showHint ? `${this.id}_hint` : '';
        },
        errorId() {
            return this.showValidationMessage ? `${this.id}_error` : '';
        },
        rootClass() {
            return twMerge('tw-datetime-picker__wrapper', this.pt.root);
        },
        fieldClass() {
            return 'tw-datetime-picker rounded-[8px]';
        },
        computedDatePickerClass() {
            return twMerge(this.getSizeClass(this.size), this.fieldClass);
        },
        getDefaultValue() {
            const parsedDate = this.modelValue ? new Date(this.modelValue) : null;
            if (
                this.modelValue &&
                parsedDate &&
                !isNaN(parsedDate.getTime()) &&
                parsedDate >= this.min &&
                parsedDate <= this.max
            ) {
                // If modelValue is a valid Date and falls within the min and max range, return it
                return parsedDate;
            } else {
                // If modelValue is not a valid Date or is out of range, return defaultValue
                return this.defaultValue;
            }
        },
    },
    methods: {
        getSizeClass(size) {
            switch (size) {
                case 'xs':
                    return 'h-7.5';
                case 'sm':
                    return 'h-9';
                case 'base':
                    return 'h-10';
                case 'lg':
                    return 'h-11';
                case 'xl':
                    return 'h-12';
                case 'auto':
                    return 'h-auto';
                default:
                    return 'h-10';
            }
        },
        handleInput(e) {
            // Update the model value for two-way binding
            this.internalValue = e.value;
            this.$emit('input', e);
        },
        handleChange(e) {
            // Update the model value for two-way binding
            this.internalValue = e.value;
            this.$emit('change', e);
        },
        handleBlur(e) {
            this.$emit('blur', e);
        },
        handleFocus(e) {
            this.$emit('focus', e);
        },
        handleIconClick(e) {
            this.$emit('iconclick', e);
        },
    },
};
</script>
