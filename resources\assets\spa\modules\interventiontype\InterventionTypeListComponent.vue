<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="false"
        :has-bulk-actions="false"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :actions="['edit']"
        :search-placeholder="'Search by type or strategy...'"
    >
        <template #bulk-actions>
            <DropdownButton
                variant="primary"
                :autoHide="true"
                :icon-name="'plus'"
                :button-label="'Add New'"
                :placement="'bottom-end'"
            >
                <template #dropdown-content>
                    <button
                        class="btn-menu-item flex-col items-start gap-0"
                        @click="store.formDialog = true"
                    >
                        <p>Add Intervention Type</p>
                        <span class="text-xs text-gray-400">Create new intervention category</span>
                    </button>
                    <button
                        class="btn-menu-item flex-col items-start gap-0"
                        @click="handleAddStrategy"
                    >
                        <p>Add Strategy to Existing Type</p>
                        <span class="text-xs text-gray-400">Add strategy to current types</span>
                    </button>
                    <button
                        class="btn-menu-item flex-col items-start gap-0"
                        @click="handleAddStrategy"
                    >
                        <p>Use Strategy Template</p>
                        <span class="text-xs text-gray-400"
                            >Quick setup with pre-built strategies</span
                        >
                    </button>
                </template>
            </DropdownButton>
        </template>
        <template #body-cell-intervention_type="{ props }">
            <div class="font-medium text-gray-800" v-if="props.dataItem?.intervention_type">
                {{ props.dataItem?.intervention_type }}
            </div>
        </template>
        <template #body-cell-strategies="{ props }">
            <div class="group space-y-1 border-x py-2">
                <div class="max-h-60 overflow-y-auto">
                    <div
                        class="subgroup flex items-center justify-between border-b px-3 py-3 text-sm"
                        v-for="(strategy, index) in props.dataItem?.strategies ?? []"
                        :class="{
                            'border-b-0': index === props.dataItem?.strategies.length - 1,
                            'bg-gray-100': index % 2 === 1,
                        }"
                        :key="index"
                    >
                        {{ strategy.strategy }}
                        <div class="subgroup-hover:visible invisible items-center gap-3">
                            <GridActionButton
                                @click="
                                    () => {
                                        interventionStrategyStore.edit(strategy);
                                        store.fetchPaged();
                                    }
                                "
                                :tooltip-title="'Edit'"
                            >
                                <!-- Edit Icon -->
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                    class="size-5"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
                                    />
                                </svg>
                            </GridActionButton>
                            <GridActionButton
                                @click="
                                    async () => {
                                        await interventionStrategyStore.confirmDelete(strategy, {
                                            onAccept: () => {
                                                store.fetchPaged();
                                            },
                                        });
                                    }
                                "
                                :tooltip-title="'Delete'"
                                class="text-red-500"
                            >
                                <!-- Delete Icon -->
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                    class="size-5"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                                    />
                                </svg>
                            </GridActionButton>
                        </div>
                    </div>
                </div>
                <div class="px-3">
                    <Button
                        variant="tertiary"
                        class="invisible border border-dashed border-primary-blue-400 normal-case transition-all duration-300 group-hover:visible"
                        size="xs"
                        @click="handleAddStrategy(props.dataItem)"
                    >
                        <icon name="plus" fill="currentColor" width="14" height="14" />
                        Add another strategy for this type
                    </Button>
                </div>
            </div>
        </template>
    </AsyncGrid>
    <InterventionTypeForm />
    <InterventionStrategyForm :store="interventionStrategyStore" @success="handleSuccess" />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useInterventionTypeStore } from '@spa/stores/modules/interventiontype/useInterventionTypeStore.js';
import InterventionTypeForm from '@spa/modules/interventiontype/InterventionTypeForm.vue';
import dayjs from 'dayjs';
import Button from '@spa/components/Buttons/Button.vue';
import DropdownButton from '@spa/components/Buttons/DropdownButton.vue';
import InterventionStrategyForm from '@spa/modules/interventionstrategy/InterventionStrategyForm.vue';
import { useInterventionStrategyStore } from '@spa/stores/modules/interventionstrategy/useInterventionStrategyStore.js';
import GridActionButton from '@spa/components/AsyncComponents/Grid/Partials/GridActionButton.vue';

const store = useInterventionTypeStore();
const interventionStrategyStore = useInterventionStrategyStore();

const columns = [
    {
        field: 'intervention_type',
        name: 'intervention_type',
        title: 'Intervention Type',
        width: '250px',
        sortable: true,
        replace: true,
    },
    {
        field: 'strategies',
        name: 'strategies',
        title: 'Strategy',
        replace: true,
        sortable: false,
    },
    // Add more columns as needed
];
const initFilters = () => {
    store.filters = {};
};

const handleAddStrategy = (dataItem = null) => {
    interventionStrategyStore.formDialog = true;
    if (dataItem) {
        interventionStrategyStore.formData.intervention_type_id = dataItem.id;
        return;
    }
    interventionStrategyStore.formData.intervention_type_id = null;
};

const handleSuccess = (res) => {
    console.log('checking', res);
    store.fetchPaged();
};
onMounted(() => {
    initFilters();
});
</script>
<style scoped>
.subgroup:hover .subgroup-hover\:visible {
    visibility: visible;
}
</style>
