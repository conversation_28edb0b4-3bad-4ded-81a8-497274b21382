<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Support\Traits\UsesToggleTrait;

class SmtpSetup extends Model
{
    use HasFactory;
    use LogsActivity;
    use UsesToggleTrait;

    protected $table = 'rto_smtp_setup';

    const STATUS_ACTIVE = 1;

    const STATUS_DISABLED = 0;

    protected $fillable = [
        'college_id',
        'username',
        'host',
        'port',
        'email',
        'password',
        'encryption',
        'name',
        'status',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => "SMTP Setup has been {$eventName}");
        // Chain fluent methods for configuration options
    }

    /* This method is fired before saving the log to db. */
    public function tapActivity(Activity $activity, string $eventName) {}

    public function isValid()
    {
        return $this->username && $this->host && $this->port && $this->email && $this->password && $this->encryption;
    }

    public static function getSMTPDetail()
    {
        return SmtpSetup::where('status', self::STATUS_ACTIVE)->first();
    }

    public static function ApplyCustomSmtp()
    {
        $smtp = self::getSMTPDetail();
        if ($smtp && $smtp->isValid()) {
            $mailers = config('mail.mailers');

            $config = array_merge(config('mail'), [
                'default' => 'smtp',
                'mailers' => array_merge($mailers, [
                    'smtp' => array_merge($mailers['smtp'], [
                        'host' => $smtp->host,
                        'port' => $smtp->port,
                        'encryption' => $smtp->encryption,
                        'username' => $smtp->username,
                        'password' => $smtp->password,
                    ]),
                ]),
                'from' => [
                    'address' => $smtp->email,
                    'name' => $smtp->name,
                ],
                // 'sendmail'   => '/usr/sbin/sendmail -bs',
                // 'pretend'    => false,
                // 'stream' => [
                //     'ssl' => [
                //        'allow_self_signed' => true,
                //        'verify_peer' => false,
                //        'verify_peer_name' => false,
                //     ]
                // ],
            ]);
            // dd($config);
            // Config::set('mail', $config);
            config(['mail' => $config]);
        }

    }
}
