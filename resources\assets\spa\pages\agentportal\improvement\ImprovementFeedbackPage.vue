<script setup>
import { provide } from 'vue';
import RegisterImprovementsListComponent from '@spa/modules/register-improvements/RegisterImprovementsListComponent.vue';
import Layout from '@spa/pages/Layouts/Layout';
import { Head } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';

const props = defineProps(['agentId']);
provide('requestedBy', props.agentId);
</script>
<template>
    <Layout :no-spacing="true">
        <Head title="Improvement Feedback" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Improvement Feedback" :back="false" />
        </template>
        <div class="h-screen-header flex flex-col px-8 py-6">
            <RegisterImprovementsListComponent
                :filters="{
                    agentId: agentId,
                }"
                :userType="4"
            />
            <!--            GalaxyAPI\Enums\ImprovementUserTypeEnum-->
        </div>
    </Layout>
</template>

<style scoped></style>
