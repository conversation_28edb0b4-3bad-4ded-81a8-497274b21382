<?php

namespace App\Http\Livewire\Testing;

use App\Model\v2\Courses;
use App\Model\v2\CoursesIntakeDate;
use App\Model\v2\Staff;
use App\Model\v2\Student;
use App\Model\v2\Timetable;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;
use Meilisearch\Endpoints\Indexes;

class Beta extends Component
{
    use WithPagination;

    public $form = [
        'keyword' => '',
        'courses' => [],
        'status' => [],
        'origin' => [],
        'batches' => [],
        'teachers' => [],
        'intake_dates' => [],
        'nationality' => [],
        'sort_by' => '',
        'sort_direction' => 'asc',

    ];

    protected $students;

    public $filters = [
        'courses' => [],
        'status' => [],
        'origin' => [],
        'batches' => [],
        'teachers' => [],
        'intake_dates' => [],
        'nationality' => [],
    ];

    public function mount()
    {
        $this->initFilters();
    }

    public function getCachedFiltersProperty()
    {
        if ($this->filters['courses'] && count($this->filters['courses'])) {
            return $this->filters;
        }

        $this->initFilters();

        return $this->filters;
    }

    public function initFilters()
    {
        $collegeId = auth()->user()->college_id;
        $this->filters['courses'] = Courses::where(['college_id' => $collegeId])
            ->select(['id as value', DB::raw("CONCAT(course_code,' - ',course_name) as label")])
            ->orderBy('value', 'ASC')
            ->get()
            ->toArray();

        $this->filters['nationality'] = Student::alias('rto_students as rs')
            ->leftjoin('rto_country as country', 'country.id', '=', 'rs.current_country')
            ->where('rs.college_id', $collegeId)
            ->where('country.nationality', '!=', '')
            ->select(['country.id as value', 'country.nationality as label'])
            ->orderBy('country.nationality', 'ASC')
            ->groupBy('country.id')
            ->get()
            ->toArray();

        $this->filters['status'] = collect(config('constants.arrCourseStatus'))->map(function ($item) {
            return ['value' => $item, 'label' => $item];
        })->toArray();

        $this->filters['batches'] = Timetable::where(['college_id' => $collegeId])->groupBy('batch')->select(['batch as label', 'batch as value'])->orderBy('value', 'ASC')->get()->toArray();
        $this->filters['teachers'] = Staff::where(['college_id' => $collegeId, 'position' => Staff::POSITION_TEACHER])->select(['id as value', DB::raw("CONCAT(first_name,' ',last_name) as label")])->orderBy('label', 'ASC')
            ->get()
            ->toArray();

        $this->filters['intake_dates'] = CoursesIntakeDate::where(['college_id' => $collegeId])->select(['intake_year as value', 'intake_year as text'])->groupBy('intake_year')->orderBy('intake_year', 'DESC')->get()->toArray();
    }

    public function updatedForm($val)
    {
        $this->form['sort_by'] = '';
        $this->search($val);
    }

    public function sort($field)
    {
        $this->form['sort_by'] = $field;
        $this->form['sort_direction'] = $this->form['sort_direction'] == 'desc' ? 'asc' : 'desc';
        $this->search();
    }

    public function search($keyword = null)
    {
        $scoutBuilder = Student::search($this->form['keyword'], function (Indexes $searchEngine, string $query, array $options) {
            $options['matchingStrategy'] = 'all';
            // dd($options);
            /* "filter" => "courses.course.course_name="Certificate IV in Accounting and Bookkeeping""
  "matchingStrategy" => "all" */

            return $searchEngine->search($query, $options);
        });

        if (count($this->form['courses'])) {
            $scoutBuilder->whereIn('courses.course.id', $this->form['courses']);
        }
        if (count($this->form['status'])) {
            $scoutBuilder->whereIn('courses.status', $this->form['status']);
        }
        if (count($this->form['nationality'])) {
            $scoutBuilder->whereIn('nationality', $this->form['nationality']);
        }
        if (count($this->form['teachers'])) {
            $scoutBuilder->whereIn('teacher_id', $this->form['teachers']);
        }
        if (count($this->form['intake_dates'])) {
            $scoutBuilder->whereIn('courses.intake_year', $this->form['intake_dates']);
        }
        if (count($this->form['batches'])) {
            $scoutBuilder->whereIn('enrollments.batch', $this->form['batches']);
        }

        if (@$this->form['sort_by']) {
            $scoutBuilder->orderBy($this->form['sort_by'], $this->form['sort_direction']);
        }
        // ->where('courses.course.course_name', 'Diploma of Accounting')
        // ->where('courses.course.course_name', 'Certificate IV in Accounting and Bookkeeping')

        $this->students = $scoutBuilder->query(function (Builder $builder) {
            $today = date('Y-m-d');

            return $builder
                ->select(
                    'rto_students.*',
                    DB::raw('COUNT(rsa.attendance_date) as total_day'),
                    DB::raw("SUM(rsa.status = 'absent' AND rsa.attendance_date < '$today') as absent_day"),
                    DB::raw("SUM(rsa.status = 'present' AND rsa.attendance_date < '$today') as present_day"),
                    DB::raw("SUM(rsa.status = 'absent' AND rsa.attendance_date > '$today') as pending_day")
                )
                ->leftJoin('rto_student_attendance as rsa', 'rsa.student_id', '=', 'rto_students.id')
                ->groupBy('rto_students.id')
                ->with([
                    'studentCourses.course',
                    'enrollments' => function ($q) use ($today) {
                        return $q
                            ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rto_student_subject_enrolment.unit_id')
                            // ->where($whereArr)
                            ->select(
                                'rto_student_subject_enrolment.id',
                                'rto_student_subject_enrolment.student_id',
                                'rto_student_subject_enrolment.unit_id',
                                'rto_student_subject_enrolment.final_outcome',
                                'rsu.unit_name as unit_title',
                                DB::raw("CONCAT(rsu.vet_unit_code,' : ',rsu.unit_name) as unit_name"),
                                DB::raw('(CASE WHEN rto_student_subject_enrolment.final_outcome = "C" THEN "green-500" ELSE (CASE WHEN rto_student_subject_enrolment.final_outcome = "NYC" THEN "red-500" ELSE "gray-200" END) END) as color'),
                                DB::raw('(CASE WHEN rto_student_subject_enrolment.activity_start_date <= "'.$today.'" AND rto_student_subject_enrolment.activity_finish_date >= "'.$today.'" THEN 1 ELSE 0 END) as is_active')
                            );
                    },
                    'initialPayments' => function ($q) {
                        return $q->leftJoin('rto_student_initial_payment_details as a2', 'a2.student_course_id', '=', 'rto_student_initial_payment.student_course_id')
                            ->select(
                                'rto_student_initial_payment.student_id',
                                DB::raw('rto_student_initial_payment.tution_fee as total'),
                                DB::raw('(SUM(CASE When a2.payment_status != "unpaid" THEN a2.upfront_fee_pay ELSE 0 END)) as paid'),
                                DB::raw('(SUM(CASE When a2.payment_status != "paid" AND a2.due_date > now() THEN (a2.upfront_fee_to_pay - a2.upfront_fee_pay) ELSE 0 END)) as unpaid'),
                                DB::raw('(SUM(CASE When a2.payment_status != "paid" AND a2.due_date < now() THEN (a2.upfront_fee_to_pay - a2.upfront_fee_pay) ELSE 0 END)) as missed'),
                                // DB::raw('(COUNT(a2.id)) as total_count'),
                                DB::raw('(SUM(CASE When a2.payment_status = "paid" THEN 1 ELSE 0 END)) as paid_count'),
                                DB::raw('(SUM(CASE When a2.payment_status != "paid" AND a2.due_date > now() THEN 1 ELSE 0 END)) as unpaid_count'),
                                DB::raw('(SUM(CASE When a2.payment_status != "paid" AND a2.due_date < now() THEN 1 ELSE 0 END)) as missed_count')
                            )
                            ->groupBy('rto_student_initial_payment.student_id')
                            ->groupBy('rto_student_initial_payment.id');
                    },
                ]);
        })->paginate(10);

        // dd($this->results['tasks']);
    }

    public function render()
    {
        // dd();
        return view('livewire.testing.beta.index', [
            'results' => $this->students,
        ]);
    }
}
