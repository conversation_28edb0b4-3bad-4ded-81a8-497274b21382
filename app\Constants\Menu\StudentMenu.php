<?php

namespace App\Constants\Menu;

use Support\Auth\Access;

class StudentMenu
{
    public const STUDENT_MENU_ITEMS = [
        [
            'label' => 'Dashboard',
            'url' => '/students/dashboard',
            'mainmenu' => ['profile', 'dashboard', 'oshc', 'document', 'material', 'attendance', 'result', 'timetable', 'payment', 'log', 'communication', 'elearning'],
            'activeurls' => ['dashboard'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 12L5 10M5 10L12 3L19 10M5 10V20C5 20.5523 5.44772 21 6 21H9M19 10L21 12M19 10V20C19 20.5523 18.5523 21 18 21H15M9 21C9.55228 21 10 20.5523 10 20V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V20C14 20.5523 14.4477 21 15 21M9 21H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>',
            'permissions' => [
                // Access::SP_DASHBOARD_ACCESS->value,
            ],
        ],
        [
            'label' => 'Document',
            'url' => '#',
            'gap_after' => true,
            'mainmenu' => ['profile', 'dashboard', 'oshc', 'document', 'material', 'attendance', 'result', 'timetable', 'payment', 'log', 'communication', 'elearning'],
            'activeurls' => ['view-college-document-student'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 21H17C18.1046 21 19 20.1046 19 19V9.41421C19 9.149 18.8946 8.89464 18.7071 8.70711L13.2929 3.29289C13.1054 3.10536 12.851 3 12.5858 3H7C5.89543 3 5 3.89543 5 5V19C5 20.1046 5.89543 21 7 21Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>',
            'sub_menu' => [
                [
                    'label' => 'Documents',
                    'url' => '/students/view-college-document-student/0',
                    'permissions' => [
                        Access::SP_DOCUMENT_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Subject Materials',
                    'url' => '/students/subjectSpecific/0',
                    'permissions' => [
                        Access::SP_DOCUMENT_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'Evaluation',
            'url' => '/students/evaluation-view',
            'mainmenu' => ['profile', 'dashboard', 'oshc', 'document', 'material', 'attendance', 'result', 'timetable', 'payment', 'log', 'communication', 'elearning'],
            'activeurls' => ['evaluation-view'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M17.03 11.03a.75.75 0 1 0-1.06-1.06L11 14.94l-1.97-1.97a.75.75 0 0 0-1.06 1.06l2.5 2.5a.75.75 0 0 0 1.06 0zm-1.036-6.946A2.25 2.25 0 0 0 13.75 2h-3.5a2.25 2.25 0 0 0-2.236 2H6.25A2.25 2.25 0 0 0 4 6.25v13.5A2.25 2.25 0 0 0 6.25 22h11.5A2.25 2.25 0 0 0 20 19.75V6.25A2.25 2.25 0 0 0 17.75 4h-1.764zm0 .012L16 4.25q0-.078-.005-.154M10.25 6.5h3.5c.78 0 1.467-.397 1.871-1h2.129a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75.75H6.25a.75.75 0 0 1-.75-.75V6.25a.75.75 0 0 1 .75-.75h2.129c.404.603 1.091 1 1.871 1m0-3h3.5a.75.75 0 0 1 0 1.5h-3.5a.75.75 0 0 1 0-1.5" />
</svg>',
            'permissions' => [

            ],
        ],
        [
            'label' => 'OSHC',
            'url' => '/students/oshc-info',
            'mainmenu' => ['profile', 'dashboard', 'oshc', 'document', 'material', 'attendance', 'result', 'timetable', 'payment', 'log', 'communication', 'elearning'],
            'activeurls' => ['student-oshc-info'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M10.11 15.5a4.5 4.5 0 0 1 .41-1.5H4.254a2.25 2.25 0 0 0-2.25 2.248v.578c0 .892.32 1.756.9 2.435C4.467 21.095 6.854 22 10 22q2.121 0 3.774-.55l-1.215-1.215Q11.415 20.5 10 20.5c-2.738 0-4.704-.745-5.957-2.213a2.25 2.25 0 0 1-.54-1.461v-.578a.75.75 0 0 1 .75-.749zM10 2.003a5 5 0 1 1 0 10a5 5 0 0 1 0-10m0 1.5a3.5 3.5 0 1 0 0 7a3.5 3.5 0 0 0 0-7m7.044 19.492a.75.75 0 0 1-.53-.22l-4.409-4.408a3.467 3.467 0 0 1 4.903-4.903l.036.036l.036-.036a3.467 3.467 0 0 1 4.903 4.903l-4.409 4.408a.75.75 0 0 1-.53.22" />
</svg>',
            'permissions' => [
                Access::SP_OSHC_ACCESS->value,
            ],
        ],
        [
            'label' => 'Attendance',
            'url' => '/students/weekly-attendance-detail',
            'mainmenu' => ['profile', 'dashboard', 'oshc', 'document', 'material', 'attendance', 'result', 'timetable', 'payment', 'log', 'communication', 'elearning'],
            'activeurls' => ['weekly-attendance-detail', 'overall-attendance-detail'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M11.3135 15.4999C11.4859 14.9665 11.7253 14.4631 12.0219 13.9999H4.25278C3.01076 13.9999 2.00391 15.0068 2.00391 16.2488V16.8265C2.00391 17.7193 2.32242 18.5828 2.90219 19.2617C4.46849 21.0959 6.8545 22.0011 10.0004 22.0011C10.9314 22.0011 11.7961 21.9218 12.5927 21.7626C12.2335 21.3494 11.9256 20.8903 11.6789 20.3954C11.1555 20.4658 10.5962 20.5011 10.0004 20.5011C7.26206 20.5011 5.29618 19.7553 4.04287 18.2876C3.69502 17.8802 3.50391 17.3622 3.50391 16.8265V16.2488C3.50391 15.8352 3.83919 15.4999 4.25278 15.4999H11.3135ZM10.0004 2.00464C12.7618 2.00464 15.0004 4.24321 15.0004 7.00464C15.0004 9.76606 12.7618 12.0046 10.0004 12.0046C7.23894 12.0046 5.00036 9.76606 5.00036 7.00464C5.00036 4.24321 7.23894 2.00464 10.0004 2.00464ZM10.0004 3.50464C8.06737 3.50464 6.50036 5.07164 6.50036 7.00464C6.50036 8.93764 8.06737 10.5046 10.0004 10.5046C11.9334 10.5046 13.5004 8.93764 13.5004 7.00464C13.5004 5.07164 11.9334 3.50464 10.0004 3.50464ZM17.5 12C20.5376 12 23 14.4624 23 17.5C23 20.5376 20.5376 23 17.5 23C14.4624 23 12 20.5376 12 17.5C12 14.4624 14.4624 12 17.5 12ZM19.5 17.5001H17.5L17.5 14.9999C17.5 14.7238 17.2761 14.4999 17 14.4999C16.7239 14.4999 16.5 14.7238 16.5 14.9999L16.5 17.9985L16.5 18.0001C16.5 18.2762 16.7239 18.5001 17 18.5001H19.5C19.7761 18.5001 20 18.2762 20 18.0001C20 17.7239 19.7761 17.5001 19.5 17.5001Z" fill="currentColor"/>
            </svg>',
            'permissions' => [
                Access::SP_ATTENDANCE_ACCESS->value,
            ],
        ],
        [
            'label' => 'Results',
            'url' => '/students/timetable-student',
            'mainmenu' => ['profile', 'dashboard', 'oshc', 'document', 'material', 'attendance', 'result', 'timetable', 'payment', 'log', 'communication', 'elearning'],
            'activeurls' => ['timetable-student'],
            'svgicon' => '<svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>',
            'permissions' => [
                Access::SP_RESULTS_ACCESS->value,
            ],
        ],
        [
            'label' => 'Timetable',
            'url' => '/students/study-plan',
            'mainmenu' => ['profile', 'dashboard', 'oshc', 'document', 'material', 'attendance', 'result', 'timetable', 'payment', 'log', 'communication', 'elearning'],
            'activeurls' => ['study-plan'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 6.25C21 4.45507 19.5449 3 17.75 3H6.25C4.45507 3 3 4.45507 3 6.25V17.75C3 19.5449 4.45507 21 6.25 21H12.0218C11.7253 20.5368 11.4858 20.0335 11.3135 19.5H6.25C5.2835 19.5 4.5 18.7165 4.5 17.75V8.5H19.5V11.3135C20.0335 11.4858 20.5368 11.7253 21 12.0218V6.25ZM6.25 4.5H17.75C18.7165 4.5 19.5 5.2835 19.5 6.25V7H4.5V6.25C4.5 5.2835 5.2835 4.5 6.25 4.5ZM23 17.5C23 14.4624 20.5376 12 17.5 12C14.4624 12 12 14.4624 12 17.5C12 20.5376 14.4624 23 17.5 23C20.5376 23 23 20.5376 23 17.5ZM17.5 17.5H19.5C19.7761 17.5 20 17.7239 20 18C20 18.2762 19.7761 18.5 19.5 18.5H17C16.7268 18.5 16.5048 18.2809 16.5001 18.0089L16.5 17.9999V14.9999C16.5 14.7238 16.7239 14.4999 17 14.4999C17.2761 14.4999 17.5 14.7238 17.5 14.9999L17.5 17.5Z" fill="currentColor"/>
            </svg>',
            'permissions' => [
                Access::SP_TIMETABLE_ACCESS->value,
            ],
        ],
        [
            'label' => 'Weekly Timetable',
            'url' => '/students/weekly-timetable',
            'gap_after' => true,
            'mainmenu' => ['profile', 'dashboard', 'oshc', 'document', 'material', 'attendance', 'result', 'timetable', 'payment', 'log', 'communication', 'elearning'],
            'activeurls' => ['weekly-timetable'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 6.25C21 4.45507 19.5449 3 17.75 3H6.25C4.45507 3 3 4.45507 3 6.25V17.75C3 19.5449 4.45507 21 6.25 21H12.0218C11.7253 20.5368 11.4858 20.0335 11.3135 19.5H6.25C5.2835 19.5 4.5 18.7165 4.5 17.75V8.5H19.5V11.3135C20.0335 11.4858 20.5368 11.7253 21 12.0218V6.25ZM6.25 4.5H17.75C18.7165 4.5 19.5 5.2835 19.5 6.25V7H4.5V6.25C4.5 5.2835 5.2835 4.5 6.25 4.5ZM23 17.5C23 14.4624 20.5376 12 17.5 12C14.4624 12 12 14.4624 12 17.5C12 20.5376 14.4624 23 17.5 23C20.5376 23 23 20.5376 23 17.5ZM17.5 17.5H19.5C19.7761 17.5 20 17.7239 20 18C20 18.2762 19.7761 18.5 19.5 18.5H17C16.7268 18.5 16.5048 18.2809 16.5001 18.0089L16.5 17.9999V14.9999C16.5 14.7238 16.7239 14.4999 17 14.4999C17.2761 14.4999 17.5 14.7238 17.5 14.9999L17.5 17.5Z" fill="currentColor"/>
            </svg>',
            'permissions' => [
                Access::SP_PAYMENT_ACCESS->value,
            ],
        ],
        [
            'label' => 'Payment',
            'url' => '/students/student-payment-information',
            'mainmenu' => ['profile', 'dashboard', 'oshc', 'document', 'material', 'attendance', 'result', 'timetable', 'payment', 'log', 'communication', 'elearning'],
            'activeurls' => ['student-payment-information'],
            'svgicon' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M13.8434 9.65538C14.2053 10.0725 14.8369 10.1173 15.254 9.7553C15.6712 9.39335 15.7159 8.76176 15.354 8.34462L13.8434 9.65538ZM10.1567 14.3446C9.79471 13.9275 9.16313 13.8827 8.74599 14.2447C8.32885 14.6067 8.28411 15.2382 8.64607 15.6554L10.1567 14.3446ZM13 7C13 6.44772 12.5523 6 12 6C11.4477 6 11 6.44772 11 7H13ZM11 17C11 17.5523 11.4477 18 12 18C12.5523 18 13 17.5523 13 17L11 17ZM20 12C20 16.4183 16.4183 20 12 20V22C17.5228 22 22 17.5228 22 12H20ZM12 20C7.58172 20 4 16.4183 4 12H2C2 17.5228 6.47715 22 12 22V20ZM4 12C4 7.58172 7.58172 4 12 4V2C6.47715 2 2 6.47715 2 12H4ZM12 4C16.4183 4 20 7.58172 20 12H22C22 6.47715 17.5228 2 12 2V4ZM12 11C11.3415 11 10.7905 10.8202 10.4334 10.5822C10.0693 10.3394 10 10.1139 10 10H8C8 10.9907 8.6023 11.7651 9.32398 12.2463C10.0526 12.732 11.0017 13 12 13V11ZM10 10C10 9.8861 10.0693 9.66058 10.4334 9.41784C10.7905 9.17976 11.3415 9 12 9V7C11.0017 7 10.0526 7.26796 9.32398 7.75374C8.6023 8.23485 8 9.00933 8 10H10ZM12 9C12.9038 9 13.563 9.33231 13.8434 9.65538L15.354 8.34462C14.5969 7.47209 13.3171 7 12 7V9ZM12 13C12.6585 13 13.2095 13.1798 13.5666 13.4178C13.9308 13.6606 14 13.8861 14 14H16C16 13.0093 15.3977 12.2348 14.676 11.7537C13.9474 11.268 12.9983 11 12 11V13ZM11 7V8H13V7H11ZM11 16L11 17L13 17L13 16L11 16ZM12 15C11.0962 15 10.437 14.6677 10.1567 14.3446L8.64607 15.6554C9.40317 16.5279 10.683 17 12 17L12 15ZM14 14C14 14.1139 13.9308 14.3394 13.5666 14.5822C13.2095 14.8202 12.6586 15 12 15V17C12.9983 17 13.9474 16.732 14.676 16.2463C15.3977 15.7651 16 14.9907 16 14H14ZM11 8L11 16L13 16L13 8L11 8Z" fill="currentColor"/>
            </svg>',
            'permissions' => [
                Access::SP_PAYMENT_ACCESS->value,
            ],
        ],
        [
            'label' => 'Warning Log',
            'url' => '/students/warning-log',
            'mainmenu' => ['profile', 'dashboard', 'oshc', 'document', 'material', 'attendance', 'result', 'timetable', 'payment', 'log', 'communication', 'elearning'],
            'activeurls' => ['warning-log'],
            'svgicon' => '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
	<path fill="currentColor" d="M13 17a.999.999 0 1 0-1.998 0a.999.999 0 0 0 1.997 0m-.26-7.853a.75.75 0 0 0-1.493.103l.004 4.501l.007.102a.75.75 0 0 0 1.493-.103l-.004-4.502zm1.23-5.488c-.857-1.548-3.082-1.548-3.938 0L2.286 17.66c-.83 1.5.255 3.34 1.97 3.34h15.49c1.714 0 2.799-1.84 1.969-3.34zm-2.626.726a.75.75 0 0 1 1.313 0l7.746 14.002a.75.75 0 0 1-.657 1.113H4.256a.75.75 0 0 1-.657-1.113z" />
</svg>',
            'permissions' => [
                Access::SP_WARNING_LOG_ACCESS->value,
            ],
        ],
        [
            'label' => 'Communicatioin',
            'url' => '#',
            'mainmenu' => ['profile', 'dashboard', 'oshc', 'document', 'material', 'attendance', 'result', 'timetable', 'payment', 'log', 'communication', 'elearning'],
            'activeurls' => ['email-feedback', 'email-to-trainer', 'email-communication-log'],
            'svgicon' => '<svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>',
            'sub_menu' => [
                [
                    'label' => 'Email Feedback',
                    'url' => '/students/email-feedback',
                    'permissions' => [
                        Access::SP_EMAIL_FEEDBACK_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Email To Trainer',
                    'url' => '/students/email-to-trainer',
                    'permissions' => [
                        Access::SP_EMAIL_TO_TRAINER_ACCESS->value,
                    ],
                ],
                [
                    'label' => 'Email Communication Log',
                    'url' => '/students/email-communication-log',
                    'permissions' => [
                        Access::SP_EMAIL_COMMUNICATION_LOG_ACCESS->value,
                    ],
                ],
            ],
        ],
        [
            'label' => 'Elearning Link',
            'url' => '/students/view-student-elearning-link-list',
            'mainmenu' => ['profile', 'dashboard', 'oshc', 'document', 'material', 'attendance', 'result', 'timetable', 'payment', 'log', 'communication', 'elearning'],
            'activeurls' => ['view-student-elearning-link-list'],
            'svgicon' => '<svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                        </svg>',
            'permissions' => [
                // Access::SP_ELEAR->value
            ],
        ],
        [
            'label' => 'Edit Profile',
            'url' => '/profile-manage',
            'mainmenu' => ['profile', 'dashboard', 'oshc', 'document', 'material', 'attendance', 'result', 'timetable', 'payment', 'log', 'communication', 'elearning'],
            'activeurls' => ['student_profile'],
            'svgicon' => '<svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>',
            'permissions' => [
                Access::SP_EDIT_PROFILE_ACCESS->value,
            ],
        ],
    ];
}
