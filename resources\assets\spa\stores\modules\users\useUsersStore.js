import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref, computed } from 'vue';
import _ from 'lodash';
import useConfirm from '@spa/services/useConfirm.js';
import { router } from '@inertiajs/vue3';
export const useUsersStore = defineStore('useUsersStore', () => {
    const storeUrl = ref('v2/tenant/users');
    const commonStoreProps = useCommonStore(storeUrl.value);
    const { selected, formDialog, excluded, allSelected, contextLoading } = commonStoreProps;

    const actionDialog = ref(false);
    const bulkAssign = ref(false);

    const userType = ref('staff');
    const statusCounts = ref({});
    const confirm = useConfirm();

    const selectedIds = computed(() => {
        return _.map(selected.value, (item) => {
            return item?.id;
        });
    });

    const excludedIds = computed(() => {
        return _.map(excluded.value, (item) => {
            return item?.id;
        });
    });

    const payload = ref({
        send_invite_to_all: true,
        skip_users_with_access: false,
        re_enable_users: false,
    });

    const onResetPassword = async (payload) => {
        let data = {
            ids: selectedIds.value,
            user_type: userType.value,
        };
        try {
            contextLoading('reset-password', true);
            return await $http.post('api/v2/tenant/users/reset-password', data);
        } catch (e) {
            return null;
        } finally {
            contextLoading('reset-password', false);
        }
    };

    const onDisableUser = async () => {
        let data = {
            ids: selectedIds.value,
            user_type: userType.value,
        };
        try {
            contextLoading('disable-user', true);
            const response = await $http.post('api/v2/tenant/users/disable', data);
            actionDialog.value = false;
            selected.value = [];
            return response;
        } catch (e) {
            return null;
        } finally {
            contextLoading('disable-user', false);
        }
    };

    const onEnableUser = async () => {
        let data = {
            ids: selectedIds.value,
            user_type: userType.value,
        };

        try {
            contextLoading('enable-user', true);
            const response = await $http.post('api/v2/tenant/users/enable', data);
            actionDialog.value = false;
            selected.value = [];
            return response;
        } catch (e) {
            return null;
        } finally {
            contextLoading('enable-user', false);
        }
    };

    const onSendInvite = async () => {
        let data = {
            ids: selectedIds.value,
            user_type: userType.value,
            ...payload.value,
        };
        try {
            contextLoading('send-invite', true);
            const response = await $http.post('api/v2/tenant/users/send-invite', data);
            actionDialog.value = false;
            selected.value = [];
            return response;
        } catch (e) {
            return null;
        } finally {
            contextLoading('send-invite', false);
        }
    };
    const onAssignRoles = async (roles) => {
        let data = {
            ids: selectedIds.value,
            user_type: userType.value,
            ...roles,
        };
        try {
            contextLoading('assign-roles', true);
            const response = await $http.post('/api/v2/tenant/users/assign-roles', data);
            formDialog.value = false;
            return response;
        } catch (e) {
            console.log('error', e);
            return null;
        } finally {
            contextLoading('assign-roles', false);
        }
    };

    const handleBulkActions = async (action) => {
        try {
            contextLoading('bulk-actions', true);
            const response = await $http.post('/api/v2/tenant/users/bulk-actions', {
                action,
                type: allSelected.value ? 'all' : 'selective',
                selectedIds: selectedIds.value,
                user_type: userType.value,
                excludeIds: excludedIds.value || [],
                ...(action === 'send_invite' ? payload.value : {}),
            });
            actionDialog.value = false;
            selected.value = [];
            return response;
        } catch (e) {
            return null;
        } finally {
            contextLoading('bulk-actions', false);
        }
    };

    const getUserMeta = async (userType) => {
        try {
            contextLoading('user-meta', true);
            const response = await $http.get(`api/v2/tenant/users/${userType}/user-meta`);
            if (response?.success) {
                statusCounts.value = response?.data.counts;
            }
        } catch (e) {
            return null;
        } finally {
            contextLoading('user-meta', false);
        }
    };

    const forceLogout = async (payload) => {
        confirm.require({
            message: 'Are you sure you want to force logout this user?',
            header: 'Force Logout User?',
            icon: 'pi pi-exclamation-triangle',
            accept: async () => {
                try {
                    contextLoading('force-logout', true);
                    const response = await $http.post(
                        `api/v2/tenant/user/${payload.id}/force-logout`
                    );
                    return response;
                } catch (e) {
                    return null;
                } finally {
                    contextLoading('force-logout', false);
                }
            },
            reject: () => {
                return false;
            },
            onHide: () => {
                return false;
            },
        });
    };

    const updateUserStatus = async (payload) => {
        try {
            contextLoading('update-status', true);
            const response = await $http.post('api/v2/tenant/users/update-status', payload);
            return response;
        } catch (e) {
            return null;
        } finally {
            contextLoading('update-status', false);
        }
    };

    const getAlreadyAssignRoles = async (payload) => {
        try {
            contextLoading('get-already-assign-roles', true);
            const response = await $http.get(
                `api/v2/tenant/users/${payload.id}/get-already-assign-roles`
            );
            return response;
        } catch (e) {
            return null;
        } finally {
            contextLoading('get-already-assign-roles', false);
        }
    };

    return {
        ...commonStoreProps,
        actionDialog,
        bulkAssign,
        userType,
        statusCounts,
        onResetPassword,
        onDisableUser,
        onEnableUser,
        onSendInvite,
        onAssignRoles,
        handleBulkActions,
        getUserMeta,
        forceLogout,
        updateUserStatus,
        getAlreadyAssignRoles,
        payload,
    };
});
