<template>
    <div class="space-y-4">
        <Card
            :pt="{
                root: 'p-4 lg:p-6 rounded-lg',
            }"
        >
            <template #content>
                <div class="flex items-center justify-between gap-4">
                    <HelpTitle
                        :title="'Import Results'"
                        :pt="{ title: 'text-base' }"
                        :popup-align="{ horizontal: 'left' }"
                        :anchor-align="{ horizontal: 'right' }"
                    >
                        <template #content>
                            <div
                                class="max-h-[600px] max-w-2xl overflow-auto p-4 text-sm text-gray-600"
                            >
                                <p class="mb-4 font-medium">
                                    Upload a CSV file with the following headers:
                                </p>

                                <div class="space-y-4">
                                    <div class="rounded-md bg-blue-50 p-3">
                                        <h4 class="mb-2 font-medium text-blue-800">
                                            Required Fields (All Course Types)
                                        </h4>
                                        <ul class="list-disc space-y-2 pl-5">
                                            <li>
                                                <strong>StudentId</strong> - Unique identifier for
                                                each student
                                                <div class="mt-1 text-xs text-gray-500">
                                                    Example: STU001, STU002
                                                </div>
                                            </li>
                                            <li>
                                                <strong>CourseType</strong> - Type of course the
                                                student is enrolled in
                                                <div class="mt-1 text-xs text-gray-500">
                                                    Accepted values: "HigherEd" or "VET"
                                                </div>
                                            </li>
                                            <li>
                                                <strong>UnitId</strong> - Unit code being assessed
                                                <div class="mt-1 text-xs text-gray-500">
                                                    Example: SUB001, SUB002
                                                </div>
                                            </li>
                                        </ul>
                                    </div>

                                    <div class="rounded-md bg-green-50 p-3">
                                        <h4 class="mb-2 font-medium text-green-800">
                                            For HigherEd Courses
                                        </h4>
                                        <ul class="list-disc space-y-2 pl-5">
                                            <li>
                                                <strong>TotalMark</strong> - Overall numerical score
                                                for the unit (Required)
                                                <div class="mt-1 text-xs text-gray-500">
                                                    Example: 85, 75 (numerical values)
                                                </div>
                                            </li>
                                            <li>
                                                <strong>Assessment_*</strong> - Individual
                                                assessment scores
                                                <div class="mt-1 text-xs text-gray-500">
                                                    Example: Assessment_1: 28, Assessment_2: 27,
                                                    Assessment_3: 30
                                                </div>
                                            </li>
                                        </ul>
                                    </div>

                                    <div class="rounded-md bg-orange-50 p-3">
                                        <h4 class="mb-2 font-medium text-orange-800">
                                            For VET Courses
                                        </h4>
                                        <ul class="list-disc space-y-2 pl-5">
                                            <li>
                                                <strong>FinalOutcome</strong> - Final competency
                                                outcome (Required)
                                                <div class="mt-1 text-xs text-gray-500">
                                                    Accepted values: C (Competent), NYC (Not Yet
                                                    Competent), CT (Credit Transfer), RPL
                                                    (Recognition of Prior Learning)
                                                </div>
                                            </li>
                                            <li>
                                                <strong>Assessment_*</strong> - Individual
                                                assessment outcomes
                                                <div class="mt-1 text-xs text-gray-500">
                                                    Example: Assessment_1: S (Satisfactory),
                                                    Assessment_2: S, Assessment_3: NYS (Not Yet
                                                    Satisfactory)
                                                </div>
                                            </li>
                                        </ul>
                                    </div>

                                    <div class="rounded-md bg-gray-50 p-3">
                                        <h4 class="mb-2 font-medium text-gray-800">
                                            Assessment Fields
                                        </h4>
                                        <div class="text-xs text-gray-600">
                                            <strong>Note:</strong>
                                            <ul class="list-disc space-y-2 pl-5">
                                                <li>
                                                    In the sample CSV file, the columns named
                                                    Assessment 1, Assessment 2, and Assessment 3
                                                    should be replaced with the actual names of the
                                                    assessments.
                                                </li>
                                                <li>
                                                    Assessment fields are dynamic - you can include
                                                    as many as needed (Assessment_1, Assessment_2,
                                                    Assessment_3, etc.).
                                                </li>
                                                <li>
                                                    The content depends on the course type:
                                                    numerical scores for HigherEd, competency
                                                    outcomes for VET.
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </HelpTitle>
                    <Button
                        variant="ghost"
                        class="flex min-w-32 cursor-pointer justify-start px-0"
                        size="xs"
                        @click="handleReset"
                    >
                        <icon
                            :name="'sync'"
                            :fill="'#1890FF'"
                            :className="reset ? 'animate-spin' : ''"
                        />
                        <span> Reset Filters </span>
                    </Button>
                </div>
                <div class="grid gap-6 lg:grid-cols-3">
                    <FilterBlockWrapper label="Status">
                        <EnumSelect
                            className="tw-w-full"
                            enum-class="GalaxyAPI\Enums\ResultImportStatusEnum"
                            v-model="filterValues.status"
                            :has-select-all="true"
                            :placeholder="'Select Status'"
                        />
                    </FilterBlockWrapper>
                </div>
            </template>
        </Card>

        <div class="flex flex-wrap items-center justify-between gap-2">
            <div>
                <search-input
                    v-model.lazy="filterValues.search"
                    :pt="{ root: 'w-64 h-9' }"
                    placeholder="Search by Student ID/Subject ID"
                    :debounce="300"
                    :autocomplete="'off'"
                />
            </div>
            <div class="flex flex-wrap items-center gap-3">
                <Popover
                    :popup-align="{ horizontal: 'center', vertical: 'top' }"
                    :anchor-align="{ horizontal: 'center', vertical: 'bottom' }"
                >
                    <template #picker>
                        <Button variant="secondary" class="h-9" :loading="refreshing">
                            <file-icon name="csv" class="h-6 w-6 text-gray-500" />
                            <span>Download Sample</span>
                            <icon
                                name="chevron-down"
                                width="16"
                                height="16"
                                :fill="'currentColor'"
                            />
                        </Button>
                    </template>
                    <template #popup>
                        <div class="w-52 py-1 text-sm text-gray-600">
                            <button
                                variant="secondary"
                                class="flex w-full items-center px-3 py-2 hover:bg-gray-100"
                                @click="downloadSample('highered')"
                            >
                                <span>HigherEd Sample</span>
                            </button>
                            <button
                                variant="secondary"
                                class="flex w-full items-center px-3 py-2 hover:bg-gray-100"
                                @click="downloadSample('vet')"
                            >
                                <span>VET Sample</span>
                            </button>
                        </div>
                    </template>
                </Popover>

                <Button variant="primary" class="h-9" @click="openFileUpload">
                    <icon :name="'upload'" :fill="'currentColor'" :width="'16'" :height="'16'" />
                    <span>Upload CSV</span>
                </Button>
                <Button
                    variant="secondary"
                    class="h-9"
                    @click="toggleAutoRefresh"
                    :class="{ 'border-green-500 bg-green-100': autoRefreshEnabled }"
                >
                    <icon
                        :name="autoRefreshEnabled ? 'pause' : 'play'"
                        :fill="'currentColor'"
                        :width="'16'"
                        :height="'16'"
                    />
                    <span>{{
                        autoRefreshEnabled ? 'Stop Auto Refresh' : 'Start Auto Refresh'
                    }}</span>
                </Button>
            </div>
        </div>
    </div>
</template>

<script>
import { debounce } from 'lodash';
import IconInput from '@spa/components/IconInput.vue';
import Button from '@spa/components/Buttons/Button.vue';
import Card from '@spa/components/Card/Card.vue';
import HelpTitle from '@spa/components/Card/HelpTitle.vue';
import HighlightBox from '@spa/components/HighlightBox/HighlightBox.vue';
import Popover from '@spa/components/Popover/Popover.vue';
import EnumSelect from '@spa/components/AsyncComponents/Select/EnumSelect.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';

export default {
    components: {
        FilterBlockWrapper,
        EnumSelect,
        Popover,
        'search-input': IconInput,
        Button,
        Card,
        HelpTitle,
        HighlightBox,
    },
    props: {
        sortDirection: { type: String, default: 'desc' },
        refreshing: { type: Boolean, default: false },
        autoRefreshEnabled: { type: Boolean, default: false },
    },
    data() {
        return {
            filterValues: {
                search: '',
                status: '',
            },
            debouncedEmitFilter: debounce(function (newval) {
                if (newval.search === '') {
                    newval.search = null;
                }
                if (newval.status === '') {
                    newval.status = null;
                }
                this.$emit('filter', newval);
            }, 300), // 300ms debounce delay
            reset: false,
        };
    },
    watch: {
        'filterValues.search': function () {
            this.debouncedEmitFilter(this.filterValues);
        },
        'filterValues.status': function () {
            console.log('status changed');
            this.debouncedEmitFilter(this.filterValues);
        },
    },
    methods: {
        downloadSample(type) {
            this.$emit('download-sample', type);
        },
        openFileUpload() {
            this.$emit('open-file-upload');
        },
        toggleSortDirection() {
            this.$emit('toggle-sort');
        },
        refreshData() {
            this.$emit('refresh');
        },
        toggleAutoRefresh() {
            this.$emit('toggle-auto-refresh');
        },
        handleReset() {
            this.reset = true;
            setTimeout(() => {
                this.reset = false;
            }, 500);
            this.filterValues.search = '';
            this.filterValues.status = '';
        },
    },
};
</script>
