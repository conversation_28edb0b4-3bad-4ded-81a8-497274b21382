<template>
    <div class="kendo-modal-wrapper">
        <Dialog
            v-if="visible"
            :title="prefillWithDataItem ? 'Edit Template' : title"
            :width="470"
            @close="handleClose"
            dialog-class="tw-content-p-0 custom-modal-wrapper"
        >
            <div class="modal-content">
                <div class="">
                    <div class="px-6 pt-4" v-if="!prefillWithDataItem">
                        <HighlightBox v-if="showInfoBox" :variant="'info'" :pt="{ root: 'p-2' }">
                            <template #icon>
                                <icon name="info-help" />
                            </template>
                            <template #content>
                                This will create a new copy of the
                                {{ isStudentId ? 'student ID' : 'certificate' }}.
                            </template>
                        </HighlightBox>
                    </div>

                    <Form
                        ref="formRef"
                        @submit="handleSubmit"
                        :initial-values="formInitialValues"
                        id="formRef"
                        :ignoreModified="true"
                    >
                        <FormElement class="space-y-4 pt-4">
                            <fieldset class="k-form-fieldset">
                                <div class="space-y-4 px-6">
                                    <Field
                                        :id="'name'"
                                        :name="'name'"
                                        :label="`Template Name:`"
                                        :component="'roleTemplate'"
                                        :validator="requiredtrue"
                                        :pt="{
                                            root: 'flex items-center gap-5',
                                            input: 'w-full',
                                        }"
                                    >
                                        <template #roleTemplate="{ props }">
                                            <FormInput
                                                v-bind="props"
                                                @change="props.onChange"
                                                @blur="props.onBlur"
                                                @focus="props.onFocus"
                                            />
                                        </template>
                                    </Field>

                                    <!-- Only show certificate number format field for certificates -->
                                    <Field
                                        v-if="!isStudentId"
                                        :id="'certificate_number_formate_id'"
                                        :name="'certificate_number_formate_id'"
                                        :label="'Certificate Number Format:'"
                                        :data-items="certificateIdFormate"
                                        :default-item="{
                                            id: '',
                                            name: 'Select Certificate number format',
                                        }"
                                        :text-field="'name'"
                                        :data-item-key="'id'"
                                        :value-field="'id'"
                                        :valuePrimitive="true"
                                        :validator="requiredtrue"
                                        :component="'CertificateNumberFormat'"
                                        :popup-settings="popupSettings"
                                        :pt="{
                                            root: 'flex items-center gap-5',
                                            input: 'w-full',
                                        }"
                                    >
                                        <template #CertificateNumberFormat="{ props }">
                                            <FormDropDown
                                                v-bind="props"
                                                @change="props.onChange"
                                                @blur="props.onBlur"
                                            >
                                            </FormDropDown>
                                        </template>
                                    </Field>
                                </div>
                            </fieldset>
                            <div
                                class="sticky bottom-0 flex w-full items-center justify-end gap-4 bg-white px-6 py-4 shadow-inner-top"
                            >
                                <Button :variant="'secondary'" size="sm" @click="handleClose"
                                    ><span>Cancel</span></Button
                                >
                                <Button
                                    type="submit"
                                    size="sm"
                                    variant="primary"
                                    class="min-w-[100px]"
                                    :loading="loaderStore.contextLoaders['button']"
                                    loadingText="Saving..."
                                    >{{ prefillWithDataItem ? 'Update' : 'Save' }}
                                </Button>
                            </div>
                        </FormElement>
                    </Form>
                </div>
            </div>
        </Dialog>
    </div>
</template>

<script setup>
import { ref, watch, computed, onMounted, nextTick } from 'vue';
import { Dialog, DialogActionsBar } from '@progress/kendo-vue-dialogs';
import Button from '@spa/components/Buttons/Button.vue';
import { Form, FormElement, Field } from '@progress/kendo-vue-form';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import FormCheckbox from '@spa/components/KendoInputs/FormCheckbox.vue';
import { useCertificateStore } from '@spa/stores/modules/certificate.store';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import { getCurrentInstance } from 'vue';
import HighlightBox from '@spa/components/HighlightBox/HighlightBox.vue';

const { proxy } = getCurrentInstance(); // access $refs

const store = useCertificateStore();
const loaderStore = useLoaderStore();

const certificateType = [
    {
        id: 1,
        name: 'Certificate',
    },
    {
        id: 2,
        name: 'Duplicate Certificate',
    },
    {
        id: 3,
        name: 'Statement of Attainment',
    },
    {
        id: 4,
        name: 'Intrim Transcript',
    },
    {
        id: 5,
        name: 'Record of Result',
    },
];

const popupSettings = {
    animate: false,
    offset: { left: 150, top: 50 },
};

const props = defineProps({
    visible: {
        type: Boolean,
        required: true,
    },
    // Optional legacy props to keep backward compatibility
    decryptItParentId: {
        type: String,
        required: false,
        default: '',
    },
    formData: {
        type: Object,
        required: false,
        default: () => ({}),
    },
    dataItem: {
        type: Object,
        default: () => ({}),
    },
    certificateIdFormate: {
        type: [Array, Object],
        default: () => [],
    },
    isStudentId: {
        type: Boolean,
        default: false,
    },
    title: {
        type: String,
        default: 'Save As New',
    },
    showInfoBox: {
        type: Boolean,
        default: true,
    },
    prefillWithDataItem: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['close', 'submit']);

const handleSubmit = (formData) => {
    // Remove certificate_number_formate_id from formData if isStudentId is true
    if (props.isStudentId && formData.certificate_number_formate_id) {
        const { certificate_number_formate_id, ...cleanFormData } = formData;
        emit('submit', cleanFormData);
    } else {
        emit('submit', formData);
    }
};

const handleSaveAndPreview = async (formData) => {
    const formRef = proxy.$refs.formRef;
    console.log('formRef', formRef.form);

    if (formRef) {
        if (formRef.form.valid) {
            const formData = formRef.form.values; // get values
            console.log('formData (preview)', formData);

            // Remove certificate_number_formate_id from formData if isStudentId is true
            if (props.isStudentId && formData.certificate_number_formate_id) {
                const { certificate_number_formate_id, ...cleanFormData } = formData;
                emit('submitPreview', cleanFormData);
            } else {
                emit('submitPreview', formData);
            }
        } else {
            formRef.form.validate();
            console.warn('Form is invalid. Fix errors first!');
            // You can also show a toast, error message, etc.
        }
    }
};

const handleClose = () => {
    emit('close');
};

// Computed property for initial values based on isStudentId
const formInitialValues = computed(() => {
    const baseValues = {
        name: '',
        certificate_type: 0,
        template_type: props.isStudentId ? 'student-id' : 'certificate',
        is_default: 0,
    };

    // Only include certificate_number_formate_id for certificates (not student IDs)
    if (!props.isStudentId) {
        baseValues.certificate_number_formate_id = 0;
    }

    // If dataItem is provided, use its values
    if (props.dataItem) {
        const { name = '', type = 0, certificate_number_formate_id = 0 } = props.dataItem;
        baseValues.name = props.prefillWithDataItem ? name : '';
        baseValues.certificate_type = parseInt(type);

        if (!props.isStudentId) {
            baseValues.certificate_number_formate_id = parseInt(certificate_number_formate_id);
        }
    }

    return baseValues;
});
</script>

<style lang=""></style>
