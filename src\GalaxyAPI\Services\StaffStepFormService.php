<?php

namespace GalaxyAPI\Services;

use App\Enums\StaffQualificationTypeEnum;
use App\Model\Users;
use App\Model\v2\Staff;
use App\StaffEmploymentHistory;
use App\StaffProfessionalDevelopmentHistory;
use App\StaffQualification;
use GalaxyAPI\Enums\UserStatusEnum;
use GalaxyAPI\Interfaces\UserTypeStepFormInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class StaffStepFormService implements UserTypeStepFormInterface
{
    public function __construct(
        private readonly UserService $userService,
    ) {}

    public function map(string $step, array $data, Model $staff): Model
    {
        if ($step === 'review') {
            return $staff;
        }

        /** @var Staff $staff */
        return match ($step) {
            'user_info' => $this->saveUserInfoStep($staff, $data),
            'emergency_contact' => $this->saveEmergencyContactStep($staff, $data),
            'address_postal' => $this->saveAddressPostalStep($staff, $data),
            'employment_info' => $this->saveEmploymentInfoStep($staff, $data),
            'qualifications' => $this->saveQualificationsStep($staff, $data),
            'personal_development' => $this->savePersonalDevelopmentStep($staff, $data),
            'bank_details' => $this->saveBankDetailsStep($staff, $data),
            'tcsi_report' => $this->saveTCSIReportStep($staff, $data),
            'upload_docs' => $this->saveDocuments($staff, $data),
            default => throw new \InvalidArgumentException("Unsupported step: $step for Staff"),
        };
    }

    private function saveUserInfoStep(Staff $staff, array $data)
    {
        $user = null;
        $collegeId = Auth::user()->college_id;
        if (! $staff->user_id && isset($data['email'])) {
            $user = Users::where('email', $data['email'])->first();
            if (! $user) {
                $userData = [
                    'college_id' => $collegeId,
                    'name' => trim(($data['first_name'] ?? '').' '.($data['last_name'] ?? '')),
                    'username' => $data['email'],
                    'email' => $data['email'],
                    'phone' => $data['phone'] ?? null,
                    'mobile' => $data['mobile'] ?? null,
                    'role_id' => 10,
                    'password' => Hash::make(Str::random(12)),
                    'status' => UserStatusEnum::PENDING->value,
                ];

                $user = Users::create($userData);
            }
            $staff->user_id = $user->id;
        }
        $file = $data['user_image'] ?? null;
        // Only upload if it's a new file (UploadedFile instance), not an existing URL string
        if ($file && $staff->user_id && $file instanceof \Illuminate\Http\UploadedFile) {
            $path = $this->userService->uploadProfile($staff->user_id, $file);
            // Update the user's profile picture
            if ($path) {
                $userToUpdate = $user ?? Users::find($staff->user_id);
                if ($userToUpdate) {
                    $userToUpdate->update(['profile_picture' => $path]);
                }
            }
        }
        $staff->fill([
            'name_title' => $data['name_title'] ?? null,
            'first_name' => $data['first_name'] ?? null,
            'last_name' => $data['last_name'] ?? null,
            'personal_email' => $data['email2'] ?? null,
            'email' => $data['email'] ?? null,
            'phone' => $data['phone'] ?? null,
            'mobile' => $data['mobile'] ?? null,
            'gender' => $data['gender'] ?? null,
            'birth_date' => $data['birth_date'] ?? null,
            'country' => $data['country'] ?? null,
            'staff_type' => $data['user_type'] === 'staff' ? 'Staff' : 'Staff-Teacher',
            'is_active' => 1,

            'emergency_contact_name' => $data['emergency_contact_name'] ?? null,
            'emergency_contact_relationship' => $data['emergency_contact_relationship'] ?? null,
            'emergency_phone_1' => $data['emergency_phone_1'] ?? null,
            'emergency_phone_2' => $data['emergency_phone_2'] ?? null,
            'emergency_email' => $data['emergency_email'] ?? null,
            'emergency_address' => $data['emergency_address'] ?? null,
        ]);

        if (! $staff->college_id) {
            $staff->college_id = auth()->user()->college_id ?? 1;
        }

        if (! $staff->exists) {
            $staff->created_by = auth()->id();
        }

        $staff->updated_by = auth()->id();

        return $staff;
    }

    private function saveAddressPostalStep(Staff $staff, array $data)
    {
        $staff->fill([
            'residential_address' => $data['residential_address'] ?? null,
            'residential_country' => $data['residential_country'] ?? null,
            'residential_state' => $data['residential_state'] ?? null,
            'residential_city' => $data['residential_city'] ?? null,
            'residential_postcode' => $data['residential_postcode'] ?? null,
            'residential_abn' => $data['residential_abn'] ?? null,
            'is_post_residential_same' => $data['is_post_residential_same'] ?? null,

            'postal_address' => $data['postal_address'] ?? null,
            'postal_country' => $data['postal_country'] ?? null,
            'postal_state' => $data['postal_state'] ?? null,
            'postal_city' => $data['postal_city'] ?? null,
            'postal_postcode' => $data['postal_postcode'] ?? null,
            'postal_abn' => $data['postal_abn'] ?? null,
            'postal_address_type' => $data['postal_address_type'] ?? null,
        ]);

        $staff->updated_by = auth()->id();

        return $staff;

    }

    private function saveEmploymentInfoStep(Staff $staff, array $data)
    {
        $staff->fill([
            'staff_number' => $data['staff_number'] ?? null,
            'employment_type' => $data['employment_type'] ?? null,
            'joined_on' => $data['joined_on'] ?? null,
            'campus_location' => $data['campus_location'] ?? null,
            'position' => $data['job_position'] ?? null,
            'line_manager' => $data['line_manager'] ?? null,
        ]);
        $staff->updated_by = auth()->id();
        $staff->save();

        if (isset($data['employment_histories']) && is_array($data['employment_histories'])) {
            foreach ($data['employment_histories'] as $history) {
                $staffEmploymentHistory = @$history['id'] ? StaffEmploymentHistory::find(@$history['id']) : null;
                if ($staffEmploymentHistory) {
                    $staffEmploymentHistory->update([
                        'title' => $history['title'] ?? null,
                        'organization' => $history['organization'] ?? null,
                        'start_date' => $history['start_date'] ?? null,
                        'end_date' => $history['end_date'] ?? null,
                    ]);

                } else {
                    $staffEmploymentHistory = StaffEmploymentHistory::create([
                        'staff_id' => $staff->id,
                        'title' => $history['title'] ?? null,
                        'organization' => $history['organization'] ?? null,
                        'start_date' => $history['start_date'] ?? null,
                        'end_date' => $history['end_date'] ?? null,
                    ]);
                }
                $file = $history['document'] ?? null;
                // Only upload if it's a new file, not an existing URL string
                if ($file && $file instanceof \Illuminate\Http\UploadedFile) {
                    $staffEmploymentHistory->update([
                        'document_path' => $this->userService?->uploadUserFile($file),
                    ]);
                }
            }
        }

        return $staff;
    }

    private function saveQualificationsStep(Staff $staff, array $data)
    {
        $staff->updated_by = auth()->id();
        $collegeId = Auth::user()->college_id;

        // Save after updating staff record to ensure we have an ID
        $staff->save();

        // Save education qualifications
        if (isset($data['education_qualifications']) && is_array($data['education_qualifications'])) {
            foreach ($data['education_qualifications'] as $qualification) {
                $staffQualification = @$qualification['id'] ? StaffQualification::find(@$qualification['id']) : null;
                if ($staffQualification) {
                    $staffQualification->update([
                        'type' => StaffQualificationTypeEnum::educational,
                        'code' => $qualification['code'] ?? null,
                        'provider' => $qualification['provider'] ?? null,
                        'name' => $qualification['name'] ?? null,
                        'start_date' => $qualification['start_date'] ?? null,
                        'end_date' => $qualification['end_date'] ?? null,
                    ]);
                } else {
                    $staffQualification = StaffQualification::create([
                        'staff_id' => $staff->id,
                        'type' => StaffQualificationTypeEnum::educational,
                        'code' => $qualification['code'] ?? null,
                        'provider' => $qualification['provider'] ?? null,
                        'name' => $qualification['name'] ?? null,
                        'start_date' => $qualification['start_date'] ?? null,
                        'end_date' => $qualification['end_date'] ?? null,
                    ]);
                }
                $file = $qualification['document'] ?? null;
                // Only upload if it's a new file, not an existing URL string
                if ($file && $file instanceof \Illuminate\Http\UploadedFile) {
                    $staffQualification->update([
                        'document_path' => $this->userService?->uploadUserFile($file),
                    ]);
                }
            }
        }

        // Save training qualifications
        if (isset($data['training_qualifications']) && is_array($data['training_qualifications'])) {
            foreach ($data['training_qualifications'] as $qualification) {
                $staffQualification = @$qualification['id'] ? StaffQualification::find(@$qualification['id']) : null;
                if ($staffQualification) {
                    $staffQualification->update([
                        'type' => StaffQualificationTypeEnum::training,
                        'code' => $qualification['code'] ?? null,
                        'provider' => $qualification['provider'] ?? null,
                        'name' => $qualification['name'] ?? null,
                        'start_date' => $qualification['start_date'] ?? null,
                        'end_date' => $qualification['end_date'] ?? null,
                    ]);
                } else {
                    $staffQualification = StaffQualification::create([
                        'staff_id' => $staff->id,
                        'type' => StaffQualificationTypeEnum::training,
                        'code' => $qualification['code'] ?? null,
                        'provider' => $qualification['provider'] ?? null,
                        'name' => $qualification['name'] ?? null,
                        'start_date' => $qualification['start_date'] ?? null,
                        'end_date' => $qualification['end_date'] ?? null,
                    ]);
                }
                $file = $qualification['document'] ?? null;
                // Only upload if it's a new file, not an existing URL string
                if ($file && $file instanceof \Illuminate\Http\UploadedFile) {
                    $staffQualification->update([
                        'document_path' => $this->userService?->uploadUserFile($file),
                    ]);
                }

            }
        }

        return $staff;
    }

    private function savePersonalDevelopmentStep(Staff $staff, array $data)
    {
        $staff->updated_by = auth()->id();
        $staff->save();

        // Save professional development history
        if (isset($data['professional_developments']) && is_array($data['professional_developments'])) {
            foreach ($data['professional_developments'] as $history) {
                $staffProfessionalDevelopmentHistory = @$history['id'] ? StaffProfessionalDevelopmentHistory::find(@$history['id']) : null;
                if ($staffProfessionalDevelopmentHistory) {
                    $staffProfessionalDevelopmentHistory->update([
                        'event_name' => $history['event_name'] ?? null,
                        'organized_by' => $history['organized_by'] ?? null,
                        'activity_name' => $history['activity_name'] ?? null,
                        'event_from' => $history['event_from'] ?? null,
                        'event_to' => $history['event_to'] ?? null,
                        'cpd_points' => $history['cpd_points'] ?? null,
                        'comments' => $history['comments'] ?? null,
                    ]);
                } else {
                    $staffProfessionalDevelopmentHistory = StaffProfessionalDevelopmentHistory::create([
                        'staff_id' => $staff->id,
                        'event_name' => $history['event_name'] ?? null,
                        'organized_by' => $history['organized_by'] ?? null,
                        'activity_name' => $history['activity_name'] ?? null,
                        'event_from' => $history['event_from'] ?? null,
                        'event_to' => $history['event_to'] ?? null,
                        'cpd_points' => $history['cpd_points'] ?? null,
                        'comments' => $history['comments'] ?? null,
                    ]);
                }
                $file = $history['document'] ?? null;
                // Only upload if it's a new file, not an existing URL string
                if ($file && $file instanceof \Illuminate\Http\UploadedFile) {
                    $staffProfessionalDevelopmentHistory->update([
                        'document' => $this->userService?->uploadUserFile($file),
                    ]);
                }

            }
        }

        return $staff;
    }

    private function saveTCSIReportStep(Staff $staff, array $data)
    {
        $staff->fill([
            'tcsi_not_applicable' => $data['not_applicable'] ?? false,
            'birth_date' => $data['birth_date'] ?? null,
            'gender' => $data['gender'] ?? $staff->gender,
            'joining_date' => $data['joining_date'] ?? $staff->joining_date,
            'atsi_code' => isset($data['atsi_code']['code']) ? $data['atsi_code']['code'] : ($data['atsi_code'] ?? null),
            'highest_qualification_code' => isset($data['highest_qualification_code']['code']) ? $data['highest_qualification_code']['code'] : ($data['highest_qualification_code'] ?? null),
            'highest_qualification_place_code' => isset($data['highest_qualification_place_code']['code']) ? $data['highest_qualification_place_code']['code'] : ($data['highest_qualification_place_code'] ?? null),
            'work_contract_code' => isset($data['work_contract_code']['code']) ? $data['work_contract_code']['code'] : ($data['work_contract_code'] ?? null),
            'staff_work_level_code' => isset($data['staff_work_level_code']['code']) ? $data['staff_work_level_code']['code'] : ($data['staff_work_level_code'] ?? null),
            'organisational_unit_code' => isset($data['organisational_unit_code']['code']) ? $data['organisational_unit_code']['code'] : ($data['organisational_unit_code'] ?? null),
            'work_sector_code' => isset($data['work_sector_code']['code']) ? $data['work_sector_code']['code'] : ($data['work_sector_code'] ?? null),
            'function_code' => isset($data['function_code']['code']) ? $data['function_code']['code'] : ($data['function_code'] ?? null),
        ]);

        $staff->updated_by = auth()->id();

        return $staff;
    }

    private function saveBankDetailsStep(Staff $staff, array $data)
    {
        $staff->updated_by = auth()->id();
        $staff->fill([
            'bank_name' => $data['bank_name'] ?? null,
            'account_name' => $data['account_name'] ?? null,
            'bsb' => $data['bsb'] ?? null,
            'account_number' => $data['account_number'] ?? null,
            'swift_code' => $data['swift_code'] ?? null,
            'tax_file_number' => $data['tax_file_number'] ?? null,
            'super_fund_name' => $data['super_fund_name'] ?? null,
            'super_member_number' => $data['super_member_number'] ?? null,
        ]);
        $staff->save();

        return $staff;
    }

    private function saveDocuments(Staff $staff, array $data)
    {
        $proof_of_identification = $data['proof_of_identification'] ?? null;
        $resume = $data['resume'] ?? null;
        $active_contract = $data['active_contract'] ?? null;
        $other_documents = $data['other_documents'] ?? [];
        $updateFields = [];
        // Only upload if it's a new file, not an existing URL string
        if ($proof_of_identification && $proof_of_identification instanceof \Illuminate\Http\UploadedFile) {
            $updateFields['proof_of_identification'] = $this->userService?->uploadUserFile($proof_of_identification);
        }
        if ($resume && $resume instanceof \Illuminate\Http\UploadedFile) {
            $updateFields['resume'] = $this->userService?->uploadUserFile($resume);
        }
        if ($active_contract && $active_contract instanceof \Illuminate\Http\UploadedFile) {
            $updateFields['active_contract'] = $this->userService?->uploadUserFile($active_contract);
        }
        if ($other_documents && is_array($other_documents)) {
            foreach ($other_documents as $document) {
                // Only upload if it's a new file, not an existing URL string
                if ($document instanceof \Illuminate\Http\UploadedFile) {
                    $updateFields['other_documents'][] = $this->userService?->uploadUserFile($document);
                }
            }
        }
        if (! empty($updateFields)) {
            $staff->update($updateFields);
        }

        return $staff;
    }

    private function saveEmergencyContactStep(Staff $staff, array $data)
    {
        $staff->fill([
            'emergency_contact_name' => $data['emergency_contact_name'] ?? null,
            'emergency_contact_relationship' => $data['emergency_contact_relationship'] ?? null,
            'emergency_phone_1' => $data['emergency_phone_1'] ?? null,
            'emergency_phone_2' => $data['emergency_phone_2'] ?? null,
            'emergency_email' => $data['emergency_email'] ?? null,
            'emergency_address' => $data['emergency_address'] ?? null,
        ]);

        $staff->updated_by = auth()->id();

        return $staff;
    }
}
