var GroupManagement = function () {

    var initBlukEnrollment = function () {

        dateFormate('.dateField');

        $('.finish_date').datepicker({
            autoclose: true,
            format: 'dd-mm-yyyy'
        }).on('blur', function (ev) {
            compareStartEndDate();
        }).on('changeDate', function (ev) {
            compareStartEndDate();
        });

        $('.activity_end_date').datepicker({
            autoclose: true,
            format: 'dd-mm-yyyy'
        }).on('blur', function (ev) {
            validateActivityEndDate();
        }).on('changeDate', function (ev) {
            validateActivityEndDate();
        });

        function compareStartEndDate(){
            var startDate = $('.start_date').val();
            var endDate = $('.finish_date').val();

            var startdateSplit = startDate.split('-');
            var endDateSplit = endDate.split('-');

            var convertedStartdate = new Date(startdateSplit[2], startdateSplit[1] - 1, startdateSplit[0]);
            var convertedEnddate = new Date(endDateSplit[2], endDateSplit[1] - 1, endDateSplit[0]);

            const convertedStartdateToTime = convertedStartdate.getTime();
            const convertedEnddateToTime = convertedEnddate.getTime();

            if (convertedStartdateToTime > convertedEnddateToTime) {
                alert("Finish Date Must be after From Start Date.");
                $('#finish_date').val('');
            }
        }

        function validateActivityEndDate(){
            var activityEndDate = $('.activity_end_date').val();
            var finalOutcome = $('#select_final_outcome').val();

            if (activityEndDate && (finalOutcome === 'C' || finalOutcome === 'NYC' || finalOutcome === 'CT' || finalOutcome === 'RPL')) {
                var dateSplit = activityEndDate.split('-');
                var convertedDate = new Date(dateSplit[2], dateSplit[1] - 1, dateSplit[0]);
                var today = new Date();
                today.setHours(0, 0, 0, 0);

                if (convertedDate.getTime() > today.getTime()) {
                    alert("Activity End Date cannot be a future date when final outcome is C, NYC, CT, or RPL.");
                    $('#activity_end_date').val('');
                }
            }
        }

        // Toggle activity end date required indicator based on final outcome
        $('#select_final_outcome').change(function() {
            var finalOutcome = $(this).val();
            if (finalOutcome === 'C' || finalOutcome === 'NYC' || finalOutcome === 'CT' || finalOutcome === 'RPL') {
                $('#activity_end_date_required').show();
            } else {
                $('#activity_end_date_required').hide();
            }
            validateActivityEndDate();
        });
        setTimeout(function () {
            setCourse();
            checkAndManageHigherEducationField();
        }, 1000);
        $('#course_type').change(function () {
            setCourse();
            checkAndManageHigherEducationField();
        });
        $('#campus').change(function () {
            setCourse();
        });
        $('#course').change(function () {
            setGroup();
            setSemester();
        });

        $('#group').change(function () {
            setStudentData();
        });

        $('#semester').change(function () {
            setTerm();
            $('#finish_date').val("");
        });

        $('#term').change(function () {
            setSubject();
        });

        $('#subject').change(function () {
            setBatch();
        });

        $('#orderBy').change(function () {
            setStudentData();
        });
        $('#ascDescOrder').change(function () {
            setStudentData();
        });
        $('.filterType').change(function () {
            setStudentData();
            setFundingSourceData();
        });

        $('#batch').change(function () {
            var that = $(this);
            setVanueList();
            setStartEndDate(that);
            setEnrollStudentDetail();
            setTimeout(function() {
                    setUnEnrollStudentDetail();
                  }, 2000);
        });

        $('body').on('click', '#view_student', function () {
            setEnrollStudentDetail();
            setTimeout(function() {
                    setUnEnrollStudentDetail();
                  }, 2000);
        });

        $('body').on('click', '#export_grid', function () {
            exportGridForEnrollStudent();
        });

        $('body').on('ifChecked', '.checkAll', function () {
            $('.studentId:checkbox').each(function () {
                $('.studentId').iCheck('check');
            });
        });
        
        $('body').on('ifUnchecked', '.checkAll', function () {
            $('.studentId:checkbox').each(function () {
                $('.studentId').iCheck('uncheck');
            });
        });

        // Delivery mode and predominant delivery mode are now set automatically on server-side by model observer based on final outcome
        // getPredominantDeliveryMode();

        function setFundingSourceData()
        {
            $("#state").removeAttr("disabled").selectric('refresh');
            var filtertype = $(".filterType").val();
            if (filtertype == 'Domestic')
            {
                $('#state').val('20').change().selectric('refresh');
                $('#funding_source_state').attr("disabled", "disabled");
//                $('#state').prop('selectedIndex', 3).selectric('refresh');
            } else {
                $('#state').val('FEE').change().selectric('refresh');
                $('#state').attr("disabled", "disabled").selectric('refresh');
                 $("#funding_source_state").removeAttr("disabled");
//                $('#state').prop('selectedIndex', 4).selectric('refresh');
            }
        }

        // Delivery mode and predominant delivery mode are now set automatically on server-side by model observer based on final outcome
        // No need for getPredominantDeliveryMode() function or delivery_mode change handler
        /*
        function getPredominantDeliveryMode() {
            var delivery_mode = $("#delivery_mode").val();
            var dataArr = {'delivery_mode': delivery_mode};
            $.ajax({
                url: site_url + "student-subject/ajaxAction",
                method: "POST",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                data: {'action': 'getPredominantDeliveryMode', 'data': dataArr},
                success: function (data) {
                    //$('#studentAssessmentDetail').hide();
                    var obj = jQuery.parseJSON(data);
                    setOption("#predominant_delivery_mode", obj);

                    var old_predominant_delivery_mode = $("#old_predominant_delivery_mode").val();
                    if (old_predominant_delivery_mode != "")
                    {
                        var element = document.getElementById('predominant_delivery_mode');
                        element.value = old_predominant_delivery_mode;
                        $('#predominant_delivery_mode').selectric('refresh');
                    }
//                    setOption('#state', '30');
                    $('#funding_source_state').val('FEE');
                    $('#state').val('FEE').change().selectric('refresh');
                    $('#state').attr("disabled", "disabled").selectric('refresh');
                }
            });
        }

        $('body').on('change', '#delivery_mode', function () {
            getPredominantDeliveryMode();

        });
        */

        function checkAndManageHigherEducationField(){
            let courseTypeText = $('#course_type :selected').text().toLowerCase();
            let isHigherEd = (jQuery.inArray(courseTypeText.trim(), specialType) != -1) ? true : false;
            if(isHigherEd){
                $('#mark_outcome').show();
                $('#final_outcome').hide();
            }else{
                $('#mark_outcome').hide();
                $('#final_outcome').show();
            }
        }

        var setCourse = function () {
            var courseType = $('#course_type').val();
            var campusId = $('#campus').val();
            var dataArr = {'campusId': campusId, 'courseType': courseType};
            $.ajax({
                type: "POST",
                url: site_url + "compliance/ajaxAction",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                data: {'action': 'getFullCourses', 'data': dataArr},
                success: function (data) {
                    var result = jQuery.parseJSON(data);
                    setOption('#course', result);
                    $('#course').selectric('refresh');
                    setGroup();
                    setSemester();
                },
                error: function (err) {
                    $('.teacher_email').text('');
                }
            });
        };

        var setGroup = function () {
            var courseId = $('#course').val();
            var courseType = $('#course_type').val();
            var dataArr = {'courseId': courseId, 'courseType': courseType};
            $.ajax({
                type: "POST",
                url: site_url + "compliance/ajaxAction",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                data: {'action': 'getGroupFromCourse', 'data': dataArr},
                success: function (data) {
                    var result = jQuery.parseJSON(data);

                    setOption('#group', result);
                    $('#group').selectric('refresh');
                    setStudentData();
                },
                error: function (err) {
                    $('.teacher_email').text('');
                }
            });
        };

        var setStudentData = function () {
            var groupId = $('#group').val();
            var ascDescOrder = $('#ascDescOrder').val();
            var orderBy = $('#orderBy').val();
            var filterType = $('.filterType').val();
            var campusId = $('#campus').val();
            if (groupId != "")
            {
                var dataArr = {'groupId': groupId,'campusId':campusId, 'orderBy': orderBy, 'ascDescOrder': ascDescOrder, 'filterType': filterType};
                var url = "compliance/ajaxAction";
                var action = 'getStudentHasGroup';
                ajaxAction(url, action, dataArr, function (resultData) {
                    //$('#studentHasGroup tbody').html("");
                    noRecordFoundNew('#studentHasGroup tbody', 0, 9);
                    var result = jQuery.parseJSON(resultData);

                    var rowCnt = result.length;
                    var totalRecord = (rowCnt > 0) ? ((rowCnt > 1) ? rowCnt + " records" : rowCnt + " record") : "0 record";

                    var groupName = $('#group option[value="' + groupId + '"]').text();
                    $('.StudentListTitle').html(groupName + " (" + totalRecord + ")");

                    var table = "";
                    $.each(result, function (i, item) {

                        var originName = '';
                        if (item.student_type == 'Offshore') {
                            originName = 'Overseas Student (Offshore)';
                        } else if (item.student_type == 'Onshore') {
                            originName = 'Overseas Student in Australia (Onshore)';
                        } else {
                            originName = 'Resident Student (Domestic)';
                        }
                        table += '<tr>' +
                                '<td><input type="checkbox" name="studentId[]" class="studentId" value="' + item.studentid + '-' + item.generated_stud_id + '-' + item.studentcourseid + '">' + item.generated_stud_id + '</td>' +
                                '<td>' + item.student_name + '</td>' +
                                '<td>' + item.campus_name + '</td>' +
                                '<td>' + item.course_code + ':' + item.course_name + '</td>' +
                                '<td>' + item.course_attempt + '</td>' +
                                '<td>' + formatDate(item.start_date) + ' TO<br>' + formatDate(item.finish_date) + '</td>' +
                                '<td>' + item.status + '</td>' +
                                '<td>' + originName + '</td>' 
                                '</tr>';
                    });
                    $('#studentHasGroup tbody').html(table);
                    noRecordFoundNew('#studentHasGroup tbody', rowCnt, 9);
                    radioCheckboxClass();
                });
            } else {
                var groupName = '#';
                $('.StudentListTitle').html(groupName + " (0 record)");
                //$('#studentHasGroup tbody').html("");
                //$('#noRecord').show();
                noRecordFoundNew('#studentHasGroup tbody', 0, 9);
            }
        };

        var setSemester = function () {
            var courseId = $('#course').val();
            var courseType = $('#course_type').val();
            var dataArr = {'courseId': courseId, 'courseType': courseType};
            var url = "compliance/ajaxAction";
            var action = 'getSemesterHasCourse';

            ajaxAction(url, action, dataArr, function (resultData) {
                var result = jQuery.parseJSON(resultData);
                setOption('#semester', result);
                $('#semester').selectric('refresh');
                setTerm();
            });
        };

        var setTerm = function () {
            var semester = $('#semester').val();
            var dataArr = {'semester': semester};
            var url = "compliance/ajaxAction";
            var action = 'getTermBySemester';

            ajaxAction(url, action, dataArr, function (resultData) {
                var result = jQuery.parseJSON(resultData);
                setOption('#term', result);
                $('#term').selectric('refresh');
                setSubject();
            });
        };

        var setSubject = function () {
            var semester = $('#semester').val();
            var term = $('#term').val();
            var course = $('#course').val();
            var dataArr = {'semester': semester, 'term': term, 'course': course};
            var url = "compliance/ajaxAction";
            var action = 'getSubjectBySemester';
            ajaxAction(url, action, dataArr, function (resultData) {
                var result = jQuery.parseJSON(resultData);
                setOption('#subject', result);
                $('#subject').selectric('refresh');
                setBatch();
            });
        };

        var setBatch = function () {
            var semester = $('#semester').val();
            var term = $('#term').val();
            var subject = $('#subject').val();
            var dataArr = {'semester': semester, 'term': term, 'subject': subject};
            var url = "compliance/ajaxAction";
            var action = 'getBatchBySubject';

            ajaxAction(url, action, dataArr, function (resultData) {

                var result = jQuery.parseJSON(resultData);
                setOption('#batch', result);
                $('#batch').selectric('refresh');
                noRecordFoundNew('#enrollStudent tbody', 0, 11);
                //$('#enrollStudent tbody').html("");
                setTimeout(function () {
                    $('#batch').trigger('change');
                }, 200);
                setEnrollStudentDetail();
                setTimeout(function() {
                    setUnEnrollStudentDetail();
                  }, 2000);
                
            });
//            setTimeout(function(){
//                setEnrollStudentDetail();
//                setEnrollStudentDetailWithoutBatchTerm();
//            }, 500);
        };

        var setVanueList = function () {

            var batch = $('#batch').val();
            if (batch != '') {
                var semester = $('#semester').val();
                var term = $('#term').val();
                var subject = $('#subject').val();
                var campusId = $('#campus').val();
                var dataArr = {'semester': semester, 'term': term, 'subject': subject, 'campusId':campusId, 'batch': batch};
                var url = "compliance/ajaxAction";
                var action = 'getVenueList';
                ajaxAction(url, action, dataArr, function (resultData) {
                    var result = jQuery.parseJSON(resultData);
                    setOption('#venue', result);
                    $('#venue').selectric('refresh');
                });
            } else {
                $('#venue').find('option').remove().end().append('<option value="">- - Select Venue - -</option>').val('');
                $('#venue').selectric('refresh');
            }

        };

        var setStartEndDate = function (that) {
            var text = $("#batch option:selected").text();

            var fields = text.split('(');
            //console.log(fields);
            //if (fields != '-- Select Batch --') {
            if (fields != '- - Select Batch - -' && text != 'No Batch Found') {
                var removeFirst = fields[1];
                if (removeFirst != "undefined") {
                    var removeSecond = removeFirst.split(')');
                    var removeSecondNew = removeSecond[0];
                    var SeprateDate = removeSecondNew.split(' - ');

                    var startDate = SeprateDate[0].split("/").join("-");
                    var endDate = SeprateDate[1].split("/").join("-");
//                    $('#start_date').val(startDate);
//                    $('#finish_date').val(endDate);
                    $('#start_date').datepicker('setDate', startDate);
                    $('#finish_date').datepicker('setDate', endDate);
                }
            } else {
                $('#start_date').val('');
                $('#finish_date').val('');
            }
        };

        var setEnrollStudentDetail = function () {
            var semester = $('#semester').val();
            var term = $('#term').val();
            var subject = $('#subject').val();
            var batch = $('#batch').val();
            var dataArr = {'semester': semester, 'term': term, 'subject': subject, 'batch': batch};
            var url = "compliance/ajaxAction";
            var action = 'getSemsterEnrollStudent';

            //$('#enrollStudent tbody').html("");
            
                    
                      
            ajaxAction(url, action, dataArr, function (resultData) {

                var result = jQuery.parseJSON(resultData);
                var resultStudentData = result.studentdata;
                var resultFunddingData = result.arrFundingSource;
                var rowCnt = resultStudentData.length;
                   
                setEnrollStudListTitle(rowCnt);
//                var semesterName = (semester != '') ? $('#semester option[value="' + semester + '"]').text() : '#';
//                var subjectName = (subject != '') ? $('#subject option[value="' + subject + '"]').text() : '#';
//                //var batchName = (batch != '') ? $('#batch option[value="' + batch + '"]').text() : '#';
//                var batchName = (batch != '') ? $('#batch option:selected').text() : '#';
//                
//                var totalRecord = (rowCnt > 0) ? ((rowCnt > 1) ? rowCnt + " records" : rowCnt + " record") : "0 record";
//                var enrollListTitle = semesterName + '; Subject: ' + subjectName + '; Batch: ' + batchName + ' (' + totalRecord + ')';
//                $('.SemesterListTitle').html(enrollListTitle);

//                var semesterName = $('#semester option[value="' + semester + '"]').text();
//                var subjectName = $('#subject option[value="' + subject + '"]').text();
//                var batchName = $('#batch option:selected').text();
//                $('.SemesterListTitle').html(semesterName + "; Subject: " + subjectName + "; Batch: " + batchName + " (" + (result.length) + " record)");

                var table = "";
                var studentArr=new Array();
                $.each(resultStudentData, function (i, item) {
                      studentArr.push(item.student_id);

                    if(item.outcome_value==null)
                        {
                            var outcome_value='-';
                        }else{
                            var outcome_value=item.outcome_value;
                        }
                        if(item.title==null)
                        {
                            var title='-';
                        }else{
                            var title=item.title;
                        }
                    table += '<tr>' +
                             '<td>' +
                             '<div class="action-overlay">' +
                            '<ul class="icon-actions-set">' +
                            '<li>' +
                            //'<span data-toggle="modal" class="delete" data-id="' + item.primaryId + '" data-target="#deleteModal">' +
                            '<a class="link-black text-sm delete clickDelete" data-id="' + item.primaryId + '"  data-toggle="tooltip" data-original-title="Delete" href="javascript:;"><i class="fa fa-remove"></i></a>' +
                            //'</span>' +
                            '</li>' +
                            '</ul>' +
                            '</div>' +
                             '<input type="checkbox" name="Student[]" class="enrollId" value="' + item.primaryId + '" >'+ '</td>' +
                            '<td>' +
                            item.generated_stud_id +
                            '</td>' +
                            '<td>' + item.student_name + '</td>' +
                            '<td>' + item.semester_name + ' / ' + item.term + '</td>' +
                            '<td>' + item.subject_code + ':' + item.subject_name + '</td>' +
                            '<td>' + item.unit_code + ':' + item.unit_name + '</td>' +
                            '<td>' + item.batch + '</td>' +
                            '<td>' + formatDate(item.activity_start_date) + ' TO<br> ' + formatDate(item.activity_finish_date) + '</td>' +
                            '<td>' + title + '</td>' +
                            '<td>' + resultFunddingData[item.funding_source_state] + '</td>' +
                            '<td>' + outcome_value + '</td>' +
                            '</tr>';
                });
//                console.log(studentArr);
                $('#enrollStudent tbody').html(table);
                $('#studentArr').val(studentArr);
                noRecordFoundNew('#enrollStudent tbody', resultStudentData.length, 11);
                radioCheckboxClass();  
                 $('#checkboxAll').iCheck('uncheck');
            }); 
        };
       
       $('#enrollStudent').on('ifChecked', '#checkboxAll', function () {
            $('.enrollId:checkbox').each(function () {
                $('.enrollId').iCheck('check');
            });
        });
       $('#enrollStudent').on('ifUnchecked', '#checkboxAll', function () {
            $('.enrollId:checkbox').each(function () {
                $('.enrollId').iCheck('uncheck');
            });
        });
        var exportGridForEnrollStudent = function () {
            var semester = $('#semester').val();
            var term = $('#term').val();
            var subject = $('#subject').val();
            var batch = $('#batch').val();

            if (semester != "" && term != "" && subject != "" && batch != "") {
                window.location.href = "/export-grid-enroll-student/" + semester + "/" + term + "/" + subject + "/" + batch;
            } else {
                alert("Please select all require option");
            }
        };

        function setEnrollStudListTitle(rowCnt) {

            var semester = $('#semester').val();
            var subject = $('#subject').val();
            var batch = $('#batch').val();

            var semesterName = (semester != '') ? $('#semester option[value="' + semester + '"]').text() : '#';
            var subjectName = (subject != '') ? $('#subject option[value="' + subject + '"]').text() : '#';
            //var batchName = (batch != '') ? $('#batch option[value="' + batch + '"]').text() : '#';
            var batchName = (batch != '') ? $('#batch option:selected').text() : '#';

            var totalRecord = (rowCnt > 0) ? ((rowCnt > 1) ? rowCnt + " records" : rowCnt + " record") : "0 record";
            var enrollListTitle = semesterName + '; Subject: ' + subjectName + '; Batch: ' + batchName + ' (' + totalRecord + ')';
            $('.SemesterListTitle').html(enrollListTitle);
        }
        ////////////////////////
      
           var setUnEnrollStudentDetail = function () {
//            var semester = $('#semester').val();
//            var term = $('#term').val();
//            var subject = $('#subject').val();
//            var batch = $('#batch').val();
            var groupId = $('#group').val();
            var ascDescOrder = $('#ascDescOrder').val();
            var orderBy = $('#orderBy').val();
            var filterType = $('.filterType').val();
            var studentArr = $('#studentArr').val();
            var dataArr = {'groupId': groupId, 'orderBy': orderBy, 'ascDescOrder': ascDescOrder, 'filterType': filterType, 'studentArr': studentArr};
            
//            alert(studentArr);
//            var dataArr = {'semester': semester, 'term': term, 'subject': subject, 'batch': batch, 'studentArr': studentArr};
            var url = "compliance/ajaxAction";
            var action = 'getSemsterUnenrollStudent';

            //$('#enrollStudent tbody').html("");

            ajaxAction(url, action, dataArr, function (resultData) {
//                console.log(resultData);
                var result = jQuery.parseJSON(resultData);
                var resultStudentData = result.studentdata;
                var resultFunddingData = result.arrFundingSource;
                var rowCnt = resultStudentData.length;
                setEnrollStudListTitleWithoutBatchTerm(rowCnt);
//                var semesterName = (semester != '') ? $('#semester option[value="' + semester + '"]').text() : '#';
//                var subjectName = (subject != '') ? $('#subject option[value="' + subject + '"]').text() : '#';
//                //var batchName = (batch != '') ? $('#batch option[value="' + batch + '"]').text() : '#';
//                var batchName = (batch != '') ? $('#batch option:selected').text() : '#';
//                
//                var totalRecord = (rowCnt > 0) ? ((rowCnt > 1) ? rowCnt + " records" : rowCnt + " record") : "0 record";
//                var enrollListTitle = semesterName + '; Subject: ' + subjectName + '; Batch: ' + batchName + ' (' + totalRecord + ')';
//                $('.SemesterListTitle').html(enrollListTitle);

//                var semesterName = $('#semester option[value="' + semester + '"]').text();
//                var subjectName = $('#subject option[value="' + subject + '"]').text();
//                var batchName = $('#batch option:selected').text();
//                $('.SemesterListTitle').html(semesterName + "; Subject: " + subjectName + "; Batch: " + batchName + " (" + (result.length) + " record)");
                var unenrolledStudent=$('#unenrolledStudent').val();
                var table = "";
                var resultStudentCount=0;
                $.each(resultStudentData, function (i, item) {
                        if(unenrolledStudent.includes(item.generated_stud_id)){
                            var originName = '';
                            if (item.student_type == 'Offshore') {
                                originName = 'Overseas Student (Offshore)';
                            } else if (item.student_type == 'Onshore') {
                                originName = 'Overseas Student in Australia (Onshore)';
                            } else {
                                originName = 'Resident Student (Domestic)';
                            }
                            table += '<tr>' +
                                    '<td>' + item.generated_stud_id + '</td>' +
                                    '<td>' + item.student_name + '</td>' +
                                    '<td>' + item.campus_name + '</td>' +
                                    '<td>' + item.course_code + ':' + item.course_name + '</td>' +
                                    '<td>' + item.course_attempt + '</td>' +
                                    '<td>' + formatDate(item.start_date) + ' TO<br> ' + formatDate(item.finish_date) + '</td>' +
                                    '<td>' + item.status + '</td>' +
    //                                '<td>' + item.reasionTitle + '</td>' +
                                    '<td>' + originName + '</td>' 
                                    '</tr>';
                                    resultStudentCount++;
                        }
                });
                $('#enrollStudentWithoutBatchTerm tbody').html(table);
                noRecordFoundNew('#enrollStudentWithoutBatchTerm tbody', resultStudentCount, 10);
            });
        };
        
     function setEnrollStudListTitleWithoutBatchTerm(rowCnt) {
              
            var semester = $('#semester').val();
            var subject = $('#subject').val();
//            var batch = $('#batch').val();

            var semesterName = (semester != '') ? $('#semester option[value="' + semester + '"]').text() : '#';
           var subjectName = (subject != '') ? $('#subject option[value="' + subject + '"]').text() : '#';
            //var batchName = (batch != '') ? $('#batch option[value="' + batch + '"]').text() : '#';
//            var batchName = (batch != '') ? $('#batch option:selected').text() : '#';

            var totalRecord = (rowCnt > 0) ? ((rowCnt > 1) ? rowCnt + " records" : rowCnt + " record") : "0 record";
            var enrollListTitle = semesterName + '; Subject: ' + subjectName + ' (' + totalRecord + ')';
            $('.SemesterListTitleWithoutBatchTerm').html(enrollListTitle);
        }
        //////////////////////////
        $('body').on('click', '.clickDelete', function () {
            var dataid = $(this).attr('data-id');
            $('.yes-sure').attr('data-id', dataid);
            $('#deleteModal').modal('show');
        });

        $('body').on('click', '.deleteData', function () {
            var dataid = $(this).attr('data-id');
            var dataArr = {'dataid': dataid};
            var url = "delete-bulkenrollment/ajaxAction";
            var action = 'deleteBulkEnrollment';
            ajaxAction(url, action, dataArr, function (resultData) {
                setTimeout(function () {
                  //  sessionDisplayMessage('alert-success', 'Student Bulk Enrollment By Group Delete successfullt.');
                    showToster('success', 'Student Bulk Enrollment By Group Delete successfullt.');
                }, 100)
                $('#deleteModal').modal('hide');
                setEnrollStudentDetail()
                setTimeout(function() {
                    setUnEnrollStudentDetail();
                  }, 2000);
              
            });
        });
        
        $('body').on('click', '#deleteInfo', function () {
            var status = 'false';
            $('input[name="Student[]"]:checkbox').each(function () {
                if ($(this).is(':checked')) {
                    status = 'true';
                }
            });
            if (status == 'false') {
                alert('Select at least one Student Record');
                return false;
            }
            var checkBox = $('input[name="Student[]"]:checked');
            var StudentIds = '';
            $.each(checkBox, function () {
                 var dataid = $(this).val();
            var dataArr = {'dataid': dataid};
            var url = "delete-bulkenrollment/ajaxAction";
            var action = 'deleteBulkEnrollment';
            ajaxAction(url, action, dataArr, function (resultData) {
                setTimeout(function () {
                  showToster('success', 'Student enrollment delete successfully');
                }, 100)
                $('#deleteModal').modal('hide');
                setEnrollStudentDetail()
                setTimeout(function() {
                    setUnEnrollStudentDetail();
                  }, 2000);
              
            });
            });
           });

        $('body').on('click', '#addBulkEnrollInfo', function () {
            //e.preventDefault();
            var status = 'false';
            $('input:checkbox').each(function () {
                if ($(this).is(':checked')) {
                    status = 'true';
                }
            });
            if (status == 'false') {
                alert('Select at least one Student Record');
            }
        });

//        $('body').on('click', '#addBulkEnrollInfo', function(e){
//            e.preventDefault();

        // Add custom validation method for activity end date
        $.validator.addMethod("requiredIfFinalOutcome", function(value, element) {
            var finalOutcome = $('#select_final_outcome').val();
            if (finalOutcome === 'C' || finalOutcome === 'NYC' || finalOutcome === 'CT' || finalOutcome === 'RPL') {
                return value && value.trim() !== '';
            }
            return true;
        }, "Activity End Date is required when final outcome is C, NYC, CT, or RPL.");

        $.validator.addMethod("notFutureDate", function(value, element) {
            if (!value) return true;
            var finalOutcome = $('#select_final_outcome').val();
            if (finalOutcome === 'C' || finalOutcome === 'NYC' || finalOutcome === 'CT' || finalOutcome === 'RPL') {
                var dateSplit = value.split('-');
                var convertedDate = new Date(dateSplit[2], dateSplit[1] - 1, dateSplit[0]);
                var today = new Date();
                today.setHours(0, 0, 0, 0);
                return convertedDate.getTime() <= today.getTime();
            }
            return true;
        }, "Activity End Date cannot be a future date when final outcome is C, NYC, CT, or RPL.");

        $('#bulkEnrollInfoForm').validate({
            rules: {
                'studentId[]': {required: true},
                campus: {required: true},
                course_type: {required: true},
                course: {required: true},
                group: {required: true},
                enroll_type: {required: true},
                semester: {required: true},
                subject: {required: true},
                state: {required: true},
                start_date: {required: true},
                term: {required: true},
                batch: {required: true},
                venue: {required: true},
                finish_date: {required: true},
                //study_reason: {required: true},
                funding_source_nat: {required: true},
                activity_end_date: {
                    requiredIfFinalOutcome: true,
                    notFutureDate: true
                }
            },
            messages: {
                //name: "Please enter Name"
            },
            errorPlacement: function (error, element) {
                // error Msg
            },
            submitHandler: function (form) {
                var formData = serializePost(form);
                $.ajax({
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                    },
                    url: site_url + "compliance/ajaxAction",
                    data: {'action': 'saveBulkEnrollInfo', 'data': {'formData': formData}},
                    success: function (data) {

                        //window.location.reload();
                        var result = jQuery.parseJSON(data);
                        var table = "";
                        $('#errorSection').html('');
                        $('#unenrolledStudent').empty();
                         var unenrolledStudent = [];
                        $.each(result, function (i, item) {
                           
                            var statusType = (item.status === 'alert-danger' || item.status === 'danger' || item.status === 'failed' || item.status === 'error') ? 'error' : 'success';
                            showToster(statusType, item.message);
                            if (statusType === 'error') {
                                unenrolledStudent.push(item.generatedId);                                
                            }
                            $('html,body').animate({
                                scrollTop: $(".content").offset().top},
                            'slow');
                        });
                        $('#unenrolledStudent').val(unenrolledStudent);
                        setEnrollStudentDetail();
                        setTimeout(function() {
                            setUnEnrollStudentDetail();
                        }, 2000);
                        setTimeout(function() {
                            $("#errorSection").slideUp(3000);
                        }, 20000);
                        $('.closeIcon').click(function(){
                            $(this).closest('.alert-danger').hide();
                        });
                        $('.studentId').iCheck('uncheck'); 
                    },
                    error: function (err) {
                        var msg = 'An unexpected error occurred.';
                        function extractMsg(obj) {
                            if (!obj) return null;
                            if (typeof obj === 'string') return obj;
                            if (Array.isArray(obj) && obj.length) {
                                if (typeof obj[0] === 'string') return obj[0];
                                if (obj[0] && typeof obj[0].message === 'string') return obj[0].message;
                            }
                            if (typeof obj.message === 'string' && obj.message) return obj.message;
                            if (Array.isArray(obj.message) && obj.message.length) {
                                if (typeof obj.message[0] === 'string') return obj.message[0];
                                if (obj.message[0] && typeof obj.message[0].message === 'string') return obj.message[0].message;
                            }
                            var bag = obj.errors || obj.data;
                            if (bag && typeof bag === 'object') {
                                var keys = Object.keys(bag);
                                if (keys.length) {
                                    var v = bag[keys[0]];
                                    if (Array.isArray(v) && v.length) {
                                        if (typeof v[0] === 'string') return v[0];
                                        if (v[0] && typeof v[0].message === 'string') return v[0].message;
                                    } else if (typeof v === 'string') {
                                        return v;
                                    }
                                }
                            }
                            return null;
                        }
                        try {
                            var fromRJ = extractMsg(err && err.responseJSON ? err.responseJSON : null);
                            if (fromRJ) {
                                msg = fromRJ;
                            } else if (err && err.responseText) {
                                var parsed = JSON.parse(err.responseText);
                                var fromParsed = extractMsg(parsed);
                                if (fromParsed) msg = fromParsed;
                            }
                        } catch (e) { /* ignore parse errors */ }
                        showToster('error', msg);
                    }   
                });
            }
        });
    };

    var initFlexibleAllocation = function () {
        dateFormate('.dateField');
        radioCheckboxClass();
        checkDateRange('.dateField', '#start_date', '#finish_date', 'Finish Date Finish Must be after Start Date');
        $('body').on('click', '.submitBtn', function () {
            var checkedStudent = $('input[name="studentId[]"]:checked');
            var checkedTimetable = $('input[name="timeTableDay[]"]:checked');

            if (checkedStudent.length == 0) {
                sessionDisplayMessage('alert-danger', 'Please Select Any Student..');
                return false;
            }

            if (checkedTimetable.length == 0) {
                sessionDisplayMessage('alert-danger', 'Please Select Any Timetable..');
                return false;
            }

            $(".has-error").removeClass('has-error');

            if ($('#start_date').val() == '') {
                $('#start_date').closest('.form-group').addClass('has-error');
            }

            if ($('#finish_date').val() == '') {
                $('#finish_date').closest('.form-group').addClass('has-error');
            }

            if ($('#start_date').val() == '' || $('#finish_date').val() == '') {
                return false;
            }

            var studentIds = '';
            $.each(checkedStudent, function () {
                var id = $(this).val();
                studentIds += (studentIds == '') ? id : ',' + id;
            });

            var timetableIds = '';
            $.each(checkedTimetable, function () {
                var id = $(this).val();
                timetableIds += (timetableIds == '') ? id : ',' + id;
            });

            var startDate = $('#start_date').val();
            var finishDate = $('#finish_date').val();
            var courseId = $('#course').val();

            var dataArr = {'studentIds': studentIds, 'timetableIds': timetableIds, 'startDate': startDate, 'finishDate': finishDate, 'courseId': courseId};

            ajaxCall(site_url + "compliance/ajaxAction", {'action': 'saveFlexibleTimetable', 'data': dataArr}, function (data) {
                var result = jQuery.parseJSON(data);
                sessionDisplayMessage(result.status, result.message);

                getFlexibleEnrolledList();
            });
        });

        $('body').on('change', '#campus', function () {
            setCourse();
        });
        $('body').on('change', '#course_type', function () {
            setCourse();
        });
        $('body').on('change', '#course', function () {
            setSubject();
            setGroup();
            setSemester();
            getFlexibleEnrolledList();
        });
        $('body').on('change', '#group', function () {
            setStudentData();
        });
        setTimeout(function () {
            setCourse();
        }, 1000);
        $('body').on('change', '#semester', function () {
            setTerm();
        });
        $('body').on('change', '#term', function () {
            setBatch();
        });
        $('body').on('change', '#subject', function () {
            setBatch();
        });

        $('#studentHasGroup').on('ifChecked', '.checkAll', function () {
            $('.studentId:checkbox').each(function () {
                $('.studentId').iCheck('check');
            });
        });

        $('#studentHasGroup').on('ifUnchecked', '.checkAll', function () {
            $('.studentId:checkbox').each(function () {
                $('.studentId').iCheck('uncheck');
            });
        });

        $('body').on('change', '#batch', function () {
            setStudentData();
            setClassTableData();
            getFlexibleEnrolledList();
        });

        var ajaxCall = function (url, postData, callBack) {
            $.ajax({
                type: "POST",
                url: url,
                headers: {'X-CSRF-TOKEN': $('input[name="_token"]').val()},
                data: postData,
                success: callBack
            });
        };

        var setCourse = function () {
            var courseType = $('#course_type').val();
            var campusId = $('#campus').val();
            var dataArr = {'campusId': campusId, 'courseType': courseType};

            ajaxCall(site_url + "compliance/ajaxAction", {'action': 'getFlexibleCourses', 'data': dataArr}, function (data) {
                var result = jQuery.parseJSON(data);
                setOption('#course', result);
                $('#course').selectric('refresh');
                setGroup();
                setSemester();
                setSubject();
            });
        };

        var setGroup = function () {
            var courseId = $('#course').val();
            var courseType = $('#course_type').val();
            var dataArr = {'courseId': courseId, 'courseType': courseType};

            ajaxCall(site_url + "compliance/ajaxAction", {'action': 'getGroupFromCourse', 'data': dataArr}, function (data) {
                var result = jQuery.parseJSON(data);
                setOption('#group', result);
                $('#group').selectric('refresh');
            });
        };

        var setSemester = function () {
            var courseId = $('#course').val();
            var courseType = $('#course_type').val();
            var dataArr = {'courseId': courseId, 'courseType': courseType};

            ajaxCall(site_url + "compliance/ajaxAction", {'action': 'getSemesterHasCourse', 'data': dataArr}, function (data) {
                var result = jQuery.parseJSON(data);
                setOption('#semester', result);
                $('#semester').selectric('refresh');
                setTerm();
            });
        };

        var setTerm = function () {
            var semester = $('#semester').val();
            var dataArr = {'semester': semester};

            ajaxCall(site_url + "compliance/ajaxAction", {'action': 'getTermBySemester', 'data': dataArr}, function (data) {
                var result = jQuery.parseJSON(data);
                setOption('#term', result);
                $('#term').selectric('refresh');
                setBatch();
            });
        };

        var setSubject = function () {
            var courseId = $('#course').val();
            var dataArr = {'courseId': courseId};

            ajaxCall(site_url + "compliance/ajaxAction", {'action': 'getSubjectsByCourse', 'data': dataArr}, function (data) {
                var result = jQuery.parseJSON(data);
                setOption('#subject', result);
                $('#subject').selectric('refresh');
                //setBatch();
            });
        };

        var setBatch = function () {
            var semester = $('#semester').val();
            var term = $('#term').val();
            var subject = $('#subject').val();
            var dataArr = {'semester': semester, 'term': term, 'subject': subject};

            ajaxCall(site_url + "compliance/ajaxAction", {'action': 'getBatchBySubject', 'data': dataArr}, function (data) {
                var result = jQuery.parseJSON(data);
                setOption('#batch', result);
                $('#batch').selectric('refresh');
                setStudentData();
                setClassTableData();
            });
        };

        var setStudentData = function () {
            var groupId = $('#group').val();
            var courseId = $('#course').val();
            var semesterId = $('#semester').val();
            var termId = $('#term').val();
            var batchId = $('#batch').val();

            if (groupId == "" || courseId == "" || semesterId == "" || termId == "" || batchId == "") {
                var groupName = '#';
                if (groupId != "") {
                    groupName = $('#group option[value="' + groupId + '"]').text();
                }
                $('.grpName').html(groupName + " (0 record)");
                //$('#studentHasGroup tbody').html("");
                noRecordFoundNew('#studentHasGroup tbody', 0, 7);
            } else {
                var dataArr = {'groupId': groupId, 'courseId': courseId, 'semesterId': semesterId, 'termId': termId, 'batchId': batchId};

                ajaxCall(site_url + "compliance/ajaxAction", {'action': 'getStudentHasGroupEnrolled', 'data': dataArr}, function (data) {
                    var result = jQuery.parseJSON(data);

                    var groupName = $('#group option[value="' + groupId + '"]').text();
                    $('.grpName').html(groupName + " (" + (result.length) + " records)");

                    var table = "";
                    $.each(result, function (i, item) {
                        table += '<tr>' +
                                '<td><input type="checkbox" name="studentId[]" class="studentId" value="' + item.studentid + '"> ' + item.generated_stud_id + '</td>' +
                                '<td>' + item.first_name + '</td>' +
                                '<td>' + item.course_attempt + '</td>' +
                                '<td>' + formatDate(item.start_date) + ' TO<br> ' + formatDate(item.finish_date) + '</td>' +
                                '<td>' + item.status + '</td>' +
                                '<td>1</td>' +
                                '<td>-</td>' +
                                '</tr>';
                    });
                    $('#studentHasGroup tbody').html(table);
                    noRecordFoundNew('#studentHasGroup tbody', result.length, 7);
                });
            }
        };

        var setClassTableData = function () {

            var batchId = $('#batch').val();
            var subjectId = $('#subject').val();

            if (batchId == "" || subjectId == "") {
                noRecordFoundNew('#classListTable tbody', 0, 2);
                //$('#classListTable tbody').html("");
            } else {
                var dataArr = {'batchId': batchId, 'subjectId': subjectId};

                ajaxCall(site_url + "compliance/ajaxAction", {'action': 'getAvailableClassList', 'data': dataArr}, function (data) {
                    var result = jQuery.parseJSON(data);

                    var table = "";
                    $.each(result, function (i, item) {
                        table += '<tr>' +
                                '<td>' + item[0] + '</td>' +
                                '<td>' + item[1] + '</td>' +
                                '</tr>';
                    });
                    $('#classListTable tbody').html(table);
                    radioCheckboxClass();
                });
            }
        };

        var getFlexibleEnrolledList = function () {
            var batchId = $('#batch').val();
            var courseId = $('#course').val();

            if (batchId == "" || courseId == "") {
                $('#flexibleEnrolledList tbody').html("");
            } else {
                var dataArr = {'batchId': batchId, 'courseId': courseId};

                ajaxCall(site_url + "compliance/ajaxAction", {'action': 'getFlexibleEnrolledList', 'data': dataArr}, function (data) {
                    var result = jQuery.parseJSON(data);
                    $('#flexibleEnrolledList tbody').html(result);
                });
            }
        };

    };

    return{
        initBlukEnrollment: function () {
            initBlukEnrollment();
            radioCheckboxClass();
        },
        initFlexibleAllocation: function () {
            initFlexibleAllocation();
        }
    };
}();