<script setup>
import FormWrapper from '@spa/components/KendoModals/SidebarDrawer.vue';
import { useUsersStore } from '@spa/stores/modules/users/useUsersStore.js';
import BankDetails from '@spa/modules/staff-create/partials/BankDetail.vue';
import TCSIReport from '@spa/modules/staff-create/partials/TCSIReport.vue';
import Qualifications from '@spa/modules/staff-create/partials/Qualifications.vue';
import TargetRecruitmentStep from '@spa/modules/staff-create/agent/TargetRecruitmentStep.vue';
import EmploymentInfo from '@spa/modules/staff-create/partials/EmploymentInfo.vue';
import AddressPostal from '@spa/modules/staff-create/partials/AddressAndPostal.vue';
import RefereeStep from '@spa/modules/staff-create/agent/RefereeStep.vue';
import UserInfo from '@spa/modules/staff-create/partials/UserInfo.vue';
import AgentStatusStep from '@spa/modules/staff-create/agent/AgentStatusStep.vue';
import PersonalDevelopment from '@spa/modules/staff-create/partials/PersonalDevelopement.vue';
import UploadDocs from '@spa/modules/staff-create/partials/UploadDocs.vue';
import { useStepFormStore } from '@spa/stores/modules/users/useStepFormStore.js';
import { storeToRefs } from 'pinia';
import { computed, provide, watch, onMounted, nextTick } from 'vue';
import EmergencyContact from '@spa/modules/staff-create/partials/EmergencyContact.vue';
import PostalAddress from '@spa/modules/staff-create/partials/PostalAddress.vue';
import PayrollInformation from '@spa/modules/staff-create/partials/PayrollInformation.vue';
import EmploymentHistory from '@spa/modules/staff-create/partials/EmploymentHistory.vue';
import TrainingQualifications from '@spa/modules/staff-create/partials/TrainingQualifications.vue';
const props = defineProps({
    store: Object,
});
const userStore = useUsersStore();

const stepFormStore = useStepFormStore();

const { editDialog, selectedTab, selectedSubTab, formData } = storeToRefs(stepFormStore);

provide('isEdit', true);
const handleSubmit = async () => {
    const success = await stepFormStore.saveStep(
        formData.value[selectedTab.value],
        selectedTab.value,
        selectedSubTab.value
    );
    if (success) {
        editDialog.value = false;
    }
};

const getModalTitle = computed(() => {
    const titleMappings = {
        user_info: 'User Info',
        emergency_contact: 'Emergency Contact',
        address_postal: 'Address & Postal',
        employment_info: 'Employment Info',
        qualifications: 'Qualifications',
        personal_development: 'Personal Development',
        bank_details: 'Bank Details',
        tcsi_report: 'TCSI Report',
        upload_docs: 'Upload Docs',
        target_recruitment_countries: 'Target Recruitment Country',
        referees: 'Referee Details',
        agent_status: 'Agent Status',
    };
    return titleMappings[selectedTab.value] || 'Edit User';
});

const maxWidth = computed(() => {
    if (
        selectedTab.value === 'employment_info' ||
        selectedTab.value === 'qualifications' ||
        selectedTab.value === 'personal_development' ||
        selectedTab.value === 'upload_docs'
    )
        return '1100px';
    return '624px';
});

watch(
    () => editDialog.value,
    (newVal) => {
        if (newVal) {
            stepFormStore.userId = props.store.formData.secure_id;
            stepFormStore.setUserType(props.store.userType);
            formData.value = props.store.formData;
        }
    }
);
</script>

<template>
    <FormWrapper
        :visibleDialog="editDialog"
        :hideOnOverlayClick="false"
        :fixedActionBar="true"
        :width="'85%'"
        :maxWidth="maxWidth"
        :style="{
            maxWidth: maxWidth,
        }"
        :primaryBtnLabel="'Save Changes'"
        :secondaryBtnLabel="'Cancel'"
        :isDisabled="userStore.loading"
        :isSubmitting="userStore.loading"
        @drawerclose="editDialog = false"
        @drawersaved="handleSubmit"
        :position="'right'"
        :pt="{ content: 'px-5 pt-4 pb-8 space-y-4' }"
    >
        <template #title>
            <div class="text-xl font-medium">{{ getModalTitle }}</div>
        </template>
        <template #content>
            <div>
                <UserInfo
                    v-if="selectedTab === 'user_info' && selectedSubTab === 'personal_details'"
                    v-model="formData.user_info"
                    :user-type="formData.user_type"
                />
                <EmergencyContact
                    v-if="selectedTab === 'user_info' && selectedSubTab === 'emergency_contact'"
                    v-model="formData.user_info"
                    :user-type="formData.user_type"
                />
                <AddressPostal
                    v-if="
                        selectedTab === 'address_postal' && selectedSubTab === 'residential_address'
                    "
                    v-model="formData.address_postal"
                    :user-type="formData.user_type"
                />
                <PostalAddress
                    v-if="selectedTab === 'address_postal' && selectedSubTab === 'postal_address'"
                    v-model="formData.address_postal"
                    :user-type="formData.user_type"
                />
                <EmploymentInfo
                    v-if="selectedTab === 'employment_info' && selectedSubTab === 'employment_info'"
                    v-model="formData.employment_info"
                    :user-type="formData.user_type"
                />
                <EmploymentHistory
                    v-if="
                        selectedTab === 'employment_info' && selectedSubTab === 'employment_history'
                    "
                    v-model="formData.employment_info"
                    :user-type="formData.user_type"
                />
                <Qualifications
                    v-if="
                        selectedTab === 'qualifications' &&
                        selectedSubTab === 'education_qualifications'
                    "
                    v-model="formData.qualifications"
                    :user-type="formData.user_type"
                />
                <TrainingQualifications
                    v-if="
                        selectedTab === 'qualifications' &&
                        selectedSubTab === 'training_qualifications'
                    "
                    v-model="formData.qualifications"
                    :user-type="formData.user_type"
                />
                <PersonalDevelopment
                    v-if="selectedTab === 'personal_development'"
                    v-model="formData.personal_development"
                    :user-type="formData.user_type"
                />
                <BankDetails
                    v-if="selectedTab === 'bank_details' && selectedSubTab === 'bank_details'"
                    v-model="formData.bank_details"
                    :user-type="formData.user_type"
                />
                <PayrollInformation
                    v-if="
                        selectedTab === 'bank_details' && selectedSubTab === 'payroll_information'
                    "
                    v-model="formData.bank_details"
                    :user-type="formData.user_type"
                />
                <TCSIReport
                    v-if="selectedTab === 'tcsi_report'"
                    v-model="formData.tcsi_report"
                    :user-type="formData.user_type"
                />
                <UploadDocs
                    v-if="selectedTab === 'upload_docs'"
                    v-model="formData.upload_docs"
                    :user-type="formData.user_type"
                />
                <AgentStatusStep
                    v-if="selectedTab === 'agent_status'"
                    v-model="formData.agent_status"
                    :user-type="formData.user_type"
                />
                <TargetRecruitmentStep
                    v-if="selectedTab === 'target_recruitment_countries'"
                    v-model="formData.target_recruitment_countries"
                    :user-type="formData.user_type"
                />
                <RefereeStep
                    v-if="selectedTab === 'referees'"
                    v-model="formData.referees"
                    :user-type="formData.user_type"
                />
            </div>
        </template>
    </FormWrapper>
</template>

<style scoped></style>
