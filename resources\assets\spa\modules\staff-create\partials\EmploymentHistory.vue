<script setup>
import { computed, inject } from 'vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormUploader from '@spa/components/KendoInputs/FormUploader.vue';
import Button from '@spa/components/Buttons/Button.vue';
import { getValidationMessage } from '@spa/composables/formComposables.js';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
import StaffPositionSelect from '@spa/modules/staffposition/StaffPositionSelect.vue';

const props = defineProps({
    modelValue: {},
});
const isEdit = inject('isEdit');
const emit = defineEmits(['update:modelValue']);
const formData = computed({
    get() {
        const data = props.modelValue || {};
        if (!data.employment_histories) {
            return (data.employment_histories = []);
        }
        return data;
    },
    set(value) {
        emit('update:modelValue', value);
    },
});
const removeEmploymentHistory = (index) => {
    if (formData.value.employment_histories && formData.value.employment_histories.length > 0) {
        formData.value.employment_histories.splice(index, 1);
    }
};
const addEmploymentHistory = () => {
    if (!formData.value.employment_histories) {
        formData.value.employment_histories = [];
    }
    formData.value.employment_histories.push({
        title: '',
        organization: '',
        startDate: null,
        endDate: null,
        document: null,
    });
};
</script>
<template>
    <div class="space-y-4">
        <h3 class="mb-6 text-lg font-semibold">Employment History</h3>
        <div class="hidden grid-cols-6 items-end gap-4 xl:grid">
            <div class="px-4 py-2 md:col-span-1">
                <h4 class="text-sm font-medium leading-5 text-gray-500">Job Title</h4>
            </div>
            <div class="px-4 py-2 md:col-span-1">
                <h4 class="text-sm font-medium leading-5 text-gray-500">Organization</h4>
            </div>
            <div class="px-4 py-2 md:col-span-1">
                <h4 class="text-sm font-medium leading-5 text-gray-500">Start Date</h4>
            </div>
            <div class="px-4 py-2 md:col-span-1">
                <h4 class="text-sm font-medium leading-5 text-gray-500">End Date</h4>
            </div>
            <div class="px-4 py-2 md:col-span-2">
                <h4 class="text-sm font-medium leading-5 text-gray-500">Related Document</h4>
            </div>
        </div>
        <div class="border-t border-gray-200 bg-white">
            <div
                v-for="(job, index) in formData.employment_histories"
                :key="index"
                class="grid grid-cols-1 items-start items-end gap-4 border-b border-gray-200 p-4 xl:grid-cols-6"
            >
                <div class="md:col-span-1">
                    <FormInput
                        v-model="job.title"
                        placeholder="Title"
                        :required="true"
                        v-bind="
                            getValidationMessage(formData, `employment_histories.${index}.title`)
                        "
                    />
                </div>
                <div class="md:col-span-1">
                    <FormInput
                        v-model="job.organization"
                        placeholder="Organization"
                        :required="true"
                        v-bind="
                            getValidationMessage(
                                formData,
                                `employment_histories.${index}.organization`
                            )
                        "
                    />
                </div>
                <div class="md:col-span-1">
                    <FormDatePicker
                        v-model="job.start_date"
                        type="date"
                        :required="true"
                        v-bind="
                            getValidationMessage(
                                formData,
                                `employment_histories.${index}.start_date`
                            )
                        "
                        emit-format="yyyy-MM-dd"
                        placeholder="Start Date"
                    />
                </div>
                <div class="md:col-span-1">
                    <FormDatePicker
                        v-model="job.end_date"
                        :required="true"
                        v-bind="
                            getValidationMessage(formData, `employment_histories.${index}.end_date`)
                        "
                        placeholder="End Date"
                        emit-format="yyyy-MM-dd"
                    />
                </div>
                <div class="flex items-end gap-2 md:col-span-2">
                    <FormUploader
                        v-model="job.document"
                        accept=".pdf,.doc,.docx"
                        mode="simple"
                        class="flex-1"
                        placeholder="Document"
                    />
                    <button
                        type="button"
                        @click="removeEmploymentHistory(index)"
                        class="flex-shrink-0 rounded-full p-2 text-red-500 transition-colors duration-150 hover:bg-red-50 hover:text-red-700"
                        :aria-label="`Remove employment history ${index + 1}`"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="18"
                            height="18"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            class="pointer-events-none"
                        >
                            <circle cx="12" cy="12" r="10" />
                            <path d="m15 9-6 6" />
                            <path d="m9 9 6 6" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <div class="flex justify-end p-4">
            <Button
                @click="() => addEmploymentHistory()"
                variant="secondary"
                class="flex items-center gap-2"
            >
                <svg
                    class="h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                >
                    <path d="M12 5v14M5 12h14" />
                </svg>
                Add More
            </Button>
        </div>
    </div>
</template>

<style scoped></style>
