<template>
    <SidebarDrawer
        :visibleDialog="visible"
        :hideOnOverlayClick="false"
        :fixedActionBar="false"
        :width="'50%'"
        @drawerclose="cancelProcess"
        :primaryBtnLabel="'Save'"
        :existTertiary="true"
        :isDisabled="false"
        :isSubmitting="isSaving"
    >
        <template #title>
            <div class="text-lg font-medium">{{ modelTitle }}</div>
        </template>
        <template #content>
            <k-form
                @submit="handleSave"
                :initialValues="intakeData"
                :ignoreModified="false"
                :key="JSON.stringify(intakeData)"
            >
                <formcontent
                    @cancel="cancelProcess"
                    :saving="isSaving"
                    :course-duration="fees.course_duration"
                    :duration-type="
                        formInits.course_duration_types?.find(
                            (type) => type.id === fees.couse_duration_type
                        )
                    "
                />
            </k-form>
        </template>
    </SidebarDrawer>
</template>

<script>
import { router } from '@inertiajs/vue3';
import { useForm } from '@inertiajs/vue3';
import axios from 'axios';
import { Form } from '@progress/kendo-vue-form';
import { mapState } from 'pinia';
import { useCoursesStore } from '@spa/stores/modules/courses';
import { courseIntakes } from '@spa/services/courseFormResource';
import FormContent from '@spa/pages/courses/includes/forms/CourseIntakeFormContent.vue';
import SidebarDrawer from '@spa/components/KendoModals/SidebarDrawer.vue';
import { Button } from '@progress/kendo-vue-buttons';

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        modelTitle: {
            type: String,
            default: '',
        },
        intakeData: {
            type: Object,
            default: () => ({}),
        },
        filters: {
            type: Object,
            default: () => ({}),
        },
    },
    data: function () {
        return {
            cancelSource: axios.CancelToken.source(),
            isSaving: false,
        };
    },
    components: {
        'k-form': Form,
        formcontent: FormContent,
        Button,
        SidebarDrawer,
    },
    computed: {
        ...mapState(useCoursesStore, ['course', 'fees', 'formInits', 'getIntakes', 'setIntakes']),
        getCourseData: function () {
            //format the data to provide it to the form
            return this.courseGeneral(this.course);
        },
    },
    methods: {
        courseIntakes,
        handleScheduleTypeChange() {
            if (this.newIntake.scheduleType === 'single') {
                this.newIntake.recurringPattern = '';
            } else {
                this.newIntake.recurringPattern = 'daily';
            }
        },
        editIntake(intake) {
            this.newIntake = { ...intake };
        },
        deleteIntake(intake) {
            if (confirm('Are you sure you want to delete this intake?')) {
                this.intakes = this.intakes.filter((i) => i.id !== intake.id);
            }
        },
        formatDate(date) {
            return new Date(date).toLocaleDateString();
        },
        cancelProcess() {
            this.isSaving = false;
            this.$emit('cancel');
        },
        closeForm() {
            this.isSaving = false;
            this.$emit('saved');
        },
        getDateString(date) {
            if (!(date instanceof Date) || isNaN(date.getTime())) {
                return null; // not a valid date
            }
            const yy = String(date.getFullYear()); // last 2 digits of year
            const mm = String(date.getMonth() + 1).padStart(2, '0'); // months are 0-based
            const dd = String(date.getDate()).padStart(2, '0');
            return `${yy}-${mm}-${dd}`;
        },
        handleSave(formData) {
            console.log('formData', formData);
            if (this.isSaving) {
                return;
            }
            this.isSaving = true;

            /* normalize the date without timezone */
            formData['class_start_date'] = this.getDateString(formData['class_start_date']);
            formData['class_end_date'] = this.getDateString(formData['class_end_date']);
            formData['intake_start'] = this.getDateString(formData['intake_start']);
            formData['intake_end'] = this.getDateString(formData['intake_end']);

            formData['filters'] = this.filters;
            $http
                .post(this.route('spa.courses.saveshortcourseintake'), formData, {
                    cancelToken: this.cancelSource.token,
                })
                .then((resp) => {
                    if (resp['success']) {
                        this.setIntakes(resp?.intakes, resp?.meta);
                        this.closeForm();
                    }
                })
                .catch((error) => {
                    this.isSaving = false;
                })
                .finally(() => {
                    this.isSaving = false;
                });
        },
    },
    watch: {},
};
</script>
