<script setup>
import Layout from '@spa/pages/Layouts/Layout';
import { Head } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import InterventionStrategyListComponent from '@spa/modules/interventionstrategy/InterventionStrategyListComponent.vue';
import InterventionTypeListComponent from '@spa/modules/interventiontype/InterventionTypeListComponent.vue';
</script>
<template>
    <Layout :no-spacing="true">
        <Head title="Intervention Setup" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Intervention Setup" :back="false" />
        </template>
        <div class="h-screen-header flex flex-col px-8 py-6">
            <!--            <InterventionStrategyListComponent />-->
            <InterventionTypeListComponent />
        </div>
    </Layout>
</template>
<style scoped></style>
