<template>
    <AsyncForm
        :initial-values="formData"
        @submit="onSubmit"
        @submitcheck="handleSubmitclick"
        @change="onChange"
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        :dialogTitle="'Assign Subject'"
        :override="true"
        :store="store"
        :max-width="'600px'"
    >
        <div class="grid flex-1 grid-cols-1 gap-6 overflow-y-auto p-4 px-6">
            <FormRadioGroup
                :id="'course_status'"
                :name="'course_status'"
                :label="'Course Status'"
                :disabled="isEdit"
                :data-items="[
                    {
                        label: 'Active',
                        value: 1,
                    },
                    {
                        label: 'Inactive',
                        value: 0,
                    },
                ]"
                :default-value="1"
                v-model="formData.course_status"
                :required="true"
                :layout="'horizontal'"
                @change="
                    () => {
                        courseStore.filters.course_status = formData.course_status;
                    }
                "
                :indicaterequired="true"
                :validation-message="store.errors?.course_status"
                :valid="!store.errors?.course_status"
            />
            <CoursesSelect
                name="course_id"
                :disabled="isEdit"
                label="Select Course"
                :filters="{
                    status: formData.course_status,
                }"
                v-model="formData.course_id"
                :required="true"
                :indicaterequired="true"
                :validation-message="store.errors?.course_id"
                :valid="!store.errors?.course_id"
            />
            <div class="space-y-2">
                <template v-if="isEdit">
                    <label class="k-label">Selected Subject</label>
                    <div class="flex items-center gap-2">
                        <Checkbox v-model="formData.subject_id" :checked="true" :disabled="true" />
                        <label for="is_primary">{{ formData.subject }}</label>
                    </div>
                </template>
                <CourseSubjectCheckbox
                    name="subject_id"
                    :label="isEdit ? '' : 'Select Subject'"
                    :filters="{
                        course_id: formData.course_id,
                    }"
                    :model-value="formData.subject_ids"
                    @update:modelValue="
                        (val) => {
                            formData.subject_id = val[0];
                            formData.subject_ids = val;
                        }
                    "
                    :required="true"
                    :indicaterequired="true"
                    :validation-message="store.errors?.subject_id"
                    :valid="!store.errors?.subject_id"
                />
            </div>

            <FormDropDown
                name="knowledge_level"
                label="Knowledge Level"
                :placeholder="'Select Knowledge Level'"
                :default-item="{ text: 'Select Knowledge Level', value: '' }"
                :text-field="'text'"
                :data-item-key="'value'"
                :value-field="'value'"
                :data-items="knowledgeLevelOptions"
                v-model="formData.knowledge_level"
                :required="true"
                :indicaterequired="true"
                :value-primitive="true"
                :validation-message="store.errors?.knowledge_level"
                :valid="!store.errors?.knowledge_level"
            />
        </div>
    </AsyncForm>
</template>
<script setup>
import { computed, watch, inject } from 'vue';
import { useTeacherMatrixStore } from '@spa/stores/modules/teacher/useTeacherMatrixStore.js';
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import SubjectsCheckbox from '@spa/modules/teacher/partials/SubjectsCheckbox.vue';
import TeacherSelect from '@spa/modules/teacher/TeacherSelect.vue';
import { Field } from '@progress/kendo-vue-form';
import CoursesSelect from '@spa/modules/teacher/partials/CoursesSelect.vue';
import { useCoursesStore } from '@spa/stores/modules/courses/useCoursesStore.js';
import { useCountryStore } from '@spa/stores/modules/config/useCountryStore.js';
import { teacherConfig } from '@spa/config/teacherConfig.js';
import { storeToRefs } from 'pinia';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import { requiredtrue } from '@spa/services/validators/_courseValidator.js';
import FormRadioGroup from '@spa/components/KendoInputs/FormRadioGroup.vue';
import StaffsSelect from '@spa/modules/users/staffs/StaffsSelect.vue';
import CourseSubjectCheckbox from '@spa/modules/coursesubject/CourseSubjectCheckbox.vue';
import FormValidationWrapper from '@spa/components/KendoInputs/FormValidationWrapper.vue';
import FormCheckbox from '@spa/components/KendoInputs/FormCheckbox.vue';
import Checkbox from '@spa/components/Checkbox.vue';
import FormCheckboxInline from '@spa/components/KendoInputs/FormCheckboxInline.vue';

const props = defineProps({
    store: {
        type: Object,
        required: true,
    },
});

const teacherId = inject('teacherId');
const store = useTeacherMatrixStore();

const { formData } = storeToRefs(store);

const isEdit = computed(() => {
    return !!formData.value.id;
});

const courseStore = useCoursesStore();

const countryStore = useCountryStore();

const { knowledge_level_code } = teacherConfig;

const knowledgeLevelOptions = computed(() => {
    return Object.entries(knowledge_level_code).map(([key, value]) => ({
        text: value,
        value: key,
    }));
});

const handleSubmitclick = (value) => {
    store.isFormValid = value;
};

watch(
    () => store.formDialog,
    (val) => {
        if (val) {
            formData.value = {
                ...formData.value,
                teacher_id: teacherId,
            };
        }
    }
);
</script>
<style lang=""></style>
