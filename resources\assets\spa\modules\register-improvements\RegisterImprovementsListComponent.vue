<template>
    <AsyncGrid
        :columns="getColumns"
        :store="store"
        :show-refresh-button="false"
        :show-filter-button="false"
        :add-permissions="null"
        :enableSelection="false"
        :has-create-action="true"
        :has-export="false"
        :has-filters="true"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
                store.selected = [];
            }
        "
        :filter-columns="3"
        :create-btn-label="'Register Improvement'"
        :search-placeholder="'Search by category, requester...'"
        :has-actions="true"
        :actions-config="{
            width: 150,
        }"
        :has-sticky-action="true"
        :actions="getActions"
    >
        <template #filters>
            <FilterBlockWrapper label="Category">
                <ImprovementCategorySelect
                    v-model="store.filters.category"
                    :placeholder="'Select Category'"
                    :has-create-action="false"
                />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Logged By" v-if="userType === 6">
                <ImprovementFilterSelect
                    v-model="store.filters.loggedBy"
                    :options="store.loggers"
                    :placeholder="'Select Logged By'"
                />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Requested By" v-if="userType === 6">
                <ImprovementFilterSelect
                    v-model="store.filters.requestedBy"
                    :options="store.requesters"
                    :placeholder="'Select Requested By'"
                />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Lodged Date">
                <FormDateRangePicker v-model="store.filters.loggedDate" />
            </FilterBlockWrapper>
        </template>
        <template #bulk-actions> </template>
        <template #body-cell-logged_date="{ props }">
            {{ convertJsDateFormat(props.dataItem?.logged_date) }}
        </template>
        <template #body-cell-status="{ props }">
            <Badge :variant="props.dataItem?.status === '1' ? 'success' : 'error'">{{
                props.dataItem?.status === '1' ? 'Open' : 'Closed'
            }}</Badge>
        </template>
        <template #body-cell-user_type="{ props }">
            <Badge :variant="'default'">{{ props.dataItem?.user_type_name }}</Badge>
        </template>
        <template #body-cell-logged_by="{ props }">
            <span class="font-medium">{{ props.dataItem?.logged_by_name }}</span>
        </template>
        <template #body-cell-requester_name="{ props }">
            <div class="flex items-center gap-2">
                <Avatar
                    :label="getInitialName(props.dataItem?.requester?.full_name)"
                    class="shrink-0"
                />
                <div class="space-y-0">
                    <span class="text-left font-medium text-gray-900">{{
                        props.dataItem?.requester?.full_name
                    }}</span>
                </div>
            </div>
        </template>
        <template #body-cell-case_detail="{ props }">
            <span class="line-clamp-3 text-gray-500">{{ props.dataItem?.case_detail }}</span>
        </template>
        <template #body-cell-category="{ props }">
            <span class="font-medium">{{ props.dataItem?.category?.name }}</span>
        </template>
        <template #body-cell-actions="{ props }">
            <div class="flex justify-start space-x-2">
                <Tooltip
                    :anchor-element="'target'"
                    :position="'top'"
                    :parentTitle="true"
                    :tooltipClassName="'flex !p-1'"
                    :class="'w-full'"
                >
                    <button
                        @click="store.edit(props.dataItem)"
                        class="cursor-pointer text-gray-400"
                        title="Edit"
                    >
                        <icon :name="'pencil'" :width="16" :height="16" :fill="'currentColor'" />
                    </button>
                </Tooltip>
                <Tooltip
                    :anchor-element="'target'"
                    :position="'top'"
                    :parentTitle="true"
                    :tooltipClassName="'flex !p-1'"
                    :class="'w-full'"
                >
                    <button
                        @click="store.confirmDelete(props.dataItem)"
                        class="cursor-pointer"
                        title="Delete"
                    >
                        <icon :name="'delete'" :width="16" :height="16" />
                    </button>
                </Tooltip>
            </div>
        </template>
        <template #filter-user_type="{ props, shown }">
            <GridColumnCheckboxFilter
                :value="store.filters?.user_type"
                :options="props.options"
                @update:value="store.filters.user_type = $event"
                @apply="store.fetchPaged()"
            />
        </template>
        <template #filter-status="{ props, shown }">
            <GridColumnCheckboxFilter
                :value="store.filters?.status"
                :options="props.options"
                @update:value="store.filters.status = $event"
                @apply="store.fetchPaged()"
            />
        </template>
    </AsyncGrid>
    <RegisterImprovementForm v-if="userType === 6" />
    <RegisterFeedbackForm v-else />
    <RegisterImprovementViewComponent :userType="userType" />
</template>

<script setup>
import { useRegisterImprovementStore } from '@spa/stores/modules/continuous-improvement/registerImprovementStore.js';
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { onMounted, computed, ref, provide } from 'vue';
import FormatDate from '@spa/components/FormatDate.vue';
import { Tooltip } from '@progress/kendo-vue-tooltip';
import RegisterImprovementForm from '@spa/modules/register-improvements/RegisterImprovementForm.vue';
import Badge from '@spa/components/badges/Badge.vue';
import ImprovementFilterSelect from '@spa/modules/register-improvements/partials/ImprovementFilterSelect.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import FormDateRangePicker from '@spa/components/KendoInputs/FormDateRangePicker.vue';
import GridColumnCheckboxFilter from '@spa/components/AsyncComponents/Grid/Partials/GridColumnCheckboxFilter.vue';
import Avatar from '@spa/components/Avatar/Avatar.vue';
import { getInitialName } from '@spa/composables/strComposables.js';
import ImprovementCategorySelect from '@spa/modules/improvementcategory/ImprovementCategorySelect.vue';
import RegisterFeedbackForm from '@spa/modules/register-improvements/partials/RegisterFeedbackForm.vue';
import RegisterImprovementViewComponent from '@spa/modules/register-improvements/RegisterImprovementViewComponent.vue';
import { convertJsDateFormat } from '../../composables/dateTimeComposables.js';

const store = useRegisterImprovementStore();

const props = defineProps({
    filters: {
        type: Object,
        default: () => {},
    },
    userType: {
        type: Number,
        default: 6,
    },
});

provide('isFeedback', props.userType !== 6);
provide('userType', props.userType);

const columns = ref([
    {
        name: 'requester_name',
        title: 'Requested By',
        field: 'requester_name',
        sortable: true,
        replace: true,
        width: 200,
    },
    {
        field: 'user_type',
        name: 'user_type',
        title: 'User Type',
        replace: true,
        sortable: true,
        filterable: true,
        customFilter: true,
        width: 120,
        filterableConfig: {
            type: 'select',
            options: [
                { value: 1, label: 'Staff' },
                { value: 2, label: 'Student' },
                { value: 3, label: 'Teacher' },
                { value: 4, label: 'Agent' },
                { value: 5, label: 'Employer' },
                { value: 6, label: 'Admin' },
            ],
        },
    },
    {
        name: 'category',
        title: 'Category',
        field: 'category',
        sortable: true,
        replace: true,
        width: 200,
    },
    {
        name: 'logged_by',
        title: 'Logged By',
        field: 'logged_by',
        sortable: true,
        replace: true,
        width: 200,
    },
    {
        name: 'logged_date',
        title: 'Logged Date',
        field: 'logged_date',
        sortable: true,
        replace: true,
        width: 150,
    },
    {
        name: 'case_detail',
        title: 'Details',
        field: 'case_detail',
        width: 300,
        sortable: false,
        replace: true,
    },
    {
        name: 'status',
        title: 'Status',
        field: 'status',
        sortable: true,
        replace: true,
        width: 100,
        filterable: true,
        customFilter: true,
        filterableConfig: {
            type: 'select',
            options: [
                {
                    label: 'Open',
                    value: 1,
                },
                {
                    label: 'Closed',
                    value: 0,
                },
            ],
        },
    },
]);

const getActions = computed(() => {
    if (props.userType !== 6) {
        return ['view'];
    }
    return ['edit', 'delete', 'view'];
});

const getColumns = computed(() => {
    if (props.userType !== 6) {
        return columns.value.filter(
            (col) => !['logged_by', 'user_type', 'requester_name'].includes(col.name)
        );
    }
    return columns.value;
});

const initFilters = () => {
    store.filters = {
        loggedDate: null,
        category: null,
        loggedBy: null,
        requestedBy: null,
        status: null,
        userType: null,
        ...props.filters,
    };
};

const initInertiaData = () => {};
onMounted(() => {
    initFilters();
    store.getCategories();
});
</script>
