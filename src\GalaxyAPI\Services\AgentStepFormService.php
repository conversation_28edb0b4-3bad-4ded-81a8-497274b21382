<?php

namespace GalaxyAPI\Services;

use App\Helpers\Helpers;
use App\Model\CommonModel;
use App\Model\Users;
use App\Model\v2\Agent;
use App\Model\v2\AgentImages;
use GalaxyAPI\Enums\UserStatusEnum;
use GalaxyAPI\Interfaces\UserTypeStepFormInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class AgentStepFormService implements UserTypeStepFormInterface
{
    public function __construct(
        public readonly UserService $userService,
    ) {}

    public function map(string $step, array $data, Model $agent): Model
    {
        if ($step === 'review') {
            return $agent;
        }

        /** @var Agent $agent */
        return match ($step) {
            'user_info' => $this->saveUserInfoStep($agent, $data),
            'address_postal' => $this->saveAddressAndPostalStep($agent, $data),
            'bank_details' => $this->saveBankDetailsStep($agent, $data),
            'target_recruitment_countries' => $this->saveTargetRecruitmentCountries($agent, $data),
            'referees' => $this->saveRefereeDetails($agent, $data),
            'upload_docs' => $this->saveDocuments($agent, $data),
            default => throw new \InvalidArgumentException("Unsupported step: $step for Staff"),
        };
    }

    private function saveUserInfoStep(Agent $agent, array $data): Agent
    {
        $user = null;
        $collegeId = Auth::user()->college_id;
        if (! $agent->user_id && isset($data['email'])) {
            $user = Users::where('email', $data['email'])->first();
            if (! $user) {
                $userData = [
                    'college_id' => $collegeId,
                    'name' => trim(($data['name'] ?? '')),
                    'username' => $data['email'],
                    'email' => $data['email2'],
                    'phone' => $data['phone'] ?? null,
                    'mobile' => $data['mobile'] ?? null,
                    'role_id' => @$data['super_agent_id'] ? 15 : 14,
                    'password' => Hash::make(Str::random(12)),
                    'status' => UserStatusEnum::PENDING->value,
                ];

                $user = Users::create($userData);
            }
            $agent->user_id = $user->id;
        }
        $file = $data['user_image'] ?? null;
        if ($file) {
            $this->userService->uploadProfile($agent->user_id, $file);
        }
        $agent->fill([
            'agent_code' => $data['agent_code'] ?? null,
            'agency_name' => $data['name'] ?? null,
            'contact_person' => $data['emergency_contact_name'] ?? null,
            'contact_person_phone_1' => $data['emergency_phone_1'] ?? null,
            'contact_person_phone_2' => $data['emergency_phone_2'] ?? null,
            'contact_person_email' => $data['emergency_email'] ?? null,
            'contact_person_address' => $data['emergency_address'] ?? null,
            'primary_email' => $data['email'] ?? null,
            'alertnet_email' => $data['work_email'] ?? null,
            'website' => $data['website'] ?? null,
            'telephone' => $data['phone'] ?? null,
            'fax' => $data['fax'] ?? null,
            'mobile1' => $data['mobile'] ?? null,
            'mobile2' => $data['mobile2'] ?? null,
            'industry_id' => $data['industry_id'] ?? null,
            'account_manager_id' => $data['account_manager_id'] ?? null,
            'super_agent_id' => $data['super_agent_id'] ?? null,
            'total_employess' => $data['total_employes'] ?? null,
            'notes' => $data['notes'] ?? null,
        ]);

        return $agent;
    }

    private function saveAddressAndPostalStep(Agent $agent, array $data)
    {
        $agent->fill([
            'office_address' => $data['residential_address'] ?? null,
            'office_country' => $data['residential_country'] ?? null,
            'office_city' => $data['residential_city'] ?? null,
            'office_state' => $data['residential_state'] ?? null,
            'office_postcode' => $data['residential_postcode'] ?? null,
            'office_ABN' => $data['residential_abn'] ?? null,
            'postal_address' => $data['postal_address'] ?? null,
            'postal_country' => $data['postal_country'] ?? null,
            'postal_city' => $data['postal_city'] ?? null,
            'postal_state' => $data['postal_state'] ?? null,
            'postal_postcode' => $data['postal_postcode'] ?? null,
            'postal_ABN' => $data['postal_abn'] ?? null,
        ]);

        $agent->updated_by = auth()->id();

        return $agent;

    }

    private function saveBankDetailsStep(Agent $agent, array $data)
    {
        $agent->fill([
            'bank_name' => $data['bank_name'] ?? null,
            'bank_branch' => $data['bank_branch'] ?? null,
            'bank_account_name' => $data['account_name'] ?? null,
            'bank_account_number' => $data['account_number'] ?? null,
            'BSB' => $data['bsb'] ?? null,
            'bank_swift_code' => $data['swift_code'] ?? null,
            'bank_country' => $data['bank_country'] ?? null,
        ]);

        return $agent;

    }

    private function saveTargetRecruitmentCountries(Agent $agent, array $data)
    {
        $agent->fill([
            'target_primary_country' => $data['target_recruitment_countries'][0]['name'] ?? null,
            'target_secondry_country' => $data['target_recruitment_countries'][1]['name'] ?? null,
            'targeted_country' => implode(',', array_column($data['target_recruitment_countries'], 'name')) ?? null,
        ]);

        return $agent;
    }

    private function saveRefereeDetails(Agent $agent, array $data)
    {
        $agent->agentReferees()->delete();
        foreach ($data['referee_details'] as $referee) {
            $agent->agentReferees()->create([
                'client_name' => $referee['name'],
                'email' => $referee['email'],
                'college_id' => auth()->user()->college_id,
            ],
            );
        }

        return $agent;
    }

    private function saveDocuments(Agent $agent, array $data)
    {
        $agentId = $agent->id;
        $user = auth()->user();

        $rootFolder = Config::get('constants.arrCollegeRootFolder');
        $filePath = Config::get('constants.uploadFilePath.AgentFiles');
        $destinationPath = Helpers::changeRootPath($filePath, $agentId);

        $objCommonModel = new CommonModel;

        $meterialInfo = [
            'college_id' => $user->college_id,
            'folder_name' => $rootFolder['AgentFiles'],
            'sub_folder_name' => $agentId,
            'user_id' => $user->id,
        ];

        $clgMaterialParentId = $objCommonModel->getSubParentId($meterialInfo);

        foreach ($data as $key => $file) {
            if (Str::startsWith($key, 'agent_document_') && $file instanceof UploadedFile) {
                $checklistId = str_replace('agent_document_', '', $key);

                $originalName = $file->getClientOriginalName();
                $filename = now()->format('YmdHisu').'-'.$originalName;
                $fileSize = ceil($file->getSize() / 1024); // in KB

                $file->move($destinationPath['default'], $filename);

                // Get optional meta fields if provided
                $documentName = $data['document_name'][$checklistId] ?? 'Unknown Document';
                $isCompulsory = $data['is_compulsory'][$checklistId] ?? 0;
                $approved = $data['approved'][$checklistId] ?? 1;

                $existingFile = $agent->agentImages()->where('checklist_id', $checklistId)->first();

                if ($existingFile) {
                    $existingFile->update([
                        'original_name' => $originalName,
                        'images' => $filename,
                        'document_name' => $documentName,
                        'checklist_id' => $checklistId,
                        'is_compulsory' => $isCompulsory,
                        'comment' => ' ',
                        'approved' => $approved,
                    ]);
                } else {
                    AgentImages::create([
                        'agent_id' => $agentId,
                        'original_name' => $originalName,
                        'images' => $filename,
                        'document_name' => $documentName,
                        'checklist_id' => $checklistId,
                        'is_compulsory' => $isCompulsory,
                        'comment' => ' ',
                        'approved' => $approved,
                    ]);
                }

                $objCommonModel->addCollegeMaterialInfo([
                    'college_id' => $user->college_id,
                    'original_name' => $originalName,
                    'file_name' => $filename,
                    'size' => $fileSize,
                    'type' => 'File',
                    'parent_id' => $clgMaterialParentId,
                    'file_path' => $destinationPath['view'].$agentId.'/',
                    'user_id' => $user->id,
                ]);
            }
        }

        return $agent;
    }
}
