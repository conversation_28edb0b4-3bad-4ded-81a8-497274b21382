<script setup>
import { onMounted } from 'vue';
import AsyncSelect from '@spa/components/AsyncComponents/Select/AsyncSelect.vue';
import { useUsiStudentStore } from '@spa/stores/modules/usi/usiStudentStore.js';
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';

import { computed } from 'vue';
const props = defineProps({
    modelValue: [String, Number, Array, Object],
    label: String,
    className: String,
    optionValue: {
        type: String,
        default: 'id',
    },
    optionLabel: {
        type: String,
        default: 'student_name',
    },
    disabled: Boolean,
    clearable: {
        type: Boolean,
        default: true,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    readonly: Boolean,
    useChips: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: 'Select Student',
    },
    hasCreateAction: {
        type: Boolean,
        default: false,
    },
    valid: {
        type: <PERSON>olean,
        default: true,
    },
    validationMessage: {
        type: String,
        default: '',
    },
    indicaterequired: {
        type: Boolean,
        default: false,
    },
    filters: {
        type: Object,
        default: () => ({}),
    },
    sortBy: {
        type: String,
        default: 'id',
    },
});
const emit = defineEmits(['update:modelValue']);
const vModel = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});
const store = useUsiStudentStore();
</script>
<template>
    <AsyncSelect
        :label="label"
        :className="className"
        :optionValue="optionValue"
        :optionLabel="optionLabel"
        :disabled="disabled"
        :store="store"
        v-model="vModel"
        :clearable="clearable"
        :multiple="multiple"
        :readonly="readonly"
        :useChips="useChips"
        :placeholder="placeholder"
        :hasCreateAction="hasCreateAction"
        :valid="valid"
        :validationMessage="validationMessage"
        :indicaterequired="indicaterequired"
        :filters="filters"
        :sort-by="sortBy"
        :enable-virtual="false"
    />
</template>
<style scoped></style>
