<template>
    <form-element>
        <fieldset class="k-form-fieldset space-y-6" :style="{ maxWidth: '650px' }">
            <div class="mb-3 flex items-center justify-between">
                <div class="w-auto">
                    <label for="flexible_attendance" class="font-medium text-gray-700">
                        Does this qualification have flexible attendance?
                    </label>
                </div>
                <div class="w-auto">
                    <field
                        :id="'flexible_attendance'"
                        :name="'flexible_attendance'"
                        :component="'switchTemplate'"
                    >
                        <template v-slot:switchTemplate="{ props }">
                            <formswitch
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
            </div>
            <div class="mb-3 flex items-center justify-between" v-if="isShortCourse">
                <field
                    :id="'course_delivery_mode'"
                    :name="'course_delivery_mode'"
                    :label="'Enter Course Delivery Mode'"
                    :component="'courseDeliveryMode'"
                    :text-field="'text'"
                    :data-item-key="'id'"
                    :valueField="'id'"
                    :valuePrimitive="true"
                    :default-item="defaultDeliveryMode"
                    :data-items="courseDeliveryModes"
                    :validator="required"
                    @change="handleDeliveryModeChange"
                >
                    <template v-slot:courseDeliveryMode="{ props }">
                        <formdropdown
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
            </div>
            <div class="space-y-4 rounded-md border border-gray-200 p-4">
                <div class="text-base text-gray-900">Hours</div>
                <div class="grid grid-cols-1 gap-x-6 gap-y-4 md:grid-cols-2">
                    <field
                        :id="'course_duration'"
                        :name="'course_duration'"
                        :label="'Course Duration'"
                        :step="counterStep"
                        :spinners="showSpinnersInNumbers"
                        :component="'hoursFees'"
                        :placeholder="'Enter Course Duration'"
                        :validator="requiredDuration"
                        :suffix="getCourseDurationType"
                        :dropdown-data="durationTypesData"
                        :dropdown-field="'text'"
                        :dropdown-key="'id'"
                        :dropdown-value="'id'"
                        :dropdown-primitive="true"
                        @suffixchanged="changeDurationType"
                    >
                        <template v-slot:hoursFees="{ props }">
                            <numerictextbox
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                    <field
                        :id="'maximum_weekly_study'"
                        :name="'maximum_weekly_study'"
                        :label="'Maximum Weekly Study'"
                        :component="'hoursFees'"
                        :step="counterStep"
                        :spinners="showSpinnersInNumbers"
                        :placeholder="'Enter Maximum Weekly Study'"
                        :validator="requiredpositiveinteger"
                        :suffix="'hrs'"
                    >
                        <template v-slot:hoursFees="{ props }">
                            <numerictextbox
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div
                    class="grid grid-cols-2 gap-x-6 gap-y-4"
                    v-if="showOnlineHours || showOfflineHours"
                >
                    <field
                        :id="'online_hours'"
                        :name="'online_hours'"
                        :label="'Online Hours'"
                        :component="'hoursFees'"
                        :step="counterStep"
                        :spinners="showSpinnersInNumbers"
                        :placeholder="'Enter Online Study Hours'"
                        :validator="checkOnlineHours"
                        :suffix="'hrs'"
                        v-if="showOnlineHours"
                    >
                        <template v-slot:hoursFees="{ props }">
                            <numerictextbox
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                    <field
                        :id="'face_to_face_hours'"
                        :name="'face_to_face_hours'"
                        :label="'Face To Face Hours'"
                        :component="'hoursFees'"
                        :step="counterStep"
                        :spinners="showSpinnersInNumbers"
                        :placeholder="'Enter Face-to-face Study Hours'"
                        :validator="checkFaceToFaceHours"
                        :suffix="'hrs'"
                        v-if="showOfflineHours"
                    >
                        <template v-slot:hoursFees="{ props }">
                            <numerictextbox
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div class="grid grid-cols-2 gap-x-6 gap-y-4" v-if="isHigherEd">
                    <field
                        :id="'effective_start'"
                        :name="'effective_start'"
                        :label="effectiveStartText"
                        :component="'hoursFees'"
                        :placeholder="effectiveStartText"
                        :validator="checkEffectiveDate"
                    >
                        <template v-slot:hoursFees="{ props }">
                            <datepicker
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                    <field
                        :id="'effective_end'"
                        :name="'effective_end'"
                        :label="effectiveEndText"
                        :component="'hoursFees'"
                        :placeholder="effectiveEndText"
                        :validator="checkEffectiveEnd"
                    >
                        <template v-slot:hoursFees="{ props }">
                            <datepicker
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
            </div>
            <div class="rounded-md border border-gray-200 p-4">
                <div class="mb-4 text-base text-gray-900">Fees</div>
                <div class="grid grid-cols-2 gap-x-6 gap-y-4" v-if="isShortCourse">
                    <div v-if="showFaceToFaceFee">
                        <field
                            :id="'facetoface_fee'"
                            :name="'facetoface_fee'"
                            :label="'Tuition Fee For Face-To-Face Delivery Mode'"
                            :component="'hoursFees'"
                            :step="counterStep"
                            :spinners="showSpinnersInNumbers"
                            :placeholder="'Tuition Fee For Face-To-Face Delivery Mode'"
                            :validator="checkFaceToFaceFeeRequired"
                            :format="'c2'"
                        >
                            <template v-slot:hoursFees="{ props }">
                                <numerictextbox
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                    <div v-if="showOnlineFee">
                        <field
                            :id="'online_fee'"
                            :name="'online_fee'"
                            :label="'Tuition Fee For Online Delivery Mode'"
                            :component="'hoursFees'"
                            :step="counterStep"
                            :spinners="showSpinnersInNumbers"
                            :placeholder="'Tuition Fee For Online Delivery Mode'"
                            :validator="checkOnlineFeeRequired"
                            :format="'c2'"
                        >
                            <template v-slot:hoursFees="{ props }">
                                <numerictextbox
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                    <div v-if="showBlendedFee">
                        <field
                            :id="'tuition_fee'"
                            :name="'tuition_fee'"
                            :label="'Tuition Fee For Blended Mode'"
                            :component="'hoursFees'"
                            :step="counterStep"
                            :spinners="showSpinnersInNumbers"
                            :placeholder="'Tuition Fee For Blended Mode'"
                            :validator="checkBlenedFeeRequired"
                            :format="'c2'"
                        >
                            <template v-slot:hoursFees="{ props }">
                                <numerictextbox
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-x-6 gap-y-4" v-else>
                    <div v-if="showInternationalFee">
                        <field
                            :id="'tuition_fee'"
                            :name="'tuition_fee'"
                            :label="'International Tuition Fee'"
                            :component="'hoursFees'"
                            :step="counterStep"
                            :spinners="showSpinnersInNumbers"
                            :placeholder="'Enter Tuition Fee'"
                            :validator="checkInternationalFeeRequired"
                            :format="'c2'"
                        >
                            <template v-slot:hoursFees="{ props }">
                                <numerictextbox
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                    <div v-if="showDomesticFee">
                        <field
                            :id="'domestic_fee'"
                            :name="'domestic_fee'"
                            :label="'Domestic Tuition Fee'"
                            :component="'hoursFees'"
                            :step="counterStep"
                            :spinners="showSpinnersInNumbers"
                            :placeholder="'Enter Domestic Tuition Fee'"
                            :validator="checkDomesticFeeRequired"
                            :format="'c2'"
                        >
                            <template v-slot:hoursFees="{ props }">
                                <numerictextbox
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                    <div v-if="showOffshoreFee">
                        <field
                            :id="'international_offshore_fee'"
                            :name="'international_offshore_fee'"
                            :label="'International Offshore Tuition Fee'"
                            :component="'hoursFees'"
                            :step="counterStep"
                            :spinners="showSpinnersInNumbers"
                            :placeholder="'Enter Tuition Fee For International Offshore Students'"
                            :validator="checkOffshoreFeeRequired"
                            :format="'c2'"
                        >
                            <template v-slot:hoursFees="{ props }">
                                <numerictextbox
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                    <div v-if="showOnshoreFee">
                        <field
                            :id="'international_onshore_fee'"
                            :name="'international_onshore_fee'"
                            :label="'International Onshore Tuition Fee'"
                            :component="'hoursFees'"
                            :step="counterStep"
                            :spinners="showSpinnersInNumbers"
                            :placeholder="'Enter Tuition Fee For International Onshore Students'"
                            :validator="checkOnshoreFeeRequired"
                            :format="'c2'"
                        >
                            <template v-slot:hoursFees="{ props }">
                                <numerictextbox
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                </div>
                <div v-if="isShortCourse" class="mt-2">
                    <div class="mb-3 flex items-center justify-between">
                        <div class="w-auto">
                            <label for="flexible_attendance" class="font-medium text-gray-700">
                                Is the fee recurring?
                            </label>
                        </div>
                        <div class="w-auto">
                            <field
                                :id="'is_fee_recurring'"
                                :name="'is_fee_recurring'"
                                :component="'switchTemplate'"
                            >
                                <template v-slot:switchTemplate="{ props }">
                                    <formswitch
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                        </div>
                    </div>
                    <div class="mb-3 flex items-center justify-between">
                        <div class="w-auto">
                            <label for="flexible_attendance" class="font-medium text-gray-700">
                                Is seat limited for this qualification?
                            </label>
                        </div>
                        <div class="w-auto">
                            <field
                                :id="'is_seat_fixed'"
                                :name="'is_seat_fixed'"
                                :component="'switchTemplate'"
                            >
                                <template v-slot:switchTemplate="{ props }">
                                    <formswitch
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <div class="w-1/2" v-if="isRecurring">
                            <field
                                :id="'fee_recurring_type'"
                                :name="'fee_recurring_type'"
                                :label="'Fee Recurring Type'"
                                :component="'feeRecurringType'"
                                :text-field="'text'"
                                :data-item-key="'id'"
                                :valueField="'id'"
                                :valuePrimitive="true"
                                :default-item="defaultRecurringType"
                                :data-items="courseRecurringData"
                                :validator="checkRecurringTypeRequired"
                            >
                                <template v-slot:feeRecurringType="{ props }">
                                    <formdropdown
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                        </div>
                        <div class="w-1/2" v-if="isLimited">
                            <field
                                :id="'seat_limit'"
                                :name="'seat_limit'"
                                :label="'Max. Seat Limit'"
                                :component="'seatLimit'"
                                :step="counterStep"
                                :spinners="showSpinnersInNumbers"
                                :placeholder="'Enter Max. Seat Limit'"
                                :max="999"
                                :validator="checkHours"
                            >
                                <template v-slot:seatLimit="{ props }">
                                    <numerictextbox
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <navigationbuttons :allowsave="kendoForm.allowSubmit" />
    </form-element>
</template>

<script>
import { Field, FormElement } from '@progress/kendo-vue-form';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
import FormNumericInput from '@spa/components/KendoInputs/FormNumericInput.vue';
import FormNumericTextbox from '@spa/components/KendoInputs/FormNumericTextBox.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormSwitch from '@spa/components/KendoInputs/FormSwitch.vue';
import { Button } from '@progress/kendo-vue-buttons';
import { mapState } from 'pinia';
import { useCoursesStore } from '@spa/stores/modules/courses';
import NavigationButtons from '@spa/pages/courses/includes/NavigationButtons.vue';
import {
    requiredtrue,
    requiredpositivenumber,
    requiredpositiveinteger,
    requiredmonetoryvalue,
    requiredCode,
    validDate,
    validEndDate,
} from '@spa/services/validators/kendoCommonValidator.js';

export default {
    props: {
        coursedata: Object,
    },
    components: {
        field: Field,
        'form-element': FormElement,
        forminput: FormInput,
        numericinput: FormNumericInput,
        numerictextbox: FormNumericTextbox,
        datepicker: FormDatePicker,
        formdropdown: FormDropDown,
        formswitch: FormSwitch,
        kbutton: Button,
        navigationbuttons: NavigationButtons,
    },
    inject: {
        kendoForm: { default: {} },
    },
    computed: {
        ...mapState(useCoursesStore, ['formInits', 'currentposition']),
        getCourseDurationType() {
            const durationtype = parseInt(this.kendoForm.valueGetter('couse_duration_type'));
            return durationtype || 2;
        },
        getCourseDurationTypeText() {
            const type = this.getCourseDurationType;
            return (this.durationTypesData.find((item) => item.id === type) ?? {}).text;
        },
        getDeliveryTarget() {
            return this.coursedata?.delivery_target || null;
        },
        showDomesticFee: function () {
            return this.getDeliveryTarget == 'Domestic' || this.getDeliveryTarget == 'Both';
        },
        showInternationalFee: function () {
            return (
                !this.showOffshoreFee &&
                !this.showOnshoreFee &&
                this.getDeliveryTarget != 'Domestic'
            );
        },
        showOffshoreFee() {
            return (
                this.getDeliveryTarget == 'InternationalOffShore' ||
                this.getDeliveryTarget == 'Both'
            );
        },
        showOnshoreFee() {
            return (
                this.getDeliveryTarget == 'InternationalOnshore' || this.getDeliveryTarget == 'Both'
            );
        },
        showFaceToFaceFee() {
            const mode = this.kendoForm.valueGetter('course_delivery_mode') || '';
            if (!this.isShortCourse) {
                return false;
            }
            return mode == 'facetoface';
        },
        showOnlineFee() {
            const mode = this.kendoForm.valueGetter('course_delivery_mode') || '';
            if (!this.isShortCourse) {
                return false;
            }
            return mode == 'online';
        },
        showBlendedFee() {
            const mode = this.kendoForm.valueGetter('course_delivery_mode') || '';
            if (!this.isShortCourse) {
                return false;
            }
            return mode == '' || mode == 'hybrid';
        },
        isHigherEd: function () {
            const courseType = this.coursedata?.course_type_id;
            return courseType == 17;
        },
        durationTypesData: function () {
            return this.formInits?.course_duration_types;
        },
        // isHigherEd: function () {
        //     return this.kendoForm.valueGetter("course_type_id") == 17;
        // },
        isShortCourse: function () {
            return this.isCourseShortCourse(this.kendoForm.valueGetter('course_type_id'));
        },
        effectiveStartText: function () {
            return this.isHigherEd
                ? 'Course Effective From Date (E609)'
                : 'Course Effective From Date';
        },
        effectiveEndText: function () {
            return this.isHigherEd ? 'Course Effective To Date (E610)' : 'Course Effective To Date';
        },
        courseDeliveryModes: function () {
            return this.formInits?.delivery_mode;
        },
        courseRecurringData: function () {
            return this.formInits?.course_recurring_types;
        },
        isRecurring: function () {
            return this.kendoForm.valueGetter('is_fee_recurring') == true;
        },
        isLimited: function () {
            return this.kendoForm.valueGetter('is_seat_fixed') == true;
        },
        showOnlineHours: function () {
            const mode = this.kendoForm.valueGetter('course_delivery_mode') || '';
            if (this.isHigherEd) {
                return false;
            }
            return true;
            return mode == 'hybrid' || mode == 'blended' || mode == 'online';
        },
        showOfflineHours: function () {
            const mode = this.kendoForm.valueGetter('course_delivery_mode') || '';
            if (this.isHigherEd) {
                return false;
            }
            return true;
            return mode == '' || mode == 'hybrid' || mode == 'blended' || mode == 'facetoface';
        },
    },
    data: function () {
        return {
            dataitem: [],
            counterStep: parseInt(1),
            showSpinnersInNumbers: false,
            defaultDurationType: {
                text: 'Select Duration Type ...',
                id: '',
            },
            defaultRecurringType: {
                id: '',
                text: 'Select Recurring Type',
            },
            defaultDeliveryMode: {
                id: null,
                text: 'Select Delivery Mode',
            },
            originalFaceToFaceHours: 0,
            originalOnlineHourse: 0,
        };
    },
    mounted() {
        this.originalFaceToFaceHours = this.kendoForm.valueGetter('face_to_face_hours') || 0;
        this.originalOnlineHourse = this.kendoForm.valueGetter('online_hours') || 0;
    },
    methods: {
        requiredtrue,
        requiredpositivenumber,
        requiredCode,
        validDate,
        validEndDate,
        requiredpositiveinteger,
        requiredmonetoryvalue,
        handleSubmit(dataItem) {
            alert(JSON.stringify(dataItem, null, 2));
        },
        clear() {
            this.kendoForm.onFormReset();
        },
        changeDurationType(e) {
            this.kendoForm.onChange('couse_duration_type', { value: e.value });
        },
        checkRecurringTypeRequired(value) {
            const visible = this.isRecurring;
            if (visible) {
                return this.requiredtrue(value);
            }
        },
        checkMaxSeatLimitRequired(value) {
            const visible = this.isLimited;
            if (visible) {
                return this.requiredpositivenumber(value);
            }
        },
        checkInternationalFeeRequired(value) {
            if (this.showInternationalFee) {
                return this.requiredmonetoryvalue(value);
            }
        },
        checkDomesticFeeRequired(value) {
            if (this.showDomesticFee) {
                return this.requiredmonetoryvalue(value);
            }
        },
        checkOffshoreFeeRequired(value) {
            if (this.showOffshoreFee) {
                return this.requiredmonetoryvalue(value);
            }
        },
        checkOnshoreFeeRequired(value) {
            if (this.showOnshoreFee) {
                return this.requiredmonetoryvalue(value);
            }
        },
        checkFaceToFaceFeeRequired(value) {
            if (this.showFaceToFaceFee) {
                return this.requiredmonetoryvalue(value);
            }
            return '';
        },
        checkOnlineFeeRequired(value) {
            if (this.showOnlineFee) {
                return this.requiredmonetoryvalue(value);
            }
            return '';
        },
        checkBlenedFeeRequired(value) {
            if (this.showBlendedFee) {
                return this.requiredmonetoryvalue(value);
            }
            return '';
        },
        checkOnlineHours(value) {
            let max = parseInt(this.kendoForm.valueGetter('maximum_weekly_study') || 0);
            let online = parseInt(this.kendoForm.valueGetter('online_hours') || 0);
            let faceHours = parseInt(this.kendoForm.valueGetter('face_to_face_hours') || 0);
            let mode = this.kendoForm.valueGetter('course_delivery_mode');
            max = isNaN(max) ? 0 : max;
            online = isNaN(online) ? 0 : online;
            faceHours = isNaN(faceHours) ? 0 : faceHours;
            if (mode == 'online') {
                faceHours = 0;
                if (online == 0) {
                    return `Provide the correct hours for ${mode}.`;
                }
            }
            if (online + faceHours == 0) {
                return `Provide the correct hours for ${mode}.`;
            }
            if (online + faceHours > max) {
                return `Provided hours exceeds the maximum hours for ${mode}.`;
            }
            return '';
        },
        checkFaceToFaceHours(value) {
            let max = parseInt(this.kendoForm.valueGetter('maximum_weekly_study') || 0);
            let online = parseInt(this.kendoForm.valueGetter('online_hours') || 0);
            let faceHours = parseInt(this.kendoForm.valueGetter('face_to_face_hours') || 0);
            let mode = this.kendoForm.valueGetter('course_delivery_mode');
            max = isNaN(max) ? 0 : max;
            online = isNaN(online) ? 0 : online;
            faceHours = isNaN(faceHours) ? 0 : faceHours;
            if (mode == 'facetoface' || mode == '') {
                online = 0;
                if (faceHours == 0) {
                    return `Provide the correct hours for ${mode}.`;
                }
            }
            if (online + faceHours == 0) {
                return `Provide the correct hours for ${mode}.`;
            }
            if (online + faceHours > max) {
                return `Provided hours exceeds the maximum hours for ${mode}.`;
            }
            return '';
        },
        convertDateSring(value) {
            //convert the string to yyyy-mm-dd format
            if (value instanceof Date) return value;
            const parts = value ? value.split('-') : '';
            let dateString = '';
            if (parts.length === 3) {
                dateString = `${parts[2]}-${parts[1]}-${parts[0]}`;
            }
            return dateString;
        },
        checkEffectiveDate(value) {
            if (this.isHigherEd || this.isShortCourse) {
                return this.validDate(this.convertDateSring(value));
            }
        },
        checkEffectiveEnd(value) {
            const start = this.kendoForm.valueGetter('effective_start') || '';
            if (this.isHigherEd || this.isShortCourse) {
                return this.validEndDate(
                    this.convertDateSring(value),
                    this.convertDateSring(start),
                    1
                );
            }
        },
        requiredDuration(value) {
            let durationType = parseInt(this.kendoForm.valueGetter('couse_duration_type') || 0);
            if (durationType) {
                return this.requiredpositiveinteger(value);
            }
            return 'Duration type not selected.';
        },
        handleDeliveryModeChange(e) {
            let max = parseInt(this.kendoForm.valueGetter('maximum_weekly_study') || 0);
            let online = parseInt(this.kendoForm.valueGetter('online_hours') || 0);
            let faceHours = parseInt(this.kendoForm.valueGetter('face_to_face_hours') || 0);
            if (e.value == 'online') {
                this.kendoForm.onChange('face_to_face_hours', { value: 0 });
                this.kendoForm.onChange('online_hours', { value: max });
            } else if (e.value == 'facetoface') {
                this.kendoForm.onChange('face_to_face_hours', { value: max });
                this.kendoForm.onChange('online_hours', { value: 0 });
            } else {
                this.kendoForm.onChange('online_hours', { value: this.originalOnlineHourse });
                this.kendoForm.onChange('face_to_face_hours', {
                    value: this.originalFaceToFaceHours,
                });
            }
            this.kendoForm.onChange('course_delivery_mode', { value: e.value });
        },
    },
};
</script>
