<script setup>
import { computed } from 'vue';
import Card from '@spa/components/Card/Card.vue';
import TeacherMatrixListComponent from '@spa/modules/teacher-matrix/TeacherMatrixListComponent.vue';
import LeaveInfoListComponent from '@spa/modules/teachers/leave-info/LeaveInfoListComponent.vue';

const props = defineProps({
    store: Object,
});
</script>

<template>
    <Card :variant="'compact'" :pt="{ root: 'bg-gray-100' }">
        <template #header>
            <div class="flex items-center gap-2">
                <h2 class="text-lg font-medium">Leave Info</h2>
            </div>
        </template>
        <template #content>
            <div v-if="store.formData?.id">
                <LeaveInfoListComponent
                    :filters="{
                        staffId: store.formData?.id,
                    }"
                />
            </div>
        </template>
    </Card>
</template>

<style scoped></style>
