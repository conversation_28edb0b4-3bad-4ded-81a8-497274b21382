<template>
    <fieldwrapper :class="rootClasses">
        <klabel
            :id="labelId"
            :editor-id="id"
            :editor-valid="computedValid"
            :editor-disabled="disabled"
            :class="labelClasses"
        >
            <span>
                {{ label }}
                <span v-if="indicaterequired" :class="'ml-1 text-red-500'">*</span>
            </span>
            <radiogroup
                v-model="vModel"
                :layout="layout"
                :data-items="dataItems"
                :id="id"
                @change="handleChange"
                @blur="handleBlur"
                @focus="handleFocus"
                :class="fieldClasses"
                :disabled="disabled"
            />

            <error v-if="showValidationMessage">
                {{ validationMessage }}
            </error>
            <hint v-if="showHint">{{ hint }}</hint>
        </klabel>
    </fieldwrapper>
</template>

<script>
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { RadioGroup } from '@progress/kendo-vue-inputs';
import { twMerge } from 'tailwind-merge';

export default {
    props: {
        modelValue: [String, Object, Number, Boolean],
        touched: Boolean,
        label: String,
        validationMessage: String,
        hint: String,
        id: String,
        valid: Boolean,
        disabled: Boolean,
        layout: String,
        dataItems: Array,
        value: {
            type: [Boolean, Number, String],
        },
        defaultValue: {
            type: [Boolean, Number, String],
        },
        className: String,
        pt: {
            type: Object,
            default: () => ({}),
        },
        indicaterequired: { type: Boolean, default: false },
        orientation: { type: String, default: 'vertical' },
    },
    components: {
        fieldwrapper: FieldWrapper,
        error: Error,
        hint: Hint,
        klabel: Label,
        radiogroup: RadioGroup,
    },
    data() {
        return {
            internalValid: null, // Start as null to let computed handle initial state
            hasUserInteracted: false,
        };
    },
    computed: {
        vModel: {
            get() {
                if (
                    this.modelValue === null ||
                    this.modelValue === undefined ||
                    this.modelValue === ''
                ) {
                    return this.defaultValue;
                }
                return this.modelValue || this.value;
            },
            set(value) {
                this.hasUserInteracted = true;
                this.$emit('update:modelValue', value);
            },
        },
        computedValid() {
            // If we have an explicit valid prop, use it
            if (this.valid !== undefined) {
                return this.valid;
            }

            // If user hasn't interacted yet, assume valid (don't show red state)
            if (!this.hasUserInteracted && !this.touched) {
                return true;
            }

            // Use internal valid state or default to true if no validation issues
            return this.internalValid !== null ? this.internalValid : !this.showValidationMessage;
        },
        showValidationMessage() {
            return this.touched && this.validationMessage && this.validationMessage.length > 0;
        },
        hasValidValue() {
            const currentValue = this.vModel;
            return currentValue !== null && currentValue !== undefined && currentValue !== '';
        },
        showHint() {
            return !this.showValidationMessage && this.hint;
        },
        hintId() {
            return this.showHint ? `${this.id}_hint` : '';
        },
        errorId() {
            return this.showValidationMessage ? `${this.id}_error` : '';
        },
        describedBy() {
            return `${this.hintId} ${this.errorId}`.trim();
        },
        labelId() {
            return this.label ? `${this.id}_label` : '';
        },
        labelClasses() {
            if (this.orientation === 'horizontal') {
                return twMerge('mb-1 font-medium leading-5 !text-gray-700', this.pt.label);
            }
            return twMerge(
                'flex !flex-col mb-1 font-medium gap-1 leading-5 !text-gray-700',
                this.pt.label
            );
        },
        fieldClasses() {
            return twMerge('tw-input__radio-group', this.className, this.pt.field);
        },
        rootClasses() {
            if (this.orientation === 'horizontal') {
                return twMerge('tw-form__fieldwrapper field-horizontal', this.pt.root);
            }
            return twMerge('tw-form__fieldwrapper', this.pt.root);
        },
    },
    watch: {
        modelValue(newVal, oldVal) {
            if (newVal !== oldVal && this.hasUserInteracted) {
                // Reset validation state when value changes after user interaction
                this.internalValid = true;
            }
        },
        valid: {
            handler(newVal) {
                if (newVal !== undefined) {
                    this.internalValid = newVal;
                }
            },
            immediate: true,
        },
        // Reset interaction state if component is reset externally
        touched(newVal) {
            if (!newVal) {
                this.hasUserInteracted = false;
                this.internalValid = null;
            }
        },
    },
    emits: {
        change: null,
        blur: null,
        focus: null,
        'update:modelValue': null,
    },
    mounted() {
        // Set default value if needed, but don't mark as user interaction
        if (
            (this.modelValue === null || this.modelValue === undefined || this.modelValue === '') &&
            this.defaultValue !== undefined
        ) {
            this.$emit('update:modelValue', this.defaultValue);
        }
    },
    methods: {
        handleChange(e) {
            this.hasUserInteracted = true;
            this.$emit('change', e);
        },
        handleBlur(e) {
            this.hasUserInteracted = true;
            this.$emit('blur', e);
        },
        handleFocus(e) {
            this.$emit('focus', e);
        },
    },
};
</script>
