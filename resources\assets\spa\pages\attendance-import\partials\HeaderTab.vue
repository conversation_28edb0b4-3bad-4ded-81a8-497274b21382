<template>
    <div class="space-y-4">
        <HelpTitle
            :title="'Import Attendance'"
            :pt="{ title: 'text-xl' }"
            :popup-align="{ horizontal: 'left' }"
            :anchor-align="{ horizontal: 'right' }"
        >
            <template #content>
                <div class="max-h-[600px] max-w-2xl overflow-auto p-4 text-sm text-gray-600">
                    <p class="mb-2 font-medium">Upload a CSV file with the following headers:</p>
                    <ul class="list-disc space-y-1 pl-5">
                        <li><strong>StudentID</strong></li>
                        <li>
                            <strong>AttendanceDate</strong> (supports formats like YYYY-MM-DD,
                            DD/MM/YYYY)
                        </li>
                        <li><strong>AttendanceStatus</strong></li>
                    </ul>
                    <p class="mt-3 text-xs text-gray-500">
                        Note: Records are only updated if a matching Student and Attendance Date
                        already exist. No new rows are inserted.
                    </p>
                </div>
            </template>
        </HelpTitle>
        <Card :pt="{ root: 'p-4 lg:p-6 rounded-lg' }">
            <template #content>
                <div class="flex flex-col space-y-4">
                    <div class="flex flex-wrap items-center justify-between gap-4">
                        <div class="flex flex-wrap items-center gap-3">
                            <div class="flex items-center gap-2">
                                <label class="text-sm font-medium text-gray-700">Status:</label>
                                <div class="w-56">
                                    <EnumSelect
                                        className="tw-w-full"
                                        enum-class="GalaxyAPI\Enums\ResultImportStatusEnum"
                                        v-model="filterValues.status"
                                        :has-select-all="true"
                                        :placeholder="'Select Status'"
                                    />
                                </div>
                            </div>
                            <Button
                                variant="ghost"
                                class="flex min-w-32 cursor-pointer justify-start px-0"
                                @click="handleReset"
                            >
                                <icon
                                    :name="'sync'"
                                    :fill="'#1890FF'"
                                    :className="reset ? 'animate-spin' : ''"
                                />
                                <span> Reset Filters </span>
                            </Button>
                        </div>
                        <div class="flex space-x-2">
                            <Button variant="secondary" class="h-9" @click="downloadSample">
                                <icon
                                    :name="'download'"
                                    :fill="'currentColor'"
                                    :width="'16'"
                                    :height="'16'"
                                />
                                <span>Sample CSV</span>
                            </Button>
                            <Button variant="primary" class="h-9" @click="openFileUpload">
                                <icon
                                    :name="'upload'"
                                    :fill="'currentColor'"
                                    :width="'16'"
                                    :height="'16'"
                                />
                                <span>Upload CSV</span>
                            </Button>
                        </div>
                    </div>
                </div>
            </template>
        </Card>
        <div class="flex flex-wrap items-center justify-between gap-2">
            <div>
                <search-input
                    v-model.lazy="filterValues.search"
                    :pt="{ root: 'w-64 h-9' }"
                    placeholder="Search by Student ID/Date"
                    :debounce="300"
                    :autocomplete="'off'"
                />
            </div>
            <div class="flex flex-wrap items-center gap-3">
                <Button
                    variant="secondary"
                    class="h-9"
                    @click="toggleAutoRefresh"
                    :class="{ 'border-green-500 bg-green-100': autoRefreshEnabled }"
                >
                    <icon
                        :name="autoRefreshEnabled ? 'pause' : 'play'"
                        :fill="'currentColor'"
                        :width="'16'"
                        :height="'16'"
                    />
                    <span>{{
                        autoRefreshEnabled ? 'Stop Auto Refresh' : 'Start Auto Refresh'
                    }}</span>
                </Button>
            </div>
        </div>
    </div>
</template>

<script>
import { debounce } from 'lodash';
import IconInput from '@spa/components/IconInput.vue';
import Button from '@spa/components/Buttons/Button.vue';
import Card from '@spa/components/Card/Card.vue';
import EnumSelect from '@spa/components/AsyncComponents/Select/EnumSelect.vue';

import HelpTitle from '@spa/components/Card/HelpTitle.vue';
export default {
    components: { EnumSelect, 'search-input': IconInput, Button, Card, HelpTitle },
    props: {
        sortDirection: { type: String, default: 'desc' },
        refreshing: { type: Boolean, default: false },
        autoRefreshEnabled: { type: Boolean, default: false },
    },
    data() {
        return {
            filterValues: { search: '', status: '' },
            debouncedEmitFilter: debounce(function (newval) {
                if (newval.search === '') newval.search = null;
                if (newval.status === '') newval.status = null;
                this.$emit('filter', newval);
            }, 300),
            reset: false,
        };
    },
    watch: {
        'filterValues.search': function () {
            this.debouncedEmitFilter(this.filterValues);
        },
        'filterValues.status': function () {
            this.debouncedEmitFilter(this.filterValues);
        },
    },
    methods: {
        openFileUpload() {
            this.$emit('open-file-upload');
        },
        toggleSortDirection() {
            this.$emit('toggle-sort');
        },
        refreshData() {
            this.$emit('refresh');
        },
        toggleAutoRefresh() {
            this.$emit('toggle-auto-refresh');
        },
        downloadSample() {
            window.open(route('spa.attendance-import.sample'), '_blank');
        },
        handleReset() {
            this.reset = true;
            setTimeout(() => {
                this.reset = false;
            }, 500);
            this.filterValues.search = '';
            this.filterValues.status = '';
        },
    },
};
</script>
