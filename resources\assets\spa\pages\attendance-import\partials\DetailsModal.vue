<template>
    <DialogModal :show="visible" maxWidth="2xl" @close="close">
        <template #title>
            <div class="flex items-center gap-2">
                <icon name="eye" :width="20" :height="20" />
                <span>Attendance Import Details</span>
            </div>
        </template>
        <template #content>
            <div class="space-y-3 text-sm">
                <div class="grid grid-cols-2 gap-3">
                    <div>
                        <div class="text-gray-500">Student ID</div>
                        <div class="font-medium">
                            {{ importData?.import_data?.StudentId || '—' }}
                        </div>
                    </div>
                    <div>
                        <div class="text-gray-500">Unit Code</div>
                        <div class="font-medium">
                            {{ importData?.import_data?.UnitCode || '—' }}
                        </div>
                    </div>
                    <div>
                        <div class="text-gray-500">Attendance Date</div>
                        <div class="font-medium">
                            {{ importData?.import_data?.AttendanceDate || '—' }}
                        </div>
                    </div>
                    <div>
                        <div class="text-gray-500">Attendance Status</div>
                        <div class="font-medium">
                            {{ importData?.import_data?.AttendanceStatus || '—' }}
                        </div>
                    </div>
                    <div>
                        <div class="text-gray-500">Import Status</div>
                        <div class="font-medium capitalize">{{ importData?.status || '—' }}</div>
                    </div>
                </div>
                <div
                    v-if="importData?.status === 'failed' && importData?.error_message"
                    class="rounded-md bg-red-50 p-3 text-red-700"
                >
                    <div class="mb-1 font-medium">Error Details</div>
                    <ul class="list-disc pl-5 text-xs">
                        <li v-for="(error, index) in parseErrors" :key="index">
                            <strong>{{ error.error_title }}:</strong> {{ error.error_description }}
                        </li>
                    </ul>
                </div>
            </div>
        </template>
        <template #footer>
            <div class="flex items-center justify-end gap-2">
                <Button variant="secondary" @click="close">Close</Button>
                <Button
                    variant="primary"
                    v-if="importData?.status === 'failed'"
                    @click="$emit('resync', importData)"
                    >Retry</Button
                >
            </div>
        </template>
    </DialogModal>
</template>

<script>
import Button from '@spa/components/Buttons/Button.vue';
import DialogModal from '@spa/components/DialogModal.vue';

export default {
    components: { Button, DialogModal },
    props: { visible: Boolean, importData: Object },
    emits: ['update:visible', 'resync'],
    computed: {
        parseErrors() {
            try {
                return JSON.parse(this.importData?.error_message || '[]');
            } catch (e) {
                return [
                    {
                        error_title: 'Error',
                        error_description: this.importData?.error_message || 'Unknown error',
                    },
                ];
            }
        },
    },
    methods: {
        close() {
            this.$emit('update:visible', false);
        },
    },
};
</script>
