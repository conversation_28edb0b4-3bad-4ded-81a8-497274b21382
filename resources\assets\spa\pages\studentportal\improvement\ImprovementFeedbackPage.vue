<script setup>
import RegisterImprovementsListComponent from '@spa/modules/register-improvements/RegisterImprovementsListComponent.vue';
import Layout from '@spa/pages/Layouts/Layout';
import { Head } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import { provide } from 'vue';

const props = defineProps(['generatedStudentId']);
provide('requestedBy', props.generatedStudentId);
</script>
<template>
    <Layout :no-spacing="true">
        <Head title="Improvement Feedback" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Improvement Feedback" :back="false" />
        </template>
        <div class="h-screen-header flex flex-col px-8 py-6">
            <RegisterImprovementsListComponent
                :filters="{
                    generatedStudentId: generatedStudentId,
                }"
                :userType="2"
            />
            <!--            GalaxyAPI\Enums\ImprovementUserTypeEnum-->
        </div>
    </Layout>
</template>

<style scoped></style>
