<template lang="">
    <Layout :noSpacing="true">
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'Dashboard'" :back="false" />
        </template>
        <div class="space-y-4 px-4 py-8 lg:px-8">
            <div class="space-y-5">
                <h4 class="text-3xl font-medium">Welcome {{ data.name }},</h4>
                <div class="grid grid-cols-2 gap-5 md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-6">
                    <template v-for="(link, idx) in links" :key="idx">
                        <div class="col-span-1">
                            <link-card
                                :icon="link.icon"
                                :title="link.title"
                                :label="link.label"
                                :link="link.link"
                                :href="link.href"
                                :external="link.external"
                            />
                        </div>
                    </template>
                    <div class="col-span-1" v-if="shouldRedirectToMoodle">
                        <div
                            class="tw-card rounded-lg border border-gray-200 bg-white p-3 shadow md:p-4"
                        >
                            <div class="tw-card__content space-y-4 p-0">
                                <div class="group flex cursor-pointer items-center gap-4">
                                    <div
                                        class="flex items-center justify-center rounded-full bg-cyan-50 p-1"
                                    >
                                        <img :src="moodleIcon" class="h-5" alt="Moodle Logo" />
                                    </div>
                                    <div class="space-y-1">
                                        <a
                                            :href="moodleUrl"
                                            target="_blank"
                                            class="text-sm leading-5 text-gray-700"
                                        >
                                            Access your Moodle
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <PersonalDetails :data="profile" />
                    <ResidentialAddress :data="addressInfo" />
                </div>
            </div>
        </div>
        <FirstTimeLoginModal
            :userId="this.data?.userId"
            :user="data.user"
            :visible="showResetPassword"
            @cancel="closeResetModal"
        />
        <ChangeAddressModal
            :studentId="this.data?.studentId"
            :user="data.user"
            @cancel="closeAddressModal"
            :visible="showAddress"
        />
    </Layout>
</template>
<script>
import axios from 'axios';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import LinkCard from '@studentportal/dashboard/partials/LinkCard.vue';
import IconInput from '@spa/components/IconInput.vue';
import FirstTimeLoginModal from '@studentportal/common/FirstTimeLoginModal.vue';
import ChangeAddressModal from '@studentportal/common/ChangeAddressModal.vue';
import PersonalDetails from '@studentportal/dashboard/partials/PersonalDetails.vue';
import ResidentialAddress from '@studentportal/dashboard/partials/ResidentialAddress.vue';

export default {
    props: {
        data: { type: Object, default: {} },
    },
    components: {
        Layout,
        PageTitleContent,
        'link-card': LinkCard,
        IconInput,
        FirstTimeLoginModal,
        ChangeAddressModal,
        PersonalDetails,
        ResidentialAddress,
    },
    mounted() {
        const hasSeenPasswordPopup = sessionStorage.getItem('showResetPasswordPopupShown');
        if (this.data?.showResetPasswordModel) {
            this.showResetPassword = true;
        }
        const hasSeenAddressPopup = sessionStorage.getItem('showAddressPopupShown');
        if (this.data?.showChangeAddressModel) {
            this.showAddress = true;
        }
        // if (this.data?.showChangeAddressModel && !hasSeenAddressPopup) {
        //     this.showAddress = true;
        // }
        // sessionStorage.setItem("showResetPasswordPopupShown", "true");

        // sessionStorage.setItem("showAddressPopupShown", "true");
        this.ajaxheaders = {
            headers: {
                Authorization: 'Bearer ' + this.api_token,
            },
        };
    },
    data() {
        return {
            showResetPassword: false,
            showAddress: false,
            search: null,
            links: (() => {
                const base = [
                    {
                        icon: 'document-folder',
                        title: 'Documents',
                        label: 'View All',
                        link: 'spa.studentportal.document',
                    },
                    {
                        icon: 'person-clock',
                        title: 'Attendance',
                        label: 'View All',
                        link: 'spa.studentportal.attendance.overall',
                    },
                    {
                        icon: 'calendar-clock',
                        title: 'Timetable',
                        label: 'View All',
                        link: 'spa.studentportal.timetable',
                    },
                    {
                        icon: 'wallet-credit-card',
                        title: 'Payment',
                        label: 'View All',
                        link: 'spa.studentportal.paymentinfo',
                    },
                    {
                        icon: 'document-data',
                        title: 'Results',
                        label: 'View All',
                        link: 'spa.studentportal.results',
                    },
                    {
                        icon: 'person-heart',
                        title: 'OHSC',
                        label: 'View All',
                        link: 'spa.studentportal.oshcinfo',
                    },
                    {
                        icon: 'question-circle',
                        title: 'Help & Support',
                        label: 'View All',
                        link: 'spa.studentportal.email-feedback',
                    },
                    {
                        icon: 'question-circle',
                        title: 'Feedback',
                        label: 'View All',
                        link: 'spa.register-improvements',
                    },
                ];
                if (this.data?.lmsPortalUrl) {
                    base.push({
                        icon: 'book-search',
                        title: 'Access your LMS',
                        label: 'Open',
                        href: this.data.lmsPortalUrl,
                        external: true,
                    });
                }
                return base;
            })(),
            profile: {
                name: `${this.data.studentData?.first_name || ''} ${this.data.studentData?.middle_name || ''} ${this.data.studentData?.family_name || ''}`,
                email: this.data.studentData?.email,
                optional_email: this.data.studentData?.optional_email,
                mobile_number: this.data.studentData?.current_mobile_phone,
                usi: this.data.studentData?.USI,
                generated_stud_id: this.data.studentData?.generated_stud_id,
                profile_picture: this.data.studentData?.profile_picture,
            },
            addressInfo: {
                name: `${this.data.studentData?.first_name || ''} ${this.data.studentData?.middle_name || ''} ${this.data.studentData?.family_name || ''}`,
                building: this.data.studentData?.current_building_name,
                flat: this.data.studentData?.current_unit_detail,
                street_number: this.data.studentData?.current_street_no,
                street_name: this.data.studentData?.current_street_name,
                city: this.data.studentData?.current_city,
                state: this.data.studentData?.current_state,
                post_code: this.data.studentData?.current_postcode,
            },
            moodleIcon: '/v2/img/moodle-icon.svg',
            moodleUrl: this.data.moodleUrl,
            shouldRedirectToMoodle: this.data.shouldRedirectToMoodle,
            //isSyncWithMoodle: this.data.isSyncWithMoodle,
        };
    },
    methods: {
        closeResetModal() {
            this.showResetPassword = false;
            const postData = {
                id: this.data?.userId,
            };
            axios
                .post('update-last-password-change-date', postData, this.ajaxheaders)
                .then((response) => {});
        },
        closeAddressModal() {
            this.showAddress = false;
            const postData = {
                id: this.data?.studentId,
            };
            axios
                .post('update-last-address-change-date', postData, this.ajaxheaders)
                .then((response) => {});
        },
    },
};
</script>
<style lang=""></style>
