id,parameter_name,parameter_value,title,created_at,updated_at,created_by,updated_by
1,Alter<PERSON><PERSON>1,{AlterEmail1},"{""type"": ""text"",""value"": ""Alter Email""}",28-08-2017 12:35,28-08-2017 07:12,78,78
2,<PERSON><PERSON><PERSON><PERSON>2,{AlterEmail2},"{""type"": ""text"",""value"": ""Alter Email""}",28-08-2017 12:35,28-08-2017 07:12,78,78
3,<PERSON><PERSON><PERSON>,{CollegeEmail},"{""type"": ""text"",""value"": ""College Email""}",28-08-2017 12:35,28-08-2017 07:12,78,78
4,Country,{Country},"{""type"": ""text"",""value"": ""Country""}",28-08-2017 12:35,28-08-2017 07:12,78,78
5,Country<PERSON>irth,{CountryBirth},"{""type"": ""text"",""value"": ""Country Birth""}",28-08-2017 12:35,28-08-2017 07:12,78,78
6,CurrentDate,{CurrentDate},"{""type"": ""text"",""value"": ""Current Date""}",28-08-2017 12:35,28-08-2017 07:12,78,78
7,CurrentDate,{CurrentDate},"{""type"": ""text"",""value"": ""Current Date""}",28-08-2017 12:35,28-08-2017 07:12,78,78
8,DOB,{DOB},"{""type"": ""text"",""value"": ""Date of birth""}",28-08-2017 12:35,28-08-2017 07:12,78,78
9,DoB Without Stroke,{DoB Without Stroke},"{""type"": ""text"",""value"": ""DoB Without Stroke""}",28-08-2017 12:35,28-08-2017 07:12,78,78
10,Email,{Email},"{""type"": ""text"",""value"": ""Email""}",28-08-2017 12:35,28-08-2017 07:12,78,78
11,ExpDate,{ExpDate},"{""type"": ""text"",""value"": ""Expiry Date""}",28-08-2017 12:35,28-08-2017 07:12,78,78
12,Fax,{Fax},"{""type"": ""text"",""value"": ""Fax number""}",28-08-2017 12:35,28-08-2017 07:12,78,78
13,FirstName,{FirstName},"{""type"": ""text"",""value"": ""First Name""}",28-08-2017 12:35,28-08-2017 07:12,78,78
14,Gender,{Gender},"{""type"": ""text"",""value"": ""Gender""}",28-08-2017 12:35,28-08-2017 07:12,78,78
15,LastName,{LastName},"{""type"": ""text"",""value"": ""Last Name""}",28-08-2017 12:35,28-08-2017 07:12,78,78
16,MiddleName,{MiddleName},"{""type"": ""text"",""value"": ""Middle Name""}",28-08-2017 12:35,28-08-2017 07:12,78,78
17,Mobile,{Mobile},"{""type"": ""text"",""value"": ""Mobile""}",28-08-2017 12:35,28-08-2017 07:12,78,78
18,Nationality,{Nationality},"{""type"": ""text"",""value"": ""Nationality""}",28-08-2017 12:35,28-08-2017 07:12,78,78
19,NickName,{NickName},"{""type"": ""text"",""value"": ""Nick Name""}",28-08-2017 12:35,28-08-2017 07:12,78,78
20,PassportNo,{PassportNo},"{""type"": ""text"",""value"": ""Passport Number""}",28-08-2017 12:35,28-08-2017 07:12,78,78
21,Phone,{Phone},"{""type"": ""text"",""value"": ""Phone""}",28-08-2017 12:35,28-08-2017 07:12,78,78
22,Postcode,{Postcode},"{""type"": ""text"",""value"": ""Postcode""}",28-08-2017 12:35,28-08-2017 07:12,78,78
23,State,{State},"{""type"": ""text"",""value"": ""State""}",28-08-2017 12:35,28-08-2017 07:12,78,78
24,StreetAddress,{StreetAddress},"{""type"": ""text"",""value"": ""Street Address""}",28-08-2017 12:35,28-08-2017 07:12,78,78
25,StudentId,{StudentId},"{""type"": ""text"",""value"": ""Student Id""}",28-08-2017 12:35,28-08-2017 07:12,78,78
26,Suburb,{Suburb},"{""type"": ""text"",""value"": ""Suburb""}",28-08-2017 12:35,28-08-2017 07:12,78,78
27,Title,{Title},"{""type"": ""text"",""value"": ""Title""}",28-08-2017 12:35,28-08-2017 07:12,78,78
28,UserName,{UserName},"{""type"": ""text"",""value"": ""User Name""}",28-08-2017 12:35,28-08-2017 07:12,78,78
29,VisaType,{VisaType},"{""type"": ""text"",""value"": ""Visa Type""}",28-08-2017 12:35,28-08-2017 07:12,78,78
30,CollegeRtoCode,{CollegeRtoCode},"{""type"": ""text"",""value"": ""College Rto Code""}",26-10-2020 15:34,27-10-2020 21:06,1,1
31,CollegeCircosCode,{CollegeCircosCode},"{""type"": ""text"",""value"": ""College Circos Code""}",26-10-2020 15:34,27-10-2020 21:06,1,1
32,CollegeLegalName,{CollegeLegalName},"{""type"": ""text"",""value"": ""College Legal Name""}",26-10-2020 15:34,27-10-2020 21:06,1,1
33,CollegeName,{CollegeName},"{""type"": ""text"",""value"": ""College Name""}",26-10-2020 15:34,27-10-2020 21:06,1,1
34,CollegeContactPerson,{CollegeContactPerson},"{""type"": ""text"",""value"": ""College Contact Person""}",26-10-2020 15:34,27-10-2020 21:06,1,1
35,CollegeContactPhone,{CollegeContactPhone},"{""type"": ""text"",""value"": ""College Contact Phone""}",26-10-2020 15:34,27-10-2020 21:06,1,1
36,CollegeURL,{CollegeURL},"{""type"": ""text"",""value"": ""College URL""}",26-10-2020 15:34,27-10-2020 21:06,1,1
37,CollegeABN,{CollegeABN},"{""type"": ""text"",""value"": ""College ABN""}",26-10-2020 15:34,27-10-2020 21:06,1,1
38,CollegeFax,{CollegeFax},"{""type"": ""text"",""value"": ""College Fax""}",26-10-2020 15:34,27-10-2020 21:06,1,1
39,CourseType,{CourseType},"{""type"": ""text"",""value"": ""Course Type""}",26-10-2020 15:34,27-10-2020 21:06,1,1
40,Campus,{Campus},"{""type"": ""text"",""value"": ""Campus""}",26-10-2020 15:34,27-10-2020 21:06,1,1
41,CourseCode,{CourseCode},"{""type"": ""text"",""value"": ""Course Code""}",26-10-2020 15:34,27-10-2020 21:06,1,1
42,CourseName,{CourseName},"{""type"": ""text"",""value"": ""Course Name""}",26-10-2020 15:34,27-10-2020 21:06,1,1
43,StudentType,{StudentType},"{""type"": ""text"",""value"": ""Student Type""}",26-10-2020 15:34,27-10-2020 21:06,1,1
44,TeacherFirstName,{TeacherFirstName},"{""type"": ""text"",""value"": ""Teacher First Name""}",26-10-2020 15:34,27-10-2020 21:06,1,1
45,TeacherLastName,{TeacherLastName},"{""type"": ""text"",""value"": ""Teacher Last Name""}",26-10-2020 15:34,27-10-2020 21:06,1,1
46,TeacherEmail,{TeacherEmail},"{""type"": ""text"",""value"": ""Teacher Email""}",26-10-2020 15:34,27-10-2020 21:06,1,1
47,TeacherMobile,{TeacherMobile},"{""type"": ""text"",""value"": ""Teacher Mobile""}",26-10-2020 15:34,27-10-2020 21:06,1,1
48,AgencyName,{AgencyName},"{""type"": ""text"",""value"": ""Agency Name""}",26-10-2020 15:34,27-10-2020 21:06,1,1
49,AgentName,{AgentName},"{""type"": ""text"",""value"": ""Agent Name""}",26-10-2020 15:34,27-10-2020 21:06,1,1
50,AgentEmail,{AgentEmail},"{""type"": ""text"",""value"": ""Agent Email""}",26-10-2020 15:34,27-10-2020 21:06,1,1
51,AgentTelephone,{AgentTelephone},"{""type"": ""text"",""value"": ""Agent Telephone""}",26-10-2020 15:34,27-10-2020 21:06,1,1
52,CollegeLogo,{CollegeLogo},"{""type"": ""text"",""value"": ""College Logo""}",20-11-2020 15:34,24-11-2020 05:02,1,1
53,EnrolledCourseList,{EnrolledCourseList},"{""type"": ""text"",""value"": ""Enrolled Course List""}",20-11-2020 15:34,05-12-2020 00:23,1,1
54,OfferedCourseList,{OfferedCourseList},"{""type"": ""text"",""value"": ""Offered Course List""}",20-11-2020 15:34,05-12-2020 00:23,1,1
55,StreetNumber,{StreetNumber},"{""type"": ""text"",""value"": ""Street Number""}",17-12-2020 00:00,17-12-2020 20:53,78,78
56,UnitDetail,{UnitDetail},"{""type"": ""text"",""value"": ""Unit Detail""}",17-12-2020 00:00,17-12-2020 20:53,78,78
57,BuildingName,{BuildingName},"{""type"": ""text"",""value"": ""Building Name""}",17-12-2020 00:00,17-12-2020 20:53,78,78
58,CourseStartDate,{CourseStartDate},"{""type"": ""text"",""value"": ""Course Start Date""}",29-03-2024 00:00,17-12-2020 20:53,1,1
59,CourseEndDate,{CourseEndDate},"{""type"": ""text"",""value"": ""Course End Date""}",29-03-2024 00:00,17-12-2020 20:53,1,1
60,CourseDuration,{CourseDuration},"{""type"": ""text"",""value"": ""Course Duration""}",29-03-2024 00:00,17-12-2020 20:53,1,1
61,StudentContactEmail,{StudentContactEmail},"{""type"": ""text"",""value"": ""Student Contact Email""}",29-03-2024 00:00,17-12-2020 20:53,1,1
62,StudentAlternateEmail,{StudentAlternateEmail},"{""type"": ""text"",""value"": ""Student Alternate Email""}",29-03-2024 00:00,17-12-2020 20:53,1,1
63,StudentEmergencyEmail,{StudentEmergencyEmail},"{""type"": ""text"",""value"": ""Student Emergency Email""}",29-03-2024 00:00,17-12-2020 20:53,1,1
64,DeanName,{DeanName},"{""type"": ""text"",""value"": ""Dean Name""}",02-07-2025 00:00,02-07-2025 00:00,1,1
65,DeanSignature,{DeanSignature},"{""type"": ""text"",""value"": ""Dean Signature""}",02-07-2025 00:00,02-07-2025 00:00,1,1
66,CollegeSignature,{CollegeSignature},"{""type"": ""text"",""value"": ""College Signature""}",02-07-2025 00:00,02-07-2025 00:00,1,1
67,AdmissionManagerSignature,{AdmissionManagerSignature},"{""type"": ""text"", ""value"": ""Admission Manager Signature""}",02-07-2025 00:00,02-07-2025 00:00,1,1
68,StudentSupportSignature,{StudentSupportSignature},"{""type"": ""text"", ""value"": ""Student Support Signature""}",02-07-2025 00:00,02-07-2025 00:00,1,1
