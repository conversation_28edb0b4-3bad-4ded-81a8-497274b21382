<script setup>
import { computed } from 'vue';
import Card from '@spa/components/Card/Card.vue';
import TeacherMatrixListComponent from '@spa/modules/teacher-matrix/TeacherMatrixListComponent.vue';

const props = defineProps({
    store: Object,
});
</script>

<template>
    <Card :variant="'compact'" :pt="{ root: 'bg-gray-100' }">
        <template #header>
            <div class="flex items-center gap-2">
                <h2 class="text-lg font-medium">Assign Subjects (Teacher Matrix)</h2>
            </div>
        </template>
        <template #content>
            <div v-if="store.formData?.id">
                <TeacherMatrixListComponent
                    :filters="{
                        teacherId: store.formData?.id,
                    }"
                />
            </div>
        </template>
    </Card>
</template>

<style scoped></style>
