<template>
    <AsyncForm
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        max-width="400px"
        position="center"
        :dialogTitle="'Add Improvement Category'"
        :store="store"
    >
        <div class="p-4">
            <div class="p-2">
                <FormInput
                    name="name"
                    label="Name"
                    v-model="formData.name"
                    :validation-message="store.errors?.name"
                    :valid="!store.errors?.name"
                    :touched="true"
                    :indicaterequired="true"
                />
            </div>
        </div>
    </AsyncForm>
</template>
<script setup>
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import { useImprovementCategoryStore } from '@spa/stores/modules/improvementcategory/useImprovementCategoryStore.js';
import { storeToRefs } from 'pinia';
// Uncomment these if needed:
// import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
// import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';

const store = useImprovementCategoryStore();
const { formData } = storeToRefs(store);
</script>
