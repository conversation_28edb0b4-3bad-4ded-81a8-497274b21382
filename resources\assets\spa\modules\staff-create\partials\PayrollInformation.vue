<script setup>
import { computed } from 'vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import { getValidationMessage } from '@spa/composables/formComposables.js';

const props = defineProps({
    userType: {},
    modelValue: {},
});
const userType = computed(() => {
    return props.userType ?? route().params.user_type;
});
const emit = defineEmits(['update:modelValue', 'field-touched']);
const formData = computed({
    get() {
        return props.modelValue || {};
    },
    set(value) {
        emit('update:modelValue', value);
    },
});
</script>
<template>
    <!-- Payroll Information -->
    <div v-if="userType !== 'agent'">
        <h3 class="mb-6 text-lg font-semibold">Payroll Information</h3>

        <div class="mb-6">
            <FormInput
                v-model="formData.tax_file_number"
                name="tax_file_number"
                label="Tax File Number"
                placeholder="Add number"
                :required="true"
                v-bind="getValidationMessage(formData, 'tax_file_number')"
            />
        </div>

        <div class="mb-6">
            <FormInput
                v-model="formData.super_fund_name"
                name="super_fund_name"
                label="Superannuation Fund Name"
                placeholder="Add name"
                v-bind="getValidationMessage(formData, 'super_fund_name')"
            />
        </div>

        <div class="mb-6">
            <FormInput
                v-model="formData.super_member_number"
                name="super_member_number"
                label="Superannuation Member Number"
                placeholder="Add number"
                v-bind="getValidationMessage(formData, 'super_member_number')"
            />
        </div>
    </div>
</template>

<style scoped></style>
