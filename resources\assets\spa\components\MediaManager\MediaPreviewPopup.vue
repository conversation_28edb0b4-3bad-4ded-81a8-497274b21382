<template>
    <Dialog
        v-if="visible"
        :width="width"
        :height="height"
        @close="handleClose"
        :title="title"
        dialog-class="tw-content-p-0 tw-dialog"
    >
        <div class="modal-content h-full p-6">
            <template v-if="fileUrl">
                <!-- PDF Preview -->
                <template v-if="detectedFileType === 'pdf'">
                    <iframe
                        :src="embedUrl"
                        class="h-full w-full border-0"
                        type="application/pdf"
                        @load="handleLoad"
                    />
                </template>

                <!-- Image Preview -->
                <template v-else-if="detectedFileType === 'image'">
                    <div class="flex h-full items-center justify-center">
                        <img
                            :src="fileUrl"
                            :alt="fileName"
                            class="max-h-full max-w-full object-contain"
                            @load="handleLoad"
                            @error="handleImageError"
                        />
                    </div>
                </template>

                <!-- HTML/Iframe Preview -->
                <template v-else-if="detectedFileType === 'iframe'">
                    <iframe
                        :src="embedUrl"
                        class="h-full w-full border-0"
                        type="text/html"
                        @load="handleLoad"
                    />
                </template>

                <!-- Unsupported File Type - Download Option -->
                <template v-else>
                    <NoData
                        title="Preview not available"
                        :subtitle="`${fileName} cannot be previewed in the browser.`"
                        :pt="{ root: 'h-full' }"
                    >
                        <template #actions>
                            <div class="mt-6 flex flex-col items-center gap-4">
                                <div class="text-sm text-gray-600">
                                    File type: {{ getFileExtension() }}
                                </div>
                                <Button @click="handleDownload" class="gap-2">
                                    <i class="pi pi-download"></i>
                                    Download File
                                </Button>
                            </div>
                        </template>
                    </NoData>
                </template>
            </template>

            <!-- No File URL Provided -->
            <template v-else>
                <NoData
                    title="No file available"
                    subtitle="File URL is missing or invalid."
                    :pt="{ root: 'h-full' }"
                />
            </template>

            <!-- Error State -->
            <template v-if="hasError">
                <NoData
                    title="Failed to load file"
                    subtitle="There was an error loading the file. Please try downloading it instead."
                    :pt="{ root: 'h-full' }"
                >
                    <template #actions>
                        <Button @click="handleDownload" class="mt-6 gap-2">
                            <icon
                                :name="'download'"
                                :fill="'currentColor'"
                                :width="'16'"
                                :height="'16'"
                            />
                            Download File
                        </Button>
                    </template>
                </NoData>
            </template>
        </div>

        <!-- Dialog Actions -->
        <DialogActionsBar>
            <div class="flex w-full items-center justify-end gap-4">
                <div class="flex gap-2">
                    <Button
                        v-if="showDownload && fileUrl"
                        variant="primary"
                        @click="handleDownload"
                        class="gap-2"
                        size="sm"
                    >
                        <icon
                            :name="'download'"
                            :fill="'currentColor'"
                            :width="'16'"
                            :height="'16'"
                        />
                        Download
                    </Button>
                </div>
                <div class="flex gap-4">
                    <Button variant="secondary" size="sm" @click="handleClose">Close</Button>
                    <Button v-if="showSaveButton" variant="primary" size="sm" @click="handleSave"
                        >Save</Button
                    >
                </div>
            </div>
        </DialogActionsBar>
    </Dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Dialog, DialogActionsBar } from '@progress/kendo-vue-dialogs';
import Button from '@spa/components/Buttons/Button.vue';
import NoData from '@spa/components/NoData/NoData.vue';

const props = defineProps({
    visible: {
        type: Boolean,
        required: true,
        default: false,
    },
    fileUrl: {
        type: String,
        default: '',
    },
    fileName: {
        type: String,
        default: 'file',
    },
    title: {
        type: String,
        default: 'File Preview',
    },
    width: {
        type: Number,
        default: 1080,
    },
    height: {
        type: Number,
        default: 720,
    },
    // 'auto', 'pdf', 'image', 'iframe', 'download'
    fileType: {
        type: String,
        default: 'auto',
    },
    showDownload: {
        type: Boolean,
        default: true,
    },
    showSaveButton: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['close', 'save', 'loaded', 'error']);

const hasError = ref(false);

// Detect file type from URL or extension
const detectedFileType = computed(() => {
    if (props.fileType !== 'auto') {
        return props.fileType;
    }

    if (!props.fileUrl) return 'download';

    const url = props.fileUrl.toLowerCase();
    const extension = getFileExtension().toLowerCase();

    // Check for PDF
    if (url.includes('.pdf') || extension === 'pdf' || url.includes('application/pdf')) {
        return 'pdf';
    }

    // Check for images
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
    if (
        imageExtensions.some((ext) => url.includes(`.${ext}`)) ||
        imageExtensions.includes(extension)
    ) {
        return 'image';
    }

    // Check for HTML/embeddable content
    if (url.includes('.html') || url.includes('embed') || url.includes('viewer')) {
        return 'iframe';
    }

    // Default to download for unsupported types
    return 'download';
});

// Create embed URL for PDF and iframe types
const embedUrl = computed(() => {
    if (!props.fileUrl) return '';

    if (detectedFileType.value === 'pdf') {
        // Add #toolbar=0 to hide PDF toolbar if needed
        return props.fileUrl;
    }

    return props.fileUrl;
});

// Get file extension
const getFileExtension = () => {
    if (!props.fileName && !props.fileUrl) return 'unknown';

    const name = props.fileName || props.fileUrl;
    const parts = name.split('.');
    return parts.length > 1 ? parts.pop().toUpperCase() : 'unknown';
};

// Handle file download
const handleDownload = () => {
    if (!props.fileUrl) return;

    const link = document.createElement('a');
    link.href = props.fileUrl;
    link.download = props.fileName || 'download';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

const handleClose = () => {
    hasError.value = false;
    emit('close');
};

const handleSave = () => {
    emit('save');
};

const handleLoad = () => {
    hasError.value = false;
    emit('loaded');
};

const handleImageError = () => {
    hasError.value = true;
    emit('error');
};

// Reset error state when dialog visibility changes
watch(
    () => props.visible,
    (newVal) => {
        if (newVal) {
            hasError.value = false;
        }
    }
);
</script>

<style scoped>
.modal-content {
    overflow: auto;
}

iframe {
    border: none;
}
</style>
