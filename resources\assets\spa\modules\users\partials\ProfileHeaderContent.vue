<template>
    <div class="flex justify-between gap-4">
        <div class="flex items-start gap-2">
            <Avatar
                :label="'RG'"
                :pt="{ root: 'w-12 h-12', label: 'text-lg' }"
                :loading="store.ctxLoading['fetch-by-id']"
            />
            <div class="flex flex-col gap-1">
                <div class="flex w-full items-baseline gap-3">
                    <div
                        class="h-6 w-[200px] animate-pulse rounded-md bg-gray-300"
                        aria-label="Loading user name"
                        v-if="store.ctxLoading['fetch-by-id']"
                    ></div>
                    <p class="text-xl font-semibold" v-else>{{ data.name }}</p>
                    <Badge :variant="'default'" :outline="true">{{ data.user_type }}</Badge>
                </div>
                <div class="flex items-center gap-5">
                    <div class="flex items-center gap-1 text-gray-500">
                        <icon name="mail" :fill="'currentColor'" :width="'18'" :height="'18'" />
                        <p class="text-sm">
                            <CopyToClipboard
                                :text="data.email"
                                :auto-hide="false"
                                :loading="store.ctxLoading['fetch-by-id']"
                            />
                        </p>
                    </div>
                </div>
                <div class="mt-4 flex items-center gap-6">
                    <div class="flex items-center">
                        <LabelValuePair :label="'Status:'">
                            <UserStatusBadge :status="data.user_status" />
                        </LabelValuePair>
                    </div>
                    <template v-if="data.last_login">
                        <div class="h-6 w-px bg-gray-500"></div>
                        <LabelValuePair
                            :label="'Last Login:'"
                            :value="convertJsDateTimeFormat(data.last_login)"
                        />
                    </template>
                </div>
            </div>
        </div>
        <slot name="profile-header-actions" />
    </div>
</template>
<script setup>
import { computed } from 'vue';
import CopyToClipboard from '@spa/components/CopyAction/CopyToClipboard.vue';
import Avatar from '@spa/components/Avatar/Avatar.vue';
import Badge from '@spa/components/badges/Badge.vue';
import LabelValuePair from '@spa/components/LabelValuePair/LabelValuePair.vue';
import UserStatusBadge from '@spa/modules/users/partials/UserStatusBadge.vue';
import { convertJsDateTimeFormat } from '@spa/composables/dateTimeComposables.js';

const props = defineProps({
    store: Object,
});

const data = computed(() => {
    return props.store.formData;
});
</script>
