<template>
    <Layout :noSpacing="true" :loading="true" :pt="{ wrapper: 'h-full' }">
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'Certificate Templates'" :back="false" />
        </template>
        <!-- <HeaderTabs
            :tabs="tabs"
            :current="'templates'"
            :pt="{ root: 'pt-4' }"
        /> -->
        <div class="h-screen-header flex flex-col space-y-6 px-8 py-6">
            <div class="flex items-center justify-between">
                <IconInput
                    v-model.lazy="filters.search"
                    :pt="{ root: 'w-64 md:w-80 h-9' }"
                    placeholder="Enter keyword"
                    :debounce="300"
                    :autocomplete="'off'"
                />
                <div :class="'flex items-center justify-end gap-2'">
                    <!-- <Link :href="route('spa.certificate-builder')"> -->
                    <!--                    <Button :variant="'secondary'" size="base" @click="startTour">-->
                    <!--                        <span :class="'text-gray-600'">-->
                    <!--                            <icon :name="'help'" :fill="'currentColor'" />-->
                    <!--                        </span>-->
                    <!--                        <span>Start Tour</span></Button-->
                    <!--                    >-->
                    <Button
                        :variant="'primary'"
                        size="base"
                        @click="createNew = true"
                        data-tour="step1"
                    >
                        <span :class="'text-white'">
                            <icon :name="'add'" :fill="'#ffffff'" />
                        </span>
                        <span>Create New Template</span></Button
                    >
                    <!-- </Link> -->
                </div>
            </div>
            <Card :pt="{ content: 'space-y-6', root: 'p-6 md:p-6 flex-1 overflow-y-auto h-full' }">
                <template #content>
                    <div class="flex items-center justify-between">
                        <h2 class="text-2xl font-bold text-gray-800 md:text-2xl lg:text-3xl">
                            Templates
                        </h2>
                        <FilterBlockWrapper label="Certificate Type">
                            <EnumSelect
                                enum-class="GalaxyAPI\Enums\CertificateTypeEnum"
                                v-model="filters.certificateType"
                                :has-select-all="true"
                                :placeholder="'Select Certificate Type'"
                                ref="certificateTypeRef"
                            />
                        </FilterBlockWrapper>
                    </div>

                    <div class="relative">
                        <template v-if="loading.value">
                            <div class="pointer-events-none absolute inset-0 z-10 bg-white/60">
                                <Spinner
                                    :size="'lg'"
                                    :pt="{ root: 'items-start' }"
                                    loadingText=""
                                />
                            </div>
                        </template>
                        <div class="space-y-4">
                            <div
                                class="grid max-w-5xl grid-cols-4 gap-6 md:gap-8"
                                v-if="templates.length"
                            >
                                <template v-for="(template, index) in templates" :key="template.id">
                                    <TemplateCard
                                        :data="template"
                                        @edit="handleEdit($event, template)"
                                        @editMeta="handleEditMeta($event, template)"
                                        @settings="handleSettings($event, template)"
                                        @preview="handlePreview($event, template)"
                                        @delete="handleDelete($event, template)"
                                        @setAsDefault="handleSetAsDefault($event, template)"
                                    />
                                </template>
                            </div>
                            <div v-else>
                                <no-data>
                                    <template #icon>
                                        <span class="text-primary-blue-500">
                                            <icon
                                                name="book"
                                                width="48"
                                                height="48"
                                                fill="currentColor"
                                            />
                                        </span>
                                    </template>
                                    <template #content>
                                        <h2 class="text-center text-base font-medium text-gray-700">
                                            No Templates Found
                                        </h2>
                                        <p class="text-sm leading-5 text-gray-700">
                                            We couldn't find any
                                            <span class="font-semibold">{{
                                                currentType?.label || 'Certificates'
                                            }}</span>
                                            templates. You can create a new template.
                                        </p>
                                    </template>
                                    <template #button>
                                        <Button
                                            size="sm"
                                            variant="secondary"
                                            @click="createNew = true"
                                        >
                                            <icon :name="'add'" :fill="'currentColor'" />
                                            Create New Template
                                        </Button>
                                    </template>
                                </no-data>
                            </div>
                        </div>
                    </div>
                </template>
            </Card>
        </div>
        <PreviewPopupWithData
            :visible="openPreviewPopup"
            :data="previewJson"
            :certificateEmbedeUrl="certificateEmbedeUrl"
            @close="closePreviewPopup"
            @stopLoading="isIframeLoad"
            type="image"
            :previewItem="selectedTemplate"
        />
        <LoadingPopup :visible="isLoading" @close="isLoading = false" />
        <CreateNewPopup
            :visible="createNew"
            @close="
                () => {
                    createNew = false;
                    hasTour = false;
                }
            "
            :certificateIdFormate="certificateIdFormate"
            :has-tour="hasTour"
            @endTour="endTour"
        />
        <SaveTemplatePopup
            :visible="editMetaVisible"
            :title="'Edit Template'"
            :showInfoBox="false"
            :prefillWithDataItem="true"
            :certificateIdFormate="certificateIdFormate"
            :dataItem="selectedTemplate"
            @submit="handleEditMetaSubmit"
            @close="
                () => {
                    editMetaVisible = false;
                    hasTour = false;
                    tour.complete();
                }
            "
        />
        <TemplateSettingsPopup
            :visible="settingsVisible"
            :template="selectedTemplate"
            @submit="handleSettingsSubmit"
            @close="settingsVisible = false"
        />
    </Layout>
</template>
<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import SearchWithButtons from '@spa/modules/common/SearchWithButtons.vue';
import Card from '@spa/components/Card/Card.vue';
import TemplateCard from '@spa/modules/certificate-builder/TemplateCard.vue';
import data from './certificateProps.json';
import PreviewPopup from '@spa/modules/certificate-builder/PreviewPopup.vue';
import PreviewPopupWithData from '@spa/modules/certificate-builder/PreviewPopupWithData.vue';
import SaveTemplatePopup from '@spa/modules/certificate-builder/SaveTemplatePopup.vue';
import TemplateSettingsPopup from '@spa/modules/certificate-builder/TemplateSettingsPopup.vue';
import HeaderTabs from '@spa/modules/common/HeaderTabs.vue';
import IconInput from '@spa/components/IconInput.vue';
import Button from '@spa/components/Buttons/Button.vue';
import { Link, usePage } from '@inertiajs/vue3';
import useCertificateResource from '@spa/services/certificates/certificateResource';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import LoadingPopup from '@spa/modules/certificate-builder/LoadingPopup.vue';
import { debounce } from 'lodash';
import CreateNewPopup from '@spa/modules/certificate-builder/CreateNewPopup.vue';
import { router } from '@inertiajs/vue3';
import EnumSelect from '@spa/components/AsyncComponents/Select/EnumSelect.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import GlobalContextLoader from '@spa/components/Loader/GlobalContextLoader.vue';
import NoData from '@spa/components/NoData/NoData.vue';
import Spinner from '@spa/components/Loader/Spinner.vue';
import { useShepherd } from 'vue-shepherd';

const hasTour = ref(false);

const tour = useShepherd({
    useModalOverlay: true,
    defaultStepOptions: {
        scrollTo: true,
        cancelIcon: {
            enabled: true,
        },
    },
});

const defaultTourButtons = ref([
    {
        text: 'Back',
        action: tour.back,
        classes: 'tw-btn-secondary',
    },
    {
        text: 'Next',
        action: tour.next,
        classes: 'tw-btn-primary',
    },
    {
        text: 'Skip Tour',
        action: tour.complete,
        classes: 'tw-btn-tertiary',
    },
]);

const startTour = () => {
    hasTour.value = true;
    tour.addSteps([
        {
            attachTo: { element: '[data-tour="step1"]', on: 'left' },
            title: 'Click on Create New Template',
            text: 'This is the first step of the tour.',
            buttons: [
                {
                    text: 'Next',
                    action: handleNextStep,
                    classes: 'tw-btn-primary',
                },
                {
                    text: 'Skip Tour',
                    action: endTour,
                    classes: 'tw-btn-tertiary',
                },
            ],
        },
    ]);
    tour.start();
};

const endTour = () => {
    tour.complete();
    hasTour.value = false;
};

watch(
    () => tour.complete,
    (newVal) => {
        if (newVal) {
            endTour();
        }
    }
);

const handleNextStep = () => {
    tour.complete();
    createNew.value = true;
};
const props = defineProps({
    templates: {
        type: Array,
        default: () => [{}],
    },
    certificateIdFormate: {
        type: Object,
        default: () => [],
    },
});

const loaderStore = useLoaderStore();
const $page = usePage();

const isLoading = ref(false);
const createNew = ref(false);
const payload = ref(null);
const filters = ref({
    certificateType: '',
    search: null,
});
const certificateTypeRef = ref(null);

const $certificate = useCertificateResource('spa/certificates', {
    only: ['certificateIdFormate', 'templates'],
});

const templates = computed(() => props.templates);
const certificateEmbedeUrl = ref('');
const selectedTemplate = ref({});
const editMetaVisible = ref(false);
const settingsVisible = ref(false);
const previewJson = ref({});
const openPreviewPopup = ref(false);

const actionButtons = [
    {
        label: 'Create Blank Template',
        slug: 'create_blank',
        variant: 'primary',
        icon: 'add',
    },
];

const tabs = [
    {
        name: 'Templates',
        slug: 'templates',
        route: null,
    },
    {
        name: 'Attributes',
        slug: 'attributes',
        route: null,
    },
];

const handleEdit = (e, item) => {
    router.visit(route('spa.certificate-builder'), {
        preserveState: true,
        preserveScroll: true,
        only: ['certificate'],
        data: {
            certificateId: item.secureId,
            type: 'certificate',
        },
    });
};

const handleSettings = (e, item) => {
    selectedTemplate.value = item;
    settingsVisible.value = true;
};

const handleSettingsSubmit = async (values) => {
    await $certificate.updateTemplateSettings(selectedTemplate.value, values, {
        showToast: true,
        reFetch: true,
        onSuccess: () => {
            settingsVisible.value = false;
        },
    });
};

const handleEditMeta = (e, item) => {
    selectedTemplate.value = item;
    editMetaVisible.value = true;
};

const handleEditMetaSubmit = async (formData) => {
    // Only send required fields for update: name and certificate_number_formate_id
    loaderStore.startContextLoading('button');
    await $certificate.executeAction(
        'updateTemplate',
        {
            secure_id: selectedTemplate.value.secureId,
            name: formData.name,
            certificate_number_formate_id: formData.certificate_number_formate_id,
        },
        {
            method: 'put',
            url: `spa/certificates/update-template/${selectedTemplate.value.secureId}`,
            showToast: true,
            reFetch: true,
            onSuccess: () => {
                editMetaVisible.value = false;
            },
            onFinish: () => {
                loaderStore.stopContextLoading('button');
            },
        }
    );
};
const isIframeLoad = () => {
    if (certificateEmbedeUrl.value != '') {
        isLoading.value = false;
    }
};
const handlePreview = async (e, item) => {
    openPreviewPopup.value = true;
    await $certificate.getPreviewUrl(
        {
            id: item.id,
        },
        {
            handlePreview: (response) => {
                selectedTemplate.value = item;
                previewJson.value = item.json_data;
                certificateEmbedeUrl.value = response.embedeUrl;
            },
        }
    );
};
const closePreviewPopup = () => {
    openPreviewPopup.value = false;
};

const handleDelete = (e, item) => {
    $certificate.deleteTemplate({
        secure_id: item.secureId,
    });
};

const loading = computed(() => $certificate.loading);

watch(
    () => loaderStore.contextLoaders['generate'] && openPreviewPopup.value,
    (newVal) => {
        if (newVal) {
            isLoading.value = true;
        } else {
            isLoading.value = false;
        }
    }
);

const filterTemplates = debounce((newVal) => {
    if (newVal) {
        templates1.value = props.templates.filter((item) =>
            item.name.toLowerCase().includes(newVal.toLowerCase())
        );
    } else {
        templates1.value = props.templates;
    }
}, 300);

const handleSetAsDefault = (e, item) => {
    $certificate.setAsDefaultTemplate({
        id: item.id,
    });
};

let debounceTimeout;
let lastFetchedFilters = JSON.stringify({
    certificateType: '',
    search: null,
});

watch(
    () => filters.value,
    async (newFilters) => {
        const newFiltersStr = JSON.stringify(newFilters);
        console.log('newF', lastFetchedFilters, newFiltersStr);

        if (lastFetchedFilters !== newFiltersStr) {
            lastFetchedFilters = newFiltersStr;
            clearTimeout(debounceTimeout);
            debounceTimeout = setTimeout(async () => {
                await $certificate.fetch(newFilters);
            }, 500);
        }
    },
    { deep: true }
);

watch(
    () => filters.value.certificateType,
    (newVal) => {
        filters.value.search = '';
    }
);

onMounted(() => {
    if ($page.props && $page.props.query) {
        let params = { ...$page.props.query };
        if ('certificateType' in params && params.certificateType !== null) {
            const parsed = parseInt(params.certificateType, 10);
            params.certificateType = isNaN(parsed) ? null : parsed;
        }

        filters.value = { ...filters.value, ...params };
    }
});

const currentType = computed(() => {
    return certificateTypeRef.value?.allOptions?.find(
        (item) => item.value == filters.value.certificateType
    );
});

const handleCreateTemplate = () => {};
</script>
<style lang=""></style>
