<?php

namespace GalaxyAPI\Controllers;

use App\Helpers\Helpers;
use App\Mail\sendStudentInviteMail;
use App\Model\v2\EmailTemplateDocuments;
use App\Model\v2\Student;
use App\Repositories\StudentRepository;
use App\Services\OnboardSettingService;
use App\Users;
use GalaxyAPI\DTO\BulkActionDTO;
use GalaxyAPI\Enums\BulkActionSelectionTypeEnum;
use GalaxyAPI\Enums\UserStatusEnum;
use GalaxyAPI\Enums\UserTypeEnum;
use GalaxyAPI\Requests\EmptyRequest;
use GalaxyAPI\Resources\UsersResource;
use GalaxyAPI\Services\UserService;
use GalaxyAPI\Services\UserStepFormService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Mail;

class UsersController extends CrudBaseController
{
    public function init()
    {
        $this->withAll = ['roles'];
        $this->loadAll = ['roles'];

        $this->addBulkAction(
            BulkActionDTO::init(
                label: 'Disable',
                action: 'disable_user',
                callback: function ($params) {
                    $userType = $params->extraData['user_type'] ?? null;
                    $selectedIds = $this->getSelectedModelIds($params, $userType);
                    $userIds = $this->getUserIds($selectedIds, $userType);
                    try {
                        $this->userService->disableUsers($userIds);
                    } catch (\Exception $e) {
                        return ajaxError($e->getMessage(), 500);
                    }

                    return ajaxSuccess([], 'Users disabled successfully.');
                },
            )
        );

        $this->addBulkAction(
            BulkActionDTO::init(
                label: 'Enable',
                action: 'enable_user',
                callback: function ($params) {
                    $userType = $params->extraData['user_type'] ?? null;
                    $selectedIds = $this->getSelectedModelIds($params, $userType);
                    $userIds = $this->getUserIds($selectedIds, $userType);
                    try {
                        $this->userService->enableUsers($userIds);
                    } catch (\Exception $e) {
                        return ajaxError($e->getMessage(), 500);
                    }

                    return ajaxSuccess([], 'Users enabled successfully.');
                },
            )
        );

        $this->addBulkAction(
            BulkActionDTO::init(
                label: 'Reset Password',
                action: 'reset_password',
                callback: function ($params) {
                    $userType = $params->extraData['user_type'] ?? null;
                    $selectedIds = $this->getSelectedModelIds($params, $userType);
                    $userIds = $this->getUserIds($selectedIds, $userType);
                    try {
                        $result = $this->userService->resetPasswords($userIds);
                    } catch (\Exception $e) {
                        return ajaxError($e->getMessage(), 500);
                    }

                    return ajaxSuccess([], $result['message']);
                },
            )
        );

        $this->addBulkAction(
            BulkActionDTO::init(
                label: 'Send Invite',
                action: 'send_invite',
                callback: function ($params) {
                    $userType = $params->extraData['user_type'] ?? null;
                    $selectedIds = $this->getSelectedModelIds($params, $userType);
                    $options = Arr::except($params->extraData, ['user_type']) ?? [];
                    try {
                        $result = $this->userService->sendInvite($selectedIds, $userType, $options);
                    } catch (\Exception $e) {
                        return ajaxError($e->getMessage(), 500);
                    }

                    if ($result['success']) {
                        return ajaxSuccess([], $result['message']);
                    }

                    return ajaxError($result['message'], 400);
                },
            )
        );

        $this->addBulkAction(
            BulkActionDTO::init(
                label: 'Assign Roles',
                action: 'assign_roles',
                callback: function ($params) {
                    $userType = $params->extraData['user_type'] ?? null;
                    $roles = $params->extraData['roles'] ?? null;
                    $selectedIds = $this->getSelectedModelIds($params, $userType);
                    $userIds = $this->getUserIds($selectedIds, $userType);
                    try {
                        $result = $this->userService->assignRoles($userIds, $roles);
                    } catch (\Exception $e) {
                        return ajaxError($e->getMessage(), 500);
                    }

                    return ajaxSuccess([], 'Roles assigned successfully.');
                },
            )
        );
    }

    public function __construct(protected UserService $userService, protected UserStepFormService $userStepFormService, protected OnboardSettingService $onboardSettingService)
    {
        parent::__construct(
            model: Users::class,
            storeRequest: EmptyRequest::class,
            updateRequest: EmptyRequest::class,
            resource: UsersResource::class,
        );

        $this->init();
    }

    public function getAuthUser()
    {
        $userData = $this->userService->getAuthenticatedUserData(Auth::user());

        return ajaxSuccess(['data' => $userData], '');
    }

    public function disableUser()
    {
        $validated = $this->validateUserStatusRequest();
        $userIds = $this->getUserIds($validated['ids'], $validated['user_type']);

        try {
            $result = $this->userService->disableUsers($userIds);
            if (! $result['success']) {
                return ajaxError($result['message'], 400);
            }

            return ajaxSuccess([], $result['message']);
        } catch (\Exception $e) {
            return ajaxError($e->getMessage(), 500);
        }
    }

    public function enableUser()
    {
        $validated = $this->validateUserStatusRequest();
        $userIds = $this->getUserIds($validated['ids'], $validated['user_type']);

        try {
            $this->userService->enableUsers($userIds);
        } catch (\Exception $e) {
            return ajaxError($e->getMessage(), 500);
        }

        return ajaxSuccess([], 'Users enabled successfully.');
    }

    public function resetPassword()
    {
        $validated = $this->validateUserStatusRequest();
        $userIds = $this->getUserIds($validated['ids'], $validated['user_type']);

        try {
            $result = $this->userService->resetPasswords($userIds);
        } catch (\Exception $e) {
            return ajaxError($e->getMessage(), 500);
        }

        return ajaxSuccess([], $result['message']);
    }

    public function sendInvite()
    {
        request()->validate([
            'ids' => ['required', 'array'],
            'user_type' => ['required', 'string', UserTypeEnum::getValidationString()],
        ]);

        if (request()->get('user_type') === 'student') {
            $result = $this->sendInviteForStudent(request()->get('ids'));
            if ($result['success']) {
                return ajaxSuccess([], $result['message']);
            } else {
                return ajaxError($result['message'], 400);
            }
        }

        try {
            $result = $this->userService->sendInvite(
                request()->get('ids'),
                request()->get('user_type'),
            );
        } catch (\Exception $e) {
            return ajaxError($e->getMessage(), 500);
        }

        if ($result['success']) {
            return ajaxSuccess([], $result['message']);
        }

        return ajaxError($result['message'], 400);
    }

    public function assignRoles()
    {
        request()->validate([
            'ids' => ['required', 'array'],
            'user_type' => ['required', 'string', UserTypeEnum::getValidationString()],
            'roles' => ['required', 'array', 'min:1'],
            'roles.*' => ['required', 'integer', 'exists:galaxy_app_roles,id'],
        ]);

        $userIds = $this->getUserIds(request()->get('ids'), request()->get('user_type'));

        try {
            $task = $this->userService->assignRoles(
                $userIds,
                request()->input('roles')
            );
        } catch (\Exception $e) {
            return ajaxError($e->getMessage(), 500);
        }

        return ajaxSuccess([], 'Roles assigned successfully.');
    }

    public function getUserMetaByType(Request $request, $userType)
    {
        $counts = $this->userService->getUserStatusCount($userType);

        return ajaxSuccess(['data' => [
            'counts' => $counts,
        ]], '');
    }

    public function saveStep(Request $request)
    {
        $userType = $request->get('user_type');

        $requestClass = config("type-mappers.requests.{$userType}");

        if (! $requestClass) {
            throw new \InvalidArgumentException("No request validation for {$userType}");
        }

        $validated = app($requestClass)->validated();
        $result = $this->userStepFormService->saveStepData($request);

        return $this->success('User step saved successfully', $result);
    }

    public function updateStatus()
    {
        request()->validate([
            'ids' => ['required', 'array'],
            'user_type' => ['required', 'string', UserTypeEnum::getValidationString()],
            'status' => ['required', 'integer', UserStatusEnum::getValidationString()],
        ]);

        $userIds = $this->getUserIds(request()->get('ids'), request()->get('user_type'));
        try {
            foreach ($userIds as $userId) {
                $this->userService->updateUserStatus([$userId], request()->get('status'));
            }

            return ajaxSuccess([], 'Status updated successfully.');
        } catch (\Exception $e) {
            return ajaxError($e->getMessage(), 500);
        }
    }

    public function getAssignedRolesByIds($ids)
    {
        $users = Users::with('roles')->whereIn('id', $ids)->get();
        $roles = $users->pluck('roles')->flatten()->unique()->values()->toArray();

        return $roles;
    }

    protected function validateUserStatusRequest(): array
    {
        return request()->validate([
            'ids' => ['required', 'array'],
            'user_type' => ['required', 'string', UserTypeEnum::getValidationString()],
        ]);
    }

    protected function getSelectedModelIds($params, $userType)
    {
        $modelClass = $this->getModelClass($userType);
        if ($params->type->value === BulkActionSelectionTypeEnum::all->value) {
            $query = $modelClass::query()->select('id');
            if (! empty($params->excludeIds)) {
                $query->whereNotIn('id', $params->excludeIds);
            }

            $selectedIds = $query->pluck('id')->toArray();
        } else {
            $selectedIds = $params->selectedIds ?? [];
        }

        return $selectedIds;
    }

    protected function getUserIds($ids, $userType)
    {
        //        if ($userType === UserTypeEnum::STAFF->value) {
        //            return $ids;
        //        }
        $modelClass = $this->getModelClass($userType);
        if ($userType === UserTypeEnum::STUDENT->value) {
            $students = $modelClass::with('user')->whereIn('id', $ids)->get();

            return $students
                ->map(fn ($student) => $student->user?->id)
                ->filter()
                ->unique()
                ->values()
                ->toArray();
        }
        $models = $this->validateUserGroup($modelClass, $ids, $userType);

        return $models->pluck('user_id')->toArray();
    }

    protected function validateUserGroup(string $modelClass, array $ids, string $userType)
    {
        if (empty($ids)) {
            throw new \Exception("No {$userType}s provided");
        }

        // Get all models with the provided IDs
        $models = $modelClass::whereIn('id', $ids)->get();

        // Check if all IDs were found
        if ($models->count() !== count($ids)) {
            $foundIds = $models->pluck('id')->toArray();
            $missingIds = array_diff($ids, $foundIds);
            throw new \Exception("{$userType}(s) not found: ".implode(', ', $missingIds));
        }

        // Get all user IDs that should exist
        $modelUserIds = $models->pluck('user_id')->filter()->toArray();

        // Check if users exist in the Users table
        if (! empty($modelUserIds)) {
            $existingUserIds = Users::whereIn('id', $modelUserIds)->pluck('id')->toArray();
            $missingUserIds = array_diff($modelUserIds, $existingUserIds);

            if (! empty($missingUserIds)) {
                throw new \Exception('Referenced users do not exist: '.implode(', ', $missingUserIds));
            }
        }

        foreach ($models as $model) {
            if (! $model->user_id) {
                throw new \Exception("User does not exist for {$userType} ID: {$model->id}");
            }
        }

        return $models;
    }

    protected function getModelClass(string $userType): string
    {
        $modelClass = UserTypeEnum::modelMap()[$userType] ?? null;

        if (! $modelClass || ! class_exists($modelClass)) {
            throw new \Exception('Invalid user type');
        }

        return $modelClass;
    }

    private function sendInviteForStudent(array $ids)
    {
        if (empty($ids)) {
            throw new \Exception('No students provided');
        }

        $students = Student::whereIn('id', $ids)->get();

        if ($students->count() !== count($ids)) {
            $foundIds = $students->pluck('id')->toArray();
            $missingIds = array_diff($ids, $foundIds);
            throw new \Exception('Students not found: '.implode(', ', $missingIds));
        }

        $createdUsers = [];
        $errors = [];
        $invitedUsers = [];

        foreach ($students as $student) {
            try {
                $existingUser = Users::where('username', $student->generated_stud_id)->first();

                $user = $existingUser ?? $this->userService->createUser(Student::class, $student->id);

                // Ensure username consistency
                if (empty($user->username)) {
                    $user->username = $student->generated_stud_id;
                }

                $user->status = UserStatusEnum::ACTIVE->value;
                $user->save();

                // Send email invite
                $this->sendInviteEmailForStudent($user, $student);

                $invitedUsers[] = [
                    'model_id' => $student->id,
                    'user_data' => $user,
                ];
            } catch (\Throwable $e) {
                $errors[] = "Failed to process student ID {$student->id}: ".$e->getMessage();

                \Log::error('Student invite failed', [
                    'student_id' => $student->id,
                    'error' => $e->getMessage(),
                ]);
            }

            if (! empty($errors) && empty($invitedUsers)) {
                throw new \Exception('Failed to create users: '.implode(', ', $errors));
            }

            $successCount = count($invitedUsers);
            $message = "Invites sent successfully to {$successCount} Student(s)";

            if (! empty($errors)) {
                $message .= '. Some failed: '.implode(', ', $errors);
            }

            return [
                'success' => true,
                'message' => $message,
                'data' => $invitedUsers,
                'success_count' => $successCount,
                'error_count' => count($errors),
                'errors' => $errors,
            ];
        }

    }

    private function sendInviteEmailForStudent(Users $user, Student $student)
    {
        try {
            $template = $this->onboardSettingService->getEmailTemplateTitleData([
                'template_name' => 'studentInviteEmail',
            ]);

            if (! $template) {
                throw new \Exception('Student invite email template not found.');
            }

            $studentRepository = new StudentRepository($student);

            $content = $studentRepository->setLetterBodyContent($student->id, '', $template[0]['content']);

            $mailData = [
                'student_name' => $user->name,
                'contentData' => $content,
                'email' => $user->email,
                'subject' => $template[0]['email_subject'],
                'attachments' => $this->getMailAttachments($template[0]['id']),
            ];

            $token = Password::getRepository()->create($user);
            $mailData['actionUrl'] = route('password.reset', $token).'?email='.urlencode($user->email);

            $inviteCode = strtoupper(Str::random(8));
            $student->setMeta('invitation_code', $inviteCode);
            $mailData['inviteCode'] = $inviteCode;

            Mail::to($user->email)->send(new SendStudentInviteMail($mailData));

        } catch (\Throwable $e) {
            \Log::error('Failed to send student invite email: '.$e->getMessage(), [
                'student_id' => $student->id,
                'user_id' => $user->id,
            ]);

            throw new \Exception('Failed to send invite email: '.$e->getMessage());
        }
    }

    private function getMailAttachments($templateId)
    {
        $existFilePath = Config::get('constants.uploadFilePath.Templates');

        $existDestinationPath = Helpers::changeRootPath($existFilePath, $templateId);

        $docList = EmailTemplateDocuments::where('email_template_id', $templateId)->get()->toArray();

        $existingAttachment = [];

        foreach ($docList as $doc) {
            $existingAttachment[] = $existDestinationPath['view'].$doc['file'];
        }

        return $existingAttachment;
    }
}
