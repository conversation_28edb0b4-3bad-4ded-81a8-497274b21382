<template>
    <FormWrapper
        :visibleDialog="userStore.actionDialog"
        :hideOnOverlayClick="false"
        :fixedActionBar="true"
        :width="'50%'"
        :maxWidth="'576px'"
        :style="{
            maxWidth: '576px',
        }"
        :primaryBtnLabel="getModalButtonLabel"
        :secondaryBtnLabel="'Cancel'"
        :isDisabled="loading"
        :isSubmitting="loading"
        @drawerclose="userStore.actionDialog = false"
        @drawersaved="handleSubmit"
        :position="'center'"
        :pt="{ content: 'px-5 pt-4 pb-8 space-y-4' }"
    >
        <template #title>
            <div class="text-xl font-medium">{{ getModalTitle }}</div>
        </template>
        <template #content>
            <ExportUsersFormContent
                :store="store"
                :rows="selectedRows"
                v-model="exportFormat"
                v-if="selectedAction === 'export_users'"
            />
            <component :is="getModalComponent" :store="store" :rows="selectedRows" v-else />
        </template>
    </FormWrapper>
</template>
<script setup>
import { ref, watch, computed, onUnmounted } from 'vue';
import FormWrapper from '@spa/components/KendoModals/SidebarDrawer.vue';
import { uuid } from '@spa/helpers/index.js';
import SendInviteFormContent from '@spa/modules/users/partials/SendInviteFormContent.vue';
import ResetPasswordFormContent from '@spa/modules/users/partials/ResetPasswordFormContent.vue';
import DisableUserFormContent from '@spa/modules/users/partials/DisableUserFormContent.vue';
import ExportUsersFormContent from '@spa/modules/users/partials/ExportUsersFormContent.vue';
import { useUsersStore } from '@spa/stores/modules/users/useUsersStore.js';
import EnableUserFormContent from '@spa/modules/users/partials/EnableUserFormContent.vue';
import globalHelper from '@spa/plugins/global-helper.js';

const props = defineProps({
    store: Object,
    dialogWidth: {
        type: [String, Number],
        default: '50%',
    },
    showCancel: {
        type: Boolean,
        default: true,
    },
    maxWidth: {
        default: '600px',
    },
    selectedAction: String,
    selectedRows: Array,
});

const userStore = useUsersStore();
const loading = ref(false);
const id = route().params.id;
const exportFormat = ref('csv');

const getModalTitle = computed(() => {
    switch (props.selectedAction) {
        case 'send_invite':
            return 'Send Invite';
        case 'reset_password':
            return 'Reset Password';
        case 'disable_user':
            return 'Disable User';
        case 'enable_user':
            return 'Enable User';
        case 'export_users':
            return 'Export Users';
        default:
            return 'Assign Roles';
    }
});

const getModalComponent = computed(() => {
    switch (props.selectedAction) {
        case 'send_invite':
            return SendInviteFormContent;
        case 'reset_password':
            return ResetPasswordFormContent;
        case 'disable_user':
            return DisableUserFormContent;
        case 'enable_user':
            return EnableUserFormContent;
        case 'export_users':
            return ExportUsersFormContent;
        default:
            return null;
    }
});

const getModalButtonLabel = computed(() => {
    switch (props.selectedAction) {
        case 'send_invite':
            if (userStore.selected.length > 1) {
                return 'Send Invites';
            } else {
                return 'Send Invite';
            }
        case 'reset_password':
            return 'Send Reset Email';
        case 'disable_user':
            return 'Confirm Disable';
        case 'enable_user':
            return 'Confirm Enable';
        case 'export_users':
            return 'Export Users';
        default:
            return 'Save';
    }
});

const handleSubmit = async () => {
    loading.value = true;
    try {
        const isBulkAction = userStore.selected.length > 1;
        let response;
        if (props.selectedAction === 'export_users') {
            response = await props.store.exportData({
                format: exportFormat.value,
                ids: props.store.selected.map((item) => item.id),
            });
            if (response?.success) {
                userStore.actionDialog = false;
            }
            return;
        }

        if (isBulkAction) {
            response = await userStore.handleBulkActions(props.selectedAction);
        } else {
            response = await handleSingleAction(props.selectedAction);
        }

        if (response?.success) {
            userStore.actionDialog = false;
            if (id) {
                await props.store.fetchDataById(id);
            } else {
                await props.store.fetchPaged();
            }
        } else {
            globalHelper.methods.showPopupError('Something went wrong! Please try again.', 'Error');
        }
    } catch (error) {
        globalHelper.methods.showPopupError('Something went wrong! Please try again.', 'Error');
    } finally {
        loading.value = false;
    }
};

const handleSingleAction = async (action) => {
    loading.value = true;
    try {
        const actionMap = {
            send_invite: () => userStore.onSendInvite(),
            reset_password: () => userStore.onResetPassword(),
            disable_user: () => userStore.onDisableUser(),
            enable_user: () => userStore.onEnableUser(),
        };
        const actionHandler = actionMap[action];
        if (!actionHandler) {
            console.warn(`Unknown action: ${action}`);
            return { success: false, error: 'Unknown action' };
        }
        return await actionHandler();
    } catch (e) {
    } finally {
        loading.value = false;
    }
};

onUnmounted(() => {
    userStore.actionDialog = false;
});
</script>
<style lang=""></style>
