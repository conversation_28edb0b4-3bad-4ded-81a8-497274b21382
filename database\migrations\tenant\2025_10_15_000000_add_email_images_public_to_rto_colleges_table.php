<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('rto_colleges', function (Blueprint $table) {
            $table->boolean('allow_public_images')->default(true)->after('dean_signature')->comment('Make the images in the email template publicly accessible without authentication.');
        });
    }

    public function down(): void
    {
        Schema::table('rto_colleges', function (Blueprint $table) {
            $table->dropColumn('allow_public_images');
        });
    }
};
