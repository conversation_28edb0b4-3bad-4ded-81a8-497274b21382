<template>
    <AsyncForm
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        :max-width="'400px'"
        position="center"
        :dialogTitle="'Add Intervention Strategy'"
        :store="store"
        @success="(res) => emit('success', res)"
        :submit-text="'Save'"
    >
        <div class="space-y-4 p-6">
            <div>
                <InterventionTypeSelect
                    name="intervention_type_id"
                    label="Intervention Type"
                    v-model="formData.intervention_type_id"
                    :validation-message="store.errors?.intervention_type_id"
                    :valid="!store.errors?.intervention_type_id"
                    :touched="false"
                    :indicaterequired="false"
                    :disabled="formData.intervention_type_id"
                />
            </div>
            <div>
                <FormInput
                    name="strategy"
                    label="Intervention Strategy"
                    v-model="formData.strategy"
                    :validation-message="store.errors?.strategy"
                    :valid="!store.errors?.strategy"
                    :touched="true"
                    :indicaterequired="true"
                />
            </div>
        </div>
    </AsyncForm>
</template>
<script setup>
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import { useInterventionStrategyStore } from '@spa/stores/modules/interventionstrategy/useInterventionStrategyStore.js';
import { storeToRefs } from 'pinia';
import InterventionTypeSelect from '@spa/modules/interventiontype/InterventionTypeSelect.vue';
import { watch } from 'vue';
const props = defineProps({
    store: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['success']);
// Uncomment these if needed:
// import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
// import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';

const store = props.store ? props.store : useInterventionStrategyStore();
const { formData, formDialog } = storeToRefs(store);

watch(
    () => props.open,
    (val) => {
        formDialog.value = val;
    }
);
</script>
