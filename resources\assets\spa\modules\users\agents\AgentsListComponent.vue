<template>
    <UsersListComponent :store="store" :columns="columns" :initFilters="initFilters" type="agent">
        <template #actions="{ row, position }">
            <GridActionMenuItem
                v-if="position === 'before-delete'"
                @click="openModal(ACTIONS.CHANGE_AGENT_CODE, row)"
                :item="{
                    icon: 'exchange',
                    label: 'Change Agent Code',
                    id: 'change_agent_code',
                }"
            />
            <GridActionMenuItem
                v-if="position === 'before-delete'"
                @click="openModal(ACTIONS.CHANGE_AGENT_STATUS, row)"
                :item="{
                    icon: 'status',
                    label: 'Change Agent Status',
                    id: 'change_agent_status',
                }"
            />
            <GridActionMenuItem
                v-if="position === 'before-delete'"
                @click="openStaffModal(row)"
                :item="{
                    icon: 'users',
                    label: 'View Agent Staffs',
                    id: 'view_agent_staffs',
                }"
            />
        </template>
    </UsersListComponent>
    <AgentActionForm :store="store" :action="currentAction" :selectedRow="selectedRow" />
    <AgentStaffsPopup :store="store" :selectedRow="selectedRow" />
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import UsersListComponent from '@spa/modules/users/UsersListComponent.vue';
import { storeToRefs } from 'pinia';
import { useAgentStore } from '@spa/stores/modules/agent/useAgentStore.js';
import GridActionMenuItem from '@spa/components/AsyncComponents/Grid/Partials/GridActionMenuItem.vue';
import AgentActionForm from '@spa/modules/users/agents/partials/AgentActionForm.vue';
import AgentStaffsListComponent from '@spa/modules/users/agents-staff/AgentStaffsListComponent.vue';
import AgentStaffsPopup from '@spa/modules/users/agents/partials/AgentStaffsPopup.vue';

const ACTIONS = {
    CHANGE_AGENT_CODE: 'change_agent_code',
    CHANGE_AGENT_STATUS: 'change_agent_status',
    VIEW_AGENT_STAFFS: 'view_agent_staffs',
};

const openDialog = ref(false);

const store = useAgentStore();
const columns = reactive([
    {
        field: 'name',
        name: 'name',
        title: 'Name',
        width: '256px',
        sortable: true,
        replace: true,
        locked: true,
    },
    {
        field: 'agent_code',
        name: 'agent_code',
        title: 'Agent Code',
        width: '100px',
        sortable: false,
    },
    {
        field: 'super_agent_id',
        name: 'super_agent',
        title: 'Super Agent',
        width: '150px',
        sortable: false,
        replace: true,
        filterable: true,
        customFilter: true,
        filterableConfig: {
            type: 'select',
            options: [],
        },
    },
    {
        field: 'email',
        name: 'email',
        title: 'Email',
        width: '232px',
    },
    {
        field: 'role_id',
        name: 'user_roles',
        title: 'Role(s)',
        width: '256px',
        replace: true,
        sortable: true,
        filterable: true,
        customFilter: true,
    },
    {
        field: 'status',
        name: 'status',
        title: 'Status',
        width: '150px',
        replace: true,
    },
    {
        field: 'user_status',
        name: 'user_status',
        title: 'User Status',
        width: '150px',
        replace: true,
        sortable: true,
        filterable: true,
        customFilter: true,
        filterableConfig: {
            type: 'select',
            options: [
                { value: 1, label: 'Active' },
                { value: 0, label: 'Inactive' },
                { value: 2, label: 'Pending' },
                { value: 3, label: 'Disabled' },
            ],
        },
    },
    {
        field: 'last_login',
        name: 'last_login',
        title: 'Last Login',
        width: '200px',
        replace: true,
    },
    {
        field: 'created_by',
        name: 'created_by',
        title: 'Created By',
        width: '200px',
        replace: true,
    },
]);
const currentAction = ref(null);
const selectedRow = ref(null);

const getSuperAgents = async () => {
    try {
        const response = await $http.get('api/v2/tenant/admin-agent/get-super-agents');
        columns[2].filterableConfig.options = response.data;
    } catch (error) {
        console.log(error);
    }
};

const openModal = (action, row = null) => {
    if (!action) {
        return;
    }
    currentAction.value = action;
    selectedRow.value = row;
    store.actionDialog = true;
};

const openStaffModal = (row = null) => {
    store.openStaffDialog = true;
    selectedRow.value = row;
};

const initFilters = () => {
    store.filters = {};
};

onMounted(() => {
    initFilters();
    getSuperAgents();
});
</script>
