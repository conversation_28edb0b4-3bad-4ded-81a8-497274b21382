export const getInitialName = (name) => {
    if (!name || typeof name !== 'string') {
        return '';
    }

    const trimmedName = name.trim();
    if (!trimmedName) {
        return '';
    }

    const names = trimmedName
        .toUpperCase()
        .split(' ')
        .filter((n) => n.length > 0);

    if (!names.length) {
        return '';
    }

    return names.map((n) => n[0]).join('');
};
