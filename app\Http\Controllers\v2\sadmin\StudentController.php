<?php

namespace App\Http\Controllers\v2\sadmin;

use App;
use App\Exports\ManageStudentScoutExport;
use App\Http\Controllers\Controller;
use App\Model\CourseType;
use App\Model\PdfTemplate;
use App\Model\v2\Agent;
use App\Model\v2\Roles;
use App\Model\v2\SmsTemplate;
use App\Model\v2\Student;
use App\Model\v2\StudentCourses;
use App\Repositories\StudentProfileRepository;
use App\Services\StudentProfileCommonService;
use App\Services\StudentSummaryTabService;
use App\Services\TemplateService;
use App\Traits\CommonTrait;
use Domains\Moodle\Traits\MoodleStatusTrait;
use Domains\Students\RiskAssessment\Models\StudentRiskAssessment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Integrations\Zoho\Traits\ZohoTrait;
use Maatwebsite\Excel\Facades\Excel;
use SSO\Traits\SSOStatusTrait;
use Support\Services\UploadService;

class StudentController extends Controller
{
    use CommonTrait;
    use MoodleStatusTrait;
    use SSOStatusTrait;
    use ZohoTrait;

    protected $student;

    protected $newLimit;

    protected $studentCourse;

    protected $smsTemplate;

    protected $templateService;

    protected $studentProfileCommonService;

    public function __construct(
        Student $student,
        StudentCourses $studentCourse,
        SmsTemplate $smsTemplate,
        TemplateService $templateService,
        StudentSummaryTabService $studentSummaryTabService,
        StudentProfileCommonService $studentProfileCommonService,
    ) {
        parent::__construct();
        $this->newLimit = 5;
        $this->student = new StudentProfileRepository($student);
        $this->studentCourse = new StudentProfileRepository($studentCourse);
        $this->smsTemplate = $smsTemplate;
        $this->templateService = $templateService;
        $this->studentSummaryTabService = $studentSummaryTabService;
        $this->studentProfileCommonService = $studentProfileCommonService;
    }

    public function index()
    {
        $data['title'] = 'Students';
        $data['keywords'] = 'Students, Search';
        $data['description'] = 'Students Search';
        $data['api_token'] = $this->loginUser->api_token;
        $data['mainmenu'] = 'clients';
        $data['column_arr'] = [
            ['id' => 'student_id',  'title' => 'Student ID', 'default' => 'checked'],
            ['id' => 'course_list', 'title' => 'Course',    'default' => 'checked'],
            ['id' => 'campus',      'title' => 'Campus',    'default' => 'checked'],
            ['id' => 'attendance',  'title' => 'Attendance', 'default' => 'checked'],
            ['id' => 'payment',     'title' => 'Payment',   'default' => 'checked'],
            ['id' => 'student_name', 'title' => 'Full Name', 'default' => 'checked'],
            ['id' => 'DOB',         'title' => 'DOB',       'default' => ''],
        ];
        if (isset($_COOKIE['studentListSelectedFields'])) {
            $selectedFields = json_decode($_COOKIE['studentListSelectedFields'], true);
            foreach ($data['column_arr'] as &$column) {
                // Find the corresponding field in selectedFields
                $foundField = array_filter($selectedFields, function ($field) use ($column) {
                    return isset($field[$column['id']]);
                });
                // If the field exists, set its default value accordingly
                if (! empty($foundField)) {
                    $column['default'] = ($foundField[key($foundField)][$column['id']] === 'true') ? 'checked' : '';
                }
            }
        }

        return view('v2.sadmin.student.index', $data);
    }

    public function scout()
    {
        $data['title'] = 'Students';
        $data['keywords'] = 'Students, Search';
        $data['description'] = 'Students Search';
        $data['api_token'] = $this->loginUser->api_token;
        $data['mainmenu'] = 'clients';
        $data['column_arr'] = [
            ['id' => 'student_id', 'value' => 'generated_stud_id',  'title' => 'Student ID', 'default' => 'checked'],
            ['id' => 'student_name', 'value' => 'first_name', 'title' => 'Full Name', 'default' => 'checked'],
            ['id' => 'DOB', 'value' => 'DOB', 'title' => 'DOB',       'default' => ''],
            ['id' => 'campus', 'value' => 'campus', 'title' => 'Campus',    'default' => 'checked'],
            ['id' => 'student_type', 'value' => 'student_type', 'title' => 'Student Type', 'default' => 'checked'],
            ['id' => 'course_list', 'value' => 'course_list', 'title' => 'Course',    'default' => 'checked'],
            ['id' => 'course_progress', 'value' => 'course_progress', 'title' => 'Course Progress', 'default' => 'checked'],
            ['id' => 'attendance', 'value' => 'attendance',  'title' => 'Attendance', 'default' => 'checked'],
            ['id' => 'payment', 'value' => 'payment', 'title' => 'Payment',   'default' => 'checked'],
        ];
        $data['isMoodleConnect'] = $this->isMoodleConnected();

        $jsonArray = [];

        foreach ($data['column_arr'] as $item) {
            $jsonItem = [
                'text' => $item['title'],
                'textMain' => $item['value'],
                'value' => ($item['default'] === 'checked') ? 'true' : 'false',
            ];
            $jsonArray[] = $jsonItem;
        }
        setcookie('studentScoutListSelectedExportFields', json_encode($jsonArray));
        if (isset($_COOKIE['studentScoutListSelectedFields'])) {
            $selectedFields = json_decode($_COOKIE['studentScoutListSelectedFields'], true);

            foreach ($data['column_arr'] as &$column) {
                // Find the corresponding field in selectedFields
                $foundField = array_filter($selectedFields, function ($field) use ($column) {
                    return isset($field[$column['id']]);
                });
                // If the field exists, set its default value accordingly
                if (! empty($foundField)) {
                    $column['default'] = ($foundField[key($foundField)][$column['id']] === 'true') ? 'checked' : '';
                }
            }
        }

        return view('v2.sadmin.student.index_scout', $data);
    }

    public function getExportStudentScoutData(Request $request)
    {
        return Excel::download(new ManageStudentScoutExport($request), 'Offer_List.xlsx');
    }

    public function viewProfile(Request $request, $id)
    {
        $selectedStudentCourseId = request()->query('courseId') ?? '';
        $selectedTab = request()->query('activetab') ?? 'summary';
        if (class_exists(\Barryvdh\Debugbar\Facades\Debugbar::class) && config('app.debug')) {
            \Barryvdh\Debugbar\Facades\Debugbar::disable();
        }
        try {
            // Attempt to decrypt the ID
            $id = decryptIt($id);
            $userInfo = Auth::user();
            $collegeId = $userInfo->college_id;
            $arrRoleType = Config::get('constants.arrRoleType');
            $studentDetails = $this->student->getStudentDetail($id, $arrRoleType);
            if (count($studentDetails) == 0) {
                return redirect(route('search-student'));
            }
            // $studentDetails= $this->student->studentDetails($id);
            $studentCourses = $this->studentCourse->getStudentProfileCourses($id, $collegeId);
            $recentDocuments = $this->student->getRecentDocuments($id, $collegeId);
            $invoice_number = $this->student->getInvoiceNumber();
            $receipt_number = $this->student->getReceiptNumber();

            /*$isHigherEd = false;
            if(count($studentCourses) > 0){
                $courseTypeId = $studentCourses[0]->course_type_id;
                $isHigherEd = (new CourseType)->checkHigherEdGradingType($courseTypeId);
            }*/

            foreach ($recentDocuments as $key => $val) {
                $fileUrl = UploadService::preview($val['file_path'].$val['folder_or_file']);
                $recentDocuments[$key]['file_type'] = substr($val['original_name'], strpos($val['original_name'], '.') + 1);
                // $recentDocuments[$key]['file_url'] = asset($val['file_path'] . $val['folder_or_file']);
                $recentDocuments[$key]['file_url'] = $fileUrl;
                $recentDocuments[$key]['file_preview_path'] = $fileUrl;
            }
            // dd($recentDocuments);
            $studentTasks = $this->student->getStudentTask($id, $collegeId);
            $arrCourseStatus = Config::get('constants.arrCourseStatus');
            $i = 0;
            foreach ($arrCourseStatus as $key => $val) {
                $courseStatus[$i]['Id'] = $val;
                $courseStatus[$i]['Name'] = $val;
                $i++;
            }

            $agentId = null;
            if ($studentCourses && ! empty($studentCourses[0])) {
                $agentId = $studentCourses[0]->agent_id;
            }

            $courseId = null;
            if ($selectedStudentCourseId) {
                $studCourse = StudentCourses::find($selectedStudentCourseId);
                $agentId = $studCourse?->agent_id ?? $agentId;
                $courseId = $studCourse?->course_id ?? null;
            } else {
                $courseId = $studentCourses[0]->course_id ?? null;
            }
            $agent = $agentId ? Agent::find($agentId) : null;

            $student = Student::find($id);
            // $agentId = $studentDetails[0]->agent_id;
            // $agent = Agent::find($agentId);

            $currentRisk = '-';
            $currentRiskColor = '';
            $studentLog = StudentRiskAssessment::where('student_id', $id)->first();
            if ($studentLog) {

                $currentRisk = ($studentLog->risk_level) ? config('riskassessment.risk_types')[$studentLog->risk_level] : '-';
                $currentRiskColor = ($currentRisk) ? config('riskassessment.arrPriorityLevelColor')[$studentLog->risk_level] : '';
            }

            $data['arrCourseStatus'] = $courseStatus;
            $data['title'] = 'Students Profile';
            $data['keywords'] = 'Students, Search';
            $data['description'] = 'Students Search';
            $data['studentDetails'] = $studentDetails;
            $data['studentCourses'] = $studentCourses;
            $data['recentDocuments'] = $recentDocuments;
            $data['currentRisk'] = $currentRisk;
            $data['currentRiskColor'] = $currentRiskColor;
            $data['studentTasks'] = $studentTasks;
            $data['studentId'] = $id;
            $data['agentId'] = $agentId;
            $data['courseId'] = $courseId;
            $data['agency_name'] = $agent ? $agent->agency_name : '-';
            $data['arrStudentOrigin'] = kendify(Config::get('constants.arrStudentOrigin'), 'Id', 'Name');
            $data['arrCourseCancelReason'] = kendify(Config::get('constants.arrCourseCancelReason'), 'Id', 'Name');
            $studentRoleType = Roles::TYPE_STUDENT;
            $data['studentIsUser'] = Student::alias('rto_students as rs')
                ->join('rto_users as ru', function ($join) use ($studentRoleType) {
                    $join->on('ru.username', '=', 'rs.generated_stud_id');
                    $join->on('ru.role_id', '=', DB::raw($studentRoleType));
                })->where('rs.id', $id)->count();
            // $data['isHigherEd'] = $isHigherEd;
            $data['invoice_number'] = $invoice_number;
            $data['receipt_number'] = $receipt_number;
            $data['collegeId'] = $collegeId;
            $data['userId'] = $userInfo->id;
            $data['api_token'] = $this->loginUser->api_token;
            $data['mainmenu'] = 'clients';
            $data['headerData'] = $this->getHeaderData($id);

            $data['applicationurl'] = getStudentApplicationUrl().Auth::user()->id;
            $data['currentTab'] = ($selectedTab == '') ? 'summary' : $selectedTab;
            $isPjax = $request->header('X-PJAX') ? true : false;
            if ($isPjax) {
                return view('v2.sadmin.student.profile-tab-html', $data);
            }

            return view('v2.sadmin.student.view-profile', $data);
        } catch (\Exception $e) {
            dd($e);
            Log::info('Student Profile is not open due to some error.', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('search-student-scout');
        }
    }

    public function agentStudents($Id)
    {
        $agentName = Agent::where('id', $Id)->select('agency_name')->get()->first();
        $data['title'] = 'Students';
        $data['keywords'] = 'Students, Search';
        $data['description'] = 'Students Search';
        $data['agentId'] = $Id;
        $data['agentName'] = $agentName;
        $data['api_token'] = $this->loginUser->api_token;
        $data['mainmenu'] = 'clients';

        $data['column_arr'] = [
            ['id' => 'student_id',  'title' => 'Student ID', 'default' => 'checked'],
            ['id' => 'course_list', 'title' => 'Course',    'default' => 'checked'],
            ['id' => 'campus',      'title' => 'Campus',    'default' => 'checked'],
            ['id' => 'student_name', 'title' => 'Full Name', 'default' => 'checked'],
            ['id' => 'application_id', 'title' => 'Application Id', 'default' => 'checked'],
        ];

        return view('v2.sadmin.student.agentstudents', $data);
    }

    public function studentAction($action = '', $id = 0)
    {
        if ($id > 0 && ! empty($action)) {
            $data['title'] = $action;
            $data['keywords'] = "Student, $action";
            $data['description'] = "Student ($action)";
            $data['api_token'] = $this->loginUser->api_token;
            $data['mainmenu'] = 'clients';

            return view('v2.sadmin.student.action', $data);
        } else {
            return redirect(route('search-student'));
        }
    }

    public function failedAttendanceJobs()
    {
        $data['title'] = 'Failed Attendance Jobs';
        $data['keywords'] = 'Failed Attendance Jobs';
        $data['description'] = 'Failed Attendance Jobs';
        $data['mainmenu'] = 'clients';

        return view('v2.sadmin.student.attendance-failed-jobs', $data);
    }

    public function getCacheData($key = 'studentList')
    {
        $tenantId = tenant('id');
        $cacheKeySuffixText = Config::get('constants.cacheKeySuffix.'.$key);
        if ($cacheKeySuffixText) {
            $cacheKey = $tenantId.$cacheKeySuffixText;
            $checkStudentCache = Cache::get($cacheKey);

            if ($checkStudentCache === null || empty(json_decode($checkStudentCache))) {
                echo 'Sorry, No cache data found for tenant : <b>'.strtoupper($tenantId).'</b>';
            } else {
                $res = json_decode($checkStudentCache, true);
                echo 'Cache data found successfully for tenant : <b>'.strtoupper($tenantId).'</b>';

                dump($res);
                if (isset($res['data'])) {
                    echo '<hr/><pre>';
                    print_r($res['data'][0]);
                }
            }
        } else {
            echo 'Sorry, invalid key pass from URL';
        }
        exit;
    }

    public function offerLetterPreview($courseId, $studentId, $studentCourseID, Request $request)
    {

        $collegeId = Auth::user()->college_id;

        if (isset($studentId)) {
            $objRtoPdfTemplate = new PdfTemplate;
            $arrPdfTemplateContent = $objRtoPdfTemplate->getPdfContent(1);
            $content = $arrPdfTemplateContent[0]->pdf_template;
            $offerLetterContent = $objRtoPdfTemplate->setPdfTemplateBodyContentNew($collegeId, $studentId, $content, $type = 'preview', $courseId, $studentCourseID);

            if (isset($offerLetterContent['status']) && $offerLetterContent['status'] == 'error') {
                $request->session()->flash('session_error', $offerLetterContent['msg']);

                return redirect(route('offer-manage'));
            }
            $pdf = App::make('dompdf.wrapper');

            $offerLetterContent = cleanNonUTF($offerLetterContent);
            $pdf->loadHTML($offerLetterContent);

            return $pdf->stream();
            exit;
            // return $offerLetterContent;
        }
    }

    private function getHeaderData($studentId)
    {
        $smsTemplateData = $this->smsTemplate->getSmsTemplateList();
        $smsTemplateDataArr = (count($smsTemplateData) > 0) ? $smsTemplateData : [['id' => '', 'name' => 'No template found']];

        $studentId = $studentId;
        $flag = true;
        $courseDetail = $this->studentProfileCommonService->getStudentCoursesData($studentId, $flag);

        $data['headerDropDownCourseData'] = $courseDetail;
        $data['smsTemplateData'] = $smsTemplateDataArr;
        $data['mailTemplateData']['data'] = $this->templateService->getEmailTemplateList();
        $data['letterTemplateData']['data'] = $this->templateService->getLetterTemplateList();

        return $data;
    }
}
