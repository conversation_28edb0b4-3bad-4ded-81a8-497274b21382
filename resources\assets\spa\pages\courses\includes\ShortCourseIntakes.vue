<template>
    <div class="space-y-6">
        <div class="space-y-4 rounded-md bg-white" :class="isProfile ? 'p-6' : 'p-0'">
            <FormSectionTitle :isProfile="isProfile" :text="'Intake Details'" :info="true" />
            <courseinfo :coursedata="course" :courseClass="'mb-4'" />
            <div class="flex items-center justify-between">
                <div class="grid w-3/4 grid-cols-6 items-center gap-2">
                    <DropDownList
                        :data-items="intakeTypes"
                        :text-field="'label'"
                        :data-item-key="'value'"
                        :valueField="'value'"
                        :valuePrimitive="true"
                        v-model="filters.type"
                        :value="filters.type"
                        :default-item="{ value: 'all', label: 'All Types' }"
                        :popup-settings="popupSettings"
                        :value-render="'valueRender'"
                    >
                        <template v-slot:valueRender="{ props }">
                            <span :class="props.class" :id="props.id">
                                <span
                                    :class="props.value.label ? 'text-gray-700' : 'text-gray-500'"
                                >
                                    {{ props.value.label ? props.value.label : 'Select Batch' }}
                                </span>
                            </span>
                        </template>
                    </DropDownList>
                    <DropDownList
                        :data-items="intakeStatus"
                        :text-field="'label'"
                        :data-item-key="'value'"
                        :valueField="'value'"
                        :valuePrimitive="true"
                        v-model="filters.status"
                        :value="filters.status"
                        :default-item="{ value: 'all', label: 'All Status' }"
                        :popup-settings="popupSettings"
                        :value-render="'valueRender'"
                    >
                        <template v-slot:valueRender="{ props }">
                            <span :class="props.class" :id="props.id">
                                <span
                                    :class="props.value.label ? 'text-gray-700' : 'text-gray-500'"
                                >
                                    {{ props.value.label ? props.value.label : 'Select Batch' }}
                                </span>
                            </span>
                        </template>
                    </DropDownList>
                    <div class="relative w-96 basis-auto pr-2">
                        <span class="absolute left-3 top-1/2 -translate-y-1/2">
                            <icon :name="'lens'" width="16" height="16" />
                        </span>
                        <input
                            type="text"
                            id="offscreen"
                            v-debounce="300"
                            v-model="filters.search"
                            class="tw-input-text pl-8 font-normal"
                            placeholder="Search Student"
                            @input="updateGridFilterText"
                        />
                    </div>
                </div>
                <Button
                    :variant="'primary'"
                    :type="'button'"
                    :size="'base'"
                    @click="handleAddIntake"
                >
                    <span>Add Intake</span>
                </Button>
            </div>
            <div class="flex items-center justify-between gap-2">
                <div v-if="selectedIntakesCount > 0" class="flex items-center gap-4">
                    <div class="mr-5">{{ selectedIntakesCount }} Intake(s) Selected</div>
                    <button
                        class="flex cursor-pointer items-center gap-1 text-sm text-gray-500"
                        @click="handleStatusChange('active')"
                    >
                        <icon name="checkcirc" width="16" height="16" />
                        Activate
                    </button>
                    <button
                        class="flex cursor-pointer items-center gap-1 text-sm text-gray-500"
                        @click="handleStatusChange('inactive')"
                    >
                        <icon name="cross" width="16" height="16" />
                        Deactivate
                    </button>
                    <button
                        class="flex cursor-pointer items-center gap-1 text-sm text-gray-500"
                        @click="handleStatusChange('delete')"
                    >
                        <icon name="delete" width="16" height="16" />
                        Delete
                    </button>
                </div>
                <div v-else>Select Intake(s) to perform actions on bulk</div>
            </div>
            <br />
            <grid
                :data="gridData"
                :columns="columns"
                :hasSelect="true"
                :pagination="getPagination"
                @edit="handleEdit"
                @delete="handleDelete"
                @info="showIntakeDetailWindow"
                @changepage="changePageHandler"
            />
            <ShortCourseIntakesForm
                :visible="showIntakeForm"
                :modelTitle="modelTitle"
                :intakeData="getIntakeData"
                :filters="filters"
                @cancel="handleCancel"
                @saved="handleSaved"
            />
            <ShortCourseIntakesDetail
                :visible="showIntakeDetail"
                :course="course"
                :intakeData="getIntakeData"
                @edit="handleEdit(this.intakeData)"
                @cancel="closeIntakeDetailWindow"
            />
        </div>
    </div>
    <div class="courses-navigation-buttons absolute bottom-0 left-0 right-0 z-20">
        <div class="flex w-full justify-between border-t border-gray-100 bg-white px-8 py-3 shadow">
            <div>
                <button type="button" class="tw-btn-secondary cursor-pointer px-6" @click="back">
                    <span class="text-sm font-medium leading-6 text-gray-700">{{
                        saving ? 'Cancel' : 'Back'
                    }}</span>
                </button>
            </div>
            <div @click="next" class="tw-btn-primary cursor-pointer px-6">
                <div class="flex items-center space-x-2">
                    <icon
                        :name="'loading'"
                        :width="16"
                        :height="16"
                        :fill="'#1890FF'"
                        :stroke="'#E2E8F0'"
                        v-if="saving"
                    />
                    <span class="text-sm font-medium leading-6 text-white">Next</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { router } from '@inertiajs/vue3';
import { useForm } from '@inertiajs/vue3';
import axios from 'axios';
import { debounce } from 'lodash';
import SelectedCourseInfo from './SelectedCourseInfo.vue';
import { Form } from '@progress/kendo-vue-form';
import { mapState } from 'pinia';
import { useCoursesStore } from '@spa/stores/modules/courses';
import { courseIntakes, prepareIntakeFormData } from '@spa/services/courseFormResource';
import FormContent from '@spa/pages/courses/includes/forms/CourseIntakeFormContent.vue';
import FormSectionTitle from '@spa/pages/courses/commons/FormSectionTitle.vue';
import DynamicGrid from '@spa/modules/common/DynamicGrid.vue';
import Button from '@spa/components/Buttons/Button';
import ShortCourseIntakesForm from '@spa/pages/courses/includes/ShortCourseIntakesForm.vue';
import ShortCourseIntakesDetail from '@spa/pages/courses/includes/ShortCourseIntakesDetail.vue';
import MenuButton from '@spa/components/Buttons/MenuButton.vue';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import useConfirm from '@spa/services/useConfirm';
import { useLoaderStore } from '@spa/stores/modules/global-loader';

export default {
    setup(props) {
        const confirm = useConfirm();
        const loaderStore = useLoaderStore();

        const confirmDeletion = (count) => {
            return new Promise((resolve) => {
                confirm.require({
                    message: `Do you want to delete ${count} intake(s) selected?`,
                    header: 'Confirmation',
                    icon: 'pi pi-exclamation-triangle',
                    accept: async () => {
                        resolve(true);
                    },
                    reject: () => {
                        resolve(false);
                    },
                    onHide: () => {
                        resolve(false);
                    },
                });
            });
        };
        return {
            loaderStore,
            confirmDeletion,
        };
    },
    props: {},
    mounted() {
        this.fetchIntakes();
    },
    data: function () {
        return {
            cancelSource: axios.CancelToken.source(),
            showIntakeForm: false,
            showIntakeDetail: false,
            intakeData: {},
            filters: {
                type: null,
                status: null,
                search: null,
                page: 1,
                take: 15,
            },
            intakeTypes: [
                { value: 'all', label: 'All Types' },
                { value: 'current', label: 'Currently Running Intakes' },
                { value: 'expired', label: 'Expired Intakes' },
                { value: 'upcoming', label: 'Upcoming Intakes' },
            ],
            intakeStatus: [
                { value: 'all', label: 'All Status' },
                { value: 'active', label: 'Active' },
                { value: 'inactive', label: 'Inactive' },
            ],
            columns: [
                {
                    field: 'intake_name',
                    title: 'Intake Name',
                    visible: true,
                    sortable: true,
                    cell: 'defaultCell',
                    minResizableWidth: 250,
                },
                {
                    field: 'intake_start_end',
                    title: 'Intake Start & End',
                    visible: true,
                    sortable: false,
                    cell: 'startEndDateCell',
                    minResizableWidth: 250,
                },
                {
                    field: 'class_start_end_date',
                    title: 'Class Stated & End',
                    visible: true,
                    sortable: false,
                    cell: 'startEndDateCell',
                    minResizableWidth: 250,
                },
                {
                    field: 'status',
                    title: 'Intake Status',
                    visible: true,
                    sortable: false,
                    cell: 'badgeCell',
                    minResizableWidth: 100,
                },
                {
                    field: 'action_buttons',
                    title: 'Action',
                    visible: true,
                    sortable: false,
                    cell: 'actionCell',
                    minResizableWidth: 100,
                },
            ],
            popupSettings: {
                className: 'tw-width-auto',
                animate: false,
            },
            filterIntakes: debounce(function (newVal) {
                this.fetchIntakes();
            }, 300),
        };
    },
    components: {
        'k-form': Form,
        formcontent: FormContent,
        courseinfo: SelectedCourseInfo,
        FormSectionTitle,
        grid: DynamicGrid,
        Button,
        ShortCourseIntakesForm,
        ShortCourseIntakesDetail,
        MenuButton,
        DropDownList,
    },
    computed: {
        ...mapState(useCoursesStore, [
            'currentposition',
            'setCurrentStep',
            'totalUpdateTabs',
            'course',
            'fees',
            'updateCourse',
            'getIntakes',
            'intakesMeta',
            'setIntakes',
            'getPagination',
        ]),
        getCourseData: function () {
            //format the data to provide it to the form
            return this.courseGeneral(this.course);
        },
        isProfile() {
            return this.operationMode === 'profile';
        },
        gridData() {
            return this.getIntakes;
        },
        modelTitle() {
            return this.isProfile ? 'Edit Intake' : 'Add Intake';
        },
        getIntakeData() {
            return this.prepareIntakeFormData(this.course, this.fees, this.intakeData);
        },
        selectedIntakes() {
            return this.gridData.filter((item) => item.selected);
        },
        selectedIntakesCount() {
            return this.selectedIntakes.length;
        },
    },
    methods: {
        courseIntakes,
        prepareIntakeFormData,
        handleCancel() {
            if (!this.showIntakeDetail) {
                this.intakeData = {};
            }
            this.showIntakeForm = false;
        },
        handleSaved() {
            this.intakeData = {};
            this.showIntakeForm = false;
            this.showIntakeDetail = false;
        },
        handleAddIntake() {
            this.showIntakeForm = true;
        },
        handleEdit(item) {
            this.showIntakeForm = true;
            this.intakeData = item;
        },
        back: function () {
            const newPosition = this.currentposition > 0 ? this.currentposition - 1 : 0;
            this.setCurrentStep(newPosition);
            return false;
        },
        next: function () {
            const newPosition =
                this.currentposition < this.totalUpdateTabs
                    ? this.currentposition + 1
                    : this.currentposition;

            //the step is completed
            this.setCurrentStep(newPosition);
            return false;
        },
        fetchIntakes() {
            if (this.loading) return;
            this.loading = true;
            this.loaderStore.startContextLoading('grid-loader');
            $http
                .get(
                    route('spa.courses.getshortcourseintakes', { code: this.course.course_code }),
                    {
                        params: this.filters,
                    }
                )
                .then((response) => {
                    this.loading = false;
                    this.setIntakes(response.intakes, response.meta);
                    this.filters.page = response.meta.current_page;
                    this.filters.take = response.meta.per_page;
                    this.loaderStore.stopContextLoading('grid-loader');
                });
        },
        async handleStatusChange(status) {
            if (status == 'delete') {
                if (await this.confirmDeletion(this.selectedIntakesCount)) {
                    this.updateStatus('delete');
                }
            } else {
                this.updateStatus(status);
            }
        },
        updateStatus(status, intake = null) {
            this.loading = true;
            this.loaderStore.startContextLoading('grid-loader');
            $http
                .post(
                    route('spa.courses.intakesactions', {
                        code: this.course.course_code,
                        action: status,
                    }),
                    {
                        intakes: intake ? [intake.id] : this.selectedIntakes.map((item) => item.id),
                        filters: this.filters,
                    }
                )
                .then((response) => {
                    this.loading = false;
                    this.setIntakes(response.intakes, response.meta);
                    this.loaderStore.stopContextLoading('grid-loader');
                });
        },
        changePageHandler(page, take) {
            this.filters.page = page;
            this.filters.take = take;
        },
        async handleDelete(intake) {
            if (await this.confirmDeletion(1)) {
                this.updateStatus('delete', intake);
            }
        },
        showIntakeDetailWindow(intake) {
            this.intakeData = intake;
            this.showIntakeDetail = true;
        },
        closeIntakeDetailWindow() {
            this.intakeData = {};
            this.showIntakeDetail = false;
        },
    },
    watch: {
        filters: {
            handler(newVal) {
                this.filterIntakes(newVal);
            },
            deep: true,
        },
    },
};
</script>
