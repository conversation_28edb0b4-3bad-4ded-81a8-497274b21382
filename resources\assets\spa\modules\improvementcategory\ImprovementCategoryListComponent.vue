<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="true"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :actions="['delete', 'edit']"
    >
    </AsyncGrid>
    <ImprovementCategoryForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useImprovementCategoryStore } from '@spa/stores/modules/improvementcategory/useImprovementCategoryStore.js';
import ImprovementCategoryForm from '@spa/modules/improvementcategory/ImprovementCategoryForm.vue';

const store = useImprovementCategoryStore();

const columns = [
    {
        field: 'name',
        title: 'Name',
    },
    // Add more columns as needed
];

const initFilters = () => {
    store.filters = {};
};

onMounted(() => {
    initFilters();
});
</script>
