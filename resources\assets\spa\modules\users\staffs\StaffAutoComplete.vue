<script setup>
import { useAgentStore } from '@spa/stores/modules/agent/useAgentStore.js';
import { computed } from 'vue';
import AsyncAutoComplete from '@spa/components/AsyncComponents/Select/AsyncAutoComplete.vue';
import { useStaffStore } from '@spa/stores/modules/staff/useStaffStore.js';

const props = defineProps({
    modelValue: [String, Number, Array, Object],
    label: String,
    className: String,
    optionValue: {
        type: String,
        default: 'id',
    },
    optionLabel: {
        type: String,
        default: 'name',
    },
    disabled: Boolean,
    clearable: {
        type: Boolean,
        default: true,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    readonly: Boolean,
    useChips: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: 'Switch staff profile',
    },
});

const emit = defineEmits(['update:modelValue']);
const vModel = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});
const store = useStaffStore();
</script>

<template>
    <AsyncAutoComplete
        :label="label"
        :className="className"
        :optionValue="optionValue"
        :optionLabel="optionLabel"
        :disabled="disabled"
        :store="store"
        v-model="vModel"
        :clearable="clearable"
        :multiple="multiple"
        :readonly="readonly"
        :useChips="useChips"
        :placeholder="placeholder"
        :style="{
            width: '250px',
        }"
        @select="$emit('select', $event)"
    />
</template>

<style scoped></style>
