<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Spatie\Permission\Exceptions\UnauthorizedException;
// use Illuminate\Validation\UnauthorizedException;
use Support\Auth\Access;
use Support\Auth\UserGroup;
use Symfony\Component\HttpFoundation\Response;

class CurrentRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, $permissions, $guard = null): Response
    {
        if (! $request->user()) {
            throw UnauthorizedException::notLoggedIn();
        }

        if (! galaxy_feature('users_beta')) {
            return $next($request);
        }

        if ($request->user()->isSadmin()) {
            return $next($request);
        }

        /* TODO: get current role from user */
        // $role = UserGroup::where('id', $request->session()->get(\Support\Auth\UserGroup::SESSION_KEY))->first();
        $role = $request->user()->currentActiveRole();
        // dd($role);
        if (! $role) {
            throw new UnauthorizedException(403, 'User does not have the right permissions.');
        }

        $permissions = explode('|', $permissions);

        try {

            if (! $role->hasAnyDirectPermission($permissions)) {
                throw new UnauthorizedException(403, 'User does not have the right permissions.');
            }

            return $next($request);
        } catch (\Exception $e) {
            info('current role check failed', [$e->getMessage()]);
            throw new UnauthorizedException(403, 'User does not have the right permissions.');
        }

    }

    public static function Can($permissions = [], $guard = null)
    {
        if (! is_array($permissions)) {
            $permissions = [$permissions];
        }

        return 'current_role:'.collect($permissions)->map(fn (Access $permission) => $permission->value)->join('|');
    }
}
