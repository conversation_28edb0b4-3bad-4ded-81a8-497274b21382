<template>
    <FormWrapper
        :visibleDialog="store.actionDialog"
        :hideOnOverlayClick="false"
        :fixedActionBar="true"
        :width="'50%'"
        :maxWidth="'576px'"
        :style="{
            maxWidth: '576px',
        }"
        :primaryBtnLabel="'Add'"
        :secondaryBtnLabel="'Cancel'"
        :isDisabled="loading"
        :isSubmitting="loading"
        @drawerclose="store.actionDialog = false"
        @drawersaved="handleSubmit"
        :position="'center'"
        :pt="{ content: 'px-5 pt-4 pb-8 space-y-4' }"
    >
        <template #title>
            <div class="text-xl font-medium">Add New Category</div>
        </template>
        <template #content>
            <FormInput
                name="category_name"
                label="Category Name"
                v-model="newCategory"
                :touched="true"
                :indicaterequired="true"
            />
        </template>
    </FormWrapper>
</template>
<script setup>
import { ref, watch, computed, onUnmounted } from 'vue';
import FormWrapper from '@spa/components/KendoModals/SidebarDrawer.vue';
import { uuid } from '@spa/helpers/index.js';
import SendInviteFormContent from '@spa/modules/users/partials/SendInviteFormContent.vue';
import ResetPasswordFormContent from '@spa/modules/users/partials/ResetPasswordFormContent.vue';
import DisableUserFormContent from '@spa/modules/users/partials/DisableUserFormContent.vue';
import ExportUsersFormContent from '@spa/modules/users/partials/ExportUsersFormContent.vue';
import { useUsersStore } from '@spa/stores/modules/users/useUsersStore.js';
import EnableUserFormContent from '@spa/modules/users/partials/EnableUserFormContent.vue';
import globalHelper from '@spa/plugins/global-helper.js';
import { Form } from '@progress/kendo-vue-form';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import { storeToRefs } from 'pinia';
import { useRegisterImprovementStore } from '@spa/stores/modules/continuous-improvement/registerImprovementStore.js';

const store = useRegisterImprovementStore();
const { categories } = storeToRefs(store);

const newCategory = ref(null);

const handleSubmit = async () => {
    categories.value.push({
        text: newCategory.value,
        value: newCategory.value,
    });
    store.formData.category = newCategory.value;
    store.actionDialog = false;
    newCategory.value = null;
};
</script>
<style lang=""></style>
