<template>
    <Layout :noSpacing="true" :loading="loading">
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Attendance Import" :back="false" />
        </template>
        <div class="space-y-6 px-8 py-6 max-md:px-4">
            <HeaderTab
                :sortDirection="sortDirection"
                :refreshing="refreshing"
                :autoRefreshEnabled="autoRefreshEnabled"
                @open-file-upload="openFileUpload"
                @toggle-sort="toggleSortDirection"
                @refresh="refreshData"
                @filter="handleFilter"
                @toggle-auto-refresh="toggleAutoRefresh"
            />

            <AttendanceImportGrid
                :data="resource.state.imports.data || []"
                :pagination="paginationData"
                :sort="[{ field: resource.state.sortBy, dir: sortDirection }]"
                :selectedIds="resource.state.selectedIds"
                :selectAll="resource.state.selectAll"
                @sort="handleSort"
                @changepage="handlePageChange"
                @changepagesize="handlePageSizeChange"
                @view-details="viewDetails"
                @resync="resyncImport"
                @delete="confirmDelete"
                @edit="editRecord"
                @view-error="handleViewError"
                @toggle-select-all="resource.toggleSelectAll"
                @toggle-select-item="resource.toggleSelectItem"
                @selectionchange="handleSelectionChange"
                @headerselectionchange="handleHeaderSelectionChange"
            />

            <input
                type="file"
                ref="fileInput"
                @change="handleFileUpload"
                accept=".csv"
                class="hidden"
            />
        </div>

        <BulkActions
            v-if="resource.getSelectedCount.value > 0"
            :selectedCount="resource.getSelectedCount.value"
            :loading="resource.state.bulkDeleting"
            @bulk-delete="resource.confirmBulkDelete"
            @cancel="resource.clearSelection"
        />

        <DetailsModal
            v-model:visible="detailsModalVisible"
            :importData="selectedImport"
            @resync="resyncImport"
        />
        <DeleteModal
            v-model:visible="deleteModalVisible"
            :importData="importToDelete"
            :loading="deleting"
            @delete="deleteImport"
        />
        <EditModal
            v-model:visible="editModalVisible"
            :importData="importToEdit"
            :saving="saving"
            @save="saveEditedRecord"
        />
        <ErrorModal v-model:visible="errorModalVisible" :errors="currentErrorMessages" />
    </Layout>
</template>

<script>
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';

import HeaderTab from './partials/HeaderTab.vue';
import AttendanceImportGrid from './partials/AttendanceImportGrid.vue';
import DetailsModal from './partials/DetailsModal.vue';
import DeleteModal from '@spa/pages/result-import/partials/DeleteModal.vue';
import EditModal from './partials/EditModal.vue';
import ErrorModal from '@spa/pages/result-import/partials/ErrorModal.vue';
import BulkActions from '@spa/pages/result-import/partials/BulkActions.vue';

import useAttendanceImportResource from '@spa/services/attendance-import/attendanceImportResource.js';

export default {
    components: {
        Layout,
        PageTitleContent,
        HeaderTab,
        AttendanceImportGrid,
        DetailsModal,
        DeleteModal,
        EditModal,
        ErrorModal,
        BulkActions,
    },
    setup() {
        const loaderStore = useLoaderStore();
        const resource = useAttendanceImportResource('spa/attendance-import', {
            filters: { search: '', take: 10, page: 1 },
        });
        return { loaderStore, resource };
    },
    data() {
        return {
            fileInput: null,
            autoRefreshInterval: null,
            autoRefreshEnabled: false,
            errorModalVisible: false,
            currentErrorMessages: null,
        };
    },
    mounted() {
        this.fileInput = this.$refs.fileInput;
        this.resource.fetch();
    },
    beforeUnmount() {
        this.stopAutoRefresh();
    },
    computed: {
        sortDirection() {
            return this.resource.state.dir || 'desc';
        },
        perPage() {
            return this.resource.state.pageable.pageSizeValue;
        },
        paginationData() {
            const currentPage = this.resource.state.pageable.currentPage;
            const perPage = this.resource.state.pageable.pageSizeValue;
            return {
                skip: (currentPage - 1) * perPage,
                take: perPage,
                total: this.resource.state.pageable.totalItems || 0,
                pageSizes: this.resource.state.pageable.pageSizes,
            };
        },
        detailsModalVisible: {
            get() {
                return this.resource.state.detailsModalVisible;
            },
            set(v) {
                this.resource.state.detailsModalVisible = v;
            },
        },
        deleteModalVisible: {
            get() {
                return this.resource.state.deleteModalVisible;
            },
            set(v) {
                this.resource.state.deleteModalVisible = v;
            },
        },
        selectedImport() {
            return this.resource.state.selectedImport;
        },
        importToDelete() {
            return this.resource.state.importToDelete;
        },
        refreshing() {
            return this.resource.state.refreshing;
        },
        deleting() {
            return this.resource.state.deleting;
        },
        editModalVisible: {
            get() {
                return this.resource.state.editModalVisible;
            },
            set(v) {
                this.resource.state.editModalVisible = v;
            },
        },
        importToEdit() {
            return this.resource.state.importToEdit;
        },
        saving() {
            return this.resource.state.saving;
        },
    },
    methods: {
        refreshData() {
            this.resource.refreshData();
        },
        toggleSortDirection() {
            this.resource.toggleSortDirection();
        },
        handleSort(sort) {
            this.resource.handleSort(sort);
        },
        handlePageChange(event) {
            this.resource.handlePageChange(event);
        },
        handlePageSizeChange(event) {
            this.resource.handlePageChange(event);
        },
        handleFilter(filters) {
            this.resource.handleFilter(filters);
        },
        openFileUpload() {
            this.fileInput.click();
        },
        async handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            try {
                await this.resource.uploadFile(file);
                this.startAutoRefresh();
            } finally {
                event.target.value = '';
            }
        },
        viewDetails(importData) {
            this.resource.viewDetails(importData);
        },
        confirmDelete(importData) {
            this.resource.confirmDelete(importData);
        },
        deleteImport() {
            this.resource.deleteImport();
        },
        resyncImport(importData) {
            this.resource.resyncImport(importData);
        },
        editRecord(importData) {
            this.resource.editRecord(importData);
        },
        saveEditedRecord(data) {
            this.resource.saveEditedRecord(data);
        },
        handleViewError(errors) {
            try {
                this.currentErrorMessages =
                    typeof errors === 'string' ? JSON.parse(errors) : errors;
            } catch (e) {
                this.currentErrorMessages = errors;
            }
            this.errorModalVisible = true;
        },
        startAutoRefresh() {
            if (!this.autoRefreshEnabled) {
                this.autoRefreshEnabled = true;
                this.autoRefreshInterval = setInterval(() => {
                    this.resource.fetch();
                }, 10000);
            }
        },
        stopAutoRefresh() {
            if (this.autoRefreshInterval) {
                clearInterval(this.autoRefreshInterval);
                this.autoRefreshInterval = null;
            }
            this.autoRefreshEnabled = false;
        },
        toggleAutoRefresh() {
            this.autoRefreshEnabled ? this.stopAutoRefresh() : this.startAutoRefresh();
        },
        handleSelectionChange(event) {
            const id = event.dataItem.id;
            this.resource.state.selectAll =
                this.resource.state.selectedIds.length ===
                this.resource.state.imports.data.filter((item) => item.status !== 'completed')
                    .length;
            this.resource.toggleSelectItem(id);
        },
        handleHeaderSelectionChange() {
            this.resource.toggleSelectAll();
        },
    },
};
</script>
