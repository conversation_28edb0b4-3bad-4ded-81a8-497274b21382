<template>
    <tw-card
        :pt="{
            root: 'rounded-lg',
        }"
    >
        <template #content>
            <div class="group flex cursor-pointer items-center gap-4" @click="goTo">
                <div
                    :class="iconWrapperClass"
                    class="flex items-center justify-center rounded-full p-1"
                >
                    <component :is="getComponent(icon)" :class="iconClass" class="h-5 w-5" />
                </div>
                <div class="space-y-1">
                    <a
                        :href="computedHref"
                        :target="targetAttr"
                        :rel="relAttr"
                        class="text-sm leading-5 text-gray-700"
                    >
                        {{ title }}
                    </a>
                    <!-- <a v-if="link" :href="route(link)"
                        class="text-xs leading-7 tracking-tight text-primary-blue-500 group-hover:underline">
                        {{ label }}

                    </a>
                    <div v-else class="text-xs leading-7 tracking-tight text-primary-blue-500 group-hover:underline">
                        {{ label }}
                    </div> -->
                </div>
            </div>
        </template>
    </tw-card>
</template>
<script>
import { Link } from '@inertiajs/vue3';
import CardVue from '@spa/components/Card/Card.vue';
import { IconClipboardText24Regular } from '@iconify-prerendered/vue-fluent';
import IconMaps from '@spa/plugins/icons-maps.js';
import { twMerge } from 'tailwind-merge';

export default {
    props: {
        icon: String,
        title: String,
        label: String,
        link: String,
        href: String,
        external: Boolean,
        pt: {
            type: Object,
            default: {},
        },
    },
    components: {
        'tw-card': CardVue,
        'icon-clipboard-text': IconClipboardText24Regular,
        Link,
    },
    computed: {
        iconWrapperClass() {
            return twMerge(`bg-${this.colorCard(this.title)}-50`, this.pt.iconWrapper);
        },
        iconClass() {
            return twMerge(`text-${this.colorCard(this.title)}-500`, this.pt.icon);
        },
        computedHref() {
            console.log('href', this.href);
            return this.href ? this.href : this.link ? route(this.link) : '#';
        },
        targetAttr() {
            return this.external ? '_blank' : null;
        },
        relAttr() {
            return this.external ? 'noopener noreferrer' : null;
        },
    },
    methods: {
        goTo() {
            const href = this.computedHref;
            if (!href || href === '#') return;
            if (this.external || this.href) {
                window.open(href, '_blank', 'noopener,noreferrer');
            } else if (this.link) {
                window.location.href = href;
            }
        },
        getComponent(icon) {
            return IconMaps[icon] || null;
        },
        colorCard(title) {
            const colorMapping = {
                Documents: 'cyan',
                Attendance: 'green',
                Timetable: 'yellow',
                Payment: 'green',
                Results: 'purple',
                OHSC: 'red',
                'Help & Support': 'cyan',
                'LMS Portal': 'cyan',
            };
            return colorMapping[title] || 'gray';
        },
    },
};
</script>
<style lang=""></style>
