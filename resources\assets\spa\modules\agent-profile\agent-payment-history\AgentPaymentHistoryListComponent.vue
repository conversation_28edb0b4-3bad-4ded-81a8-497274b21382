<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="false"
        :has-export="false"
        :has-filters="true"
        :gridStyle="{
            height: '500px',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :has-actions="false"
    >
        <template #filters>
            <FilterBlockWrapper label="Student">
                <SearchStudent
                    :model-value="store.filters.studentId"
                    @select="(event) => (store.filters.studentId = event.generated_stud_id)"
                />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Course">
                <CoursesSelect v-model="store.filters.course_id" />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Paid Date">
                <FormDateRangePicker v-model="store.filters.paid_date" />
            </FilterBlockWrapper>
        </template>
        <template #body-cell-student_id="{ props }">
            <div class="flex items-center gap-2">
                <a target="_blank">
                    <Avatar
                        :label="getInitialName(`${props.dataItem?.student?.name}`)"
                        class="shrink-0"
                    />
                </a>
                <div class="space-y-0">
                    <a class="text-left font-medium text-gray-900">
                        <span class="text-left font-medium text-gray-900">{{
                            props.dataItem?.student?.name
                        }}</span>
                    </a>
                    <CopyToClipboard :text="props.dataItem?.student?.generated_stud_id" />
                </div>
            </div>
        </template>
        <template #body-cell-course="{ props }">
            {{ props.dataItem?.course?.code }} - {{ props.dataItem?.course?.name }}
        </template>
        <template #body-cell-invoice_number="{ props }">
            {{ props.dataItem?.formatted_invoice_number }}
        </template>
        <template #body-cell-paid_date="{ props }">
            <FormatDateTime :date="props.dataItem?.paid_date" format-type="DD MMM YYYY" />
        </template>
        <template #body-cell-comm_paid_date="{ props }">
            <FormatDateTime :date="props.dataItem?.comm_paid_date" format-type="DD MMM YYYY" />
        </template>
        <template #body-cell-invoice_amount="{ props }">
            <PriceColumn :value="props.dataItem?.invoice_amount" />
        </template>
        <template #body-cell-deposited_amount="{ props }">
            <PriceColumn :value="props.dataItem?.deposited_amount" />
        </template>
        <template #body-cell-paid_amount="{ props }">
            <PriceColumn :value="props.dataItem?.paid_amount" />
        </template>
        <template #body-cell-upfront_fee_to_pay="{ props }">
            <PriceColumn :value="props.dataItem?.upfront_fee_to_pay" />
        </template>
        <template #body-cell-commission_payable="{ props }">
            <PriceColumn :value="props.dataItem?.commission_payable" />
        </template>
        <template #body-cell-gst_amount="{ props }">
            <PriceColumn :value="props.dataItem?.gst_amount" />
        </template>
        <template #body-cell-commission_paid="{ props }">
            <PriceColumn :value="props.dataItem?.commission_paid" />
        </template>
        <template #body-cell-remarks="{ props }">
            <div v-html="props.dataItem?.remarks || '-'" />
        </template>
    </AsyncGrid>
    <AgentPaymentHistoryForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted, watch } from 'vue';
import { useAgentPaymentHistoryStore } from '@spa/stores/modules/agentpaymenthistory/useAgentPaymentHistoryStore.js';
import AgentPaymentHistoryForm from '@spa/modules/agent-profile/agent-payment-history/AgentPaymentHistoryForm.vue';
import FormatDateTime from '@spa/components/FormatDateTime.vue';
import PriceColumn from '@spa/components/AsyncComponents/Grid/Partials/ColumnTemplates/PriceColumn.vue';
import Avatar from '@spa/components/Avatar/Avatar.vue';
import CopyToClipboard from '@spa/components/CopyAction/CopyToClipboard.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import CoursesSelect from '@spa/modules/teacher/partials/CoursesSelect.vue';
import SearchStudent from '@spa/components/filters/SearchStudent.vue';
import FormDateRangePicker from '@spa/components/KendoInputs/FormDateRangePicker.vue';
import { getInitialName } from '@spa/composables/strComposables.js';
const store = useAgentPaymentHistoryStore();

const props = defineProps({
    agentId: Number,
});

const columns = [
    {
        field: 'formatted_invoice_number',
        name: 'invoice_number',
        title: 'Invoice Number',
        width: '200px',
        replace: true,
    },
    {
        field: 'generated_stud_id',
        name: 'student_id',
        title: 'Student',
        width: '200px',
        replace: true,
    },
    // {
    //     field: 'student_name',
    //     name: 'student_name',
    //     title: 'Student Name',
    //     width: '200px',
    //     replace: true,
    // },
    {
        field: 'course_code',
        name: 'course',
        title: 'Course',
        width: '200px',
        replace: true,
    },
    {
        field: 'course_attempt',
        title: 'Course Attempt',
        width: '200px',
    },
    {
        field: 'invoice_amount',
        name: 'upfront_fee_to_pay',
        title: 'Invoice Amount',
        width: '200px',
        replace: true,
    },
    {
        field: 'deposited_amount',
        name: 'paid_amount',
        title: 'Deposited Amount',
        width: '200px',
        replace: true,
    },
    {
        field: 'commission_payable',
        name: 'commission_payable',
        title: 'Commission Payable',
        width: '200px',
        replace: true,
    },
    {
        field: 'gst_amount',
        name: 'gst_amount',
        title: 'GST',
        width: '200px',
        replace: true,
    },
    {
        field: 'commission_paid',
        name: 'commission_paid',
        title: 'Commission Paid',
        width: '200px',
        replace: true,
    },
    {
        field: 'paid_date',
        name: 'paid_date',
        title: 'Paid Date',
        width: '200px',
        replace: true,
    },
    {
        field: 'remarks',
        name: 'remarks',
        title: 'Remarks',
        width: '200px',
        replace: true,
    },
    // Add more columns as needed
];

const initFilters = () => {
    store.filters = {
        agentId: props.agentId,
        paid_date: null,
        course_id: null,
        studentId: null,
    };
};

watch(
    () => props.agentId,
    (newVal) => {
        store.filters = {
            agentId: newVal,
        };
    },
    { immediate: true }
);

onMounted(() => {
    initFilters();
});
</script>
