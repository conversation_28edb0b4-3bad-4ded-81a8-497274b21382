<template>
    <div class="tab-wrapper">
        <!-- Tab Headers with Navigation -->
        <div class="pt-5" :class="sticky ? 'sticky top-0 z-10 bg-white' : 'relative'">
            <div class="mx-1 flex items-center border-b border-gray-200">
                <!-- Previous Button -->
                <button
                    @click="scrollTabs('prev')"
                    :disabled="!canScrollLeft"
                    :class="'flex-shrink-0 rounded p-1 text-gray-500 transition-colors hover:text-primary-blue-500 focus:outline-none focus:ring-2 focus:ring-primary-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:text-gray-300 disabled:hover:text-gray-300'"
                    aria-label="Previous tabs"
                    v-if="showArrows"
                >
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z" />
                    </svg>
                </button>

                <div
                    ref="tabsContainer"
                    class="scrollbar-hide flex overflow-x-auto scroll-smooth"
                    @scroll="updateScrollButtons"
                    style="scrollbar-width: none; -ms-overflow-style: none"
                >
                    <button
                        v-for="(tab, index) in tabs"
                        v-show="isTabVisible(tab)"
                        :key="tab.name"
                        @click="selectTab(index)"
                        :class="[
                            'flex-shrink-0 px-6 py-3 text-sm font-medium transition-colors focus:outline-none focus:ring-0',
                            'whitespace-nowrap border-b-2 text-center',
                            selected === index
                                ? 'border-primary-blue-500 text-primary-blue-500'
                                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-primary-blue-500',
                            tabs.length > 6 ? 'min-w-[130px]' : 'min-w-[150px]',
                        ]"
                    >
                        <slot
                            name="titleRender"
                            :props="{ title: tab.title, index: index, key: tab.name }"
                        >
                            <!-- Default title rendering -->
                            <span class="flex items-center justify-center gap-2">
                                <icon
                                    :name="getIconName(index)"
                                    fill="currentColor"
                                    width="20"
                                    height="20"
                                    viewBox="0 0 24 24"
                                    v-if="showIcon"
                                />
                                {{ tab.title }}
                            </span>
                        </slot>
                    </button>
                </div>

                <!-- Next Button -->
                <button
                    v-if="showArrows"
                    @click="scrollTabs('next')"
                    :disabled="!canScrollRight"
                    :class="[
                        'flex-shrink-0 p-1 text-gray-500 transition-colors hover:text-primary-blue-500',
                        'disabled:cursor-not-allowed disabled:text-gray-300 disabled:hover:text-gray-300',
                        'rounded focus:outline-none focus:ring-2 focus:ring-primary-blue-500 focus:ring-offset-2',
                    ]"
                    aria-label="Next tabs"
                >
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z" />
                    </svg>
                </button>
            </div>

            <!-- Fade overlays for visual indication -->
            <!--            <div-->
            <!--                v-show="canScrollLeft"-->
            <!--                class="pointer-events-none absolute bottom-0 left-4 top-0 z-10 w-8 bg-gradient-to-r from-white to-transparent"-->
            <!--            ></div>-->
            <!--            <div-->
            <!--                v-show="canScrollRight"-->
            <!--                class="pointer-events-none absolute bottom-0 right-4 top-0 z-10 w-8 bg-gradient-to-l from-white to-transparent"-->
            <!--            ></div>-->
        </div>

        <!-- Tab Content -->
        <div class="tab-content mt-8">
            <KeepAlive>
                <template v-for="(tab, index) in tabs" :key="tab.name">
                    <div v-if="selected === index && isTabVisible(tab)" class="tab-panel">
                        <slot
                            :name="`tab-panel-${tab.name}`"
                            :props="{ tab, index, isActive: selected === index }"
                        >
                            <div class="p-4 text-gray-600">
                                No content provided for {{ tab.title }}
                            </div>
                        </slot>
                    </div>
                </template>
            </KeepAlive>
        </div>
    </div>
</template>
<script setup>
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
const props = defineProps({
    tabs: {
        type: Array,
        required: true,
        validator: (tabs) => {
            return tabs.every(
                (tab) =>
                    typeof tab === 'object' &&
                    tab.hasOwnProperty('name') &&
                    tab.hasOwnProperty('title')
            );
        },
    },
    defaultSelected: {
        type: Number,
        default: 0,
    },
    showIcon: {
        type: Boolean,
        default: false,
    },
    scrollAmount: {
        type: Number,
        default: 200,
    },
    showArrows: {
        type: Boolean,
        default: false,
    },
    sticky: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['select']);

const selected = ref(0);
const canScrollLeft = ref(false);
const canScrollRight = ref(false);
const tabsContainer = ref(null);

const scrollTabs = (direction) => {
    if (!tabsContainer.value) return;

    const scrollAmount = props.scrollAmount;

    if (direction === 'prev') {
        tabsContainer.value.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
    } else {
        tabsContainer.value.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
};

const updateScrollButtons = () => {
    if (!tabsContainer.value) return;

    canScrollLeft.value = tabsContainer.value.scrollLeft > 0;
    canScrollRight.value =
        tabsContainer.value.scrollLeft <
        tabsContainer.value.scrollWidth - tabsContainer.value.clientWidth;
};

const scrollToActiveTab = () => {
    if (!tabsContainer.value || !tabsContainer.value.children[selected.value]) return;

    const container = tabsContainer.value;
    const activeTab = container.children[selected.value];

    const containerRect = container.getBoundingClientRect();
    const activeTabRect = activeTab.getBoundingClientRect();

    // Check if active tab is not fully visible
    if (activeTabRect.left < containerRect.left || activeTabRect.right > containerRect.right) {
        const scrollPosition =
            activeTab.offsetLeft - container.clientWidth / 2 + activeTab.clientWidth / 2;
        container.scrollTo({ left: scrollPosition, behavior: 'smooth' });
    }
};

const getIconName = (index) => {
    return props.tabs[index]?.icon || 'default-icon';
};

const isTabVisible = (tab) => {
    // If hidden property doesn't exist, tab is visible by default
    if (!tab.hasOwnProperty('hidden')) {
        return true;
    }

    // If hidden is a function, evaluate it
    if (typeof tab.hidden === 'function') {
        return !tab.hidden();
    }

    // If hidden is a boolean, return the opposite
    return !tab.hidden;
};

// Lifecycle hooks
onMounted(() => {
    // Ensure the initially selected tab is visible, otherwise select the first visible tab
    if (props.tabs[props.defaultSelected] && !isTabVisible(props.tabs[props.defaultSelected])) {
        const firstVisibleIndex = props.tabs.findIndex((tab) => isTabVisible(tab));
        if (firstVisibleIndex !== -1) {
            selected.value = firstVisibleIndex;
        }
    } else {
        selected.value = props.defaultSelected;
    }

    nextTick(() => {
        updateScrollButtons();
        scrollToActiveTab();
    });

    window.addEventListener('resize', updateScrollButtons);
});

onBeforeUnmount(() => {
    window.removeEventListener('resize', updateScrollButtons);
});

// Watchers
watch(selected, () => {
    nextTick(() => {
        scrollToActiveTab();
    });
});

const selectTab = (index) => {
    emit('select', index, props.tabs[index]);
    selected.value = index;
};

watch(
    () => props.defaultSelected,
    (newVal) => {
        // Check if the new default selected tab is visible
        if (props.tabs[newVal] && isTabVisible(props.tabs[newVal])) {
            selected.value = newVal;
        } else {
            // If not visible, find the first visible tab
            const firstVisibleIndex = props.tabs.findIndex((tab) => isTabVisible(tab));
            if (firstVisibleIndex !== -1) {
                selected.value = firstVisibleIndex;
            }
        }
    }
);
</script>

<style scoped>
.scrollbar-hide::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
}

/* Smooth scrolling */
.scroll-smooth {
    scroll-behavior: smooth;
}
</style>
