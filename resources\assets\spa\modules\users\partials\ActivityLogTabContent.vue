<template>
    <Card :variant="'compact'" :pt="{ root: 'bg-gray-100' }">
        <template #header>
            <div class="flex items-center gap-2">
                <h2 class="text-lg font-medium">Activity Log</h2>
            </div>
        </template>
        <template #content>
            <div v-if="store.userType !== 'student'">
                <ActivityListComponent :filters="{ loggable_id: store.formData.id }" />
            </div>
            <div v-else>
                <NoData
                    :title="`Activity logs of <span class='text-primary-blue-500'>${store.formData?.name}</span> can be viewed from student profile.`"
                    subtitle="Please click on view profile button to view the activity logs."
                >
                    <template #button>
                        <a
                            :href="
                                route('student-profile-view', [store.formData?.secure_id]) +
                                '?activetab=activitylog'
                            "
                            target="_blank"
                            v-if="store.formData?.secure_id"
                        >
                            <Button variant="primary" outline>
                                <span class="text-primary-blue-500">
                                    <icon name="external-link" :width="18" :height="18" />
                                </span>
                                View Activity Log
                            </Button>
                        </a>
                    </template>
                </NoData>
            </div>
        </template>
    </Card>
</template>
<script setup>
import Card from '@spa/components/Card/Card.vue';
import ActivityListComponent from '@spa/modules/activity/ActivityListComponent.vue';
import NoData from '@spa/components/NoData/NoData.vue';
import Button from '@spa/components/Buttons/Button.vue';
const props = defineProps({
    store: Object,
});
</script>
