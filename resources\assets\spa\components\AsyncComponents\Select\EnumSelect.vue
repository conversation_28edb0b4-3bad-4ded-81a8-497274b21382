<script setup>
import { computed, onMounted, ref } from 'vue';
import { DropDownList, MultiSelect } from '@progress/kendo-vue-dropdowns';
import apiClient from '@spa/services/api.client.js';
import Button from '@spa/components/Buttons/Button.vue';

const popupOptions = {
    animate: false,
    popupClass: 'tw-popup',
};

const props = defineProps({
    label: String,
    className: String,
    optionValue: {
        type: String,
        default: 'value',
    },
    optionLabel: {
        type: String,
        default: 'label',
    },
    disabled: Boolean,
    enumClass: String,
    modelValue: {},
    clearable: {
        type: Boolean,
        default: false,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    readonly: Boolean,
    useChips: {
        type: Boolean,
        default: true,
    },
    placeholder: {
        type: String,
        default: '',
    },
    defaultValue: {
        type: String,
        default: '',
    },
    hasSelectAll: {
        type: Boolean,
        default: false,
    },
    loading: {
        type: Boolean,
        default: false,
    },
    valuePrimitive: {
        type: Boolean,
        default: true,
    },
});

const emit = defineEmits(['update:modelValue', 'change', 'select']);

const allOptions = ref([]);
const defaultItem = computed(() => ({
    [props.optionLabel]: props.placeholder,
    [props.optionValue]: null,
}));

onMounted(async () => {
    fetchEnumOptions();
    // if (!computedValue.value || computedValue.value === '') {
    //     computedValue.value = props.defaultValue;
    // }
});

const computedValue = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});

const fetchEnumOptions = async () => {
    try {
        const { data } = await apiClient.get(
            `/api/v2/tenant/utilities/get-enum-options?enum=${props.enumClass}`
        );
        if (props.hasSelectAll) {
            let allOption = {};
            allOption[props.optionValue] = '';
            allOption[props.optionLabel] = 'All';
            data.unshift(allOption);
        }
        allOptions.value = data.map((item) => ({
            [props.optionValue]: item.value,
            [props.optionLabel]: item.label,
        }));
    } catch (e) {
        console.error('Error fetching options:', e);
        allOptions.value = [];
    }
};
defineExpose({
    allOptions,
});

const handleChange = (event) => {
    emit('change', event.value);
};

const handleSelect = (event) => {
    emit('select', event.dataItem);
};
</script>

<template>
    <div class="k-form-field-wrap flex-1">
        <div :class="`async-select ${className}`">
            <MultiSelect
                v-if="multiple"
                @update:multiSelectRef="
                    (val) => {
                        dropDownRef = val;
                    }
                "
                :class="'async' + label"
                :data-items="filteredOptions"
                :text-field="optionLabel"
                :value-field="optionValue"
                :data-item-key="optionValue"
                :filterable="true"
                :clear-button="clearable"
                :disabled="disabled"
                :readonly="readonly"
                :loading="loading"
                :value-primitive="valuePrimitive"
                :auto-close="false"
                :popup-settings="popupOptions"
                :default-item="defaultItem"
                :placeholder="placeholder"
                v-model="computedValue"
                :style="{
                    width: '100%',
                    minWidth: '150px',
                    maxWidth: '100%',
                    ...style,
                }"
                :touched="false"
            >
            </MultiSelect>
            <DropDownList
                v-else
                :data-items="allOptions"
                :text-field="optionLabel"
                :value-field="optionValue"
                :data-item-key="optionValue"
                v-model="computedValue"
                :filterable="false"
                :clear-button="clearable"
                :disabled="disabled"
                :readonly="readonly"
                :label="label"
                :loading="loading"
                @change="handleChange"
                @select="handleSelect"
                :default-item="defaultItem"
                :placeholder="placeholder"
                :value-primitive="valuePrimitive"
                :popup-settings="popupOptions"
            />
        </div>
    </div>
</template>
<style lang="scss">
.async-select {
    .k-multiselect {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
}
</style>
