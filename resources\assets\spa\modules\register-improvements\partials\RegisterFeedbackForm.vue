<template>
    <AsyncForm
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        position="center"
        :max-width="'700px'"
        :dialogTitle="'Improvement Request Form'"
        :store="store"
        :submit-text="'Request'"
    >
        <div class="grid grid-cols-2 gap-6 p-4 px-5">
            <ImprovementCategorySelect
                name="categoryData"
                label="Category"
                :model-value="formData.categoryData"
                @update:model-value="
                    (value) => {
                        formData.category_id = value.id;
                        formData.category = value.name;
                        formData.categoryData = value;
                    }
                "
                :validation-message="store.errors?.category_id"
                :valid="!store.errors?.category_id"
                :touched="true"
                :indicaterequired="true"
                :has-create-action="false"
                :value-primitive="false"
            />
            <FormDatePicker
                name="lodged_date"
                label="Logged Date"
                v-model="formData.lodged_date"
                :validation-message="store.errors?.lodged_date"
                :valid="!store.errors?.lodged_date"
                :touched="true"
                :indicaterequired="true"
                :format="'dd-MM-yyyy'"
                :emit-format="'yyyy-MM-dd'"
                :disabled="true"
            />
            <div class="col-span-2 grid grid-cols-1 gap-6">
                <FormTextArea
                    name="case_detail"
                    label="Description"
                    v-model="formData.case_detail"
                    :validation-message="store.errors?.case_detail"
                    :valid="!store.errors?.case_detail"
                    :touched="true"
                    :indicaterequired="true"
                    :rows="10"
                />
            </div>
        </div>
    </AsyncForm>
    <AddImporvementCategoryPopup />
</template>
<script setup>
import { ref, watch, computed, inject, reactive } from 'vue';
import { useRegisterImprovementStore } from '@spa/stores/modules/continuous-improvement/registerImprovementStore.js';
import { useStaffStore } from '@spa/stores/modules/staff/useStaffStore.js';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import { useStaffPositionStore } from '@spa/stores/modules/staff-position/useStaffPositionStore.js';
import RegisterImprovementFormContent from '@spa/modules/register-improvements/partials/RegisterImprovementFormContent.vue';
import StudentSelect from '@spa/modules/register-improvements/partials/StudentSelect.vue';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import { Field } from '@progress/kendo-vue-form';
import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
import { storeToRefs } from 'pinia';
import EnumSelect from '@spa/components/AsyncComponents/Select/EnumSelect.vue';
import StaffPositionSelect from '@spa/modules/staffposition/StaffPositionSelect.vue';
import StaffSelect from '@spa/modules/timesheet-submission/StaffSelect.vue';
import AddImporvementCategoryPopup from '@spa/modules/register-improvements/partials/AddImporvementCategoryPopup.vue';
import FormValidationWrapper from '@spa/components/KendoInputs/FormValidationWrapper.vue';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import SearchStudent from '@spa/components/filters/SearchStudent.vue';
import ImprovementCategorySelect from '@spa/modules/improvementcategory/ImprovementCategorySelect.vue';
import AgentSelect from '@spa/modules/agent/AgentSelect.vue';
import TeacherSelect from '@spa/modules/timesheet-approved/partials/TeacherSelect.vue';
const store = useRegisterImprovementStore();

const { formData, categories, loggedBy } = storeToRefs(store);

const userType = inject('userType');
const requestedBy = inject('requestedBy');

watch(
    () => store.formDialog,
    (val) => {
        if (val) {
            store.getCategories();
            formData.value = {
                ...formData.value,
                user_type: userType,
                requested_by: requestedBy,
                is_feedback: 1,
                lodged_date: new Date(),
            };
        }
    }
);

watch(
    () => store.userTypes,
    (val) => {
        if (val.length > 0) {
            formFields.user_type.options = val;
        }
    }
);

watch(
    () => store.formData,
    (val) => {}
);
</script>
<style lang=""></style>
