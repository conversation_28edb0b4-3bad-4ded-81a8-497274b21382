<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Features\UserImpersonation;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::middleware([
    'web',
    /* InitializeTenancyByDomain::class, */
    PreventAccessFromCentralDomains::class,
])->group(function () {
    Route::get('/', function () {
        if (auth()->user()) {
            return redirect('/dashboard');
        }

        return redirect()->route('user_login');
    });

    // Route::get('/impersonate/{token}', function ($token) {
    //     return UserImpersonation::makeResponse($token);
    // })->name('tenant.impersonation');

    // Route::get('/', function () {
    //     return 'This is your multi-tenant application. The id of the current tenant is ' . tenant('id');
    // });
    // Route::match(['get', 'post'],   'login',           ['as' => 'user_login', 'uses' => 'v2\sadmin\LoginController@login']);

});
