<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="false"
        :has-bulk-actions="true"
        :has-export="false"
        :has-filters="true"
        :has-search="true"
        :search-placeholder="'Search by invoice, student name, course...'"
        :gridStyle="{
            height: '500px',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :actions="['approve', 'disapprove', 'process']"
        :enable-selection="true"
    >
        <template #filters>
            <FilterBlockWrapper label="Student">
                <div class="flex-1">
                    <SearchStudent
                        :model-value="store.filters.studentId"
                        @select="(event) => (store.filters.studentId = event.generated_stud_id)"
                    />
                </div>
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Course">
                <CoursesSelect v-model="store.filters.course_id" />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Status">
                <EnumSelect
                    className="tw-w-full"
                    enum-class="GalaxyAPI\Enums\CommissionStatusEnum"
                    v-model="store.filters.status"
                    :placeholder="'Select Status'"
                    :hasSelectAll="true"
                />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Due Date">
                <FormDateRangePicker v-model="store.filters.due_date" />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Paid Date">
                <FormDateRangePicker v-model="store.filters.paid_date" />
            </FilterBlockWrapper>
        </template>
        <template #bulk-actions>
            <Button
                v-if="store.selected?.length > 0"
                class="tw-btn-primary !h-9 shadow-none"
                size="xs"
                @click="handleBulkApprove"
            >
                <icon name="check" :fill="'currentColor'" />
                Approve Commission
            </Button>
            <Button
                v-if="store.selected?.length > 0"
                class="tw-btn-primary !h-9 shadow-none"
                size="xs"
                @click="
                    () => {
                        store.formDialog = true;
                    }
                "
            >
                <icon name="sync" :fill="'currentColor'" />
                Sync To Xero
            </Button>
        </template>
        <template #body-cell-student_id="{ props }">
            <div class="flex items-center gap-2">
                <a target="_blank">
                    <Avatar
                        :label="getInitialName(`${props.dataItem?.student?.name}`)"
                        class="shrink-0"
                    />
                </a>
                <div class="space-y-0">
                    <a class="text-left font-medium text-gray-900">
                        <span class="text-left font-medium text-gray-900">{{
                            props.dataItem?.student?.name
                        }}</span>
                    </a>
                    <CopyToClipboard :text="props.dataItem?.student?.generated_stud_id" />
                </div>
            </div>
        </template>
        <template #body-cell-course="{ props }">
            {{ props.dataItem?.course_code }}: {{ props.dataItem?.course_name }}
        </template>
        <template #body-cell-invoice_number="{ props }">
            {{ props.dataItem?.formatted_invoice_number }}
        </template>
        <template #body-cell-due_date="{ props }">
            <FormatDateTime :date="props.dataItem?.due_date" format-type="DD MMM YYYY" />
        </template>
        <template #body-cell-paid_date="{ props }">
            <FormatDateTime :date="props.dataItem?.paid_date" format-type="DD MMM YYYY" />
        </template>
        <template #body-cell-comm_paid_date="{ props }">
            <FormatDateTime :date="props.dataItem?.comm_paid_date" />
        </template>
        <template #body-cell-refund_amount="{ props }">
            <PriceColumn :value="props.dataItem?.refund_amount" />
        </template>
        <template #body-cell-transaction_amount="{ props }">
            <PriceColumn :value="props.dataItem?.transaction_amount" />
        </template>
        <template #body-cell-commission="{ props }">
            <PriceColumn :value="props.dataItem?.commission" />
        </template>
        <template #body-cell-gst="{ props }">
            <PriceColumn :value="props.dataItem?.gst" />
        </template>
        <template #body-cell-commission_payable="{ props }">
            <PriceColumn :value="props.dataItem?.commission_payable" />
        </template>
        <template #body-cell-commission_paid="{ props }">
            <PriceColumn :value="props.dataItem?.commission_paid" />
        </template>
        <template #body-cell-remarks="{ props }">
            <div v-html="props.dataItem?.remarks || '-'" />
        </template>
        <template #body-cell-approval_status="{ props }">
            <div class="flex items-center gap-2">
                <Badge
                    variant="success"
                    v-if="props.dataItem?.is_process == 1 || props.dataItem?.is_paid == 1"
                    >Paid</Badge
                >
                <Badge variant="secondary" v-else-if="props.dataItem?.is_approved == 1"
                    >Approved</Badge
                >
                <Badge variant="warning" v-else>Not Approved</Badge>
            </div>
        </template>
    </AsyncGrid>
    <StudentAgentCommissionForm />
    <ApproveCommissionModal
        :visible="store.approveModalVisible"
        :item="store.approveModalItem"
        :loading="store.approveModalLoading"
        @close="store.closeApproveModal"
        @approve="handleApprove"
    />
    <BulkApproveCommissionModal
        :visible="store.bulkApproveModalVisible"
        :selectedItems="store.bulkApproveModalItems"
        :loading="store.bulkApproveModalLoading"
        :hasExpiredContracts="bulkApproveExpiredContracts"
        :expiredContractDateRange="bulkApproveExpiredDateRange"
        :editAgentCommissionUrl="bulkApproveEditUrl"
        @close="store.closeBulkApproveModal"
        @approve="handleBulkApproveConfirm"
        @extend-contract="handleExtendContract"
    />
    <ProcessCommissionModal
        :visible="store.processModalVisible"
        :item="store.processModalItem"
        :loading="store.processModalLoading"
        @close="store.closeProcessModal"
        @save="handleProcessPayment"
    />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { onMounted, watch, ref, computed } from 'vue';
import { useStudentAgentCommissionStore } from '@spa/stores/modules/student-agent-commission/useStudentAgentCommissionStore.js';
import StudentAgentCommissionForm from '@spa/modules/agent-profile/student-agent-commission/StudentAgentCommissionForm.vue';
import ApproveCommissionModal from '@spa/modules/agent-profile/student-agent-commission/ApproveCommissionModal.vue';
import BulkApproveCommissionModal from '@spa/modules/agent-profile/student-agent-commission/BulkApproveCommissionModal.vue';
import ProcessCommissionModal from '@spa/modules/agent-profile/student-agent-commission/ProcessCommissionModal.vue';
import PriceColumn from '@spa/components/AsyncComponents/Grid/Partials/ColumnTemplates/PriceColumn.vue';
import Avatar from '@spa/components/Avatar/Avatar.vue';
import FormatDateTime from '@spa/components/FormatDateTime.vue';
import CopyToClipboard from '@spa/components/CopyAction/CopyToClipboard.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import { getInitialName } from '@spa/composables/strComposables.js';
import CoursesSelect from '@spa/modules/teacher/partials/CoursesSelect.vue';
import SearchStudent from '@spa/components/filters/SearchStudent.vue';
import EnumSelect from '@spa/components/AsyncComponents/Select/EnumSelect.vue';
import Badge from '@spa/components/badges/Badge.vue';
//import Button from '@spa/components/Buttons/Button.vue';
//import icon from '@spa/components/icon/icon.vue';
import FormDateRangePicker from '@spa/components/KendoInputs/FormDateRangePicker.vue';
const props = defineProps({
    agentId: Number,
});

const store = useStudentAgentCommissionStore();

const columns = [
    {
        field: 'formatted_invoice_number',
        name: 'invoice_number',
        title: 'Invoice Number',
        width: '200px',
        replace: true,
    },
    {
        field: 'generated_stud_id',
        name: 'student_id',
        title: 'Student',
        width: '200px',
        replace: true,
    },
    {
        field: 'course',
        name: 'course',
        title: 'Course',
        width: '250px',
        replace: true,
    },
    {
        field: 'due_date',
        name: 'due_date',
        title: 'Due Date',
        width: '100px',
        replace: true,
    },
    {
        field: 'transaction_amount',
        name: 'transaction_amount',
        title: 'Transaction Amount',
        width: '150px',
        replace: true,
    },
    {
        field: 'refund_amount',
        name: 'refund_amount',
        title: 'Refund Amount',
        width: '120px',
        replace: true,
    },
    {
        field: 'commission',
        name: 'commission',
        title: 'Commission',
        width: '100px',
        replace: true,
    },
    {
        field: 'gst',
        name: 'gst',
        title: 'GST',
        width: '100px',
        replace: true,
    },
    {
        field: 'commission_payable',
        name: 'commission_payable',
        title: 'Commission Payable',
        width: '150px',
        replace: true,
    },
    {
        field: 'commission_paid',
        name: 'commission_paid',
        title: 'Commission Paid',
        width: '140px',
        replace: true,
    },
    {
        field: 'paid_date',
        name: 'paid_date',
        title: 'Paid Date',
        width: '100px',
        replace: true,
    },
    {
        field: 'remarks',
        name: 'remarks',
        title: 'Remarks',
        width: '200px',
        replace: true,
    },
    {
        field: 'is_approved',
        name: 'approval_status',
        title: 'Approval Status',
        width: '150px',
        replace: true,
    },
    // Add more columns as needed
];

const initFilters = () => {
    store.filters = {
        agentId: props.agentId || null,
        course_id: null,
        studentId: null,
        due_date: null,
        paid_date: null,
        status: null,
    };
};

watch(
    () => props.agentId,
    (newVal) => {
        store.filters = {
            agentId: newVal,
        };
    }
);

const handleApprove = ({ item, remarks }) => {
    store.approveCommission(item, remarks);
};

const handleProcessPayment = ({ item, paymentData }) => {
    store.processCommission(item, paymentData);
};

// Bulk approve functionality

const bulkApproveExpiredContracts = ref(false);
const bulkApproveExpiredDateRange = ref('');
const bulkApproveEditUrl = ref('#');

const handleBulkApprove = () => {
    const selectedItems = store.selected || [];
    if (selectedItems.length === 0) {
        if (window['Fire']) {
            window['Fire'].emit('axiosResponseError', {
                message: 'Please select at least one row',
            });
        }
        return;
    }

    // Check if any selected items are already approved
    const alreadyApproved = selectedItems.some((item) => item.is_approved == 1);
    if (alreadyApproved) {
        if (window['Fire']) {
            window['Fire'].emit('axiosResponseError', {
                message: 'Please select items that are not approved.',
            });
        }
        return;
    }

    // Check for expired contracts (similar to the old JS logic)
    let isOvervalidity = false;
    let commDateRange = '';
    let editAgentCommissionUrl = '#';

    for (const item of selectedItems) {
        if (item.is_payment_past_validity == 1) {
            isOvervalidity = true;
            commDateRange = item.comm_valid_range || '';
            editAgentCommissionUrl = item.agent_commission_id
                ? `${window.site_url || ''}edit-agent-commission/${item.agent_commission_id}`
                : '#';
            break;
        }
    }

    bulkApproveExpiredContracts.value = isOvervalidity;
    bulkApproveExpiredDateRange.value = commDateRange;
    bulkApproveEditUrl.value = editAgentCommissionUrl;

    store.confirmBulkApprove(selectedItems);
};

const handleBulkApproveConfirm = async ({ items, remarks }) => {
    const result = await store.bulkApproveCommission(items, remarks);
    if (result.success) {
        // Handle bulk success notification similar to the old JS implementation
        if (result.data && result.data.success_count > 0) {
            const { success_count, error_count, success_msg, error_msg } = result.data;

            if (window['Fire']) {
                window['Fire'].emit('axiosResponseSuccess', {
                    message: success_msg || `Successfully approved ${success_count} commission(s)`,
                });

                if (error_count > 0) {
                    setTimeout(() => {
                        window['Fire'].emit('axiosResponseError', {
                            message: error_msg || `Failed to approve ${error_count} commission(s)`,
                        });
                    }, 4000);
                }
            }
        }
    } else {
        // Error notification is automatically handled by the API client
        console.error('Bulk approve failed:', result.message);
    }
};

const handleExtendContract = ({ url }) => {
    if (url && url !== '#') {
        window.open(url, '_blank');
    }
};

onMounted(() => {
    initFilters();
});
</script>
