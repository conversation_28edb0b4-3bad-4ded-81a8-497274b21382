<template>
    <Card :variant="'compact'" :pt="{ root: 'bg-gray-100' }">
        <template #header>
            <div class="flex items-center gap-2">
                <h2 class="text-lg font-medium">Students List</h2>
            </div>
        </template>
        <template #content>
            <div v-if="store.formData.id">
                <AgentStudentsListComponent :agentId="store.formData.id" />
            </div>
        </template>
    </Card>
</template>
<script setup>
import Card from '@spa/components/Card/Card.vue';
import AgentStudentsListComponent from '@spa/modules/agent-profile/agent-students/AgentStudentsListComponent.vue';

const props = defineProps({
    store: Object,
});
</script>
