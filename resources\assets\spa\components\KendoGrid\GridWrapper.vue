<template>
    <div v-bind="$attrs" :class="tableClass">
        <slot></slot>
    </div>
</template>

<script>
export default {
    props: {
        hierarchy: { type: Boolean, default: false },
        compact: { type: Boolean, default: false },
        borderless: { type: Boolean, default: false },
        selectable: { type: Boolean, default: false },
        rounded: { type: Boolean, default: false },
        borderlessHeader: { type: Boolean, default: false },
        borderedCell: { type: Boolean, default: false },
        pager: { type: Boolean, default: true },
        overlay: { type: Boolean, default: false },
        rowHover: { type: Boolean, default: false },
        fullWidth: { type: Boolean, defult: false },
        scrollbar: { type: Boolean, default: false },
        actionSticky: { type: Boolean, default: false },
        sortable: { type: Boolean, default: true },
        selectGap: { type: Boolean, default: false },
        striped: { type: Boolean, default: true },
        isTableHeadHeight: { type: <PERSON>olean, default: true },
        isTableCellHeight: { type: <PERSON>olean, default: true },
    },
    computed: {
        tableClass() {
            let classes = ['tw-table'];
            if (this.fullWidth) classes.push('container-stretch');
            if (this.hierarchy) classes.push('tw-table__hierarchy');
            if (this.compact) classes.push('tw-table__compact');
            if (this.selectable) classes.push('tw-table__rows--check');
            if (this.striped) classes.push('tw-table__rows--striped');
            if (this.actionSticky) classes.push('tw-table__action--sticky');
            if (this.sortable) classes.push('tw-table__sortable');
            if (this.rounded) classes.push('tw-table__bordered--rounded');
            if (this.borderless) classes.push('tw-table__borderless');
            if (this.borderlessHeader) classes.push('tw-table__header--borderless');
            if (this.borderedCell) classes.push('tw-table__cell--bordered');
            if (this.pager) classes.push('tw-table__pager tw-table__pager--nospace');
            if (this.overlay) classes.push('tw-table__overlay');
            if (this.rowHover) classes.push('tw-table__row-hover');
            if (this.selectGap) classes.push('tw-table__rows--check--gap');
            if (this.isTableCellHeight) {
                classes.push('tw-table--tableCellHeight');
            }
            if (this.isTableHeadHeight) {
                classes.push('tw-table--tableHeadHeight');
            }
            if (this.scrollbar) {
                classes.push('tw-table--scrollbar');
            } else {
                classes.push('tw-table--scrollbar-hide');
            }
            return classes.join(' ');
        },
    },
};
</script>

<style scoped>
/* Add your CSS styles here */
</style>
