<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="false"
        :has-filters="false"
        :has-bulk-actions="false"
        :gridStyle="{
            height: '488px',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :has-actions="false"
        :search-placeholder="'Search for module, permissions'"
    >
        <template #bulk-actions>
            <Button variant="secondary" size="sm" @click="setAsDefault"> Set as Default </Button>
            <Button variant="link" v-if="getChangesMade.length > 0" @click="resetAllChanges">
                Reset All Changes Made
            </Button>
            <Button
                variant="link"
                v-else-if="getOverwrittenPermissions.length > 0"
                @click="resetAllOverwrittenChanges"
            >
                Reset All Permissions
            </Button>
            <Button variant="link" v-else-if="newrole" @click="resetNewRole">
                Cancel New Role
            </Button>
        </template>
        <template #body-cell-name="{ props }">
            <span class="font-medium text-gray-900" v-if="props.dataItem?.name">{{
                props.dataItem?.label
            }}</span>
            <span v-else>-</span>
        </template>
        <template #body-cell-access="{ props }">
            <ToggleSwitch
                :modelValue="getPermissionValue(props.dataItem)"
                @update:modelValue="updateIntemState(props.dataItem, $event)"
                :size="'small'"
                :label="true ? 'Yes' : 'No'"
            />
        </template>
        <template #body-cell-source="{ props }">
            <span
                v-if="props.dataItem?.permission_status?.s != props.dataItem?.permission_status?.d"
                >Overridden</span
            >
            <span v-else>Inherited from Role</span>
        </template>
        <template #body-cell-updated_at="{ props }">
            <FormatDateTime
                :format-type="'DD MMM YYYY hh:mm A'"
                :date="props.dataItem?.updated_at"
            />
        </template>
        <template #body-cell-updated_by="{ props }">
            {{ props.dataItem?.updated_by?.name || '-' }}
        </template>

        <!--        Filters -->
        <template #filter-module="{ props, shown }">
            <GridColumnCheckboxFilter
                :value="store.filters?.module"
                :options="modulesArray"
                @update:value="store.filters.module = $event"
                @apply="store.fetchPaged()"
            />
        </template>
        <template #filter-source="{ props, shown }">
            <GridColumnCheckboxFilter
                :value="store.filters?.source"
                :options="props.options"
                @update:value="store.filters.user_status = $event"
                @apply="store.fetchPaged()"
            />
        </template>
    </AsyncGrid>
    <UserGroupForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, watch, onMounted, computed } from 'vue';
import UserGroupForm from '@spa/modules/user-group/UserGroupForm.vue';
import { usePermissionStore } from '@spa/stores/modules/permission/usePermissionStore.js';
import ToggleSwitch from '@spa/components/KendoInputs/ToggleSwitch.vue';
import Button from '@spa/components/Buttons/Button.vue';
import FormatDateTime from '@spa/components/FormatDateTime.vue';
import GridColumnCheckboxFilter from '@spa/components/AsyncComponents/Grid/Partials/GridColumnCheckboxFilter.vue';
import { storeToRefs } from 'pinia';

const props = defineProps({
    user: { type: String, required: true },
    role: { type: String, required: true },
    newrole: { type: String, required: false },
});
const store = usePermissionStore();
const { modules } = storeToRefs(store);
store.serverPagination.rowsPerPage = 50;
const emit = defineEmits(['reset', 'roles-updated']);
const modulesArray = computed(() => {
    return modules.value.map((item) => {
        return {
            value: item,
            label: item,
        };
    });
});

const columns = [
    {
        field: 'name',
        name: 'name',
        title: 'Permissions',
        width: '250px',
        replace: true,
        sortable: true,
    },
    {
        field: 'module',
        title: 'Module',
        width: '150px',
        sortable: true,
        filterable: true,
        customFilter: true,
    },
    {
        field: 'access',
        name: 'access',
        title: 'Access',
        width: '140px',
        replace: true,
    },
    {
        field: 'source',
        name: 'source',
        title: 'Source',
        width: '200px',
        replace: true,
        sortable: true,
        filterable: true,
        customFilter: true,
        filterableConfig: {
            options: [
                { value: 'overridden', label: 'Overridden' },
                { value: 'inherited', label: 'Inherited from  Role' },
            ],
        },
    },
    {
        field: 'updated_by',
        name: 'updated_by',
        title: 'Last Updated By',
        width: '200px',
        replace: true,
    },
    {
        field: 'updated_at',
        name: 'updated_at',
        title: 'Last Updated At',
        width: '200px',
        replace: true,
    },

    // Add more columns as needed
];

const initFilters = () => {
    store.serverPagination.sortBy = 'name';
    store.serverPagination.descending = false;
    store.filters = {
        user: props.user,
        role: props.role,
    };
};

onMounted(() => {
    initFilters();
    store.getModules();
});
const getChangesMade = computed(() => {
    // const changed = store.all.filter((item) => {
    //     return Number(item.permission_status.s) !== Number(item.permission_status.o);
    // });
    return store.changedPermissions;
});
const getOverwrittenPermissions = computed(() => {
    const changed = store.all.filter((item) => {
        return Number(item.permission_status.s) !== Number(item.permission_status.d);
    });
    return changed;
});

const resetAllChanges = () => {
    store.resetChangeLog();
    store.all.forEach((item) => {
        item.permission_status.s = item.permission_status.o;
    });
};
const resetAllOverwrittenChanges = () => {
    store.all.forEach((item) => {
        item.permission_status.s = item.permission_status.d;
        store.maintainChangeLog(item);
    });
};
const resetNewRole = () => {
    emit('reset');
};
const updateIntemState = (item, value) => {
    item.permission_status.s = value;
    store.maintainChangeLog(item);
};
const getPermissionValue = (permission) => {
    const changeCached = store.changedPermissions.find((item) => item.id == permission.id);
    return changeCached ? changeCached.permission_status.s : permission.permission_status.s;
};

const setAsDefault = async () => {
    const saved = await store.setAsDefaultRole(props.user, props.role);
    if (saved?.roles) {
        emit('roles-updated', saved.roles);
    }
};

watch(
    () => props.user,
    (newVal) => {
        store.filters = {
            user: newVal,
            role: props.role,
        };
    }
);
watch(
    () => props.role,
    (newVal) => {
        store.filters = {
            user: props.user,
            role: newVal,
        };
    }
);
watch(
    () => props.newrole,
    (newVal) => {
        store.filters = {
            user: props.user,
            role: newVal,
            newrole: 1,
        };
    }
);
</script>
