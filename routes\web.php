<?php

use App\Http\Middleware\CurrentRole;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\URL;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;
use Support\Auth\Access;

/*
  |--------------------------------------------------------------------------
  | Web Routes
  |--------------------------------------------------------------------------
  |
  | This file is where you may define all of the routes that are handled
  | by your application. Just tell Laravel the URIs it should respond
  | to using a Closure or controller method. Build something great!
  |
 */

Route::get('terms-and-condition/{for}', ['as' => 'public.get.terms',     'uses' => 'PublicController@showTerms']);
Route::get('privacy-policy/{for}', ['as' => 'public.get.privacypolicy',     'uses' => 'PublicController@showPrivacy']);

// Route::match(['get'],           '/terms-and-condition/short-course',                   ['as' => 'public.terms',              'uses' => 'PublicController@showTerms']);
// Route::match(['get'],           '/privacy-and-policy/short-course',                   ['as' => 'showPrivacy',              'uses' => 'PublicController@showPrivacy']);

// Route::match(['get'],           '/terms-and-condition/short-course/pdf',                   ['as' => 'public.terms.pdf',              'uses' => 'PublicController@exportTermsPdf']);
// Route::match(['get'],           '/privacy-and-policy/short-course/pdf',                   ['as' => 'public.privacy.pdf',              'uses' => 'PublicController@exportPrivacyPdf']);

Route::group(['prefix' => '', 'middleware' => ['web', /* InitializeTenancyByDomain::class, */ PreventAccessFromCentralDomains::class]], function () {

    // Route::match(['get', 'post'], '/login', ['as' => 'user_login', 'uses' => 'Login\UsersController@login']);
    // Route::match(['get', 'post'], '/forgot_password', ['as' => 'forgot_password', 'uses' => 'Login\UsersController@forgotPassword']);
    Route::match(['get', 'post'], 'send-mail-test/{toemail?}', ['as' => 'send-mail-test',                    'uses' => 'TestMailController@mailSend']);
    Route::match(['get', 'post'], 'login/ajaxActionLogin', ['as' => 'ajaxActionLogin',                 'uses' => 'Login\UsersController@ajaxAction']);

    // Route: agents staff creation
    Route::match(['get', 'post'], '/agents-staff-creation/{encodedId}', ['as' => 'agents-staff-creation',           'uses' => 'Agent\AgentStaffController@createStaff']);
    Route::match(['get', 'post'], 'agents-staff-creation-ajaxcall', ['as' => 'agents-staff-creation-ajaxcall',  'uses' => 'Agent\AgentStaffController@ajaxAction']);

    Route::match(['get', 'post'], '/logout', ['as' => 'user_logout',                     'uses' => 'Login\UsersController@logout']);
    Route::match(['get', 'post'], '/profile', ['as' => 'user_profile',                    'uses' => 'Login\UsersController@profile'])->middleware(['auth']);
    Route::match(['get', 'post'], '/passwordChange', ['as' => 'user_passwordChange',             'uses' => 'Login\UsersController@passwordChange'])->middleware(['auth']);
    Route::match(['get', 'post'], '/resetQuestion', ['as' => 'user_resetQuestion',              'uses' => 'Login\UsersController@resetQuestion'])->middleware(['auth']);
    // Route::match(['get', 'post'], '/reset-password/{param?}', ['as' => 'reset-password', 'uses' => 'Login\UsersController@resetPassword']);

    Route::match(['get', 'post'], 'submit-survey/{id}', ['as' => 'submit-survey',                   'uses' => 'Login\UsersController@submitSurvey']);
    Route::match(['get', 'post'], 'survey-entry/{id}', ['as' => 'survey-entry',                    'uses' => 'Frontend\SurveyManagementController@surveyEntry']);
    Route::match(['get', 'post'], 'download/{id}', ['as' => 'download',                        'uses' => 'Login\UsersController@downloadFile']);
    Route::match(['get', 'post'], 'downloadClgDoc/{id}', ['as' => 'downloadClgDoc',                  'uses' => 'Login\UsersController@downloadClgDoc']);

    // Public Links

    // Route::get('/', function () {
    //     return redirect()->route('user_login');
    // });

});

Route::group(['prefix' => '', 'middleware' => ['auth', 'verified', 'mail', /* InitializeTenancyByDomain::class, */ PreventAccessFromCentralDomains::class]], function () {
    Route::match(['get'], 'admin-dashboard-old', ['as' => 'admin-dashboard-old',     'uses' => 'DashboardController@index']);
    Route::match(['get', 'post'], 'profile-manage', ['as' => 'profile-manage',      'uses' => 'v2\sadmin\OnboardSettingController@profileManage']);

    /* File Route */
    Route::get('media/uploads/2020/02/userfiles/{type}/{path}', 'FilesController@viewFile')
        ->name('files.protected-view')
        ->where('type', 'p|d')
        ->where('path', '.*');

    Route::get('media/uploads/2020/02/suf/{id}', 'FilesController@studentUploadFile')
        ->name('studentuploadfiles.protected-download');

    Route::get('switch-role/{roleId}', 'Login\UsersController@switchRole')->name('galaxy.switch-roles');
});

// mail middleware for smtp setting
$userPrefix = '';
Route::group(['prefix' => $userPrefix, 'middleware' => ['auth', 'verified', 'mail', 'admin', /* InitializeTenancyByDomain::class, */ PreventAccessFromCentralDomains::class]], function () {

    Route::match(['get'], 'check-redis-connection', ['as' => 'check-redis-connection',          'uses' => 'DashboardController@checkRedisConnection']);
    // Route::match(['get'],           'admin-dashboard',                          ['as' => 'admin-dashboard',                 'uses' => 'DashboardController@index']);
    Route::match(['get', 'post'], 'dashboard/ajaxAction', ['as' => 'dashboard/ajaxAction',            'uses' => 'DashboardController@ajaxAction']);
    Route::match(['get', 'post'], '/layouttest', ['as' => 'layouttest',                      'uses' => 'LayoutTestController@testLayout']);
    Route::match(['get', 'post'], '/searchStudents123', ['as' => 'search-students-123',             'uses' => 'LayoutTestController@searchStudents']);

    /* Start demo route for client confirmation */
    Route::match(['get', 'post'], 'common-form', ['as' => 'common-form',                     'uses' => 'CommonFormController@forms']);
    Route::match(['get', 'post'], 'common-table', ['as' => 'common-table',                    'uses' => 'CommonFormController@table']);
    Route::match(['get', 'post'], 'commonForm/ajaxAction', ['as' => 'commonForm-ajaxAction',                      'uses' => 'CommonFormController@ajaxAction']);
    /* End demo route for client confirmation */

    Route::match(['get', 'post'], 'dashboard', ['as' => 'user_dashboard',                  'uses' => 'Login\UsersController@dashboard']);
    Route::match(['get', 'post'], 'dashboard/user', ['as' => 'user_page',                       'uses' => 'Login\UsersController@userPage']);
    Route::match(['get', 'post'], '/update-session-value/{id}', ['as' => 'update-session-value',            'uses' => 'Login\UsersController@sessionUpdate']);

    // Left-sidebar-SetUp
    Route::match(['get', 'post'], 'list-course-template', ['as' => 'list-course-template',            'uses' => 'Frontend\CourseTemplateController@listCourseTemplate']);
    Route::match(['get', 'post'], 'add-course-template', ['as' => 'add-course-template',             'uses' => 'Frontend\CourseTemplateController@addCourseTemplate']);
    Route::match(['get', 'post'], 'edit-course-template/{id}', ['as' => 'edit-course-template',            'uses' => 'Frontend\CourseTemplateController@editCourseTemplate']);
    Route::match(['get', 'post'], 'delete-course-template/{id}', ['as' => 'delete-course-template',          'uses' => 'Frontend\CourseTemplateController@deleteCourseTemplate']);

    Route::match(['get', 'post'], 'course-template-structure/{id}', ['as' => 'course-template-structure',       'uses' => 'Frontend\CourseTemplateController@structureCourseTemplate']);
    Route::match(['get', 'post'], 'delete-course-template-structure/{id}', ['as' => 'delete-course-template-structure', 'uses' => 'Frontend\CourseTemplateController@deleteStructureCourseTemplate']);
    Route::match(['get', 'post'], 'edit-course-template-structure/edit', ['as' => 'edit-course-template-structure',  'uses' => 'Frontend\CourseTemplateController@editStructureCourseTemplate']);
    Route::match(['get', 'post'], 'courseTemplateStructure/ajaxAction', ['as' => 'courseTemplateStructure/ajaxAction', 'uses' => 'Frontend\CourseTemplateController@ajaxAction']);

    Route::match(['get', 'post'], 'list-collegematerials/{id}', ['as' => 'list-collegematerials',           'uses' => 'Frontend\CollegeMaterialsContoller@listCollegeMaterials']);
    Route::match(['get', 'post'], 'add-collegematerials', ['as' => 'add-collegematerials',            'uses' => 'Frontend\CollegeMaterialsContoller@addCollegeMaterials']);
    Route::match(['get', 'post'], 'edit-collegematerials/edit', ['as' => 'edit-collegematerials',           'uses' => 'Frontend\CollegeMaterialsContoller@editCollegeMaterials']);
    Route::match(['get', 'post'], 'delete-collegematerials/{id}', ['as' => 'delete-collegematerials',         'uses' => 'Frontend\CollegeMaterialsContoller@deleteCollegeMaterials']);
    Route::match(['get', 'post'], 'collegeMarterials/ajaxAction', ['as' => 'collegeMarterials-ajaxAction',    'uses' => 'Frontend\CollegeMaterialsContoller@ajaxAction']);

    Route::match(['get', 'post'], 'college-info', ['as' => 'college-info',                    'uses' => 'Frontend\SetUpController@collegeInfo']);
    Route::match(['get', 'post'], 'oshc-info', ['as' => 'oshc-info',                       'uses' => 'Frontend\SetUpController@oshcInfo']);
    Route::match(['get', 'post'], 'prisms-data-validation', ['as' => 'prisms-data-validation',          'uses' => 'Frontend\ValidatePrismsController@validatePrisms']);
    Route::match(['get', 'post'], 'prisms-data-validation/ajaxAction', ['as' => 'prisms-data-validation-ajaxAction', 'uses' => 'Frontend\ValidatePrismsController@ajaxAction']);
    Route::match(['get', 'post'], 'prisms-data-validation-excel/{prismsid}', ['as' => 'prisms-data-validation-excel',    'uses' => 'Frontend\ValidatePrismsController@prismsExcel']);
    Route::match(['get', 'post'], 'import-oshc', ['as' => 'import-oshc',                     'uses' => 'Frontend\SetUpController@importOshc']);
    Route::match(['get', 'post'], 'download-oshc-template-excel', ['as' => 'download-oshc-template-excel',    'uses' => 'Frontend\SetUpController@downloadOshcTemplateExcel']);
    Route::match(['get', 'post'], 'add-oshc', ['as' => 'add-oshc',                        'uses' => 'Frontend\SetUpController@oshcInfoAdd']);
    Route::match(['get', 'post'], 'oshc-info-delete/{id}', ['as' => 'oshc-info-delete',                'uses' => 'Frontend\SetUpController@oshcDeleteV2']);
    Route::match(['get', 'post'], 'nvrReport/ajaxAction', ['as' => 'nvrReport-ajaxAction',            'uses' => 'Frontend\NvrReportController@ajaxAction']);
    Route::match(['get', 'post'], 'setUp/ajaxAction', ['as' => 'setUp-ajaxAction',                'uses' => 'Frontend\SetUpController@ajaxAction']);
    Route::match(['get', 'post'], 'offer-document-checklist', ['as' => 'offer-document-checklist',        'uses' => 'Frontend\SetUpController@offerDocumentChecklist']);
    Route::match(['get', 'post'], 'oshc-fee-update', ['as' => 'oshc-fee-update',                 'uses' => 'Frontend\SetUpController@additionalOshcFeeUpdate']);

    Route::match(['get', 'post'], 'add-offer-document-checklist', ['as' => 'add-offer-document-checklist',    'uses' => 'Frontend\SetUpController@addOfferDocumentChecklist']);
    Route::match(['get', 'post'], 'edit-offer-document-checklist/{id}', ['as' => 'edit-offer-document-checklist',   'uses' => 'Frontend\SetUpController@editOfferDocumentChecklist']);
    Route::match(['get', 'post'], 'delete-offer-document-checklist/{id}', ['as' => 'delete-offer-document-checklist', 'uses' => 'Frontend\SetUpController@deleteOfferDocumentChecklist']);
    Route::match(['get', 'post'], 'student-id-formate', ['as' => 'student-id-formate',              'uses' => 'Frontend\SetUpController@studentIdFormate']);
    Route::match(['get', 'post'], 'agent-status', ['as' => 'agent-status',                    'uses' => 'Frontend\AgentController@agentStatus']);
    Route::match(['get', 'post'], 'add-agent-status', ['as' => 'add-agent-status',                'uses' => 'Frontend\AgentController@agentStatusAdd']);
    Route::match(['get', 'post'], 'view-agent-email-list', ['as' => 'view-agent-email-list',           'uses' => 'Frontend\AgentController@viewAgentEmailList']);
    Route::match(['get', 'post'], 'edit-agent-status/{id}', ['as' => 'edit-agent-status',               'uses' => 'Frontend\AgentController@agentStatusEdit']);
    Route::match(['get', 'post'], 'delete-agent-status/{id}', ['as' => 'delete-agent-status',             'uses' => 'Frontend\AgentController@agentStatusDelete']);

    Route::match(['get', 'post'], 'agent-document-checklist', ['as' => 'agent-document-checklist',        'uses' => 'Frontend\SetUpController@agentDocumentChecklist']);
    Route::match(['get', 'post'], 'add-agent-document-checklist', ['as' => 'add-agent-document-checklist',    'uses' => 'Frontend\SetUpController@addAgentDocumentChecklist']);
    Route::match(['get', 'post'], 'edit-agent-document-checklist/{id}', ['as' => 'edit-agent-document-checklist',   'uses' => 'Frontend\SetUpController@editAgentDocumentChecklist']);
    Route::match(['get', 'post'], 'delete-agent-document-checklist/{id}', ['as' => 'delete-agent-document-checklist', 'uses' => 'Frontend\SetUpController@deleteAgentDocumentChecklist']);

    Route::match(['get', 'post'], 'add-checklist', ['as' => 'add-checklist',                   'uses' => 'Frontend\SetUpController@addChecklist']);
    Route::match(['get', 'post'], 'checklist-view', ['as' => 'checklist-view',                  'uses' => 'Frontend\SetUpController@checklistView']);
    Route::match(['get', 'post'], 'edit-checklist/{id}', ['as' => 'edit-checklist',                  'uses' => 'Frontend\SetUpController@editChecklist']);
    Route::match(['get', 'post'], 'delete-checklist/{id}', ['as' => 'delete-checklist',                'uses' => 'Frontend\SetUpController@deleteChecklist']);

    Route::match(['get', 'post'], 'add-campus', ['as' => 'add-campus',                      'uses' => 'Frontend\CampusController@campusAdd']);
    Route::match(['get', 'post'], 'view-campus-list', ['as' => 'view-campus-list',                'uses' => 'Frontend\CampusController@viewCampusList']);
    Route::match(['get', 'post'], 'delete-campus/{id}', ['as' => 'delete-campus',                   'uses' => 'Frontend\CampusController@deleteCampusList']);
    Route::match(['get', 'post'], 'edit-campus/{id}', ['as' => 'edit-campus',                     'uses' => 'Frontend\CampusController@campusEdit']);

    Route::match(['get', 'post'], 'add-added-service-fee', ['as' => 'add-added-service-fee',           'uses' => 'Frontend\SetUpController@addedServiceFeeAdd']);
    Route::match(['get', 'post'], 'list-added-service-fee', ['as' => 'list-added-service-fee',          'uses' => 'Frontend\SetUpController@addedServiceFeeList']);
    Route::match(['get', 'post'], 'delete-added-service-fee/{id}', ['as' => 'delete-added-service-fee',        'uses' => 'Frontend\SetUpController@addedServiceFeeDelete']);
    Route::match(['get', 'post'], 'edit-added-service-fee/{id}', ['as' => 'edit-added-service-fee',          'uses' => 'Frontend\SetUpController@addedServiceFeeEdit']);
    // Left-sidebar-course

    Route::match(['get', 'post'], 'course-type', ['as' => 'course-type',                     'uses' => 'Frontend\CourseController@courseType']);
    Route::match(['get', 'post'], 'course-type-add', ['as' => 'course-type-add',                 'uses' => 'Frontend\CourseController@courseTypeAdd']);
    Route::match(['get', 'post'], 'course-type-delete/{id}', ['as' => 'course-type-delete',              'uses' => 'Frontend\CourseController@courseTypeDelete']);
    Route::match(['get', 'post'], 'courses-intake-date-list', ['as' => 'courses-intake-date-list',        'uses' => 'Frontend\CourseController@coursesIntakeDateList']);
    Route::match(['get', 'post'], 'courses-intake-date/add', ['as' => 'add-courses-intake-date',         'uses' => 'Frontend\CourseController@coursesIntakeDateAdd']);
    Route::match(['get', 'post'], 'courses-intake-date/delete/{id}', ['as' => 'delete-courses-intake-date',      'uses' => 'Frontend\CourseController@coursesIntakeDateDelete']);
    Route::match(['get', 'post'], 'courses-intake-date/status/{id}', ['as' => 'status-courses-intake-date',      'uses' => 'Frontend\CourseController@coursesIntakeDateStatus']);

    Route::match(['get', 'post'], 'courses-intake-date/get_date', ['as' => 'get_date',                        'uses' => 'Frontend\CourseController@getAllDate']);
    Route::match(['get', 'post'], 'courses-intake-date/get_course_type', ['as' => 'get_course_type',                 'uses' => 'Frontend\CourseController@getCourseFromType']);

    Route::match(['get', 'post'], 'training-plan-template', ['as' => 'training-plan-template',          'uses' => 'Frontend\TrainingPlanController@manageTemplate'])
        ->middleware(CurrentRole::Can([
            Access::TRAINING_PLAN_TEMPLATE_ACCESS,
        ]));
    Route::match(['get', 'post'], 'delete-template/{id}', ['as' => 'delete-template',                 'uses' => 'Frontend\TrainingPlanController@deleteTemplate']);
    Route::match(['get', 'post'], 'manageTemplate/ajaxAction', ['as' => 'manageTemplate/ajaxAction',       'uses' => 'Frontend\TrainingPlanController@ajaxAction']);

    // call all ajaxcall in this route
    Route::match(['get', 'post'], 'courses/ajaxAction', ['as' => 'courses/ajaxAction',              'uses' => 'Frontend\CourseController@ajaxAction']);
    Route::match(['get', 'post'], 'courses-upfront-fee-list', ['as' => 'courses-upfront-fee-list',        'uses' => 'Frontend\CourseController@coursesUpfrontFeeList']);
    Route::match(['get', 'post'], 'courses-upfront-fee/add', ['as' => 'add-courses-upfront-fee',         'uses' => 'Frontend\CourseController@coursesUpfrontFeeAdd']);
    Route::match(['get', 'post'], 'courses-upfront-fee/edit', ['as' => 'edit-courses-upfront-fee',        'uses' => 'Frontend\CourseController@coursesUpfrontFeeEdit']);
    Route::match(['get', 'post'], 'courses-upfront-fee/delete/{id}', ['as' => 'delete-courses-upfront-fee',      'uses' => 'Frontend\CourseController@coursesUpfrontFeeDelete']);

    Route::match(['get', 'post'], 'college-enrollment-fees-view', ['as' => 'college-enrollment-fees-list',    'uses' => 'Frontend\CourseController@collegeEnrollmentFeesView']);
    Route::match(['get', 'post'], 'college-enrollment-fees-add', ['as' => 'college-enrollment-fees',         'uses' => 'Frontend\CourseController@collegeEnrollmentFeesAdd']);
    Route::match(['get', 'post'], 'college-enrollment-fees-delete/delete/{id}', ['as' => 'college-enrollment-fees-delete', 'uses' => 'Frontend\CourseController@collegeEnrollmentFeesDelete']);
    Route::match(['get', 'post'], 'edit-courses-enrollment-fee/edit', ['as' => 'edit-courses-enrollment-fee',     'uses' => 'Frontend\CourseController@coursesEnrollmentFeeEdit']);

    Route::match(['get', 'post'], 'courses', ['as' => 'courses',                         'uses' => 'Frontend\CourseController@Courses'])
        ->middleware(CurrentRole::Can([
            Access::COURSES_ACCESS,
            Access::MANAGE_COURSES_ACCESS,
        ]));
    Route::match(['get', 'post'], 'courses/add/{id?}', ['as' => 'add-courses',                     'uses' => 'Frontend\CourseController@CoursesAdd'])
        ->middleware(CurrentRole::Can([
            Access::COURSES_ACCESS,
            Access::MANAGE_COURSES_ACCESS,
        ]));
    Route::match(['get', 'post'], 'edit-courses/{id}', ['as' => 'edit-courses',                    'uses' => 'Frontend\CourseController@CoursesEdit']);
    Route::match(['get', 'post'], 'delete-courses/{id}', ['as' => 'delete-courses',                  'uses' => 'Frontend\CourseController@courseDelete'])
        ->middleware(CurrentRole::Can([
            Access::COURSES_ACCESS,
            Access::MANAGE_COURSES_ACCESS,
        ]));
    Route::match(['get', 'post'], 'course-subject', ['as' => 'course-subject',                  'uses' => 'Frontend\CourseSubjectController@courseSubject']);
    Route::match(['get', 'post'], 'delete-course-subject/{id}', ['as' => 'delete-course-subject',           'uses' => 'Frontend\CourseSubjectController@deleteCourseSubject']);
    Route::match(['get', 'post'], 'manage-course-subject/{id}', ['as' => 'manage-course-subject',           'uses' => 'Frontend\CourseController@manageCourseSubject']);
    Route::match(['get', 'post'], 'course-structure/{id}', ['as' => 'course-structure',                'uses' => 'Frontend\CourseController@CourseStructure']);
    Route::match(['get', 'post'], 'course-structure-excel-export/{id}', ['as' => 'course-structure-excel-export',   'uses' => 'Frontend\CourseController@CourseStructureExports']);

    Route::match(['get', 'post'], 'highered-course-additional-info/{id}', ['as' => 'highered-course-additional-info',  'uses' => 'Frontend\CourseController@higherEdCourseManage']);

    Route::match(['get', 'post'], 'courseSubject/ajaxAction', ['as' => 'courseSubject/ajaxAction',                      'uses' => 'Frontend\CourseSubjectController@ajaxAction']);
    Route::match(['get', 'post'], 'faculty/add/{id?}', ['as' => 'add-faculty',                     'uses' => 'Frontend\CourseController@facultyAdd']);
    Route::match(['get', 'post'], 'delete-faculty/{id}', ['as' => 'delete-faculty',                  'uses' => 'Frontend\CourseController@facultyDelete']);
    Route::match(['get', 'post'], 'department/add/{id?}', ['as' => 'add-department',                  'uses' => 'Frontend\CourseController@departmentAdd']);
    Route::match(['get', 'post'], 'delete-department/{id}', ['as' => 'delete-department',               'uses' => 'Frontend\CourseController@departmentDelete']);

    // Left-sidebar-students
    Route::match(['get', 'post'], 'apply-online/{id?}', ['as' => 'apply-online',                    'uses' => 'CommonController@applyOnline']);
    Route::match(['get', 'post'], 'common/ajaxAction', ['as' => 'common-ajaxAction',                      'uses' => 'CommonController@ajaxAction']);
    Route::match(['get', 'post'], 'application-form/{id?}', ['as' => 'application-form',                'uses' => 'CommonController@applicationForm']);
    Route::match(['get', 'post'], 'downloadApplicationFormFile/{id}', ['as' => 'downloadApplicationFormFile',     'uses' => 'CommonController@downloadApplicationFormFile']);

    // Route::match(['get', 'post'], 'apply-online-step2/{id}', ['as' => 'apply-online-step2', 'uses' => 'Frontend\ApplicationController@applyOnlineStep2']);
    // Route::match(['get', 'post'], 'apply-online-step3/{id}/{offer?}', ['as' => 'apply-online-step3', 'uses' => 'Frontend\ApplicationController@applyOnlineStep3']);
    // Route::match(['get', 'post'], 'apply-online-step4/{id}', ['as' => 'apply-online-step4', 'uses' => 'Frontend\ApplicationController@applyOnlineStep4']);
    // Route::match(['get', 'post'], 'apply-online-confirmation/{id}', ['as' => 'apply-online-confirmation', 'uses' => 'Frontend\ApplicationController@applyOnlineConfirmation']);
    // Route::match(['get', 'post'], 'apply-online-final-step/{id}', ['as' => 'apply-online-final-step', 'uses' => 'Frontend\ApplicationController@applyOnlineFinalStep']);
    // Route::match(['get', 'post'], 'apply-online-step-thank-you/{id}', ['as' => 'apply-online-step-thank-you', 'uses' => 'Frontend\ApplicationController@applyOnlineThankYou']);

    // Route::match(['get', 'post'], 'student-online-application/{access_request}', ['as' => 'student-online-application', 'uses' => 'Frontend\ApplicationController@studentOnlineApplication']);

    Route::match(['get', 'post'], 'student-online-application', ['as' => 'student-online-application',          'uses' => 'Frontend\ApplicationController@studentNewOnlineApplication']);
    Route::match(['get', 'post'], 'student-continue-online-application', ['as' => 'student-continue-online-application', 'uses' => 'Frontend\ApplicationController@studentCountinueOnlineApplication']);

    Route::match(['get', 'post'], 'student-saved-application/{id}/{step}', ['as' => 'student-saved-application',           'uses' => 'Frontend\ApplicationController@studentSavedApplication']);
    Route::match(['get', 'post'], 'delete-student-service-request/{id}', ['as' => 'delete-student-service-request',      'uses' => 'Frontend\ApplicationController@deleteStudentServiceRequest']);

    Route::match(['get', 'post'], 'student-profile-edit/{id?}', ['as' => 'student-profile-edit',                'uses' => 'CommonController@applyOnline']);

    Route::match(['get', 'post'], 'student-profile-saved-application/{id}/{step}', ['as' => 'student-profile-saved-application', 'uses' => 'Frontend\StudentProfileController@studentSavedApplication']);
    Route::match(['get', 'post'], 'student-profile-step2/{id}', ['as' => 'student-profile-step2',               'uses' => 'Frontend\StudentProfileController@studentProfileStep2']);
    Route::match(['get', 'post'], 'student-profile-step3/{id}', ['as' => 'student-profile-step3',               'uses' => 'Frontend\StudentProfileController@studentProfileStep4']);
    Route::match(['get', 'post'], 'apply-short-course/{id?}', ['as' => 'apply-short-course',                  'uses' => 'Frontend\ApplyShortCourseController@applyShortCourse'])
        ->middleware(CurrentRole::Can([
            Access::APPLICATION_ACCESS,
            Access::APPLY_SHORT_COURSE_ACCESS,
        ]));
    Route::match(['get', 'post'], 'apply-short-course-step2/{id?}', ['as' => 'apply-short-course-step2',            'uses' => 'Frontend\ApplyShortCourseController@applyShortCourseStep2']);
    Route::match(['get', 'post'], 'apply-short-course-step-thank-you/{id}', ['as' => 'apply-short-course-step-thank-you',   'uses' => 'Frontend\ApplyShortCourseController@applyShortCourseFinalStep'])
        ->middleware(CurrentRole::Can([
            Access::APPLICATION_ACCESS,
            Access::APPLY_SHORT_COURSE_ACCESS,
        ]));
    Route::match(['get', 'post'], 'delete-stud-short-course/{student_id}/{id}/{flag}/{course_id}', ['as' => 'delete-stud-short-course', 'uses' => 'Frontend\ApplyShortCourseController@deleteStudentShortCourse']);

    // Route::get('get_collage_course/{id}', ['as' => 'get_collage_course', 'uses' => 'Frontend\StudentsController@getCollageCourse']);
    // Route::get('get_collage_enrolmentFees/{id}', ['as' => 'get_collage_course', 'uses' => 'Frontend\StudentsController@getCollageEnrolmentFees']);
    Route::match(['get', 'post'], 'student/ajaxAction', ['as' => 'student/ajaxAction',                          'uses' => 'Frontend\ApplicationController@ajaxAction']);
    Route::match(['get', 'post'], 'un-complete-application', ['as' => 'un-complete-application',             'uses' => 'Frontend\ApplicationController@uncompleteApplication']);
    Route::match(['get', 'post'], 'search-students', ['as' => 'search-students',                     'uses' => 'Frontend\StudentsController@searchStudents']);
    Route::match(['get', 'post'], 'export-student-list/{courseStatus?}', ['as' => 'export-student-list',                 'uses' => 'Frontend\StudentsController@exportStudentList']);
    Route::match(['get', 'post'], 'student-profile/{id}', ['as' => 'student-profile',                     'uses' => 'Frontend\StudentsController@studentProfile']);
    // Route::match(['get', 'post'], 'student-data-view/{id}',                 ['as' => 'student-data-view',                   'uses' => 'Frontend\StudentsController@studentDataView']);
    Route::match(['get', 'post'], 'delete-students/{id}', ['as' => 'delete-students',                     'uses' => 'Frontend\StudentsController@deleteStudents']);
    Route::match(['get', 'post'], 'student-profile-send-mail/{id}', ['as' => 'student-profile-send-mail',           'uses' => 'Frontend\StudentsController@studentProfileSendMail']);
    Route::match(['get', 'post'], 'student-profile-send-mail-receipt/{id}', ['as' => 'student-profile-send-mail-receipt',   'uses' => 'Frontend\StudentsController@studentProfileSendMailReceipt']);
    Route::match(['get', 'post'], 'student-send-letter/{id}', ['as' => 'student-send-letter',                 'uses' => 'Frontend\StudentsController@studentSendLetter']);
    Route::match(['get', 'post'], 'students-send-letter-pdf/{id}/{editor}', ['as' => 'students-send-letter-pdf',            'uses' => 'Frontend\StudentsController@studentSendLetterPdf']);
    Route::match(['get', 'post'], 'student-edit-service-information/{student_id}/{offer_id}', ['as' => 'student-edit-service-information',  'uses' => 'Frontend\StudentsServicesFeeController@studentEditServiceInformation']);
    Route::match(['get', 'post'], 'student-enroll-subject/{student_id}/{course_id}', ['as' => 'student-enroll-subject',            'uses' => 'Frontend\StudentsServicesFeeController@studentEnrollSubject']);
    Route::match(['get', 'post'], 'student-document-upload/{student_id}/{parent_id}', ['as' => 'student-document-upload',           'uses' => 'Frontend\StudentsController@studentDocumentUpload']);
    Route::match(['get', 'post'], 'edit-student-uploaded-document', ['as' => 'edit-student-uploaded-document',    'uses' => 'Frontend\StudentsController@editStudentDocumentDirecory']);
    Route::match(['get', 'post'], 'edit-student-document-directory', ['as' => 'edit-student-document-directory',   'uses' => 'Frontend\StudentsController@editStudentDocumentDirecory']);
    Route::match(['get', 'post'], 'delete-student-uploaded-file/{student_id}/{primary_id}/{document_id}', ['as' => 'delete-student-uploaded-file', 'uses' => 'Frontend\StudentsController@deleteUploadedFile']);
    Route::match(['get', 'post'], 'downloadStudentFile/{studentId}/{primaryId}', ['as' => 'downloadStudentFile',               'uses' => 'Frontend\StudentsController@downloadstudentFile']);

    Route::match(['get', 'post'], 'student-course-information/{id}', ['as' => 'student-course-information',          'uses' => 'Frontend\StudentsCourseController@studentCourseInformation']);
    Route::match(['get', 'post'], 'student-course-checklist/{id}', ['as' => 'student-course-checklist',            'uses' => 'Frontend\StudentsCourseController@studentCourseChecklist']);
    Route::match(['get', 'post'], 'student-course-edit/{id}', ['as' => 'student-course-edit',                 'uses' => 'Frontend\StudentsCourseController@studentCourseEdit']);
    Route::match(['get', 'post'], 'student-course-add/{id}', ['as' => 'student-course-add',                  'uses' => 'Frontend\StudentsCourseController@studentCourseAdd']);
    Route::match(['get', 'post'], 'student-send-sms/{id}', ['as' => 'student-send-sms',                    'uses' => 'Frontend\StudentsController@studentSendSms']);
    Route::match(['get', 'post'], 'view-academic-summary/{studentId}/{courseId}', ['as' => 'view-academic-summary',         'uses' => 'Frontend\StudentsController@viewAcademicSummary']);
    Route::match(['get', 'post'], 'student-profile-academic/ajaxAction', ['as' => 'student-profile-academic/ajaxAction', 'uses' => 'Frontend\StudentsController@ajaxAction']);
    Route::match(['get', 'post'], 'student-subject-enrollment/{id}', ['as' => 'student-subject-enrollment',          'uses' => 'Frontend\StudentResultController@studentSubjectEnrollment']);
    Route::match(['get', 'post'], 'export-excel-subject-enrollment/{student_id}/{course_id}', ['as' => 'export-excel-subject-enrollment', 'uses' => 'Frontend\StudentResultController@exportExcelSubjectEnrollment']);
    Route::match(['get', 'post'], 'student-subject-enrollment-update', ['as' => 'student-subject-enrollment-update',   'uses' => 'Frontend\StudentResultController@studentSubjectEnrollmentUpdate']);
    Route::match(['get', 'post'], 'delete-student-subject-enrollment/{id}', ['as' => 'delete-student-subject-enrollment',   'uses' => 'Frontend\StudentResultController@deleteStudentSubjectEnrollment']);
    Route::match(['get', 'post'], 'delete-student-enrollment/{id}', ['as' => 'delete-student-enrollment',           'uses' => 'Frontend\StudentResultController@deleteStudentCourse']);
    Route::match(['get', 'post'], 'edit-student-result/{id}/{course_id}', ['as' => 'edit-student-result',                 'uses' => 'Frontend\StudentResultController@editStudentResult']);
    Route::match(['get', 'post'], 'student-unit-enrollment/{id}', ['as' => 'student-unit-enrollment',                       'uses' => 'Frontend\StudentResultController@studentUnitEnrollment']);
    Route::match(['get', 'post'], 'student-flexible-timetable/ajaxAction', ['as' => 'student-flexible-timetable/ajaxAction',          'uses' => 'Frontend\StudentTimetableController@ajaxAction']);
    Route::match(['get', 'post'], 'student-flexible-timetable/{id}', ['as' => 'student-flexible-timetable',          'uses' => 'Frontend\StudentTimetableController@studentFlexibleTimetable']);
    Route::match(['get', 'post'], 'student-course-offer-checklist/{id}', ['as' => 'student-course-offer-checklist',      'uses' => 'Frontend\StudentsCourseController@studentOfferChecklistInCourse']);

    Route::match(['get', 'post'], 'edit-student-unit-enrollment/{id}', ['as' => 'edit-student-unit-enrollment',        'uses' => 'Frontend\StudentResultController@editSudentUnitEnrollment']);
    Route::match(['get', 'post'], 'delete-student-unit-enrollment/{id}', ['as' => 'delete-student-unit-enrollment/{id}', 'uses' => 'Frontend\StudentResultController@deleteSudentUnitEnrollment']);
    Route::match(['get', 'post'], 'edit-student-unit-activity/{id}', ['as' => 'edit-student-unit-activity',          'uses' => 'Frontend\StudentResultController@editStudentUnitActivity']);
    Route::match(['get', 'post'], 'student-assessment-details/{id}', ['as' => 'student-assessment-details',          'uses' => 'Frontend\StudentResultController@studentAssessmentDetails']);
    Route::match(['get', 'post'], 'student-certificate-register/{id}', ['as' => 'student-certificate-register',        'uses' => 'Frontend\StudentResultController@studentCertificateRegister']);
    Route::match(['get', 'post'], 'delete-student-certificate-register/{id}', ['as' => 'delete-student-certificate-register', 'uses' => 'Frontend\StudentResultController@deleteStudentCertificateRegister']);
    Route::match(['get', 'post'], 'upload-student-certificate-document', ['as' => 'upload-student-certificate-document', 'uses' => 'Frontend\StudentResultController@uploadStudentCertificateDocument']);
    Route::match(['get', 'post'], 'download-student-certificate/{id}', ['as' => 'download-student-certificate',        'uses' => 'Frontend\StudentResultController@downloadStudentCertificate']);
    Route::match(['get', 'post'], 'student-subject/ajaxAction', ['as' => 'student-subject/ajaxAction',                          'uses' => 'Frontend\StudentResultController@ajaxAction']);
    Route::match(['get', 'post'], 'vet-certificate-pdf/{type}/{id}', ['as' => 'vet-certificate-pdf',                 'uses' => 'Frontend\StudentResultController@generateVetPdf']);
    Route::match(['get', 'post'], 'elicos-certificate-pdf/{type}/{id}', ['as' => 'elicos-certificate-pdf',              'uses' => 'Frontend\StudentResultController@generateElicosPdf']);

    Route::match(['get', 'post'], 'tcsi-student-unit-enrollment/{id}/{course_id}', ['as' => 'tcsi-student-unit-enrollment', 'uses' => 'Frontend\StudentResultController@tcsiStudentUnitEnrollment']);

    Route::match(['get', 'post'], 'student-account/ajaxAction', ['as' => 'student-account-ajaxAction',                          'uses' => 'Frontend\StudentAccountController@ajaxAction']);
    Route::match(['get', 'post'], 'studentsController/ajaxAction', ['as' => 'studentsController/ajaxAction',       'uses' => 'Frontend\StudentsController@ajaxAction']);
    Route::match(['get', 'post'], 'student-course/ajaxAction', ['as' => 'student-course/ajaxAction',                          'uses' => 'Frontend\StudentsCourseController@ajaxAction']);

    Route::match(['get', 'post'], 'delete-students-course-offer/{primaryId}', ['as' => 'delete-students-course-offer',    'uses' => 'Frontend\OfferManageController@deleteStudentsCourseOffer']);
    Route::match(['get', 'post'], 'student-course-rejected/{primaryId}', ['as' => 'student-course-rejected',         'uses' => 'Frontend\OfferManageController@studentCourseRejected']);
    Route::match(['get', 'post'], 'offer-generate-invoice-view/{studenId}', ['as' => 'offer-generate-invoice-view',     'uses' => 'Frontend\OfferManageController@offerGenerateInvoiceView']);
    Route::match(['get', 'post'], 'offer-generate-invoice', ['as' => 'offer-generate-invoice',          'uses' => 'Frontend\OfferManageController@offerGenerateInvoice']);
    Route::match(['get', 'post'], 'student-offer-letter/{courseID}/{id}/{studentCourseId}', ['as' => 'student-offer-letter',            'uses' => 'Frontend\StudentsCourseController@offerLetterPreview']);
    Route::match(['get', 'post'], 'student-offer-letter-pdf/{courseID}/{id}/{studentCourseId}/{download?}', ['as' => 'student-offer-letter-pdf',        'uses' => 'Frontend\StudentsCourseController@offerLetterPdf']);
    Route::match(['get', 'post'], 'domestic-student-offer-letter/{courseID}/{id}/{studentCourseId}', ['as' => 'domestic-student-offer-letter',   'uses' => 'Frontend\StudentsCourseController@domesticStudentOfferLetterPreview']);
    Route::match(['get', 'post'], 'domestic-student-offer-letter-pdf/{courseID}/{id}/{studentCourseID}/{download?}', ['as' => 'domestic-student-offer-letter-pdf', 'uses' => 'Frontend\StudentsCourseController@domesticOfferLetterPdf']);
    Route::match(['get', 'post'], 'student-invoice-pdf/{courseID}/{id}/{studentCourseId?}', ['as' => 'student-invoice-pdf',             'uses' => 'Frontend\StudentsCourseController@studentInvoicePdf']);
    Route::match(['get', 'post'], 'agent-invoice-pdf/{courseID}/{id}', ['as' => 'agent-invoice-pdf',               'uses' => 'Frontend\StudentsCourseController@agentInvoicePdf']);

    Route::match(['get', 'post'], 'student-training-plan/{student_id}/{id}', ['as' => 'student-training-plan',               'uses' => 'Frontend\CourseTrainingController@addCourseTrainingPlanInfo']);
    Route::match(['get', 'post'], 'delete-course-training-plan/{course_id}/{student_id}/{id}', ['as' => 'delete-course-training-plan',         'uses' => 'Frontend\CourseTrainingController@deleteCourseTrainingPlanInfo']);
    Route::match(['get', 'post'], 'delete-student-training-plan/{student_id}/{id}', ['as' => 'delete-student-training-plan',        'uses' => 'Frontend\CourseTrainingController@deleteStudentTrainingPlanInfo']);
    Route::match(['get', 'post'], 'traineeship-activity-log/{id}', ['as' => 'traineeship-activity-log',            'uses' => 'Frontend\CourseTrainingController@addTraineeshipActivityLog']);
    Route::match(['get', 'post'], 'traineeship-unit-assessment/{id}', ['as' => 'traineeship-unit-assessment',         'uses' => 'Frontend\CourseTrainingController@traineeshipUnitAssessment']);
    Route::match(['get', 'post'], 'edit-traineeship-activity-log/{training_plan_id}/{id}', ['as' => 'edit-traineeship-activity-log',       'uses' => 'Frontend\CourseTrainingController@editTraineeshipActivityLog']);
    Route::match(['get', 'post'], 'delete-traineeship-activity-log/{id}/{trainingPlanId}', ['as' => 'delete-traineeship-activity-log',     'uses' => 'Frontend\CourseTrainingController@deleteTraineeshipActivityLog']);
    Route::match(['get', 'post'], 'delete-traineeship-unit-assessment/{training_plan_id}/{id}', ['as' => 'delete-traineeship-unit-assessment',  'uses' => 'Frontend\CourseTrainingController@deleteTraineeshipUnitAssessment']);
    Route::match(['get', 'post'], 'courseTrainingPlan/ajaxAction', ['as' => 'courseTrainingPlan/ajaxAction',                          'uses' => 'Frontend\CourseTrainingController@ajaxAction']);

    Route::match(['get', 'post'], 'student-training-plan-doc/{id}', ['as' => 'student-training-plan-doc',                   'uses' => 'Frontend\CourseTrainingController@generateTrainingPlanDOC']);
    Route::match(['get', 'post'], 'student-training-plan-pdf/{id}', ['as' => 'student-training-plan-pdf',                   'uses' => 'Frontend\CourseTrainingController@generateTrainingPlanPDF']);
    Route::match(['get', 'post'], 'student-training-plan-xls/{student_id}/{course_id}', ['as' => 'student-training-plan-xls',                   'uses' => 'Frontend\CourseTrainingController@generateTrainingPlanXLS']);

    Route::match(['get', 'post'], 'downloadTraineeshipFile/{id}', ['as' => 'downloadTraineeshipFile',                     'uses' => 'Frontend\CourseTrainingController@downloadFile']);

    Route::match(['get', 'post'], 'add-student-sanction/{id}', ['as' => 'add-student-sanction',                        'uses' => 'Frontend\SanctionController@addSanctionInfo']);
    Route::match(['get', 'post'], 'edit-student-sanction/{student_id}/{id}', ['as' => 'edit-student-sanction',                       'uses' => 'Frontend\SanctionController@editSanctionInfo']);
    Route::match(['get', 'post'], 'delete-student-sanction/{student_id}/{id}', ['as' => 'delete-student-sanction',                     'uses' => 'Frontend\SanctionController@deleteSanctionInfo']);

    Route::match(['get', 'post'], 'student-course-deferral/{id}', ['as' => 'student-course-deferral',                     'uses' => 'Frontend\StudentsController@addStCourseDeferral']);
    Route::match(['get', 'post'], 'edit-student-course-deferral/{student_id}/{id}', ['as' => 'edit-student-course-deferral',                'uses' => 'Frontend\StudentsController@editStCourseDeferral']);
    Route::match(['get', 'post'], 'delete-course-defer/{student_id}/{id}', ['as' => 'delete-course-defer',                         'uses' => 'Frontend\StudentsController@deleteCourseDeferDetail']);
    Route::match(['get', 'post'], 'print-course-defer/{student_id}/{id}', ['as' => 'print-course-defer',                          'uses' => 'Frontend\StudentsController@pdfCourseDeferDetail']);
    Route::match(['get', 'post'], 'pdf-course-defer-detail/{student_id}/{id}', ['as' => 'pdf-course-defer-detail',                     'uses' => 'Frontend\StudentsController@pdfCourseDeferDetail']);

    Route::match(['get', 'post'], 'student-interview/{id}', ['as' => 'student-interview',                           'uses' => 'Frontend\StudentsController@addStudentInterview']);
    Route::match(['get', 'post'], 'delete-student-interview/{student_id}/{id}', ['as' => 'delete-student-interview',                    'uses' => 'Frontend\StudentsController@deleteStudentInterview']);

    Route::match(['get', 'post'], 'student-intervention/{id}', ['as' => 'student-intervention',                        'uses' => 'Frontend\StudentsController@addStudentIntervention']);
    Route::match(['get', 'post'], 'edit-student-intervention/{student_id}/{id}', ['as' => 'edit-student-intervention',                   'uses' => 'Frontend\StudentsController@editStudentIntervention']);
    Route::match(['get', 'post'], 'student-intervention-communication-log/{student_id}/{id}', ['as' => 'student-intervention-communication-log', 'uses' => 'Frontend\StudentsController@studentInterventionCommunicationLog']);
    Route::match(['get', 'post'], 'student-compliance-intervention-communication-log/{student_id}/{id}', ['as' => 'student-compliance-intervention-communication-log', 'uses' => 'Frontend\StudentsController@studentComplianceInterventionCommunicationLog']);
    Route::match(['get', 'post'], 'delete-student-intervention/{student_id}/{id}', ['as' => 'delete-student-intervention',                 'uses' => 'Frontend\StudentsController@deleteStudentIntervention']);
    Route::match(['get', 'post'], 'student-intervention-docs/{student_id}/{id}', ['as' => 'student-intervention-docs',                   'uses' => 'Frontend\StudentsController@studentInterventionExportdDocs']);
    Route::match(['get', 'post'], 'student-intervention-pdf/{student_id}/{id}', ['as' => 'student-intervention-pdf',                   'uses' => 'Frontend\StudentsController@studentInterventionExportPdf']);
    Route::match(['get', 'post'], 'student-intervention-excel/{student_id}/{id}', ['as' => 'student-intervention-excel',                   'uses' => 'Frontend\StudentsController@studentInterventionExportExcel']);
    Route::match(['get', 'post'], 'intervention-letter/{id}', ['as' => 'intervention-letter',                         'uses' => 'Frontend\StudentsController@interventionLetter']);

    Route::match(['get', 'post'], 'student-course-communication/{id}', ['as' => 'student-course-communication',                'uses' => 'Frontend\StudentsCourseController@studentCourseCommunicationLog']);
    Route::match(['get', 'post'], 'delete-communication-log/{course_id}/{id}', ['as' => 'delete-communication-log',                    'uses' => 'Frontend\StudentsCourseController@deleteStudentCourseCommunicationLog']);
    Route::match(['get', 'post'], 'delete-student-course/{student_id}/{id}/{offer_id}', ['as' => 'delete-student-course',                       'uses' => 'Frontend\StudentsCourseController@deleteStudentCourse']);

    Route::match(['get', 'post'], 'student-profile-picture/{id}', ['as' => 'student-profile-picture',                     'uses' => 'Frontend\StudentsController@uploadStudentProfilePicture']);
    Route::match(['get', 'post'], 'search-student-id/{id}', ['as' => 'search-student-id',                           'uses' => 'Frontend\StudentsController@SearchStudentId']);
    Route::match(['get', 'post'], 'student-card/{id}', ['as' => 'student-card',                                'uses' => 'Frontend\StudentsController@pdfStudentCard']);

    Route::match(['get', 'post'], 'tcsi-info/{student_id}', ['as' => 'tcsi-info',                                   'uses' => 'Frontend\TcsiController@generalInfo']);
    Route::match(['get', 'post'], 'tcsi-student-course-info/{student_id}', ['as' => 'tcsi-student-course-info',                    'uses' => 'Frontend\TcsiController@studCourseInfo']);
    Route::match(['get', 'post'], 'tcsi-student-disability-info/{student_id}', ['as' => 'tcsi-student-disability-info',                'uses' => 'Frontend\TcsiController@studDisabilityInfo']);
    Route::match(['get', 'post'], 'tcsi-student-os-help/{student_id}', ['as' => 'tcsi-student-os-help',                        'uses' => 'Frontend\TcsiController@studOSHelp']);
    Route::match(['get', 'post'], 'tcsi-student-sa-help/{student_id}', ['as' => 'tcsi-student-sa-help',                        'uses' => 'Frontend\TcsiController@studSAHelp']);

    // Offer-Manage Controller
    Route::match(['get', 'post'], 'offer-manage', ['as' => 'offer-manage',                                'uses' => 'Frontend\OfferManageController@offerManage'])
        ->middleware(CurrentRole::Can([
            Access::APPLICATION_ACCESS,
            Access::MANAGE_OFFERS_ACCESS,
        ]));
    // Route::match(['get', 'post'], 'offer-manage-export',                                ['as' => 'offer-manage-export',                         'uses' => 'Frontend\OfferManageController@exportOfferManage']);

    Route::match(['get', 'post'], 'offer-letter-pdf/{courseID}/{id}/{download?}', ['as' => 'offer-letter-pdf',                            'uses' => 'Frontend\OfferManageController@offerLetterPdf']);
    Route::match(['get', 'post'], 'offer-letter-pdf-new/{courseID}/{id}/{studentCourseID}/{download?}', ['as' => 'offer-letter-pdf-new',    'uses' => 'Frontend\OfferManageController@offerLetterPdfNew']);
    Route::match(['get', 'post'], 'domestic-offer-letter-pdf/{courseID}/{id}/{studentCourseID}/{download?}', ['as' => 'domestic-offer-letter-pdf', 'uses' => 'Frontend\OfferManageController@domesticOfferLetterPdf']);
    Route::match(['get', 'post'], 'stdent-invoice-pdf/{courseID}/{id}/{download?}', ['as' => 'stdent-invoice-pdf',                          'uses' => 'Frontend\OfferManageController@studentInvoicePdf']);
    Route::match(['get', 'post'], 'download-combined-invoice/{courseID}/{id}/{download?}', ['as' => 'download-combined-invoice',                          'uses' => 'Frontend\OfferManageController@downloadStudentInvoicePdf']);
    Route::match(['get', 'post'], 'agent-payment-invoice-pdf/{courseID}/{id}', ['as' => 'agent-payment-invoice-pdf',                   'uses' => 'Frontend\OfferManageController@agentPaymentInvoicePdf']);
    Route::match(['get', 'post'], 'offer-letter/{courseID}/{id}/{studentCourseID}', ['as' => 'offer-letter',                                'uses' => 'Frontend\OfferManageController@offerLetter']);
    Route::match(['get', 'post'], 'domestic-offer-letter/{courseID}/{id}/{studentCourseID}', ['as' => 'domestic-offer-letter',                  'uses' => 'Frontend\OfferManageController@domesticOfferLetter']);
    Route::match(['get', 'post'], 'offer-manage-confirmation/{id}', ['as' => 'offer-manage-confirmation',                   'uses' => 'CommonController@applyOnline']);
    Route::match(['get', 'post'], 'offer-manage-edit/{id}/{studentId}', ['as' => 'offer-manage-edit',                           'uses' => 'Frontend\OfferManageController@offerManageEdit']);
    Route::match(['get', 'post'], 'offer-manage-add-course/{id}', ['as' => 'offer-manage-add-course',                     'uses' => 'Frontend\OfferManageController@addCourse']);

    Route::match(['get', 'post'], 'offer-manage-checklist/{id}', ['as' => 'offer-manage-checklist',                      'uses' => 'Frontend\OfferManageController@offerManageChecklist']);
    Route::match(['get', 'post'], 'offer-manage-document/{id}/{primaryStudentId}', ['as' => 'offer-manage-document',                       'uses' => 'Frontend\OfferManageController@listStudentDocument']);
    //            Route::match(['get', 'post'], 'offer-student-document/{offer_id}/{primary_id}', ['as' => 'offer-student-document', 'uses' => 'Frontend\OfferManageController@offerStudentDocument']);
    Route::match(['get', 'post'], 'list-student-document/{student_id}/{parent_id}', ['as' => 'list-student-document',                       'uses' => 'Frontend\OfferManageController@listStudentDocument']);
    Route::match(['get', 'post'], 'edit-student-document-directory', ['as' => 'edit-student-document-directory',             'uses' => 'Frontend\OfferManageController@editStudentDocumentDirecory']);
    Route::match(['get', 'post'], 'delete-offer-student-file/{student_id}/{primary_id}', ['as' => 'delete-offer-student-file',                   'uses' => 'Frontend\OfferManageController@deleteUploadedFile']);
    Route::match(['get', 'post'], 'offer-manage-send-mail/{id}', ['as' => 'offer-manage-send-mail',                      'uses' => 'Frontend\OfferManageController@offerManageSendMail']);
    Route::match(['get', 'post'], 'delete-offer-manage-document/{id}', ['as' => 'delete-offer-manage-document',                'uses' => 'Frontend\OfferManageController@deleteOfferManageDoc']);

    Route::match(['get', 'post'], 'delete-offer-manage-checklist-document/{id}', ['as' => 'delete-offer-manage-checklist-document',      'uses' => 'Frontend\OfferManageController@deleteOfferManagecheckListDocument']);
    Route::match(['get', 'post'], 'delete-student-course-checklist-document/{id}', ['as' => 'delete-student-course-checklist-document',    'uses' => 'Frontend\OfferManageController@deleteStudentCourcecheckListDocument']);

    Route::match(['get', 'post'], 'offer-manage-communication-log/{id}', ['as' => 'offer-manage-communication-log',      'uses' => 'Frontend\OfferManageController@offerManageCommunicationLog']);
    Route::match(['get', 'post'], 'offer-manage-upfront-fee-schedule/{id}/{studentCourseId}', ['as' => 'offer-manage-upfront-fee-schedule',   'uses' => 'Frontend\OfferManageController@offerManageUpfrontFeeSchedule']);
    Route::match(['get', 'post'], 'offer-manage-upfront-fee-schedule-new/{studentCourseId}', ['as' => 'offer-manage-upfront-fee-schedule-new', 'uses' => 'Frontend\OfferManageController@offerManageUpfrontFeeScheduleNew']);
    Route::match(['get', 'post'], 'delete-upfront-fee-schedule/{id}', ['as' => 'delete-upfront-fee-schedule',         'uses' => 'Frontend\OfferManageController@deleteUpfrontFeeSchedule']);
    Route::match(['get', 'post'], 'offerManage/ajaxAction', ['as' => 'offerManage/ajaxAction',              'uses' => 'Frontend\OfferManageController@ajaxAction']);
    Route::match(['get', 'post'], 'offerManage/generate-studentID/{stud_id}/{generated_id}/{primary_id}', ['as' => 'offerManage-generate-studentID',                'uses' => 'Frontend\OfferManageController@generateNewStudentID']);
    Route::match(['get', 'post'], 'offerManage/reserve-studentID/{stud_id}/{reserve_id}', ['as' => 'offerManage-reserve-studentID',                          'uses' => 'Frontend\OfferManageController@reserveNewStudentID']);

    Route::match(['get', 'post'], 'offer-mail', ['as' => 'offer-mail',                          'uses' => 'Frontend\OfferMailController@offerMail'])
        ->middleware(CurrentRole::Can([
            Access::APPLICATION_ACCESS,
            Access::OFFER_MAILING_LIST_ACCESS,
        ]));
    Route::match(['get', 'post'], 'mailing/ajaxAction', ['as' => 'mailing/ajaxAction',                  'uses' => 'Frontend\OfferMailController@ajaxAction']);
    Route::match(['get', 'post'], 'delete-stud-course/{student_id}/{id}/{flag}/{course_id}', ['as' => 'delete-stud-course',                  'uses' => 'Frontend\OfferManageController@deleteStudentCourse']);

    // Left-sidebar-agent
    Route::match(['get', 'post'], 'add-agent', ['as' => 'add-agent',                       'uses' => 'Frontend\AgentController@addAgent'])
        ->middleware(CurrentRole::Can([
            Access::ADD_AGENT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'edit-agent/{id}', ['as' => 'edit-agent',                      'uses' => 'Frontend\AgentController@editAgent']);
    Route::match(['get', 'post'], 'view-agent-list', ['as' => 'view-agent-list',                 'uses' => 'Frontend\AgentController@viewAgentList'])
        ->middleware(CurrentRole::Can([
            Access::AGENT_LIST_ACCESS,
        ]));
    Route::match(['get', 'post'], 'delete-agent/{id}', ['as' => 'delete-agent',                    'uses' => 'Frontend\AgentController@deleteAgentList'])
        ->middleware(CurrentRole::Can([
            Access::AGENT_LIST_ACCESS,
        ]));
    Route::match(['get', 'post'], 'agent/ajaxAction', ['as' => 'agent-ajaxAction',                'uses' => 'Frontend\AgentController@ajaxAction']);
    Route::match(['get', 'post'], 'agent-email/ajaxAction', ['as' => 'agent-email-ajaxAction',          'uses' => 'Frontend\AgentController@ajaxAction']);

    Route::match(['get', 'post'], 'agent-commission-add', ['as' => 'agent-commission-add',            'uses' => 'Frontend\AgentCommissionController@addAgentCommission'])
        ->middleware(CurrentRole::Can([
            Access::AGENT_COMMISSION_ACCESS,
        ]));
    Route::match(['get', 'post'], 'delete-agentCommission/{id}', ['as' => 'delete-agentCommission',          'uses' => 'Frontend\AgentCommissionController@deleteAgentCommission']);
    Route::match(['get', 'post'], 'agentCommission/ajaxAction', ['as' => 'agentCommission-ajaxAction',      'uses' => 'Frontend\AgentCommissionController@ajaxAction']);
    Route::match(['get', 'post'], 'agent-id/ajaxAction', ['as' => 'agent-id-ajaxAction',             'uses' => 'Frontend\AgentCommissionController@ajaxAction']);
    Route::match(['get', 'post'], 'edit-agent-commission/{id}', ['as' => 'edit-agent-commission',           'uses' => 'Frontend\AgentCommissionController@editAgentCommission']);

    Route::match(['get', 'post'], 'agent-communication-edit-data/{id}', ['as' => 'agent-communication-edit-data',   'uses' => 'Frontend\AgentCommissionController@editAgentCommunicationLogData']);
    Route::match(['get', 'post'], 'agent-communication-add-data/{id}', ['as' => 'agent-communication-add-data',    'uses' => 'Frontend\AgentCommissionController@addAgentCommunicationLogData']);
    Route::match(['get', 'post'], 'agent-communication-add', ['as' => 'agent-communication-add',         'uses' => 'Frontend\AgentCommissionController@addAgentCommunicationLog']);
    Route::match(['get', 'post'], 'agent-communication-list', ['as' => 'agent-communication-list',        'uses' => 'Frontend\AgentCommissionController@listAgentCommunicationLog'])
        ->middleware(CurrentRole::Can([
            Access::AGENT_COMMUNICATION_LOG_ACCESS,
        ]));
    Route::match(['get', 'post'], 'agentCommunication/ajaxAction', ['as' => 'agentCommunication-ajaxAction',   'uses' => 'Frontend\AgentCommissionController@ajaxAction']);
    Route::match(['get', 'post'], 'view-agent-payment-list', ['as' => 'view-agent-payment-list',         'uses' => 'Frontend\AgentController@viewAgentPaymentList'])
        ->middleware(CurrentRole::Can([
            Access::AGENT_PAYMENT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'agent-payment/ajaxAction', ['as' => 'agent-payment-ajaxAction',        'uses' => 'Frontend\AgentPaymentController@ajaxAction']);

    Route::match(['get', 'post'], 'list-agent-document/{id}/{agentID}/{flag}', ['as' => 'list-agent-document',             'uses' => 'Frontend\AgentCommissionController@listAgentDocument']);
    Route::match(['get', 'post'], 'list-agent-materials/{folder}/{id}/{flag}', ['as' => 'list-agent-materials',            'uses' => 'Frontend\AgentCommissionController@listAgentMaterials']);
    Route::match(['get', 'post'], 'delete-agentmaterials/{id}', ['as' => 'delete-agentmaterials',           'uses' => 'Frontend\AgentCommissionController@deleteAgentMaterials']);

    // Email Template Routes
    Route::match(['get', 'post'], 'add-email-template', ['as' => 'add-email-template',              'uses' => 'Frontend\EmailTemplateController@addEmailTemplate']);
    Route::match(['get', 'post'], 'edit-email-template/{id}', ['as' => 'edit-email-template',             'uses' => 'Frontend\EmailTemplateController@editEmailTemplate']);
    Route::match(['get', 'post'], 'view-email-template-list', ['as' => 'view-email-template-list',        'uses' => 'Frontend\EmailTemplateController@viewEmailTemplateList']);
    Route::match(['get', 'post'], 'edit-email-template-status/{id}/{status}', ['as' => 'edit-email-template-status',      'uses' => 'Frontend\EmailTemplateController@editEmailTemplateStatus']);
    Route::match(['get', 'post'], 'delete-email-template/{id}', ['as' => 'delete-email-template',           'uses' => 'Frontend\EmailTemplateController@deleteEmailTemplate']);
    Route::match(['get', 'post'], 'emailTemplate/ajaxAction', ['as' => 'emailTemplate-ajaxAction',        'uses' => 'Frontend\EmailTemplateController@ajaxAction']);
    Route::match(['get', 'post'], 'agent-email-template-setting', ['as' => 'agent-email-template-setting',    'uses' => 'Frontend\EmailTemplateController@agentEmailTemplateSetting']);
    Route::match(['get', 'post'], 'letter-setting', ['as' => 'letter-setting',                  'uses' => 'Frontend\EmailTemplateController@LetterSetting']);

    // Subject Routes
    Route::match(['get', 'post'], 'add-subject', ['as' => 'add-subject',                     'uses' => 'Frontend\SubjectController@addSubject']);
    Route::match(['get', 'post'], 'edit-subject/{id}', ['as' => 'edit-subject',                    'uses' => 'Frontend\SubjectController@editSubject']);
    Route::match(['get', 'post'], 'view-subject-list', ['as' => 'view-subject-list',               'uses' => 'Frontend\SubjectController@viewSubjectList']);
    Route::match(['get', 'post'], 'delete-subject/{id}', ['as' => 'delete-subject',                  'uses' => 'Frontend\SubjectController@deleteSubject']);
    Route::match(['get', 'post'], 'subject/ajaxAction', ['as' => 'subject/ajaxAction',              'uses' => 'Frontend\SubjectController@ajaxAction']);
    Route::match(['get', 'post'], 'edit-subject-module/{id}', ['as' => 'edit-subject-module',             'uses' => 'Frontend\SubjectController@viewUnitModule']);

    Route::match(['get', 'post'], 'add-unit-module', ['as' => 'add-unit-module',                 'uses' => 'Frontend\SubjectController@addUnitModule']);
    Route::match(['get', 'post'], 'manage-unit/{id}', ['as' => 'manage-unit',                     'uses' => 'Frontend\SubjectController@addUnitModule']);
    Route::match(['get', 'post'], 'view-unit-module-list', ['as' => 'view-unit-module-list',           'uses' => 'Frontend\SubjectController@viewUnitModule']);
    Route::match(['get', 'post'], 'edit-unit-module/{id}', ['as' => 'edit-unit-module',                'uses' => 'Frontend\SubjectController@editUnitModule']);
    Route::match(['get', 'post'], 'delete-unit-module/{id}', ['as' => 'delete-unit-module',              'uses' => 'Frontend\SubjectController@deleteunitmodule']);

    Route::match(['get', 'post'], 'element-of-unit-competency/{id}', ['as' => 'element-of-unit-competency',      'uses' => 'Frontend\SubjectController@elementOfUnitCompetency']);
    Route::match(['get', 'post'], 'edit-element-competency/{id}', ['as' => 'edit-element-competency',         'uses' => 'Frontend\SubjectController@editElementCompetency']);
    Route::match(['get', 'post'], 'delete-element-competency/{id}', ['as' => 'delete-element-competency',       'uses' => 'Frontend\SubjectController@deleteElementCompetency']);

    Route::match(['get', 'post'], 'add-staff-old', ['as' => 'add-staff-old', 'uses' => 'Frontend\StaffController@addStaff_old']);
    Route::match(['get', 'post'], 'add-staff', ['as' => 'add-staff', 'uses' => 'Frontend\StaffController@addStaff']);
    Route::match(['get', 'post'], 'staff-list', ['as' => 'staff-list', 'uses' => 'Frontend\StaffController@staffView'])
        ->middleware(CurrentRole::Can([
            Access::STAFF_LIST_ACCESS,
        ]));
    Route::match(['get', 'post'], 'delete-staff/{id}', ['as' => 'delete-staff', 'uses' => 'Frontend\StaffController@deleteStaff']);
    Route::match(['get', 'post'], 'edit-staff/{id}', ['as' => 'edit-staff', 'uses' => 'Frontend\StaffController@editStaff'])
        ->middleware(CurrentRole::Can([
            Access::STAFF_LIST_ACCESS,
        ]));
    Route::match(['get', 'post'], 'mail-staff/{id}', ['as' => 'mail-staff', 'uses' => 'Frontend\StaffController@mailStaff']);
    Route::match(['get', 'post'], 'staff/ajaxAction', ['as' => 'staff-ajaxAction', 'uses' => 'Frontend\StaffController@ajaxAction']);
    Route::match(['get', 'post'], 'list-staff-materials/{folder}/{id}/{flag}', ['as' => 'list-staff-materials', 'uses' => 'Frontend\StaffController@listStaffMaterials']);
    Route::match(['get', 'post'], 'delete-staff-documents/{id}', ['as' => 'delete-staff-documents', 'uses' => 'Frontend\StaffController@deleteStaffDicuments']);
    Route::match(['get', 'post'], 'edit-staff-folder/edit', ['as' => 'edit-staff-folder', 'uses' => 'Frontend\StaffController@editStaffFolder']);

    Route::match(['get', 'post'], 'list-staff-document/{id}/{staffTeacherID}/{flag}', ['as' => 'list-staff-document', 'uses' => 'Frontend\StaffController@listStaffDocument']);
    Route::match(['get', 'post'], 'active-deactive-staff/{id}', ['as' => 'active-deactive-staff', 'uses' => 'Frontend\StaffController@activeDeactiveStaff']);
    Route::match(['get', 'post'], 'staff-communication-log/{id}/{flag}', ['as' => 'staff-communication-log', 'uses' => 'Frontend\StaffController@communicationLogStaff']);
    Route::match(['get', 'post'], 'sms-notification', ['as' => 'sms-notification', 'uses' => 'Frontend\CommunicationController@smsNotification']);
    Route::match(['get', 'post'], 'student-communication-log-view', ['as' => 'student-communication-log-view', 'uses' => 'Frontend\CommunicationController@studentCommnunicationLog']);
    Route::match(['get', 'post'], 'enable-disable-staff-communication/{id}/{flag}', ['as' => 'enable-disable-staff-communication', 'uses' => 'Frontend\StaffController@enableDisableStaffCommunicationLog']);

    // Users Section
    Route::match(['get', 'post'], 'manage-user-account', ['as' => 'manage-user-account', 'uses' => 'Frontend\UserController@searchUsers'])
        ->middleware(CurrentRole::Can([
            Access::MANAGE_USER_ACCOUNT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'users/ajaxAction', ['as' => 'users-ajaxAction', 'uses' => 'Frontend\UserController@ajaxAction']);
    Route::match(['get', 'post'], 'active-deactive-user/{id}', ['as' => 'active-deactive-user', 'uses' => 'Frontend\UserController@activeDeactiveUser']);
    Route::match(['get', 'post'], 'delete-user/{id}', ['as' => 'delete-user', 'uses' => 'Frontend\UserController@deleteUser']);
    Route::match(['get', 'post'], 'bulk-user-management', ['as' => 'bulk-user-management', 'uses' => 'Frontend\UserController@bulkUserManage'])
        ->middleware(CurrentRole::Can([
            Access::STUDENTS_USER_ACCOUNT_MANAGEMENT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'bulk-create-student-users', ['as' => 'bulk-create-student-users', 'uses' => 'Frontend\UserController@bulkCreateStudentUsers'])
        ->middleware(CurrentRole::Can([
            Access::CREATE_STUDENTS_USER_ACCOUNT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'user-activity-track', ['as' => 'user-activity-track', 'uses' => 'Frontend\UserController@userActivityTrack'])
        ->middleware(CurrentRole::Can([
            Access::IP_TRACK_ACCESS,
        ]));
    // Left-sidebar Timetable Section
    Route::match(['get', 'post'], 'manage-timetable', ['as' => 'manage-timetable', 'uses' => 'Frontend\TimetableController@manageTimetable'])
        ->middleware(CurrentRole::Can([
            Access::TIMETABLE_ACCESS,
            Access::CLASS_TIMETABLE_ACCESS,
        ]));

    /* Start New implement with different cases */
    Route::match(['get', 'post'], 'manage-assessment-group', ['as' => 'manage-assessment-group', 'uses' => 'Frontend\TimetableAssessmentGroupController@manageAssessmentGroup'])
        ->middleware(CurrentRole::Can([
            Access::TIMETABLE_ACCESS,
            Access::ASSESSMENT_GROUP_ACCESS,
        ]));
    /* End new implement with different cases */

    Route::match(['get', 'post'], 'delete-assessment-group/{id}', ['as' => 'delete-assessment-group', 'uses' => 'Frontend\TimetableAssessmentGroupController@deleteAssessmentGroup']);
    Route::match(['get', 'post'], 'edit-manage-assessment-group', ['as' => 'edit-manage-assessment-group', 'uses' => 'Frontend\TimetableAssessmentGroupController@editManageAssessmentGroup']);
    Route::match(['get', 'post'], 'assessment/ajaxAction', ['as' => 'assessment-ajaxAction', 'uses' => 'Frontend\TimetableAssessmentGroupController@ajaxAction']);
    Route::match(['get', 'post'], 'timetable-report', ['as' => 'timetable-report', 'uses' => 'Frontend\TimetableAssessmentGroupController@timetableReport'])
        ->middleware(CurrentRole::Can([
            Access::TIMETABLE_ACCESS,
            Access::TIMETABLE_REPORT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'timetable-replacement-teacher', ['as' => 'timetable-replacement-teacher', 'uses' => 'Frontend\TimetableAssessmentGroupController@timetableReplacementTeacher'])
        ->middleware(CurrentRole::Can([
            Access::TIMETABLE_ACCESS,
            Access::REPLACEMENT_TEACHER_ACCESS,
        ]));
    Route::match(['get', 'post'], 'view-timetable', ['as' => 'view-timetable', 'uses' => 'Frontend\TimetableAssessmentGroupController@viewTimetable'])
        ->middleware(CurrentRole::Can([
            Access::TIMETABLE_ACCESS,
            Access::VIEW_TIMETABLE_ACCESS,
        ]));
    Route::match(['get', 'post'], 'edit-timetable-relief-teacher/{id}', ['as' => 'edit-timetable-relief-teacher', 'uses' => 'Frontend\TimetableAssessmentGroupController@editTimetableReliefTeacher']);

    Route::match(['get', 'post'], 'attendance-list', ['as' => 'attendance-list', 'uses' => 'Frontend\PrintAttadanceController@weeklyClass']);
    Route::match(['get', 'post'], 'attendance-list-by-class', ['as' => 'attendance-list-by-class', 'uses' => 'Frontend\PrintAttadanceController@weeklyClass'])
        ->middleware(CurrentRole::Can([
            Access::TIMETABLE_ACCESS,
            Access::PRINT_ATTENDANCE_LIST_ACCESS,
        ]));
    Route::match(['get', 'post'], 'attendance-list-for-bulk-pdf', ['as' => 'attendance-list-for-bulk-pdf', 'uses' => 'Frontend\PrintAttadanceController@bulkPdf'])
        ->middleware(CurrentRole::Can([
            Access::TIMETABLE_ACCESS,
            Access::PRINT_ATTENDANCE_LIST_ACCESS,
        ]));
    Route::match(['get', 'post'], 'attendance-list-by-individual-class', ['as' => 'attendance-list-by-individual-class', 'uses' => 'Frontend\PrintAttadanceController@individualClass']);
    Route::match(['get', 'post'], 'export-student-attendance-list/{classId}/{subjectId}/{semesterId}/{termId}', ['as' => 'export-student-attendance-list', 'uses' => 'Frontend\PrintAttadanceController@studentAttendanceList']);
    Route::match(['get', 'post'], 'attendance/ajaxAction', ['as' => 'attendance-ajaxAction', 'uses' => 'Frontend\PrintAttadanceController@ajaxAction']);

    Route::match(['get', 'post'], 'timetable/ajaxAction', ['as' => 'timetable/ajaxAction', 'uses' => 'Frontend\TimetableController@ajaxAction']);
    Route::match(['get', 'post'], 'delete-timetable/{id}', ['as' => 'delete-timetable', 'uses' => 'Frontend\TimetableController@deleteTimetable']);
    Route::match(['get', 'post'], 'default-timetable', ['as' => 'default-timetable', 'uses' => 'Frontend\TimetableController@defaultTimetable']);
    Route::match(['get', 'post'], 'delete-default-timetable/{id}', ['as' => 'delete-default-timetable', 'uses' => 'Frontend\TimetableController@deleteDefaultTimetable']);
    Route::match(['get', 'post'], 'edit-class-timetable/{id}', ['as' => 'edit-class-timetable', 'uses' => 'Frontend\TimetableController@editClassTimetable']);

    // Left-sidebar Teacher Section
    Route::match(['get', 'post'], 'add-teacher', ['as' => 'add-teacher', 'uses' => 'Frontend\TeacherController@addTeacher']);
    Route::match(['get', 'post'], 'view-teacher', ['as' => 'view-teacher', 'uses' => 'Frontend\TeacherController@viewTeacher'])
        ->middleware(CurrentRole::Can([
            Access::TRAINERS_LIST_ACCESS,
        ]));
    Route::match(['get', 'post'], 'edit-teacher/{id}', ['as' => 'edit-teacher', 'uses' => 'Frontend\TeacherController@editTeacher']);
    Route::match(['get', 'post'], 'delete-teacher/{id}', ['as' => 'delete-teacher', 'uses' => 'Frontend\TeacherController@deleteTeacher']);
    Route::match(['get', 'post'], 'teacher-send-sms/{id}', ['as' => 'teacher-send-sms', 'uses' => 'Frontend\TeacherController@teacherSendSms']);

    // Techer Matrix
    Route::match(['get', 'post'], 'add-teacher-matrix', ['as' => 'add-teacher-matrix', 'uses' => 'Frontend\TeacherController@addTeacherMatrix']);
    Route::match(['get', 'post'], 'view-teacher-matrix', ['as' => 'view-teacher-matrix', 'uses' => 'Frontend\TeacherController@viewTeacherMatrix'])
        ->middleware(CurrentRole::Can([
            Access::ASSIGN_UNITS_ACCESS,
        ]));
    Route::match(['get', 'post'], 'delete-teacher-matrix/{id}', ['as' => 'delete-teacher-matrix', 'uses' => 'Frontend\TeacherController@deleteTeacherMatrix']);
    Route::match(['get', 'post'], 'edit-teacher-matrix/{id}', ['as' => 'edit-teacher-matrix', 'uses' => 'Frontend\TeacherController@editTeacherMatrix']);
    Route::match(['get', 'post'], 'teacher/ajaxAction', ['as' => 'teacher/ajaxAction', 'uses' => 'Frontend\TeacherController@ajaxAction']);
    Route::match(['get', 'post'], 'export-excel-teacher/{viewby}/{semester}/{teacherid}/{fromtdate}/{todate}', ['as' => 'export-excel-teacher', 'uses' => 'Frontend\TeacherController@generateTeacherExcelSheet']);

    Route::match(['get', 'post'], 'teacher-matrix/ajaxAction', ['as' => 'teacher-matrix/ajaxAction', 'uses' => 'Frontend\TeacherController@ajaxAction']);
    Route::match(['get', 'post'], 'teacher-profile/{id}', ['as' => 'teacher-profile', 'uses' => 'Frontend\TeacherController@teacherProfile']);
    Route::match(['get', 'post'], 'update-teacher-profile/{id}', ['as' => 'update-teacher-profile', 'uses' => 'Frontend\TeacherController@updateTeacherProfile']);
    Route::match(['get', 'post'], 'teacher-profile-send-mail/{id}', ['as' => 'teacher-profile-send-mail', 'uses' => 'Frontend\TeacherController@sendTeacherProfileMail']);
    Route::match(['get', 'post'], 'teacher-profile-matrix/{id}', ['as' => 'teacher-profile-matrix', 'uses' => 'Frontend\TeacherController@teacherProfileMatrix']);
    Route::match(['get', 'post'], 'teacher-communication-log/{id}/{flag}', ['as' => 'teacher-communication-log', 'uses' => 'Frontend\StaffController@communicationLogStaff']);
    Route::match(['get', 'post'], 'edit-teacher-communication-log/{id}/{logid}/{flag}', ['as' => 'edit-teacher-communication-log', 'uses' => 'Frontend\StaffController@editCommunicationLogStaff']);
    Route::match(['get', 'post'], 'teacher-timetable/{id}', ['as' => 'teacher-timetable', 'uses' => 'Frontend\TeacherController@timetableForTeacher']);

    // Techer Manage Timesheet
    Route::match(['get', 'post'], 'timesheet-approval', ['as' => 'timesheet-approval', 'uses' => 'Frontend\TimesheetController@approvalInfo'])
        ->middleware(CurrentRole::Can([
            Access::TIMESHEET_ACCESS,
            Access::MANAGE_TIMESHEET_ACCESS,
        ]));
    Route::match(['get', 'post'], 'timesheet-submission', ['as' => 'timesheet-submission', 'uses' => 'Frontend\TimesheetController@submissionInfo']);
    Route::match(['get', 'post'], 'timesheet-approved', ['as' => 'timesheet-approved', 'uses' => 'Frontend\TimesheetController@approvedInfo']);
    Route::match(['get', 'post'], 'assign-supervisor', ['as' => 'assign-supervisor', 'uses' => 'Frontend\TimesheetController@assignSupervisorInfo']);
    Route::match(['get', 'post'], 'delete-assign-supervisor/{id}', ['as' => 'delete-assign-supervisor', 'uses' => 'Frontend\TimesheetController@deleteAssignSupervisorInfo']);
    Route::match(['get', 'post'], 'generate-pay-period', ['as' => 'generate-pay-period', 'uses' => 'Frontend\TimesheetController@generatePayPeriodInfo'])
        ->middleware(CurrentRole::Can([
            Access::TIMESHEET_ACCESS,
            Access::PAY_PERIOD_ACCESS,
            Access::PAY_PERIOD_BETA_ACCESS,
        ]));
    Route::match(['get', 'post'], 'delete-generated-pay-period/{id}', ['as' => 'delete-generated-pay-period', 'uses' => 'Frontend\TimesheetController@deleteGeneratedPayPeriodInfo']);
    Route::match(['get', 'post'], 'timesheet/ajaxAction', ['as' => 'timesheet/ajaxAction', 'uses' => 'Frontend\TimesheetController@ajaxAction'])
        ->middleware(CurrentRole::Can([
            Access::TIMESHEET_ACCESS,
        ]));
    Route::match(['get', 'post'], 'add-possible-timesheet', ['as' => 'add-possible-timesheet', 'uses' => 'Frontend\TimesheetController@addPossibleTimesheetInfo']);
    Route::match(['get', 'post'], 'add-manual-timesheet', ['as' => 'add-manual-timesheet', 'uses' => 'Frontend\TimesheetController@addManualTimesheetInfo']);

    // Left-sidebar Semester Section
    Route::match(['get', 'post'], 'manage-semester', ['as' => 'manage-semester', 'uses' => 'Frontend\SemesterController@semesterManage'])
        ->middleware(CurrentRole::Can([
            Access::MANAGE_CALENDAR_ACCESS,
            Access::MANAGE_SEMESTER_ACCESS,
            Access::MANAGE_SEMESTER_BETA_ACCESS,
        ]));
    Route::match(['get', 'post'], 'semester-division', ['as' => 'semester-division', 'uses' => 'Frontend\SemesterController@semesterDivision'])
        ->middleware(CurrentRole::Can([
            Access::MANAGE_CALENDAR_ACCESS,
            Access::MANAGE_SEMESTER_ACCESS,
            Access::MANAGE_SEMESTER_BETA_ACCESS,
        ]));
    Route::match(['get', 'post'], 'course-calendar', ['as' => 'course-calendar', 'uses' => 'Frontend\SemesterController@courseCalendar'])
        ->middleware(CurrentRole::Can([
            Access::MANAGE_CALENDAR_ACCESS,
            Access::COURSE_CALENDAR_TYPE_ACCESS,
        ]));
    Route::match(['get', 'post'], 'semester/ajaxAction', ['as' => 'semester/ajaxAction', 'uses' => 'Frontend\SemesterController@ajaxAction'])
        ->middleware(CurrentRole::Can([
            Access::MANAGE_CALENDAR_ACCESS,
            Access::MANAGE_SEMESTER_ACCESS,
            Access::MANAGE_SEMESTER_BETA_ACCESS,
        ]));

    Route::match(['get', 'post'], 'delete-course-calendar/{id}', ['as' => 'delete-course-calendar', 'uses' => 'Frontend\SemesterController@deleteCourseCalendar'])
        ->middleware(CurrentRole::Can([
            Access::MANAGE_CALENDAR_ACCESS,
            Access::MANAGE_SEMESTER_ACCESS,
            Access::MANAGE_SEMESTER_BETA_ACCESS,
        ]));
    Route::match(['get', 'post'], 'delete-semester/{id}', ['as' => 'delete-semester', 'uses' => 'Frontend\SemesterController@deleteSemester'])
        ->middleware(CurrentRole::Can([
            Access::MANAGE_CALENDAR_ACCESS,
            Access::MANAGE_SEMESTER_ACCESS,
            Access::MANAGE_SEMESTER_BETA_ACCESS,
        ]));
    Route::match(['get', 'post'], 'delete-semester-division/{id}', ['as' => 'delete-semester-division', 'uses' => 'Frontend\SemesterController@deleteSemesterDivision'])
        ->middleware(CurrentRole::Can([
            Access::MANAGE_CALENDAR_ACCESS,
            Access::MANAGE_SEMESTER_ACCESS,
            Access::MANAGE_SEMESTER_BETA_ACCESS,
        ]));
    Route::match(['get', 'post'], 'update-holiday-week/{id}/{value?}', ['as' => 'update-holiday-week', 'uses' => 'Frontend\SemesterController@updateHolidayWeek']);

    Route::match(['get', 'post'], 'add-country', ['as' => 'add-country', 'uses' => 'Frontend\SetUpController@addCountry']);
    Route::match(['get', 'post'], 'country-view', ['as' => 'country-view', 'uses' => 'Frontend\SetUpController@countryView']);
    Route::match(['get', 'post'], 'delete-country/{id}', ['as' => 'delete-country', 'uses' => 'Frontend\SetUpController@deleteCountry']);
    Route::match(['get', 'post'], 'edit-country/{id}', ['as' => 'edit-country', 'uses' => 'Frontend\SetUpController@editCountry']);

    Route::match(['get', 'post'], 'certificate-id-formate', ['as' => 'certificate-id-formate', 'uses' => 'Frontend\SetUpController@certificateIdFormate']);
    Route::match(['get', 'post'], 'certificate-id-formate-add', ['as' => 'certificate-id-formate-add', 'uses' => 'Frontend\SetUpController@certificateIdFormateAdd']);
    Route::match(['get', 'post'], 'certificate-id-formate/ajaxAction', ['as' => 'certificate-id-formate-ajaxAction', 'uses' => 'Frontend\SetUpController@certificateIdFormateAjaxAction']);
    Route::match(['get', 'post'], 'setupSection/ajaxAction', ['as' => 'setupSection-ajaxAction', 'uses' => 'Frontend\SetUpController@ajaxAction']);

    Route::match(['get', 'post'], 'result-grade', ['as' => 'result-grade', 'uses' => 'Frontend\SetUpController@resultGrade']);
    Route::match(['get', 'post'], 'add-result-grade', ['as' => 'add-result-grade', 'uses' => 'Frontend\SetUpController@resultGradeAdd']);
    Route::match(['get', 'post'], 'edit-result-grade/{id}', ['as' => 'edit-result-grade', 'uses' => 'Frontend\SetUpController@resultGradeEdit']);
    Route::match(['get', 'post'], 'delete-result-grade/{id}', ['as' => 'delete-result-grade', 'uses' => 'Frontend\SetUpController@resultGradeDelete']);

    Route::match(['get', 'post'], 'intervention-strategy', ['as' => 'intervention-strategy', 'uses' => 'Frontend\SetUpController@interventionStrategy']);
    Route::match(['get', 'post'], 'add-intervention-strategy', ['as' => 'add-intervention-strategy', 'uses' => 'Frontend\SetUpController@addInterventionStrategy']);
    Route::match(['get', 'post'], 'delete-intervention/{id}', ['as' => 'delete-intervention', 'uses' => 'Frontend\SetUpController@deleteInterventionStrategy']);
    Route::match(['get', 'post'], 'setup-sections', ['as' => 'setup-sections', 'uses' => 'Frontend\SetUpController@setupSections']);
    Route::match(['get', 'post'], 'delete-setup-section/{section_type}/{id}', ['as' => 'delete-setup-section', 'uses' => 'Frontend\SetUpController@deleteSetupSectionInfo']);
    Route::match(['get', 'post'], 'set-default-setup/{type_id}/{id}', ['as' => 'set-default-setup', 'uses' => 'Frontend\SetUpController@setDefaultSetupSection']);
    Route::match(['get', 'post'], 'additional-custom-fields', ['as' => 'additional-custom-fields', 'uses' => 'Frontend\SetUpController@additionalCustomFields']);

    Route::match(['get', 'post'], 'language-view', ['as' => 'language-view', 'uses' => 'Frontend\SetUpController@languageView']);
    Route::match(['get', 'post'], 'add-language', ['as' => 'add-language', 'uses' => 'Frontend\SetUpController@languageAdd']);
    Route::match(['get', 'post'], 'delete-language/{id}', ['as' => 'delete-language', 'uses' => 'Frontend\SetUpController@deleteLanguage']);
    Route::match(['get', 'post'], 'student-offer-checklist/{id}', ['as' => 'student-offer-checklist', 'uses' => 'Frontend\StudentsController@studentOfferChecklist']);
    Route::match(['get', 'post'], 'student-profile-checklist/{id}/{student_primary}', ['as' => 'student-profile-checklist', 'uses' => 'Frontend\StudentsController@studentProfileChecklist']);
    Route::match(['get', 'post'], 'student-communication-log/{id}', ['as' => 'student-communication-log', 'uses' => 'Frontend\StudentsController@studentCommunicationDiary']);
    Route::match(['get', 'post'], 'student-communication-log-edit/{id}/{visiblity}/{studentId}', ['as' => 'student-communication-log-edit', 'uses' => 'Frontend\StudentsController@studentCommunicationDiaryEdit']);
    Route::match(['get', 'post'], 'student-edit-diary/{id}', ['as' => 'student-edit-diary', 'uses' => 'Frontend\StudentsController@studenDiaryEdit']);
    Route::match(['get', 'post'], 'student-delete-diary/{id}', ['as' => 'student-delete-diary', 'uses' => 'Frontend\StudentsController@studenDiaryDelete']);
    Route::match(['get', 'post'], 'student-course-communication-edit/{id}/{visiblity}/{courseId}', ['as' => 'student-course-communication-edit', 'uses' => 'Frontend\StudentsCourseController@studentCourseCommunicationLogEdit']);
    Route::match(['get', 'post'], 'offer-manage-communication-log-edit/{id}/{visiblity}/{studentId}', ['as' => 'offer-manage-communication-log-edit', 'uses' => 'Frontend\OfferManageController@offerManageCommunicationLogEdit']);

    Route::match(['get', 'post'], 'student-approve/{student_id}/{offer_id}', ['as' => 'student-approve', 'uses' => 'Frontend\StudentsController@studentApprove']);
    Route::match(['get', 'post'], 'student-coe/{student_id}/{offer_id}', ['as' => 'student-coe', 'uses' => 'Frontend\StudentsController@studentCOE']);
    Route::match(['get', 'post'], 'add-coe-offer-manage', ['as' => 'add-coe-offer-manage', 'uses' => 'Frontend\StudentsController@studentCOE']);
    Route::match(['get', 'post'], 'pending-offer/{student_id}', ['as' => 'pending-offer', 'uses' => 'Frontend\StudentsController@pendingOffer']);
    Route::match(['get', 'post'], 'reconsider-offer/{student_id}', ['as' => 'reconsider-offer', 'uses' => 'Frontend\StudentsController@reconsiderOffer']);

    /* Start  Claim Tracking Routes */
    Route::match(['get', 'post'], 'student-claim-tracking-list/{id}', ['as' => 'student-claim-tracking-list', 'uses' => 'Frontend\StudentsController@studentClaimTrackingList']);
    Route::match(['get', 'post'], 'student-claim-tracking-add/{id}', ['as' => 'student-claim-tracking-add', 'uses' => 'Frontend\StudentsController@studentClaimTrackingAdd']);
    Route::match(['get', 'post'], 'student-claim-tracking-edit/{student_id}/{id}', ['as' => 'student-claim-tracking-edit', 'uses' => 'Frontend\StudentsController@studentClaimTrackingEdit']);

    /* Start  Vet Fee Help Routes */
    Route::match(['get', 'post'], 'student-vet-fee-help-list/{id}', ['as' => 'student-vet-fee-help-list', 'uses' => 'Frontend\StudentsVetFeeHelpController@studentVetFeeHelpList']);
    Route::match(['get', 'post'], 'student-vet-fee-help-add/{id}', ['as' => 'student-vet-fee-help-add', 'uses' => 'Frontend\StudentsVetFeeHelpController@studentVetFeeHelpAdd']);
    Route::match(['get', 'post'], 'student-vet-fee-help-edit/{student_id}/{id}', ['as' => 'student-vet-fee-help-edit', 'uses' => 'Frontend\StudentsVetFeeHelpController@studentVetFeeHelpEdit']);
    Route::match(['get', 'post'], 'student-vet-fee-help-add-course/{id}', ['as' => 'student-vet-fee-help-add-course', 'uses' => 'Frontend\StudentsVetFeeHelpController@studentVetFeeHelpAddCourse']);
    Route::match(['get', 'post'], 'student-vet-fee-help-generate-zip/{id}', ['as' => 'student-vet-fee-help-generate-zip', 'uses' => 'Frontend\StudentsVetFeeHelpController@studentVetFeeHelpGenerateZip']);

    /* Start  Avet Miss Data Exports */
    Route::match(['get', 'post'], 'student-avet-miss-data-exports', ['as' => 'student-avet-miss-data-exports', 'uses' => 'Frontend\AvetMissDataExportController@studentAvetDataExports']);
    Route::match(['get', 'post'], 'exports-to-excels', ['as' => 'exports-to-excels', 'uses' => 'Frontend\AvetMissDataExportController@generate_excel']);
    Route::match(['get', 'post'], 'avetMissDataExport/ajaxAction', ['as' => 'avetMissDataExport-ajaxAction', 'uses' => 'Frontend\AvetMissDataExportController@ajaxAction']);

    /* NSW AVETMISS Routes */
    Route::match(['get', 'post'], 'nsw-avetmiss-attpv1', ['as' => 'nsw-avetmiss-attpv1', 'uses' => 'Frontend\NswAvetmissController@nswAvetmissAttpV1Only']);
    Route::match(['get', 'post'], 'nsw-avetmiss-attpv2', ['as' => 'nsw-avetmiss-attpv2', 'uses' => 'Frontend\NswAvetmissController@nswAvetmissAttpV2']);
    Route::match(['get', 'post'], 'nswAvetmiss/ajaxAction', ['as' => 'nswAvetmiss-ajaxAction', 'uses' => 'Frontend\NswAvetmissController@ajaxAction']);

    /* Contract */
    Route::match(['get', 'post'], 'add-contract-code', ['as' => 'add-contract-code', 'uses' => 'Frontend\ContractController@addContractCode'])
        ->middleware(CurrentRole::Can([
            Access::MANAGE_TRAINING_CONTRACTS_ACCESS,
            Access::ADD_CONTRACT_ACCESS,
            Access::ADD_CONTRACT_BETA_ACCESS,
        ]));
    Route::match(['get', 'post'], 'view-contract-code', ['as' => 'view-contract-code', 'uses' => 'Frontend\ContractController@viewContractCode']);
    Route::match(['get', 'post'], 'delete-contract-code/{id}', ['as' => 'delete-contract-code', 'uses' => 'Frontend\ContractController@deleteContractCode'])
        ->middleware(CurrentRole::Can([
            Access::MANAGE_TRAINING_CONTRACTS_ACCESS,
            Access::ADD_CONTRACT_ACCESS,
            Access::ADD_CONTRACT_BETA_ACCESS,
        ]));
    Route::match(['get', 'post'], 'contract/ajaxAction', ['as' => 'contract-ajaxAction', 'uses' => 'Frontend\ContractController@ajaxAction']);

    /*  Contract Funding Source */
    Route::match(['get', 'post'], 'add-contract-funding-source', ['as' => 'add-contract-funding-source', 'uses' => 'Frontend\ContractController@addContractFundingSource']);
    Route::match(['get', 'post'], 'view-contract-funding-source', ['as' => 'view-contract-funding-source', 'uses' => 'Frontend\ContractController@viewContractFundingSource']);
    Route::match(['get', 'post'], 'delete-contract-fund-source/{id}', ['as' => 'delete-contract-fund-source', 'uses' => 'Frontend\ContractController@deleteContractFundingSource']);

    Route::match(['get', 'post'], 'view-venue-list', ['as' => 'view-venue-list', 'uses' => 'Frontend\VenueController@viewVenueList']);
    Route::match(['get', 'post'], 'add-venue', ['as' => 'add-venue', 'uses' => 'Frontend\VenueController@venueAdd']);
    Route::match(['get', 'post'], 'edit-venue/{id}', ['as' => 'edit-venue', 'uses' => 'Frontend\VenueController@venueEdit']);
    Route::match(['get', 'post'], 'delete-venue/{id}', ['as' => 'delete-venue', 'uses' => 'Frontend\VenueController@deleteVenue']);
    Route::match(['get', 'post'], 'venue/ajaxAction', ['as' => 'venue-ajaxAction', 'uses' => 'Frontend\VenueController@ajaxAction']);

    Route::match(['get', 'post'], 'view-classroom-list', ['as' => 'view-classroom-list', 'uses' => 'Frontend\ClassroomController@viewClassList']);
    Route::match(['get', 'post'], 'add-classroom', ['as' => 'add-classroom', 'uses' => 'Frontend\ClassroomController@addClassroom']);
    Route::match(['get', 'post'], 'edit-classroom/{id}', ['as' => 'edit-classroom', 'uses' => 'Frontend\ClassroomController@editClassroom']);
    Route::match(['get', 'post'], 'delete-classroom/{id}', ['as' => 'delete-classroom', 'uses' => 'Frontend\ClassroomController@deleteClassroom']);
    Route::match(['get', 'post'], 'classroom/ajaxAction', ['as' => 'classroom-ajaxAction', 'uses' => 'Frontend\ClassroomController@ajaxAction']);

    Route::match(['get', 'post'], 'create-permission-group', ['as' => 'create-permission-group', 'uses' => 'Frontend\SetupPermissionController@addPermissionGroupInfo']);
    Route::match(['get', 'post'], 'set-user-permission', ['as' => 'set-user-permission', 'uses' => 'Frontend\SetupPermissionController@setUserPermissionInfo']);
    //            Route::match(['get', 'post'], 'page-permission-setup', ['as' => 'page-permission-setup', 'uses' => 'Frontend\SetupPermissionController@viewPagePermissionSetupInfo']);
    Route::match(['get', 'post'], 'page-permission-setup', ['as' => 'page-permission-setup', 'uses' => 'Frontend\SetupPermissionController@studenAgentPermissionSetup']);
    Route::match(['get', 'post'], 'studen-agent-permission-setup', ['as' => 'studen-agent-permission-setup', 'uses' => 'Frontend\SetupPermissionController@studenAgentPermissionSetup']);
    Route::match(['get', 'post'], 'delete-student-agent-permission/{id}', ['as' => 'delete-student-agent-permission', 'uses' => 'Frontend\SetupPermissionController@deleteStudentAgentPermission']);
    Route::match(['get', 'post'], 'edit-student-agent-permission/{id}', ['as' => 'edit-student-agent-permission', 'uses' => 'Frontend\SetupPermissionController@editStudentAgentPermission']);
    Route::match(['get', 'post'], 'permission/ajaxAction', ['as' => 'permission-ajaxAction', 'uses' => 'Frontend\SetupPermissionController@ajaxAction']);
    /* Course Site Module */
    Route::match(['get', 'post'], 'add-course-site', ['as' => 'add-course-site', 'uses' => 'Frontend\ContractController@addCourseSite']);
    Route::match(['get', 'post'], 'view-course-site', ['as' => 'view-course-site', 'uses' => 'Frontend\ContractController@viewCourseSite']);
    Route::match(['get', 'post'], 'delete-course-site/{id}', ['as' => 'delete-course-site', 'uses' => 'Frontend\ContractController@deleteCourseSite']);

    Route::match(['get', 'post'], 'teacher-leave-info/{id}', ['as' => 'teacher-leave-info', 'uses' => 'Frontend\TeacherController@leaveListTeacher']);
    Route::match(['get', 'post'], 'view-leave-list', ['as' => 'view-leave-list', 'uses' => 'Frontend\LeaveController@viewLeaveList'])
        ->middleware(CurrentRole::Can([
            Access::LEAVE_INFO_ACCESS,
        ]));
    Route::match(['get', 'post'], 'add-leave-info', ['as' => 'add-leave-info', 'uses' => 'Frontend\LeaveController@addLeaveInfo']);
    Route::match(['get', 'post'], 'edit-leave-info/{id}', ['as' => 'edit-leave-info', 'uses' => 'Frontend\LeaveController@editLeaveInfo'])
        ->middleware(CurrentRole::Can([
            Access::LEAVE_INFO_ACCESS,
        ]));
    Route::match(['get', 'post'], 'delete-leave-info/{id}', ['as' => 'delete-leave-info', 'uses' => 'Frontend\LeaveController@deleteLeaveInfo']);

    Route::match(['get', 'post'], 'view-holiday-list', ['as' => 'view-holiday-list', 'uses' => 'Frontend\CourseController@holidayList']);
    Route::match(['get', 'post'], 'add-holiday', ['as' => 'add-holiday', 'uses' => 'Frontend\CourseController@holidayAdd']);
    Route::match(['get', 'post'], 'edit-holiday/{id}', ['as' => 'edit-holiday', 'uses' => 'Frontend\CourseController@holidayEdit']);
    Route::match(['get', 'post'], 'delete-holiday/{id}', ['as' => 'delete-holiday', 'uses' => 'Frontend\CourseController@deleteHoliday']);

    Route::match(['get', 'post'], 'view-elicos-discount-list', ['as' => 'view-elicos-discount-list', 'uses' => 'Frontend\CoursePromotionController@viewDiscountList']);
    Route::match(['get', 'post'], 'add-elicos-discount', ['as' => 'add-elicos-discount', 'uses' => 'Frontend\CoursePromotionController@addDiscount']);
    Route::match(['get', 'post'], 'edit-elicos-discount/{id}', ['as' => 'edit-elicos-discount', 'uses' => 'Frontend\CoursePromotionController@editDiscount']);
    Route::match(['get', 'post'], 'delete-elicos-discount/{id}', ['as' => 'delete-elicos-discount', 'uses' => 'Frontend\CoursePromotionController@deleteDiscount']);

    Route::match(['get', 'post'], 'view-promotion-price-list', ['as' => 'view-promotion-price-list', 'uses' => 'Frontend\CoursePromotionController@viewPriceList']);
    Route::match(['get', 'post'], 'course-promotion/ajaxAction', ['as' => 'course-promotion-ajaxAction', 'uses' => 'Frontend\CoursePromotionController@ajaxAction']);
    Route::match(['get', 'post'], 'country-level-update', ['as' => 'country-level-update', 'uses' => 'Frontend\CoursePromotionController@countryLevelUpdate']);
    Route::match(['get', 'post'], 'add-promotion-price', ['as' => 'add-promotion-price', 'uses' => 'Frontend\CoursePromotionController@addPrice']);
    Route::match(['get', 'post'], 'edit-promotion-price/{id}', ['as' => 'edit-promotion-price', 'uses' => 'Frontend\CoursePromotionController@editPrice']);
    Route::match(['get', 'post'], 'delete-promotion-price/{id}', ['as' => 'delete-promotion-price', 'uses' => 'Frontend\CoursePromotionController@deletePrice']);

    /* Course Site Module */
    Route::match(['get', 'post'], 'add-course-site', ['as' => 'add-course-site', 'uses' => 'Frontend\ContractController@addCourseSite']);
    Route::match(['get', 'post'], 'view-course-site', ['as' => 'view-course-site', 'uses' => 'Frontend\ContractController@viewCourseSite']);
    Route::match(['get', 'post'], 'delete-course-site/{id}', ['as' => 'delete-course-site', 'uses' => 'Frontend\ContractController@deleteCourseSite']);

    // Manage Employer
    Route::match(['get', 'post'], 'manage-employer', ['as' => 'manage-employer', 'uses' => 'Frontend\EmployerController@viewEmployer'])
        ->middleware(CurrentRole::Can([
            Access::EMPLOYER_LIST_ACCESS,
        ]));
    Route::match(['get', 'post'], 'add-employer', ['as' => 'add-employer', 'uses' => 'Frontend\EmployerController@addEmployer']);
    Route::match(['get', 'post'], 'edit-employer/{id}', ['as' => 'edit-employer', 'uses' => 'Frontend\EmployerController@editEmployer'])
        ->middleware(CurrentRole::Can([
            Access::EMPLOYER_LIST_ACCESS,
        ]));
    Route::match(['get', 'post'], 'delete-employer/{id}', ['as' => 'delete-employer', 'uses' => 'Frontend\EmployerController@deleteEmployer']);
    Route::match(['get', 'post'], 'employer/ajaxAction', ['as' => 'employer-ajaxAction', 'uses' => 'Frontend\EmployerController@ajaxAction']);

    // Register Improvment
    Route::match(['get', 'post'], 'add-register-improvement', ['as' => 'add-register-improvement', 'uses' => 'Frontend\RegisterImprovementController@addRegisterImprovement']);
    Route::match(['get', 'post'], 'view-register-improvement', ['as' => 'view-register-improvement', 'uses' => 'Frontend\RegisterImprovementController@viewRegisterModule'])
        ->middleware(CurrentRole::Can([
            Access::CONTINUOUS_IMPROVEMENT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'edit-register-improvement/{id}', ['as' => 'edit-register-improvement', 'uses' => 'Frontend\RegisterImprovementController@editRegisterImprovement']);
    Route::match(['get', 'post'], 'register-improvement/ajaxAction', ['as' => 'register-improvement/ajaxAction', 'uses' => 'Frontend\RegisterImprovementController@ajaxAction'])
        ->middleware(CurrentRole::Can([
            Access::CONTINUOUS_IMPROVEMENT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'delete-register-improvement/{id}', ['as' => 'register-improvement/delete-register-improvement', 'uses' => 'Frontend\RegisterImprovementController@deleteRegisterImprovement']);

    // Account
    Route::match(['get', 'post'], 'student-account', ['as' => 'student-account', 'uses' => 'Frontend\StudentAccountController@viewStudentList'])
        ->middleware(CurrentRole::Can([
            Access::PAYMENT_MAIN_ACCESS,
            Access::PAYMENT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'student-payment-summary/{id}', ['as' => 'student-payment-summary', 'uses' => 'Frontend\StudentAccountController@studentPaymentSummary']);
    Route::match(['get', 'post'], 'student-initial-payment/{id}', ['as' => 'student-initial-payment', 'uses' => 'Frontend\StudentAccountController@studentInitialPayment']);
    Route::match(['get', 'post'], 'student-process-transaction/{id}', ['as' => 'student-process-transaction', 'uses' => 'Frontend\StudentAccountController@studentProcessTransaction']);
    Route::match(['get', 'post'], 'student-miscellaneous-payment/{id}', ['as' => 'student-miscellaneous-payment', 'uses' => 'Frontend\StudentAccountController@studentMiscellaneousPayment']);
    Route::match(['get', 'post'], 'student-miscellaneous-payment-refund', ['as' => 'student-miscellaneous-payment-refund', 'uses' => 'Frontend\StudentAccountController@studentMiscellaneousPaymentRefund']);
    Route::match(['get', 'post'], 'student-miscellaneous-payment-reverse-transaction', ['as' => 'student-miscellaneous-payment-reverse-transaction', 'uses' => 'Frontend\StudentAccountController@studentMiscellaneousPaymentReverse']);
    Route::match(['get', 'post'], 'student-miscellaneous-payment-refund-receipt/{student_id}/{course_id}/{id}', ['as' => 'student-miscellaneous-payment-refund-receipt', 'uses' => 'Frontend\StudentAccountController@studentMiscellaneousPaymentRefundReceiptPDF']);
    Route::match(['get', 'post'], 'student-refund-history/{id}/{refund_history}', ['as' => 'student-refund-history', 'uses' => 'Frontend\StudentAccountController@studentRefundHistory']);
    Route::match(['get', 'post'], 'student-miscellaneous-payment-action/{id}', ['as' => 'student-miscellaneous-payment-action', 'uses' => 'Frontend\StudentAccountController@studentMiscellaneousPaymentAction']);
    Route::match(['get', 'post'], 'student-miscellaneous-process/{id}', ['as' => 'student-miscellaneous-process', 'uses' => 'Frontend\StudentAccountController@studentMiscellaneousProcess']);
    Route::match(['get', 'post'], 'student-service-payment-add/{studentId}', ['as' => 'student-service-payment-add', 'uses' => 'Frontend\StudentServicesController@studentAdditionalpaymentService']);
    Route::match(['get', 'post'], 'payment-service/ajaxAction', ['as' => 'payment-service-ajaxAction', 'uses' => 'Frontend\StudentServicesController@ajaxAction']);
    Route::match(['get', 'post'], 'update-multiple-payment-service', ['as' => 'update-multiple-payment-service', 'uses' => 'Frontend\StudentServicesController@multiplePaymentService']);
    Route::match(['get', 'post'], 'payment-refund/ajaxAction', ['as' => 'payment-refund-ajaxAction', 'uses' => 'Frontend\StudentPaymentRefundController@ajaxAction']);
    Route::match(['get', 'post'], 'delete-payment-refund/{id}', ['as' => 'delete-payment-refund', 'uses' => 'Frontend\StudentPaymentRefundController@deletePaymentRefundInfo']);
    Route::match(['get', 'post'], 'student-profile-send-mail2/{id}', ['as' => 'student-profile-send-mail2', 'uses' => 'Frontend\StudentServicesController@studentProfileSendMailv2']);

    Route::match(['get', 'post'], 'service-payment-invoice-pdf/{studentId}/{courseId}/{invoiceNum}', ['as' => 'service-payment-invoice-pdf', 'uses' => 'Frontend\StudentServicesController@serviceInvoicePdf']);
    Route::match(['get', 'post'], 'service-payment-receipt-pdf/{studentId}/{courseId}/{invoiceNum}', ['as' => 'service-payment-receipt-pdf', 'uses' => 'Frontend\StudentServicesController@serviceReceiptPdf']);
    Route::match(['get', 'post'], 'service-refund-receipt-pdf/{studentId}/{serviceId}', ['as' => 'service-refund-receipt-pdf', 'uses' => 'Frontend\StudentServicesController@serviceRefundReceiptPdf']);
    Route::match(['get', 'post'], 'student-service-payment-edit/{studentId}/{serviceId}', ['as' => 'student-service-payment-edit', 'uses' => 'Frontend\StudentServicesController@studentServicePaymentEdit']);
    Route::match(['get', 'post'], 'student-service-payment/{id}', ['as' => 'student-service-payment', 'uses' => 'Frontend\StudentAccountController@studentServicePayment']);
    Route::match(['get', 'post'], 'student-service-paymentv2/{id}', ['as' => 'student-service-paymentv2', 'uses' => 'Frontend\StudentServicesController@studentServicePaymentv2']);
    Route::match(['get', 'post'], 'service-payment-refund', ['as' => 'service-payment-refund', 'uses' => 'Frontend\StudentServicesController@servicePaymentRefund']);

    Route::match(['get', 'post'], 'student-transfer-payment/{id}', ['as' => 'student-transfer-payment', 'uses' => 'Frontend\StudentAccountController@studentTransferPayment']);
    Route::match(['get', 'post'], 'student-scholarship/{id}', ['as' => 'student-scholarship', 'uses' => 'Frontend\StudentAccountController@studentScholarship']);
    Route::match(['get', 'post'], 'edit-student-scholarship/{stidentId}/{id}', ['as' => 'edit-student-scholarship', 'uses' => 'Frontend\StudentAccountController@studentEditScholarship']);
    Route::match(['get', 'post'], 'delete-student-scholarship/{id}', ['as' => 'delete-student-scholarship', 'uses' => 'Frontend\StudentAccountController@deleteStudentScholarship']);
    Route::match(['get', 'post'], 'schedule-invoice-pdf/{student_id}/{course_id}/{invoice_no}', ['as' => 'schedule-invoice-pdf', 'uses' => 'Frontend\StudentAccountController@scheduleInvoicePdf']);
    Route::match(['get', 'post'], 'agent-pro-invoice-pdf/{student_id}/{course_id}/{invoice_no}', ['as' => 'agent-pro-invoice-pdf', 'uses' => 'Frontend\StudentPaymentController@agentProInvoicePDF']);
    Route::match(['get', 'post'], 'tax-receipt-pdf/{id}/{course_id}/{payment_id}', ['as' => 'tax-receipt-pdf', 'uses' => 'Frontend\StudentPaymentController@taxReceiptPdf']);
    Route::match(['get', 'post'], 'tax-agent-receipt/{id}/{course_id}/{payment_id}', ['as' => 'tax-agent-receipt', 'uses' => 'Frontend\StudentPaymentController@taxAgentReceipt']);
    Route::match(['get', 'post'], 'genetare-invoice-excel', ['as' => 'genetare-invoice-excel', 'uses' => 'Frontend\StudentPaymentController@invoiceExcel']);

    Route::match(['get', 'post'], 'student-payment-invoice-receipt-pdf/{id}/{course_id}', ['as' => 'student-payment-invoice-receipt-pdf', 'uses' => 'Frontend\StudentPaymentController@studentPaymentInvoiceReceiptPdf']);

    Route::match(['get', 'post'], 'record-initial-payment/{id}/{course_id}/{payment_type}', ['as' => 'record-initial-payment', 'uses' => 'Frontend\StudentAccountController@recordInitialPayment']);
    Route::match(['get', 'post'], 'student-agent-commission/{id}', ['as' => 'student-agent-commission', 'uses' => 'Frontend\StudentAccountController@studentAgentCommission']);
    Route::match(['get', 'post'], 'student-payment/ajaxAction', ['as' => 'student-payment-ajaxAction', 'uses' => 'Frontend\StudentPaymentController@ajaxAction']);
    Route::match(['get', 'post'], 'student-payment-email', ['as' => 'student-payment-email', 'uses' => 'Frontend\StudentPaymentController@studentPaymentEmail']);
    Route::match(['get', 'post'], 'student-pay-schedule-fee/{id}/{paymentDetailId}', ['as' => 'student-pay-schedule-fee', 'uses' => 'Frontend\StudentPaymentController@studentPayScheduleFee']);
    Route::match(['get', 'post'], 'generate-new-schedule/{student_id}', ['as' => 'generate-new-schedule', 'uses' => 'Frontend\StudentAccountController@generateNewSchedule']);
    Route::match(['get', 'post'], 'combine-payment-schedule/{course_id}/{student_id}/{invoice_number}', ['as' => 'combine-payment-schedule', 'uses' => 'Frontend\StudentPaymentController@combinePaymentSchedule']);
    Route::match(['get', 'post'], 'delete-student-invoice-credit/{id}', ['as' => 'delete-student-invoice-credit', 'uses' => 'Frontend\StudentAccountController@deleteStudentInvoiceCredit']);

    Route::match(['get', 'post'], 'pay-update-invoice/{student_id}/{primary_id}', ['as' => 'pay-update-invoice', 'uses' => 'Frontend\StudentPaymentController@payUpdateInvoice']);
    Route::match(['get', 'post'], 'student-miscellaneous-add/{student_id}', ['as' => 'student-miscellaneous-add', 'uses' => 'Frontend\StudentPaymentController@studentMiscellaneousAdd']);
    Route::match(['get', 'post'], 'statement-of-account/{course_id}/{student_id}', ['as' => 'statement-of-account', 'uses' => 'Frontend\StudentPaymentController@statementOfAccountPdf']);
    Route::match(['get', 'post'], 'student-oshc/{id}', ['as' => 'student-oshc', 'uses' => 'Frontend\StudentOshcController@studentOshcView']);
    Route::match(['get', 'post'], 'student-profile-oshc/ajaxAction', ['as' => 'student-profile-oshc-ajaxAction', 'uses' => 'Frontend\StudentOshcController@ajaxAction']);
    Route::match(['get', 'post'], 'student-payment-checklist/{id}', ['as' => 'student-payment-checklist', 'uses' => 'Frontend\StudentAccountController@studentPaymentChecklist']);
    Route::match(['get', 'post'], 'student-payment-invoice/{student_id}/{course_id}/{invoice_id}', ['as' => 'student-payment-invoice', 'uses' => 'Frontend\StudentAccountController@studentPaymentInvoicePDF']);
    Route::match(['get', 'post'], 'student-payment-receipt/{student_id}/{course_id}', ['as' => 'student-payment-receipt', 'uses' => 'Frontend\StudentAccountController@studentPaymentReceiptPDF']);
    Route::match(['get', 'post'], 'service-payment-edit', ['as' => 'service-payment-edit', 'uses' => 'Frontend\StudentAccountController@servicePaymentEdit']);
    Route::match(['get', 'post'], 'service-payment-add', ['as' => 'service-payment-add', 'uses' => 'Frontend\StudentAccountController@servicePaymentAdd']);
    Route::match(['get', 'post'], 'service-payment-delete/{id}', ['as' => 'service-payment-delete', 'uses' => 'Frontend\StudentAccountController@servicePaymentDelete']);

    Route::match(['get', 'post'], 'agent-commission-info/{id}', ['as' => 'agent-commission-info', 'uses' => 'Frontend\StudentAccountController@editAgentCommissionInfo']);
    Route::match(['get', 'post'], 'agent-bonus-info/{id}', ['as' => 'agent-bonus-info', 'uses' => 'Frontend\StudentAccountController@editAgentBonusInfo']);
    Route::match(['get', 'post'], 'process-commission-agent/{id}', ['as' => 'process-commission-agent', 'uses' => 'Frontend\AgentPaymentController@processCommissionAgent']);
    Route::match(['get', 'post'], 'credit-bonus-allocation/{id}', ['as' => 'credit-bonus-allocation', 'uses' => 'Frontend\AgentPaymentController@creditBonusAllocation']);
    Route::match(['get', 'post'], 'delete-credit-bonus-allocation/{agent_id}/{id}', ['as' => 'delete-credit-bonus-allocation', 'uses' => 'Frontend\AgentPaymentController@deleteCreditBonusAllocation']);
    Route::match(['get', 'post'], 'payment-history/{id}', ['as' => 'payment-history', 'uses' => 'Frontend\AgentPaymentController@paymentHistory']);
    Route::match(['get', 'post'], 'edit-credit-bonus-allocation/', ['as' => 'edit-credit-bonus-allocation', 'uses' => 'Frontend\AgentPaymentController@editCreditBonusInfo']);

    Route::match(['get', 'post'], 'accountSetup/ajaxAction', ['as' => 'accountSetup-ajaxAction', 'uses' => 'Frontend\StudentAccountSetupController@ajaxAction']);

    Route::match(['get', 'post'], 'student-account-setup', ['as' => 'student-account-setup', 'uses' => 'Frontend\StudentAccountSetupController@addPaymentLedgerInfo'])
        ->middleware(CurrentRole::Can([
            Access::FINANCE_ACCESS,
            Access::SETUP_ACCOUNT_PAYMENT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'edit-payment-ledger/{id}', ['as' => 'edit-payment-ledger', 'uses' => 'Frontend\StudentAccountSetupController@editPaymentLedgerInfo'])
        ->middleware(CurrentRole::Can([
            Access::FINANCE_ACCESS,
            Access::SETUP_ACCOUNT_PAYMENT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'delete-payment-ledger/{id}', ['as' => 'delete-payment-ledger', 'uses' => 'Frontend\StudentAccountSetupController@deletePaymentLedgerInfo'])
        ->middleware(CurrentRole::Can([
            Access::FINANCE_ACCESS,
            Access::SETUP_ACCOUNT_PAYMENT_ACCESS,
        ]));

    Route::match(['get', 'post'], 'student-ledger-account', ['as' => 'student-ledger-account', 'uses' => 'Frontend\StudentAccountSetupController@addLedgerAccountInfo'])
        ->middleware(CurrentRole::Can([
            Access::FINANCE_ACCESS,
            Access::SETUP_ACCOUNT_PAYMENT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'edit-ledger-account/{id}', ['as' => 'edit-ledger-account', 'uses' => 'Frontend\StudentAccountSetupController@editLedgerAccountInfo'])
        ->middleware(CurrentRole::Can([
            Access::FINANCE_ACCESS,
            Access::SETUP_ACCOUNT_PAYMENT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'delete-ledger-account/{id}', ['as' => 'delete-ledger-account', 'uses' => 'Frontend\StudentAccountSetupController@deleteLedgerAccountInfo'])
        ->middleware(CurrentRole::Can([
            Access::FINANCE_ACCESS,
            Access::SETUP_ACCOUNT_PAYMENT_ACCESS,
        ]));

    Route::match(['get', 'post'], 'delete-setup-staff-level/{id}', ['as' => 'delete-setup-staff-level', 'uses' => 'Frontend\StudentAccountSetupController@deleteSetupStaffLevel']);
    Route::match(['get', 'post'], 'setup-staff-level', ['as' => 'setup-staff-level', 'uses' => 'Frontend\StudentAccountSetupController@setupStaffLevel']);
    Route::match(['get', 'post'], 'delete-define-level-activity/{id}', ['as' => 'delete-define-level-activity', 'uses' => 'Frontend\StudentAccountSetupController@deleteDefineLevelActivity']);
    Route::match(['get', 'post'], 'define-level-activity', ['as' => 'define-level-activity', 'uses' => 'Frontend\StudentAccountSetupController@defineLevelActivity']);
    Route::match(['get', 'post'], 'payment-mode-info', ['as' => 'payment-mode-info', 'uses' => 'Frontend\StudentAccountSetupController@addPaymentModeInfo']);
    Route::match(['get', 'post'], 'edit-payment-mode/{id}', ['as' => 'edit-payment-mode', 'uses' => 'Frontend\StudentAccountSetupController@editPaymentModeInfo']);
    Route::match(['get', 'post'], 'delete-payment-mode/{id}', ['as' => 'delete-payment-mode', 'uses' => 'Frontend\StudentAccountSetupController@deletePaymentModeInfo']);
    Route::match(['get', 'post'], 'set-default-payment-mode/{id}', ['as' => 'set-default-payment-mode', 'uses' => 'Frontend\StudentAccountSetupController@setDefaultPaymentMode']);

    // bank-reconciliation
    Route::match(['get', 'post'], 'bank-reconciliation', ['as' => 'bank-reconciliation', 'uses' => 'Frontend\BankReconciliationController@bankReconciliation'])
        ->middleware(CurrentRole::Can([
            Access::FINANCE_ACCESS,
            Access::RECONCILE_BANK_ACCESS,
        ]));
    Route::match(['get', 'post'], 'bank-reconciliation/ajaxAction', ['as' => 'bank-reconciliation-ajaxAction', 'uses' => 'Frontend\BankReconciliationController@ajaxAction'])
        ->middleware(CurrentRole::Can([
            Access::FINANCE_ACCESS,
            Access::RECONCILE_BANK_ACCESS,
        ]));
    Route::match(['get', 'post'], 'delete-bank-reconciliation/{id}', ['as' => 'delete-bank-reconciliation', 'uses' => 'Frontend\BankReconciliationController@deleteBankReconciliation'])
        ->middleware(CurrentRole::Can([
            Access::FINANCE_ACCESS,
            Access::RECONCILE_BANK_ACCESS,
        ]));

    Route::match(['get', 'post'], 'generate-invoice', ['as' => 'generate-invoice', 'uses' => 'Frontend\GenerateInvoiceController@generateInvoice'])
        ->middleware(CurrentRole::Can([
            Access::PAYMENT_MAIN_ACCESS,
            Access::INVOICE_ACCESS,
        ]));
    Route::match(['get', 'post'], 'generate-invoice/ajaxAction', ['as' => 'generate-invoice-ajaxAction', 'uses' => 'Frontend\GenerateInvoiceController@ajaxAction']);

    // Setup Assessment Task
    Route::match(['get', 'post'], 'setup-assessment-task', ['as' => 'setup-assessment-task', 'uses' => 'Frontend\AssessmentTaskController@addAssessmentTask']);

    Route::match(['get', 'post'], 'assessmentTask/ajaxAction', ['as' => 'assessmentTask-ajaxAction', 'uses' => 'Frontend\AssessmentTaskController@ajaxAction'])
        ->middleware(CurrentRole::Can([
            Access::COMPETENCY_ACCESS,
            Access::SETUP_ASSESSMENT_TASK_ACCESS,
        ]));
    // Task Entry
    Route::match(['get', 'post'], 'task-entry', ['as' => 'task-entry', 'uses' => 'Frontend\TaskEntryController@taskEntry'])
        ->middleware(CurrentRole::Can([
            Access::COMPETENCY_ACCESS,
            Access::TASK_ENTRY_ACCESS,
        ]));
    Route::match(['get', 'post'], 'task-entry/ajaxAction', ['as' => 'task-entry-ajaxAction', 'uses' => 'Frontend\TaskEntryController@ajaxAction'])
        ->middleware(CurrentRole::Can([
            Access::COMPETENCY_ACCESS,
            Access::TASK_ENTRY_ACCESS,
        ]));

    // Task Results Entry
    Route::match(['get', 'post'], 'task-results-entry-redirect', ['as' => 'task-results-entry-redirect', 'uses' => 'Frontend\TaskResultsEntryController@taskResultsEntryRedirect']);
    Route::match(['get', 'post'], 'task-results-entry', ['as' => 'task-results-entry', 'uses' => 'Frontend\TaskResultsEntryController@taskResultsEntry'])
        ->middleware(CurrentRole::Can([
            Access::COMPETENCY_ACCESS,
            Access::TASK_RESULTS_ENTRY_ACCESS,
        ]));
    Route::match(['get', 'post'], 'export-grid-student-list/{course_type_id}/{assessment_task_id}/{subject_id}/{semester_id}/{class_id}', ['as' => 'export-grid-student-list', 'uses' => 'Frontend\TaskResultsEntryController@getExcelSheetStudentList']);
    Route::match(['get', 'post'], 'task-results-entry/ajaxAction', ['as' => 'task-results-entry-ajaxAction', 'uses' => 'Frontend\TaskResultsEntryController@ajaxAction']);

    // Transfer Results
    Route::match(['get', 'post'], 'transfer-results', ['as' => 'transfer-results', 'uses' => 'Frontend\TransferResultsController@transferResults'])
        ->middleware(CurrentRole::Can([
            Access::COMPETENCY_ACCESS,
            Access::TRANSFER_RESULTS_ACCESS,
        ]));
    Route::match(['get', 'post'], 'export-grid-student-list-for/{subject_id}/{semester_id}/{class_id}', ['as' => 'export-grid-student-list-for', 'uses' => 'Frontend\TransferResultsController@getExcelSheetStudentListFor']);
    Route::match(['get', 'post'], 'transfer-results/ajaxAction', ['as' => 'transfer-results-ajaxAction', 'uses' => 'Frontend\TransferResultsController@ajaxAction']);

    // Transfer Results By Unit
    Route::match(['get', 'post'], 'transfer-results-by-unit', ['as' => 'transfer-results-by-unit', 'uses' => 'Frontend\TransferResultsByUnitController@transferResultsByUnit'])
        ->middleware(CurrentRole::Can([
            Access::COMPETENCY_ACCESS,
            Access::TRANSFER_RESULTS_ACCESS,
        ]));
    Route::match(['get', 'post'], 'export-grid-transfer-result-unit/{class_id}/{semester_id}/{subject_id}/{subject}', ['as' => 'export-grid-transfer-result-unit', 'uses' => 'Frontend\TransferResultsByUnitController@getExcelSheetTransferResultUnit']);
    Route::match(['get', 'post'], 'transfer-results-by-unit/ajaxAction', ['as' => 'transfer-results-by-unit-ajaxAction', 'uses' => 'Frontend\TransferResultsByUnitController@ajaxAction']);

    Route::match(['get', 'post'], 'vocational-placement-result', ['as' => 'vocational-placement-result', 'uses' => 'Frontend\VocationalPlacementResultController@index'])
        ->middleware(CurrentRole::Can([
            Access::COMPETENCY_ACCESS,
            Access::VOCATIONAL_PLACEMENT_RESULT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'export-grid-vocational-placement-list-for/{subject_id}/{semester_id}/{class_id}', ['as' => 'export-grid-vocational-placement-list-for', 'uses' => 'Frontend\VocationalPlacementResultController@getExcelSheetVocationalListFor']);
    Route::match(['get', 'post'], 'vocational-placement-result/ajaxAction', ['as' => 'vocational-placement-result-ajaxAction', 'uses' => 'Frontend\VocationalPlacementResultController@ajaxAction']);

    Route::match(['get', 'post'], 'employer-invoice', ['as' => 'employer-invoice', 'uses' => 'Frontend\EmployerInvoiceController@employerInvoice'])
        ->middleware(CurrentRole::Can([
            Access::EMPLOYER_INVOICE_ACCESS,
        ]));
    Route::match(['get', 'post'], 'provider-payment', ['as' => 'provider-payment', 'uses' => 'Frontend\ProviderPaymentController@providerPayment']);
    Route::match(['get', 'post'], 'provider-payment-v2', ['as' => 'provider-payment-v2', 'uses' => 'Frontend\ProviderPaymentController@providerPaymentV2'])
        ->middleware(CurrentRole::Can([
            Access::PROVIDER_PAYMENT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'provider-payment/ajaxAction', ['as' => 'provider-payment-ajaxAction', 'uses' => 'Frontend\ProviderPaymentController@ajaxAction']);
    Route::match(['get', 'post'], 'provider-paymentv2/ajaxAction', ['as' => 'provider-paymentv2-ajaxAction', 'uses' => 'Frontend\ProviderPaymentController@ajaxAction']);

    // Compliance Group Management Route
    Route::match(['get', 'post'], 'compliance-intake-group', ['as' => 'compliance-intake-group', 'uses' => 'Frontend\GroupManagementController@intakegroup'])
        ->middleware(CurrentRole::Can([
            Access::COHORTS_ACCESS,
            Access::SETUP_INTAKE_GROUP_ACCESS,
        ]));
    Route::match(['get', 'post'], 'compliance/ajaxAction', ['as' => 'compliance-ajaxAction', 'uses' => 'Frontend\GroupManagementController@ajaxAction']);
    Route::match(['get', 'post'], 'delete-bulkenrollment/ajaxAction', ['as' => 'delete-bulkenrollment-ajaxAction', 'uses' => 'Frontend\GroupManagementController@ajaxAction']);
    Route::match(['get', 'post'], 'delete-compliance-group/{id}', ['as' => 'delete-compliance-group', 'uses' => 'Frontend\GroupManagementController@deleteComplianceGroup']);
    Route::match(['get', 'post'], 'student-bulk-enrollment-by-group', ['as' => 'student-bulk-enrollment-by-group', 'uses' => 'Frontend\GroupManagementController@bulkEnrollmentByGroup'])
        ->middleware(CurrentRole::Can([
            Access::COHORTS_ACCESS,
            Access::BULK_ENROLLMENT_GROUP_ACCESS,
        ]));
    Route::match(['get', 'post'], 'compliance-intervention/{id?}', ['as' => 'compliance-intervention', 'uses' => 'Frontend\GroupManagementController@complianceIntervention']);
    Route::match(['get', 'post'], 'compliance-intervention-export', ['as' => 'compliance-intervention-export', 'uses' => 'Frontend\GroupManagementController@complianceInterventionExport']);
    Route::match(['get', 'post'], 'compliance-studentassign-group', ['as' => 'compliance-studentassign-group', 'uses' => 'Frontend\GroupManagementController@complianceStudentAssigngroup'])
        ->middleware(CurrentRole::Can([
            Access::COHORTS_ACCESS,
            Access::ASSIGN_STUDENT_GROUP_ACCESS,
        ]));
    Route::match(['get', 'post'], 'compliance-studentassign-group-management', ['as' => 'compliance-studentassign-group-management', 'uses' => 'Frontend\GroupManagementController@complianceStudentAssigngroupManagement']);
    Route::match(['get', 'post'], 'flexible-timetable-allocation-by-group', ['as' => 'flexible-timetable-allocation-by-group', 'uses' => 'Frontend\GroupManagementController@flexibleTimetableAllocationByGroup'])
        ->middleware(CurrentRole::Can([
            Access::COHORTS_ACCESS,
            Access::FLEXIBLE_TIMETABLE_ALLOCATION_ACCESS,
        ]));
    Route::match(['get', 'post'], 'bulk-enrollment-by-subject', ['as' => 'bulk-enrollment-by-subject', 'uses' => 'Frontend\GroupManagementController@bulkEnrollmentBySubject'])
        ->middleware(CurrentRole::Can([
            Access::COHORTS_ACCESS,
            Access::BULK_ENROLLMENT_SUBJECT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'export-excel-bulkenrollment/{semester}/{term}/{subject}/{batch}', ['as' => 'export-excel-bulkenrollment', 'uses' => 'Frontend\GroupManagementController@exportExcelBulkenrollment']);
    Route::match(['get', 'post'], 'export-grid-enroll-student/{semester}/{term}/{subject}/{batch}', ['as' => 'export-grid-enroll-student', 'uses' => 'Frontend\GroupManagementController@exportGridEnrollForStudent']);
    Route::match(['get', 'post'], 'delete-bulk-enrolled-student/{id}', ['as' => 'delete-bulk-enrolled-student', 'uses' => 'Frontend\GroupManagementController@deleteBulkEnrolledStudentInfo']);
    Route::match(['get', 'post'], 'assign-student-excel', ['as' => 'assign-student-excel', 'uses' => 'Frontend\GroupManagementController@generateAssignStudentExcel']);

    // Compliance Attendance Management Route
    Route::match(['get', 'post'], 'add-class-attendance', ['as' => 'add-class-attendance', 'uses' => 'Frontend\AttendanceManagementController@classAttendance']);
    Route::match(['get', 'post'], 'attendance-management/ajaxAction', ['as' => 'attendance-management-ajaxAction', 'uses' => 'Frontend\AttendanceManagementController@ajaxAction']);
    Route::match(['get', 'post'], 'attendance-summary', ['as' => 'attendance-summary', 'uses' => 'Frontend\AttendanceManagementController@attendanceSummary'])
        ->middleware(CurrentRole::Can([
            Access::ATTENDANCE_ACCESS,
            Access::ATTENDANCE_SUMMARY_ACCESS,
        ]));
    Route::match(['get', 'post'], 'student-attendance-summary/{id}', ['as' => 'student-attendance-summary', 'uses' => 'Frontend\AttendanceManagementController@studentAttendanceSummary']);
    Route::match(['get', 'post'], 'student-attadance-certificate/{id}/{course_id}/{todate}/{fromdate}', ['as' => 'student-attadance-certificate', 'uses' => 'Frontend\AttendanceManagementController@studentAttendanceCertificate']);
    Route::match(['get', 'post'], 'bulk-attendance-weekly', ['as' => 'bulk-attendance-weekly', 'uses' => 'Frontend\AttendanceManagementController@bulkAttendanceWeekly'])
        ->middleware(CurrentRole::Can([
            Access::ATTENDANCE_ACCESS,
            Access::BULK_ATTENDANCE_ACCESS,
        ]));
    Route::match(['get', 'post'], 'report-and-warnings', ['as' => 'report-and-warnings', 'uses' => 'Frontend\AttendanceManagementController@reportAndWarnings'])
        ->middleware(CurrentRole::Can([
            Access::ATTENDANCE_ACCESS,
            Access::ATTENDANCE_REPORTS_ACCESS,
        ]));
    Route::match(['get', 'post'], 'export-reports-student-list/{course}/{semester}/{term}/{week_period}/{type}/{value}', ['as' => 'export-reports-student-list', 'uses' => 'Frontend\AttendanceManagementController@exportReportForStudent']);
    Route::match(['get', 'post'], 'bulk-attendance-subject', ['as' => 'bulk-attendance-subject', 'uses' => 'Frontend\AttendanceManagementController@bulkAttendanceSubject']);

    Route::match(['get', 'post'], 'bulkUpdate/ajaxAction', ['as' => 'bulkUpdate-ajaxAction', 'uses' => 'Frontend\BulkUpdateController@ajaxAction']);
    Route::match(['get', 'post'], 'maintain-student-activity/{id}', ['as' => 'maintain-student-activity', 'uses' => 'Frontend\BulkUpdateController@maintainStudentActivity']);
    Route::match(['get', 'post'], 'update-unit-outcome', ['as' => 'update-unit-outcome', 'uses' => 'Frontend\BulkUpdateController@updateUnitOutcome']);
    Route::match(['get', 'post'], 'bulk-update-result', ['as' => 'bulk-update-result', 'uses' => 'Frontend\BulkUpdateController@resultAvetmissInfo']);
    Route::match(['get', 'post'], 'bulk-completion-update', ['as' => 'bulk-completion-update', 'uses' => 'Frontend\BulkUpdateController@completionUpdateInfo']);
    Route::match(['get', 'post'], 'bulk-update-student-course', ['as' => 'bulk-update-student-course', 'uses' => 'Frontend\BulkUpdateController@updateStudentCourseInfo'])
        ->middleware(CurrentRole::Can([
            Access::UPDATE_STATUS_ACCESS,
            Access::UPDATE_STUDENT_COURSE_ACCESS,
        ]));
    Route::match(['get', 'post'], 'bulk-update-course-template', ['as' => 'bulk-update-course-template', 'uses' => 'Frontend\BulkUpdateController@updateCourseTemplateInfo'])
        ->middleware(CurrentRole::Can([
            Access::UPDATE_STATUS_ACCESS,
            Access::UPDATE_STUDENT_COURSE_TEMPLATE_ACCESS,
        ]));
    Route::match(['get', 'post'], 'update-student-result/{id}', ['as' => 'update-student-result', 'uses' => 'Frontend\BulkUpdateController@updateStudentResultInfo']);
    Route::match(['get', 'post'], 'update-student-unit-result/{id}', ['as' => 'update-student-unit-result', 'uses' => 'Frontend\BulkUpdateController@updateStudentUnitResultInfo']);
    Route::match(['get', 'post'], 'delete-student-unit-result/{id}', ['as' => 'delete-student-unit-result', 'uses' => 'Frontend\BulkUpdateController@deleteStudentUnitResultInfo']);
    Route::match(['get', 'post'], 'update-student-activity/{id}', ['as' => 'update-student-activity', 'uses' => 'Frontend\BulkUpdateController@updateStudentActivityInfo']);
    Route::match(['get', 'post'], 'bulk-update-course-template-excel', ['as' => 'bulk-update-course-template-excel', 'uses' => 'Frontend\BulkUpdateController@courseTemplateExcel']);

    Route::match(['get', 'post'], 'generate-bulk-certificate', ['as' => 'generate-bulk-certificate',   'uses' => 'Frontend\BulkCertificateController@generateBulkCertificateInfo']);
    Route::match(['get', 'post'], 'generate-bulk-certificate-with-template', ['as' => 'generate-bulk-certificate-with-template',   'uses' => 'Spa\CertificateGenerateController@generateBulkCertificates']);
    Route::match(['get', 'post'], 'bulkCertificate/ajaxAction', ['as' => 'bulkCertificate-ajaxAction',                  'uses' => 'Frontend\BulkCertificateController@ajaxAction']);
    Route::match(['get', 'post'], 'pdf-template-list', ['as' => 'pdf-template-list',           'uses' => 'Frontend\PdfTemplateController@pdfTemplateList'])
        ->middleware(CurrentRole::Can([
            Access::LETTER_TEMPLATE_ACCESS,
            Access::PDF_TEMPLATE_LIST_ACCESS,
        ]));
    Route::match(['get', 'post'], 'add-pdf-template', ['as' => 'add-pdf-template',            'uses' => 'Frontend\PdfTemplateController@addPdfTemplate'])
        ->middleware(CurrentRole::Can([
            Access::LETTER_TEMPLATE_ACCESS,
            Access::PDF_TEMPLATE_LIST_ACCESS,
        ]));
    Route::match(['get', 'post'], 'edit-pdf-template/{id}', ['as' => 'edit-pdf-template',           'uses' => 'Frontend\PdfTemplateController@editPdfTemplate'])
        ->middleware(CurrentRole::Can([
            Access::LETTER_TEMPLATE_ACCESS,
            Access::PDF_TEMPLATE_LIST_ACCESS,
        ]));
    Route::match(['get', 'post'], 'preview-pdf-template/{id}', ['as' => 'preview-pdf-template',        'uses' => 'Frontend\PdfTemplateController@viewPdfTemplate']);
    Route::match(['get', 'post'], 'pdf-template/ajaxAction', ['as' => 'pdf-template-ajaxAction',                  'uses' => 'Frontend\PdfTemplateController@ajaxAction'])
        ->middleware(CurrentRole::Can([
            Access::LETTER_TEMPLATE_ACCESS,
            Access::PDF_TEMPLATE_LIST_ACCESS,
        ]));

    Route::match(['get', 'post'], 'view-elearning-link-list', ['as' => 'view-elearning-link-list', 'uses' => 'Frontend\ElearningLinkController@elearningLinkList'])
        ->middleware(CurrentRole::Can([
            Access::LETTER_TEMPLATE_ACCESS,
            Access::PDF_TEMPLATE_LIST_ACCESS,
        ]));
    Route::match(['get', 'post'], 'add-elearning-link', ['as' => 'add-elearning-link', 'uses' => 'Frontend\ElearningLinkController@addElearningLink']);
    Route::match(['get', 'post'], 'edit-elearning-link/{id}', ['as' => 'edit-elearning-link', 'uses' => 'Frontend\ElearningLinkController@editElearningLink']);
    Route::match(['get', 'post'], 'delete-elearning-link/{id}', ['as' => 'delete-elearning-link', 'uses' => 'Frontend\ElearningLinkController@deleteElearningLink']);
    Route::match(['get', 'post'], 'elearning-link/ajaxAction', ['as' => 'elearning-link-ajaxAction', 'uses' => 'Frontend\ElearningLinkController@ajaxAction']);

    Route::match(['get', 'post'], 'download-unit-academic-summary-pdf/{id}', ['as' => 'download-unit-academic-summary-pdf', 'uses' => 'Frontend\StudentsController@downloadUnitAcademicSummaryPDF']);
    Route::match(['get', 'post'], 'transcript-pdf/{type}/{id}', ['as' => 'transcript-pdf', 'uses' => 'Frontend\BulkCertificateController@generateTranscriptPdf']);
    Route::match(['get', 'post'], 'elicos-pdf/{type}/{id}', ['as' => 'elicos-pdf', 'uses' => 'Frontend\BulkCertificateController@generateElicosPdf']);
    Route::match(['get', 'post'], 'transcript-pdf-multiple/{type}/{id}', ['as' => 'transcript-pdf-multiple', 'uses' => 'Frontend\BulkCertificateController@generateTranscriptPdfMultiple']);
    Route::match(['get', 'post'], 'elicos-pdf-multiple/{type}/{id}', ['as' => 'elicos-pdf-multiple', 'uses' => 'Frontend\BulkCertificateController@generateElicosPdfMultiple']);

    Route::match(['get', 'post'], 'add-intervention', ['as' => 'add-intervention', 'uses' => 'Frontend\GroupManagementController@complianceInterventionAdd']);

    Route::match(['get', 'post'], 'additional-service-fee', ['as' => 'additional-service-fee', 'uses' => 'Frontend\AdditionalServiceFeeController@additionalServiceFee']);
    Route::match(['get', 'post'], 'delete-additional-service/{id}', ['as' => 'delete-additional-service', 'uses' => 'Frontend\AdditionalServiceFeeController@deleteAdditionalService']);
    Route::match(['get', 'post'], 'delete-additional-service-provider/{id}', ['as' => 'delete-additional-service-provider', 'uses' => 'Frontend\AdditionalServiceFeeController@deleteAdditionalServiceProvider']);
    Route::match(['get', 'post'], 'additional-service-provider-update', ['as' => 'additional-service-provider-update', 'uses' => 'Frontend\AdditionalServiceFeeController@additionalServiceProviderUpdate']);
    Route::match(['get', 'post'], 'additional-service-update', ['as' => 'additional-service-update', 'uses' => 'Frontend\AdditionalServiceFeeController@additionalServiceUpdate']);
    Route::match(['get', 'post'], 'additional-service-provider', ['as' => 'additional-service-provider', 'uses' => 'Frontend\AdditionalServiceFeeController@additionalServiceProvider']);
    Route::match(['get', 'post'], 'service-provider-dependent', ['as' => 'service-provider-dependent', 'uses' => 'Frontend\AdditionalServiceFeeController@serviceProviderDependent']);
    Route::match(['get', 'post'], 'additioanal-service-fee/ajaxAction', ['as' => 'additioanal-service-fee-ajaxAction', 'uses' => 'Frontend\AdditionalServiceFeeController@ajaxAction']);

    Route::match(['get', 'post'], 'trineeship-visits', ['as' => 'trineeship-visits', 'uses' => 'Frontend\TrineeshipVisitsController@trineeshipVisits'])
        ->middleware(CurrentRole::Can([
            Access::TRAINEESHIP_APPRENTICESHIP_ACCESS,
            Access::TRAINEESHIP_APPRENTICESHIP_BETA_ACCESS,
        ]));
    Route::match(['get', 'post'], 'traineeshipVisits/ajaxAction', ['as' => 'traineeshipVisits-ajaxAction', 'uses' => 'Frontend\TrineeshipVisitsController@ajaxAction']);

    Route::match(['get', 'post'], 'generate-offer-invoice/{course_id}/{student_id}', ['as' => 'generate-offer-invoice', 'uses' => 'Frontend\StudentPaymentController@generateOfferInvoice']);

    Route::match(['get', 'post'], 'survey-manager-question-management', ['as' => 'survey-manager-question-management', 'uses' => 'Frontend\SurveyManagementController@questionManagementInfo'])
        ->middleware(CurrentRole::Can([
            Access::SURVEY_MANAGEMENT_ACCESS,
            Access::SURVEY_MANAGEMENT_BETA_ACCESS,
        ]));
    Route::match(['get', 'post'], 'survey-manager-question-management-edit/{id}', ['as' => 'survey-manager-question-management-edit', 'uses' => 'Frontend\SurveyManagementController@questionManagementEdit'])
        ->middleware(CurrentRole::Can([
            Access::SURVEY_MANAGEMENT_ACCESS,
            Access::SURVEY_MANAGEMENT_BETA_ACCESS,
            Access::SURVEY_ACTIVATION_ACCESS,
        ]));
    Route::match(['get', 'post'], 'survey-manager-activation', ['as' => 'survey-manager-activation', 'uses' => 'Frontend\SurveyManagementController@activationInfo'])
        ->middleware(CurrentRole::Can([
            Access::SURVEY_MANAGEMENT_ACCESS,
            Access::SURVEY_MANAGEMENT_BETA_ACCESS,
            Access::SURVEY_RESULTS_NOT_SENT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'survey-result-not-send-request', ['as' => 'survey-result-not-send-request', 'uses' => 'Frontend\SurveyResultController@notSendRequestInfo'])
        ->middleware(CurrentRole::Can([
            Access::SURVEY_MANAGEMENT_ACCESS,
            Access::SURVEY_MANAGEMENT_BETA_ACCESS,
            Access::SURVEY_RESULTS_NOT_SENT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'survey-result-send-request', ['as' => 'survey-result-send-request', 'uses' => 'Frontend\SurveyManagementController@sendRequestInfo'])
        ->middleware(CurrentRole::Can([
            Access::SURVEY_MANAGEMENT_ACCESS,
            Access::SURVEY_MANAGEMENT_BETA_ACCESS,
            Access::SURVEY_RESULTS_SENT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'survey-add-new-value', ['as' => 'survey-add-new-value', 'uses' => 'Frontend\SurveyManagementController@addNewValue'])
        ->middleware(CurrentRole::Can([
            Access::SURVEY_MANAGEMENT_ACCESS,
            Access::SURVEY_MANAGEMENT_BETA_ACCESS,
            Access::SURVEY_ACTIVATION_ACCESS,
        ]));
    Route::match(['get', 'post'], 'survey-edit-new-value', ['as' => 'survey-edit-new-value', 'uses' => 'Frontend\SurveyManagementController@editNewValue'])
        ->middleware(CurrentRole::Can([
            Access::SURVEY_MANAGEMENT_ACCESS,
            Access::SURVEY_MANAGEMENT_BETA_ACCESS,
        ]));
    Route::match(['get', 'post'], 'survay-manager-send-request/{id}', ['as' => 'survay-manager-send-request', 'uses' => 'Frontend\SurveyManagementController@sendRequest']);
    Route::match(['get', 'post'], 'survey-manager/ajaxAction', ['as' => 'survey-manager-ajaxAction', 'uses' => 'Frontend\SurveyManagementController@ajaxAction']);
    Route::match(['get', 'post'], 'edit-activation', ['as' => 'edit-activation', 'uses' => 'Frontend\SurveyManagementController@editActivaion']);
    Route::match(['get', 'post'], 'delete-activation/{id}', ['as' => 'delete-activation', 'uses' => 'Frontend\SurveyManagementController@deleteActivaion']);
    Route::match(['get', 'post'], 'preview-submit-details/{studentId}/{formId}', ['as' => 'preview-submit-details', 'uses' => 'Frontend\SurveyResultController@previewSubmitDetails']);
    Route::match(['get', 'post'], 'preview-submit-details-pdf/{studentId}/{formId}', ['as' => 'preview-submit-details-pdf', 'uses' => 'Frontend\SurveyResultController@previewSubmitDetailsPdf']);
    Route::match(['get', 'post'], 'delete-submitted-result/{studentId}', ['as' => 'delete-submitted-result', 'uses' => 'Frontend\SurveyResultController@deleteSubmitedResult']);
    Route::match(['get', 'post'], 'survey-question-excel/{formId}/{type}', ['as' => 'survey-question-excel', 'uses' => 'Frontend\SurveyResultController@surveyExcel']);
    Route::match(['get', 'post'], 'survey-question-excel-summary/{formId}/{type}', ['as' => 'survey-question-excel-summary', 'uses' => 'Frontend\SurveyResultController@surveyExcelSummary']);
    Route::match(['get', 'post'], 'survey-question-text/{formId}/{type}', ['as' => 'survey-question-text', 'uses' => 'Frontend\SurveyResultController@surveyText']);
    Route::match(['get', 'post'], 'survay-admin-comment', ['as' => 'survay-admin-comment', 'uses' => 'Frontend\SurveyResultController@updateAdminComment']);
    Route::match(['get', 'post'], 'no-send-request-excel/{questionId}', ['as' => 'no-send-request-excel', 'uses' => 'Frontend\SurveyResultController@noSendRequestExcel']);
    Route::match(['get', 'post'], 'survey-result/ajaxAction', ['as' => 'survey-result-ajaxAction', 'uses' => 'Frontend\SurveyResultController@ajaxAction']);

    // Reports
    // Route::match(['get', 'post'], 'generate-reports-xls', ['as' => 'generate-reports-xls', 'uses' => 'Frontend\ReportsController@generateReportsXLS']);
    Route::match(['get', 'post'], 'generate-reports-xls/{inputArr}', ['as' => 'generate-reports-xls', 'uses' => 'Frontend\ReportsController@generateReportsXlsSheet']);

    Route::match(['get', 'post'], 'generate-reports', ['as' => 'generate-reports', 'uses' => 'Frontend\ReportsController@generateReports']);
    Route::match(['get', 'post'], 'generate-reports-letter', ['as' => 'generate-reports-letter', 'uses' => 'Frontend\ReportsController@generateReportsLetter']);
    Route::match(['get', 'post'], 'generate-reports/ajaxAction', ['as' => 'generate-reports-ajaxAction', 'uses' => 'Frontend\ReportsController@ajaxAction']);

    Route::match(['get', 'post'], 'nvr-report-data-extraction', ['as' => 'nvr-report-data-extraction', 'uses' => 'Frontend\NvrReportController@NvrReportDataExtraction']);
    Route::match(['get', 'post'], 'cqr-report-data-extraction', ['as' => 'cqr-report-data-extraction', 'uses' => 'Frontend\CqrReportController@cqrReportDataExtraction']);
    Route::match(['get', 'post'], 'cqrReport/ajaxAction', ['as' => 'cqrReport-ajaxAction', 'uses' => 'Frontend\CqrReportController@ajaxAction']);

    Route::match(['get', 'post'], 'staff-invoice/{year}/{period_id}', ['as' => 'staff-invoice', 'uses' => 'Frontend\StaffTimesheetController@staffInvoice'])
        ->middleware(CurrentRole::Can([
            Access::VIEW_TIMESHEET_INVOICE_ACCESS,
        ]));
    Route::match(['get', 'post'], 'process-timesheet', ['as' => 'process-timesheet', 'uses' => 'Frontend\StaffTimesheetController@processTimesheet'])
        ->middleware(CurrentRole::Can([
            Access::PROCESS_TIMESHEET_ACCESS,
        ]));
    Route::match(['get', 'post'], 'process-timesheet-document/{id}', ['as' => 'process-timesheet-document', 'uses' => 'Frontend\StaffTimesheetController@processTimesheetDocument']);
    Route::match(['get', 'post'], 'change-timesheet-status', ['as' => 'change-timesheet-status', 'uses' => 'Frontend\StaffTimesheetController@changeStatusForTimesheet']);
    Route::match(['get', 'post'], 'staffTimesheet/ajaxAction', ['as' => 'staffTimesheet-ajaxAction', 'uses' => 'Frontend\StaffTimesheetController@ajaxAction']);

    Route::match(['get', 'post'], 'allocateProvider/ajaxAction', ['as' => 'allocateProvider-ajaxAction', 'uses' => 'Frontend\AllocateProviderController@ajaxAction']);
    Route::match(['get', 'post'], 'allocate-provider', ['as' => 'allocate-provider', 'uses' => 'Frontend\AllocateProviderController@allocateProvider']);

    // End here
    // Routes for latter setup
    Route::match(['get', 'post'], 'report-letter-setup', ['as' => 'report-letter-setup', 'uses' => 'Frontend\ReportsController@letterSetupList']);
    Route::match(['get', 'post'], 'add-new-letter/{id?}', ['as' => 'add-new-letter', 'uses' => 'Frontend\ReportsController@addNewLatter']);
    Route::match(['get', 'post'], 'edit-new-letter/{id}', ['as' => 'edit-new-letter', 'uses' => 'Frontend\ReportsController@editNewLatter']);
    Route::match(['get', 'post'], 'letter-file-attach', ['as' => 'letter-file-attach', 'uses' => 'Frontend\ReportsController@lettetFileAttachment']);
    Route::match(['get', 'post'], 'add-extra-parameter', ['as' => 'add-extra-parameter', 'uses' => 'Frontend\ReportsController@letterAddExtraParameter']);
    Route::match(['get', 'post'], 'letter-pdf-preview', ['as' => 'letter-pdf-preview', 'uses' => 'Frontend\ReportsController@letterPdfPreview']);
    Route::match(['get', 'post'], 'delete-extra-parameter/{id}', ['as' => 'delete-extra-parameter', 'uses' => 'Frontend\ReportsController@deleteExtraParameter']);

    // Routes from send mail test url
    Route::match(['get', 'post'], 'college-document/{id}', ['as' => 'college-document', 'uses' => 'Frontend\CollegeDocumentController@collegeDocument']);
    // Route::match(['get', 'post'], 'view-college-document/{id}', ['as' => 'view-college-document', 'uses' => 'Frontend\CollegeDocumentController@collegeDocument']);
    Route::match(['get', 'post'], 'view-college-document/{id}', ['as' => 'view-college-document', 'uses' => 'Frontend\CollegeDocumentController@collegeDocumentList']);
    Route::match(['get', 'post'], 'collegeDocument/ajaxAction', ['as' => 'collegeDocument-ajaxAction', 'uses' => 'Frontend\CollegeDocumentController@ajaxAction']);
    Route::match(['get', 'post'], 'edit-collegeDocument/edit', ['as' => 'edit-collegeDocument', 'uses' => 'Frontend\CollegeDocumentController@editCollegeDocument']);
    Route::match(['get', 'post'], 'delete-collegeDocument/{id}', ['as' => 'delete-collegeDocument', 'uses' => 'Frontend\CollegeDocumentController@deleteCollegeDocument']);
    Route::match(['get', 'post'], 'assign-role-document', ['as' => 'assign-role-document', 'uses' => 'Frontend\CollegeDocumentController@assignRoleDocument']);

    Route::match(['get', 'post'], 'create-agent-user', ['as' => 'create-agent-user', 'uses' => 'Frontend\AgentController@createAgentUser']);
    Route::match(['get', 'post'], 'add-agent-agency-status', ['as' => 'add-agent-agency-status', 'uses' => 'Frontend\AgentController@addAgentAgencyStatus']);

    // Start Route for SMS Template

    Route::match(['get', 'post'], 'add-sms-template', ['as' => 'add-sms-template', 'uses' => 'Frontend\SmsTemplateController@addSMSTemplate']);
    Route::match(['get', 'post'], 'edit-sms-template/{id}', ['as' => 'edit-sms-template', 'uses' => 'Frontend\SmsTemplateController@editSMSTemplate']);
    Route::match(['get', 'post'], 'view-sms-template-list', ['as' => 'view-sms-template-list', 'uses' => 'Frontend\SmsTemplateController@viewSMSTemplateList']);
    Route::match(['get', 'post'], 'edit-sms-template-status/{id}/{status}', ['as' => 'edit-sms-template-status', 'uses' => 'Frontend\SmsTemplateController@editSMSTemplateStatus']);
    Route::match(['get', 'post'], 'delete-sms-template/{id}', ['as' => 'delete-sms-template', 'uses' => 'Frontend\SmsTemplateController@deleteSMTemplate']);
    Route::match(['get', 'post'], 'smsTemplete/ajaxAction', ['as' => 'smsTemplete/ajaxAction', 'uses' => 'Frontend\SmsTemplateController@ajaxAction']);

    Route::match(['get', 'post'], 'subject_specific/{id}', ['as' => 'subject_specific', 'uses' => 'Frontend\CourseMaterialController@subjectSpecificDoc']);
    Route::match(['get', 'post'], 'course_specific/{id}', ['as' => 'course_specific', 'uses' => 'Frontend\CourseMaterialController@courseSpecificDoc']);

    Route::match(['get', 'post'], 'downloadSubjectFile/{id}', ['as' => 'downloadSubjectFile', 'uses' => 'Frontend\CourseMaterialController@downloadSubjectFile']);
    Route::match(['get', 'post'], 'downloadCourseFile/{id}', ['as' => 'downloadCourseFile', 'uses' => 'Frontend\CourseMaterialController@downloadCourseFile']);

    Route::match(['get', 'post'], 'edit-subject-specific', ['as' => 'edit-subject-specific', 'uses' => 'Frontend\CourseMaterialController@editSubjectSpecificDoc']);
    Route::match(['get', 'post'], 'edit-course-specific', ['as' => 'edit-course-specific', 'uses' => 'Frontend\CourseMaterialController@editCourseSpecificDoc']);

    Route::match(['get', 'post'], 'delete-subject-material/{id}', ['as' => 'delete-subject-material', 'uses' => 'Frontend\CourseMaterialController@deleteSubjectMaterial']);
    Route::match(['get', 'post'], 'delete-course-material/{id}', ['as' => 'delete-course-material', 'uses' => 'Frontend\CourseMaterialController@deleteCourseMaterial']);

    Route::match(['get', 'post'], 'assign-role-subject-material', ['as' => 'assign-role-subject-material', 'uses' => 'Frontend\CourseMaterialController@assignSubjectMaterialRole']);
    Route::match(['get', 'post'], 'assign-role-course-material', ['as' => 'assign-role-course-material', 'uses' => 'Frontend\CourseMaterialController@assignCourseMaterialRole']);

    Route::match(['get', 'post'], 'courseMaterial/ajaxAction', ['as' => 'courseMaterial-ajaxAction', 'uses' => 'Frontend\CourseMaterialController@ajaxAction']);
    Route::match(['get', 'post'], 'verify-student-usi', ['as' => 'verify-student-usi', 'uses' => 'Frontend\UsiVerificationProcessController@verifyUSI']);
    Route::match(['get', 'post'], 'create-student-usi', ['as' => 'create-student-usi', 'uses' => 'Frontend\UsiVerificationProcessController@createUSI']);
    Route::match(['get', 'post'], 'locate-student-usi', ['as' => 'locate-student-usi', 'uses' => 'Frontend\UsiVerificationProcessController@locateUSI']);
    Route::match(['get', 'post'], 'aus-key-config', ['as' => 'aus-key-config', 'uses' => 'Frontend\UsiVerificationProcessController@ausKeyConfig']);

    Route::match(['get', 'post'], 'usi/ajaxAction', ['as' => 'usi-ajaxAction', 'uses' => 'Frontend\UsiVerificationProcessController@ajaxAction']);
    Route::match(['get', 'post'], 'add-dvs-info', ['as' => 'add-dvs-info', 'uses' => 'Frontend\UsiVerificationProcessController@addDVSInformation']);
    Route::match(['get', 'post'], 'update-student-record', ['as' => 'update-student-record', 'uses' => 'Frontend\UsiVerificationProcessController@updateStudentInfo']);
    Route::match(['get', 'post'], 'usi-verify-student-list-xls', ['as' => 'usi-verify-student-list-xls', 'uses' => 'Frontend\UsiVerificationProcessController@generateUSIVerifyStudentXLS']);
    Route::match(['get', 'post'], 'staff-verified-student-list-xls', ['as' => 'staff-verified-student-list-xls', 'uses' => 'Frontend\UsiVerificationProcessController@generateStaffVerifiedStudentXLS']);
    Route::match(['get', 'post'], 'locate-missing-student-list-xls', ['as' => 'locate-missing-student-list-xls', 'uses' => 'Frontend\UsiVerificationProcessController@generateLocateMissingStudentXLS']);

    // Vet Fee Help (VFH Reporting)
    Route::match(['get', 'post'], 'vet-fee-help', ['as' => 'vet-fee-help', 'uses' => 'Frontend\VetFeeHelpController@studentVetFeeHelpList'])
        ->middleware(CurrentRole::Can([
            Access::DATA_REPORTING_ACCESS,
            Access::VET_FEE_HELP_ACCESS,
        ]));
    Route::match(['get', 'post'], 'vet-fee-help/ajaxAction', ['as' => 'vet-fee-help-ajaxAction', 'uses' => 'Frontend\VetFeeHelpController@ajaxAction']);
    Route::match(['get', 'post'], 'vet-fee-export-file', ['as' => 'vet-fee-export-file', 'uses' => 'Frontend\VetFeeHelpController@vetFeeExportFile']);

    Route::match(['get', 'post'], 'student-certificate-email/{id}/{primary_id}', ['as' => 'student-certificate-email', 'uses' => 'Frontend\StudentResultController@studentCertificateEmail']);
    Route::match(['get', 'post'], 'student-certificate-send-email/{id}/{primary_id}', ['as' => 'student-certificate-send-email', 'uses' => 'Frontend\StudentResultController@studentCertificateSendEmail']);

    // Manage Reports
    Route::match(['get', 'post'], 'manage-reports', ['as' => 'manage-reports', 'uses' => 'Frontend\ManageReportsController@viewManageReports']);
    Route::match(['get', 'post'], 'manage-reports/ajaxAction', ['as' => 'manage-reports-ajaxAction', 'uses' => 'Frontend\ManageReportsController@ajaxAction']);

    // Failed Emails
    // Route::match(['get', 'post'], 'failed-emails', ['as' => 'failed-emails', 'uses' => 'Frontend\FailedEmailsController@viewFailEmails']);
    // Route::match(['get', 'post'], 'failed-emails/ajaxAction', ['as' => 'failed-emails-ajaxAction', 'uses' => 'Frontend\FailedEmailsController@ajaxAction']);

    // Evaluation For Admin
    Route::match(['get', 'post'], 'admin-evaluation', ['as' => 'admin-evaluation', 'uses' => 'Frontend\EvaluationController@viewEvaluationInfo'])
        ->middleware(CurrentRole::Can([
            Access::EVALUATION_ACCESS,
        ]));
    Route::match(['get', 'post'], 'admin-evaluation/ajaxAction', ['as' => 'admin-evaluation-ajaxAction', 'uses' => 'Frontend\EvaluationController@ajaxAction']);

    // Assessment Due Date Extention
    Route::match(['get', 'post'], 'assessment-due-date-extention', ['as' => 'assessment-due-date-extention', 'uses' => 'Frontend\AssessmentDueDateController@viewAssessmentList']);
    // Admin Setup services facility
    Route::match(['get', 'post'], 'services-setup', ['as' => 'services-setup', 'uses' => 'Frontend\SetupServicesController@managerServicesSetup']);
    Route::match(['get', 'post'], 'provider-facility', ['as' => 'provider-facility', 'uses' => 'Frontend\SetupServicesController@managerProviderFacility'])
        ->middleware(CurrentRole::Can([
            Access::ASSIGN_FACILITY_ACCESS,
        ]));
    Route::match(['get', 'post'], 'provider-setup', ['as' => 'provider-setup', 'uses' => 'Frontend\SetupServicesController@providerSetup']);
    Route::match(['get', 'post'], 'provider-setup-add', ['as' => 'provider-setup-add', 'uses' => 'Frontend\SetupServicesController@providerSetupAdd']);
    Route::match(['get', 'post'], 'provider-setup-edit/{providerId}', ['as' => 'provider-setup-edit', 'uses' => 'Frontend\SetupServicesController@providerSetupEdit']);
    Route::match(['get', 'post'], 'services-setup/ajaxAction', ['as' => 'services-setup/ajaxAction', 'uses' => 'Frontend\SetupServicesController@ajaxAction']);
    Route::match(['get', 'post'], 'courses/autocomplete', ['as' => 'courses/autocomplete', 'uses' => 'Frontend\CourseController@autocomplete']);
    Route::match(['get', 'post'], 'unit-autocomplete', ['as' => 'unit-autocomplete', 'uses' => 'Frontend\SubjectController@autocomplete']);

    // Service Request Allocation Routes
    Route::match(['get', 'post'], 'service-request-allocation', ['as' => 'service-request-allocation', 'uses' => 'Frontend\ServiceRequestAllocationController@serviceProviderAllocation'])
        ->middleware(CurrentRole::Can([
            Access::UPDATE_SERVICE_ALLOCATION_ACCESS,
        ]));
    Route::match(['get', 'post'], 'service-request-allocation/ajaxAction', ['as' => 'service-request-allocation-ajaxAction', 'uses' => 'Frontend\ServiceRequestAllocationController@ajaxAction']);

    Route::match(['get', 'post'], 'delete-payment-record/{id}', ['as' => 'delete-payment-record', 'uses' => 'Frontend\StudentAccountController@deletePaymentRecord']);
    Route::match(['get', 'post'], 'get-autocomplete-data', ['as' => 'get-autocomplete-data', 'uses' => 'Frontend\StudentsController@getAutocompleteData']);
    Route::match(['get', 'post'], 'get-student-autocomplete-data', ['as' => 'get-student-autocomplete-data', 'uses' => 'Frontend\StudentsController@getStudentAutocompleteData']);

    Route::match(['get', 'post'], 'student-mail-waring-setting', ['as' => 'student-mail-waring-setting', 'uses' => 'Frontend\SettingController@studentWarningMailSetting'])
        ->middleware(CurrentRole::Can([
            Access::ATTENDANCE_ACCESS,
            Access::WARNING_DAYS_SETTING_ACCESS,
        ]));
    Route::match(['get', 'post'], 'student-mail-percentage-waring-setting', ['as' => 'student-mail-percentage-waring-setting', 'uses' => 'Frontend\SettingController@studentPercentageWarningMailSetting'])
        ->middleware(CurrentRole::Can([
            Access::ATTENDANCE_ACCESS,
            Access::WARNING_PERCENTAGE_SETTING_ACCESS,
        ]));
    Route::match(['get', 'post'], 'audit-report', ['as' => 'audit-report', 'uses' => 'Frontend\AuditReportController@auditReport'])
        ->middleware(CurrentRole::Can([
            Access::OTHER_ACCESS,
            Access::ASQA_AUDIT_REPORT_ACCESS,
        ]));
    Route::match(['get', 'post'], 'audit-report-download', ['as' => 'audit-report-download', 'uses' => 'Frontend\AuditReportController@downloadAuditXls'])
        ->middleware(CurrentRole::Can([
            Access::OTHER_ACCESS,
            Access::ASQA_AUDIT_REPORT_ACCESS,
        ]));

    //    Route::resource('departments', Frontend\DepartmentController::class);
    //    Route::post('departments/ajaxAction','Frontend\DepartmentController@ajaxAction');
    //    Route::match(['get', 'post'], 'department-grid-view', ['as' => 'department-grid-view', 'uses' => 'Frontend\DepartmentController@departmentGridView']);

    Route::resource('tasks', Frontend\TaskController::class);
    Route::get('/tasks', function () {
        return redirect()->route('task-list');
    });
    Route::post('tasks/ajaxAction', 'Frontend\TaskController@ajaxAction');
    Route::match(['get', 'post'], 'view-staff-task', ['as' => 'view-staff-task', 'uses' => 'Newdev\TaskManageController@stafftasklist']);
    // Route::match(['get', 'post'], 'view-staff-task', ['as' => 'view-staff-task', 'uses' => 'Frontend\TaskController@viewStaffTask']);
    Route::match(['get', 'post'], 'downloadTaskFile/{id}', ['as' => 'downloadTaskFile', 'uses' => 'Frontend\TaskController@downloadTaskFile']);
});

// Admin Routs Start
$adminPrefix = 'webpanel';
Route::group(['prefix' => $adminPrefix, 'middleware' => ['admin']], function () {

    Route::get('/dashboard', ['as' => 'adminIndex', 'uses' => 'Login\AdminController@dashboard']);
    // Users
    Route::match(['get', 'post'], 'users', ['as' => 'registered_users', 'uses' => 'Admin\UserController@index']);
    Route::match(['get', 'post'], 'users/add', ['as' => 'add_registered_users', 'uses' => 'Admin\UserController@frontendUserAdd']);
    Route::match(['get', 'post'], 'users/delete/{id}', ['as' => 'delete_registered_users', 'uses' => 'Admin\UserController@delete']);
    Route::match(['get', 'post'], 'users/edit/{id}', ['as' => 'edit_registered_users', 'uses' => 'Admin\UserController@edit']);

    // Admin Users
    Route::match(['get', 'post'], 'adminusers', ['as' => 'admin_registered_users', 'uses' => 'Admin\AdminUserController@index']);
    Route::match(['get', 'post'], 'adminusers/add', ['as' => 'admin_add_registered_users', 'uses' => 'Admin\AdminUserController@add']);
    Route::match(['get', 'post'], 'adminusers/delete/{id}', ['as' => 'admin_delete_registered_users', 'uses' => 'Admin\AdminUserController@delete']);
    Route::match(['get', 'post'], 'adminusers/edit/{id}', ['as' => 'admin_edit_registered_users', 'uses' => 'Admin\AdminUserController@edit']);

    // Admin change profile and password
    Route::match(['get', 'post'], '/profile', ['as' => 'admin_profile', 'uses' => 'Admin\AdminUserController@profile']);
    Route::match(['get', 'post'], '/passwordChange', ['as' => 'admin_passwordChange', 'uses' => 'Admin\AdminUserController@passwordChange']);

    // College Info
    Route::match(['get', 'post'], 'college-info/list', ['as' => 'college-info-list', 'uses' => 'Admin\CollegeInfoController@collegeInfoList']);
    Route::match(['get', 'post'], 'college-info/add', ['as' => 'college-info-add', 'uses' => 'Admin\CollegeInfoController@collegeInfoAdd']);
    Route::match(['get', 'post'], 'college-info/edit/{id}', ['as' => 'college-info-edit', 'uses' => 'Admin\CollegeInfoController@collegeInfoEdit']);
    Route::match(['get', 'post'], 'college-info/delete/{id}', ['as' => 'delete-collegeinfo', 'uses' => 'Admin\CollegeInfoController@collegeInfoDelete']);
});

/* web route to generate certificate */
Route::group(['prefix' => 'students', 'middleware' => ['auth']], function () {
    Route::get('courses/certificate/{id}', ['as' => 'studentweb.course.certificate.download',     'uses' => 'Student\StudentProfileController@generateCertificate']);
});

// Route::get($adminPrefix.'/', ['as' => 'admin_root', 'uses' => 'Login\AdminController@login']);
// Route::get($adminPrefix.'/login', ['as' => 'admin_login', 'uses' => 'Login\AdminController@login']);
// Route::post($adminPrefix.'/login', ['as' => 'admin_login_post', 'uses' => 'Login\AdminController@login']);
// Route::get($adminPrefix.'/logout', ['as' => 'admin_logout', 'uses' => 'Login\AdminController@logout']);

Route::get('lastFiveAttendanceCron', 'TestMailController@lastFiveAttendanceCron');
Route::get('totalAttendanceCron', 'TestMailController@totalAttendanceCron');
Route::get('exportExcelReportAndWarnings', 'Frontend\AttendanceManagementController@exportExcelReportAndWarnings');

// Admin Routes End.
Route::get('_admin-galaxy-logs', [\Rap2hpoutre\LaravelLogViewer\LogViewerController::class, 'index'])
    ->middleware([PreventAccessFromCentralDomains::class, 'auth', 'auth.onlySAdmin']);
