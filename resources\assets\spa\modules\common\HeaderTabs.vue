<template>
    <div :class="rootClass">
        <ul class="flex gap-8 font-medium" role="tablist">
            <li
                class="w-auto"
                role="presentation"
                v-for="(tab, index) in getTabs"
                :key="index"
                v-show="isTabVisible(tab)"
            >
                <Link
                    :href="tab.route || tab.link || '#'"
                    :class="[
                        linkClass,
                        current === tab.slug || tab.is_current == true
                            ? 'border-b-2 border-primary-blue-500 text-primary-blue-500'
                            : '',
                    ]"
                    prefetch
                >
                    {{ tab.name }}
                </Link>
            </li>
        </ul>
    </div>
</template>
<script>
import { Link } from '@inertiajs/vue3';
import { twMerge } from 'tailwind-merge';

export default {
    props: {
        current: { type: String, default: '' },
        query: { type: [Object, Array], default: [] },
        tabs: { type: [Object, Array], default: [] },
        pt: {
            type: Object,
            default: {},
        },
        stretched: {
            type: Boolean,
            default: true,
        },
    },
    components: {
        Link,
    },
    computed: {
        getTabs() {
            return this.tabs || [];
        },
        rootClass() {
            return twMerge(
                'flex w-full items-center justify-start gap-6 px-8 border-b border-gray-200',
                this.pt.root
            );
        },
        linkClass() {
            return twMerge(
                'flex w-auto cursor-pointer items-center border-solid px-1 pb-4 leading-5 text-gray-500 hover:border-b-2 hover:border-primary-blue-400',
                this.pt.link
            );
        },
    },
    methods: {
        isTabVisible(tab) {
            // If hidden property doesn't exist, tab is visible by default
            if (!tab.hasOwnProperty('hidden')) {
                return true;
            }

            // If hidden is a function, evaluate it
            if (typeof tab.hidden === 'function') {
                return !tab.hidden();
            }

            // If hidden is a boolean, return the opposite
            return !tab.hidden;
        },
    },
};
</script>
