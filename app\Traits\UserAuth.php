<?php

namespace App\Traits;

use App\Roles;
use GalaxyAPI\Enums\UserStatusEnum;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Spatie\Permission\PermissionRegistrar;
use Support\DTO\MetaKey;

trait UserAuth
{
    public function isAcountant()
    {
        return Session::get('currentRole') == Roles::TYPE_ACCOUNTANT;
    }

    public function isAdmin()
    {
        return Session::get('currentRole') == Roles::TYPE_ADMIN;
    }

    public function isDos()
    {
        return Session::get('currentRole') == Roles::TYPE_DOS || Session::get('currentRole') == Roles::TYPE_DOSELICOS || Session::get('currentRole') == Roles::TYPE_DOSHS || Session::get('currentRole') == Roles::TYPE_DOSVET;
    }

    public function isIT()
    {
        return Session::get('currentRole') == Roles::TYPE_IT;
    }

    public function isMarketing()
    {
        return Session::get('currentRole') == Roles::TYPE_MARKETING;
    }

    public function isSadmin()
    {
        return Session::get('currentRole') == Roles::TYPE_SADMIN;
    }

    public function isStaff()
    {
        return Session::get('currentRole') == Roles::TYPE_STAFF;
    }

    public function isStudentService()
    {
        return Session::get('currentRole') == Roles::TYPE_STUDENTSERVICE;
    }

    public function isAgent()
    {
        return Session::get('currentRole') == Roles::TYPE_AGENT;
    }

    public function isTeacher()
    {
        return Session::get('currentRole') == Roles::TYPE_TEACHER;
    }

    public function isStudent()
    {
        return Session::get('currentRole') == Roles::TYPE_STUDENT;
    }

    public function isAgentStaff()
    {
        return Session::get('currentRole') == Roles::TYPE_AGENT_STAFF;
    }

    public function clearFailedAttempts()
    {
        $this->wrong_password_count = 0;

        return $this->save();
    }

    public function activate()
    {
        $this->status = 1;

        return $this->save();
    }

    public function isActive()
    {
        if ($this->status instanceof UserStatusEnum) {
            return $this->status == UserStatusEnum::ACTIVE;
        }

        return $this->status == 1;
    }

    public function getUUID()
    {
        if (! $this->uuid) {
            $this->uuid = Str::uuid();
            $this->save();
        }

        return $this->uuid;
    }

    public function userRoles(): BelongsToMany
    {
        $relation = $this->morphToMany(
            config('permission.models.role'),
            'model',
            config('permission.table_names.model_has_roles'),
            config('permission.column_names.model_morph_key'),
            app(PermissionRegistrar::class)->pivotRole
        )->withPivot('is_default');

        if (! app(PermissionRegistrar::class)->teams) {
            return $relation;
        }

        $teamsKey = app(PermissionRegistrar::class)->teamsKey;
        $relation->withPivot($teamsKey);
        $teamField = config('permission.table_names.roles').'.'.$teamsKey;

        return $relation->wherePivot($teamsKey, getPermissionsTeamId())
            ->where(fn ($q) => $q->whereNull($teamField)->orWhere($teamField, getPermissionsTeamId()));
    }

    public function currentActiveRole()
    {
        return $this->userRoles()->where('id', session()->get(\Support\Auth\UserGroup::SESSION_KEY))->first();
    }

    public function getCurrentActiveRolePermissions()
    {
        return $this->isSadmin() ? ['all'] : $this->currentActiveRole()?->permissions?->pluck('name')->toArray();
    }

    public function isImpersonating()
    {
        return session()->has(MetaKey::ADMIN_IMPERSONATING);
    }
}
