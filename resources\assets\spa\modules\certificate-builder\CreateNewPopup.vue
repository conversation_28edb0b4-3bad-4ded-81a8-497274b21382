<template>
    <Dialog
        v-if="visible"
        :width="isStudentId ? 800 : 1280"
        title="Create New"
        @close="handleClose"
        dialog-class="tw-content-p-0 tw-dialog"
    >
        <div class="modal-content">
            <div class="grid" :class="isStudentId ? 'grid-cols-2' : 'grid-cols-3'">
                <div class="space-y-4 p-6" :class="{ 'col-span-2': !isStudentId }">
                    <h2 class="text-lg font-bold text-gray-800">Templates for you</h2>
                    <hr />
                    <div class="space-y-4 overflow-y-auto" ref="templateContainer">
                        <div
                            class="grid max-h-[450px] gap-4"
                            :class="isStudentId ? 'grid-cols-1' : 'grid-cols-4'"
                        >
                            <button
                                class="group grid cursor-pointer space-y-1 p-1"
                                @click="handleSelect(null)"
                            >
                                <div
                                    class="relative h-full w-full overflow-hidden border bg-white group-hover:border-primary-blue-300"
                                    :class="{
                                        'ring-1 ring-primary-blue-500': selectedTemplate === null,
                                        'aspect-[707/1000]': !isStudentId,
                                        'aspect-[320/204]': isStudentId,
                                    }"
                                >
                                    <span
                                        class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
                                    >
                                        <IconAdd24Regular class="h-8 w-8 font-bold text-white" />
                                    </span>
                                    <span
                                        class="absolute right-2 top-2"
                                        v-if="selectedTemplate === null"
                                    >
                                        <IconCheckboxChecked24Filled
                                            class="h-6 w-6 text-green-500"
                                        />
                                    </span>
                                </div>
                                <div class="text-xs text-gray-800">Blank Template</div>
                            </button>
                            <template v-for="(template, index) in templates" :key="template.id">
                                <button
                                    class="group grid h-full cursor-pointer space-y-1 p-1"
                                    @click="handleSelect(template)"
                                >
                                    <div
                                        class="relative h-full w-full overflow-hidden border group-hover:border-primary-blue-300"
                                        :class="{
                                            'ring-1 ring-primary-blue-500':
                                                selectedTemplate &&
                                                selectedTemplate?.id === template?.id,
                                        }"
                                    >
                                        <img
                                            v-if="template.name"
                                            :src="template.thumbnail"
                                            class="image-item aspect-[707/1000] h-full w-full object-contain"
                                            alt="Certificate Image"
                                        />
                                        <span
                                            class="invisible absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:visible group-hover:opacity-100"
                                        >
                                            <IconAdd24Regular
                                                class="h-8 w-8 font-bold text-white"
                                            />
                                        </span>
                                        <span
                                            class="absolute right-2 top-2"
                                            v-if="
                                                selectedTemplate &&
                                                selectedTemplate?.id === template?.id
                                            "
                                        >
                                            <IconCheckboxChecked24Filled
                                                class="h-6 w-6 text-green-500"
                                            />
                                        </span>
                                    </div>
                                    <div class="text-xs text-gray-800">
                                        {{ template.name }}
                                    </div>
                                </button>
                            </template>
                        </div>
                    </div>
                </div>
                <div class="col-span-1 h-fit bg-gray-50 p-4" ref="formContainer">
                    <Form
                        ref="formRef"
                        @submit="handleSubmit"
                        :initial-values="{ ...dimensions }"
                        id="formRef"
                        :ignoreModified="true"
                    >
                        <SaveTemplateForm
                            :certificateIdFormate="certificateIdFormate"
                            :dataItem="dataItem"
                            :isCreate="true"
                            @submit="handleSubmit"
                            @close="handleClose"
                            v-if="!isStudentId"
                        />
                        <StudentIdTemplateForm
                            :dataItem="dataItem"
                            :isCreate="true"
                            @submit="handleSubmit"
                            @close="handleClose"
                            v-if="isStudentId"
                        />
                    </Form>
                </div>
            </div>
        </div>
    </Dialog>
</template>
<script setup>
import { ref, watch, computed, onMounted, nextTick } from 'vue';
import { Dialog, DialogActionsBar } from '@progress/kendo-vue-dialogs';
import data from '@spa/pages/certificate-builder/certificateProps.json';
import { Form, FormElement, Field } from '@progress/kendo-vue-form';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import { useCertificateStore } from '@spa/stores/modules/certificate.store';
import { IconAdd24Regular } from '@iconify-prerendered/vue-fluent';
import SaveTemplateForm from '@spa/modules/certificate-builder/SaveTemplateForm.vue';
import useCertificateResource from '@spa/services/certificates/certificateResource';
import { IconCheckboxChecked24Filled } from '@iconify-prerendered/vue-fluent';
import StudentIdTemplateForm from '@spa/pages/student-id/StudentIdTemplateForm.vue';

const props = defineProps({
    visible: {
        type: Boolean,
        required: true,
    },
    certificateIdFormate: {
        type: Object,
        default: () => [],
    },
    type: {
        type: String,
        default: 'certificate',
    },
    hasTour: {
        type: Boolean,
        default: false,
    },
});

const isStudentId = computed(() => {
    return props.type === 'student-id';
});

const dimensions = computed(() => {
    if (props.type === 'student-id') {
        return {
            width: 324,
            height: 204,
        };
    } else {
        return {
            width: 707,
            height: 1000,
        };
    }
});

const blankJson = {
    attrs: {
        width: 707,
        height: 1000,
        fill: 'white',
        stroke: '#D1D5DB',
        strokeWidth: 1,
    },
    className: 'Stage',
    children: [
        {
            attrs: [],
            className: 'Layer',
            children: [],
        },
    ],
};

const blankHTML = `
            <div id="watermark" style="position:fixed; bottom:0px; left:0px; top:0px; right:0px; width:100%; height:100%; z-index:-1000;">
            </div>
            <div class="certificate-container" style="width:707px; height: 1000px; position: relative;
                margin: auto;
                overflow: hidden;">
            </div>`;

const $certificate = useCertificateResource('spa/certificates');

const store = useCertificateStore();
const loaderStore = useLoaderStore();

const templates = computed(() => {
    if (!isStudentId.value) {
        return data.defaultTemplates;
    } else {
        return [];
    }
});
const payload = ref({
    name: '',
    json_data: JSON.stringify(blankJson),
    html_data: blankHTML,
    paper_size: 'portrait',
    orientation: 'portrait',
    thumbnail: null,
    is_default: false,
    type: '0',
    certificate_number_formate_id: '',
    fonts_used: '',
});
const selectedTemplate = ref(null);

const emit = defineEmits(['close', 'submit']);

const handleSelect = (template = null) => {
    selectedTemplate.value = template;
    if (!template) {
        payload.value = {
            name: '',
            json_data: JSON.stringify(blankJson),
            html_data: blankHTML,
            paper_size: 'portrait',
            orientation: 'portrait',
            thumbnail: null,
            is_default: false,
            type: '0',
            certificate_number_formate_id: '',
            fonts_used: '',
        };
    }
    payload.value = {
        name: '',
        json_data: template.json_data ? JSON.stringify(template.json_data) : '',
        html_data: template.html_data || null,
        paper_size: template.paper_size || 'portrait',
        orientation: template.orientation || 'portrait',
        thumbnail: template.thumbnail || null,
        is_default: false,
        id: '',
        type: template.type,
        certificate_number_formate_id: '',
        fonts_used: template.fonts_used || '',
    };
};

const handleSubmit = async (values) => {
    const updatedJson = JSON.parse(
        JSON.stringify(selectedTemplate.value ? selectedTemplate.value?.json_data : blankJson)
    );
    updatedJson.attrs.width = parseInt(values.width) || 707;
    updatedJson.attrs.height = parseInt(values.height) || 1000;
    const templateType = isStudentId.value ? 'student-id' : 'certificate';
    payload.value = {
        ...payload.value,
        json_data: JSON.stringify(updatedJson),
        name: values.name,
        certificate_number_formate_id: values.certificate_number_formate_id || 10005,
        template_type: templateType,
    };

    await $certificate.saveTemplate(payload.value, { redirection: true }, templateType);
};

const handleClose = () => {
    emit('close');
};

const templateContainer = ref(null);
const formContainer = ref(null);
</script>
<style lang=""></style>
