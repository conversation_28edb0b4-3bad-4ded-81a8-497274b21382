<template>
    <Card
        :pt="{
            root: 'relative p-4 lg:p-6 rounded-lg shadow-none border-none h-full max-h-[calc(100vh-100px)] flex flex-col',
            header: 'border-b max-md:pb-4 pb-6',
            content: 'flex-1 overflow-y-auto',
        }"
    >
        <template #header>
            <ProfileHeaderContent :store="store">
                <template #profile-header-actions v-if="hasProfileHeaderActions">
                    <slot name="profile-header-actions" />
                </template>
            </ProfileHeaderContent>
        </template>
        <template #content>
            <div class="relative max-md:px-0 3xl:px-14">
                <Tabs
                    :defaultSelected="activeTab"
                    :tabs="tabs"
                    :show-icon="false"
                    @select="handleTabSelect"
                    :show-arrows="showArrows"
                    :sticky="true"
                >
                    <template #tab-panel-overview="{ tab, index, isActive }">
                        <OverviewTabContent :store="store" />
                    </template>
                    <template #tab-panel-login_access="{ tab, index, isActive }">
                        <LoginAccessTabContent :store="store" />
                    </template>
                    <template #tab-panel-roles_permissions="{ tab, index, isActive }">
                        <RolesPermissionTabContent :store="store" />
                    </template>
                    <template #tab-panel-activity_log="{ tab, index, isActive }">
                        <ActivityLogTabContent :store="store" />
                    </template>
                    <template #tab-panel-profile_details="{ tab, index, isActive }">
                        <ProfileDetailsTabContent :store="store" />
                    </template>
                    <template
                        v-for="(item, i) in extraTabs"
                        v-slot:[`tab-panel-${item?.name}`]="{ tab, index, isActive }"
                        :key="i"
                    >
                        <slot
                            :name="`tab-panel-${item?.name}`"
                            :tab="tab"
                            :index="index"
                            :isActive="isActive"
                        />
                    </template>
                </Tabs>
                <div
                    v-if="store.ctxLoading['fetch-by-id']"
                    class="pointer-events-none absolute inset-0 z-10 flex items-center justify-between bg-white/50"
                >
                    <Spinner :size="'lg'" :pt="{ root: 'items-center' }" loadingText="" />
                </div>
            </div>
        </template>
    </Card>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import Card from '@spa/components/Card/Card.vue';
import Avatar from '@spa/components/Avatar/Avatar.vue';
import CopyToClipboard from '@spa/components/CopyAction/CopyToClipboard.vue';
import LabelValuePair from '@spa/components/LabelValuePair/LabelValuePair.vue';
import Badge from '@spa/components/badges/Badge.vue';
import Tabs from '@spa/components/ui/tabs/Tabs.vue';
import RolesPermissionTabContent from '@spa/modules/users/partials/RolesPermissionTabContent.vue';
import LoginAccessTabContent from '@spa/modules/users/partials/LoginAccessTabContent.vue';
import ActivityLogTabContent from '@spa/modules/users/partials/ActivityLogTabContent.vue';
import ProfileDetailsTabContent from '@spa/modules/users/partials/ProfileDetailsTabContent.vue';
import OverviewTabContent from '@spa/modules/users/partials/OverviewTabContent.vue';
import ProfileHeaderContent from '@spa/modules/users/partials/ProfileHeaderContent.vue';
import Spinner from '@spa/components/Loader/Spinner.vue';
import { useUsersStore } from '@spa/stores/modules/users/useUsersStore.js';

const props = defineProps({
    store: Object,
    tabs: {
        type: Array,
        default: () => [
            {
                title: 'Overview',
                name: 'overview',
            },
            {
                title: 'Login & Access',
                name: 'login_access',
            },
            {
                title: 'Roles & Permissions',
                name: 'roles_permissions',
            },
            {
                title: 'Activity Log',
                name: 'activity_log',
            },
            {
                title: 'Profile Details',
                name: 'profile_details',
            },
        ],
    },
    showArrows: {
        type: Boolean,
        default: false,
    },
    hasProfileHeaderActions: {
        type: Boolean,
        default: false,
    },
});

const userStore = useUsersStore();

const activeTab = ref(0);

onMounted(() => {
    const hash = window.location.hash.replace('#', '');
    const tabIndex = props.tabs.findIndex((t) => t.name === hash);
    if (tabIndex !== -1) {
        activeTab.value = tabIndex;
    }
    userStore.userType = props.store.userType;
});

const extraTabs = computed(() => {
    return props.tabs.filter(
        (tab) =>
            ![
                'overview',
                'login_access',
                'roles_permissions',
                'activity_log',
                'profile_details',
            ].includes(tab.name)
    );
});

const handleTabSelect = (index, tab) => {
    if (tab?.redirect) {
        window.open(tab.redirect, '_blank');
        return;
    }
    activeTab.value = index;
    if (tab?.name) {
        window.location.hash = tab.name;
    }
};
</script>
<style scoped></style>
