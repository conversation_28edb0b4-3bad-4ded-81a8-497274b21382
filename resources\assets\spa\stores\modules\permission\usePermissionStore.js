import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref } from 'vue';
import useConfirm from '@spa/services/useConfirm';

export const usePermissionStore = defineStore('usePermissionStore', () => {
    const storeUrl = ref('v2/tenant/permissions');
    const commonStoreProps = useCommonStore(storeUrl.value);
    const modules = ref([]);
    const changedPermissions = ref([]);
    const confirm = useConfirm();

    const getModules = async () => {
        try {
            const response = await $http.get(`api/${storeUrl.value}/get-modules`);
            modules.value = response.data || [];
            return response.data;
        } catch (e) {
            console.error('Error fetching modules:', e);
            throw e;
        }
    };

    const updatePermissionsInBulk = async (data) => {
        commonStoreProps.loaderStore.startContextLoading('confirmpermissions');
        try {
            const response = await $http.post(`api/${storeUrl.value}/update-access`, data);
            return response.data;
        } catch (e) {
            console.error('Error updating permissions in bulk:', e);
            throw e;
        } finally {
            commonStoreProps.loaderStore.stopContextLoading('confirmpermissions');
        }
    };
    const removeRoleForUser = async (data) => {
        return new Promise((resolve, reject) => {
            confirm.require({
                message:
                    'Are you sure you want to Unassign the role for the user? This action cannot be undone.',
                header: 'Delete Item?',
                icon: 'pi pi-exclamation-triangle',
                variant: 'danger',
                acceptLabel: 'Unassign',
                rejectLabel: 'Cancel',
                width: 500,
                accept: async () => {
                    commonStoreProps.loaderStore.startContextLoading('removerole');
                    try {
                        const response = await $http.post(
                            `api/${storeUrl.value}/unassign-role`,
                            data
                        );
                        resolve(response.data);
                    } catch (e) {
                        console.error('Error removing role:', e);
                        reject(e);
                    } finally {
                        commonStoreProps.loaderStore.stopContextLoading('removerole');
                    }
                },
                reject: () => {
                    resolve(false);
                },
                onHide: () => {
                    resolve(false);
                },
            });
        });
    };
    const maintainChangeLog = (permission) => {
        if (!permission) return;
        // remove all entries with the same id
        changedPermissions.value = changedPermissions.value.filter(
            (item) => item.id !== permission.id
        );
        // add only if permission changed
        if (Number(permission.permission_status.s) !== Number(permission.permission_status.o)) {
            changedPermissions.value = [...changedPermissions.value, permission];
        }
    };
    const resetChangeLog = () => {
        changedPermissions.value = [];
    };

    const setAsDefaultRole = async (userId, roleId) => {
        commonStoreProps.loaderStore.startContextLoading('setdefaultrole');
        try {
            const response = await $http.post(`api/${storeUrl.value}/set-as-default`, {
                user_id: userId,
                role_id: roleId,
            });
            return response.data;
        } catch (e) {
            console.error('Error setting default role:', e);
            throw e;
        } finally {
            commonStoreProps.loaderStore.stopContextLoading('setdefaultrole');
        }
    };

    return {
        ...commonStoreProps,
        getModules,
        modules,
        changedPermissions,
        updatePermissionsInBulk,
        maintainChangeLog,
        resetChangeLog,
        removeRoleForUser,
        setAsDefaultRole,
    };
});
