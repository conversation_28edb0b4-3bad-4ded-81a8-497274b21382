<script setup>
import { computed } from 'vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormUploader from '@spa/components/KendoInputs/FormUploader.vue';
import Button from '@spa/components/Buttons/Button.vue';
import FormNumericInput from '@spa/components/KendoInputs/FormNumericInput.vue';
import { getValidationMessage } from '@spa/composables/formComposables.js';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';

const props = defineProps({
    modelValue: {},
});
const emit = defineEmits(['update:modelValue']);
const formData = computed({
    get() {
        const data = props.modelValue || {};
        if (!data.professional_developments) {
            data.professional_developments = [];
        }
        return data;
    },
    set(value) {
        emit('update:modelValue', value);
    },
});
const removeProfessionalDevelopment = (index) => {
    if (
        formData.value.professional_developments &&
        formData.value.professional_developments.length > 0
    ) {
        formData.value.professional_developments.splice(index, 1);
    }
};
const addProfessionalDevelopment = () => {
    if (!formData.value.professional_developments) {
        formData.value.professional_developments = [];
    }
    formData.value.professional_developments.push({
        title: '',
        organization: '',
        startDate: null,
        endDate: null,
        document: null,
    });
};
</script>
<template>
    <div>
        <h3 class="mb-6 text-lg font-semibold">Professional Development</h3>
        <div class="overflow-x-auto">
            <div class="hidden min-w-[1200px] grid-cols-1 items-end gap-4 md:grid-cols-9 xl:grid">
                <div class="px-4 py-2 md:col-span-1">
                    <h4 class="text-sm font-medium leading-5 text-gray-500">
                        Event Name<span class="text-red-500">*</span>
                    </h4>
                </div>
                <div class="px-4 py-2 md:col-span-1">
                    <h4 class="text-sm font-medium leading-5 text-gray-500">
                        Organized By<span class="text-red-500">*</span>
                    </h4>
                </div>
                <div class="px-4 py-2 md:col-span-1">
                    <h4 class="text-sm font-medium leading-5 text-gray-500">
                        Activity Name<span class="text-red-500">*</span>
                    </h4>
                </div>
                <div class="px-4 py-2 md:col-span-1">
                    <h4 class="text-sm font-medium leading-5 text-gray-500">
                        Event From<span class="text-red-500">*</span>
                    </h4>
                </div>
                <div class="px-4 py-2 md:col-span-1">
                    <h4 class="text-sm font-medium leading-5 text-gray-500">
                        Event To<span class="text-red-500">*</span>
                    </h4>
                </div>
                <div class="px-4 py-2 md:col-span-1">
                    <h4 class="text-sm font-medium leading-5 text-gray-500">
                        CPD Points<span class="text-red-500">*</span>
                    </h4>
                </div>
                <div class="px-4 py-2 md:col-span-1">
                    <h4 class="text-sm font-medium leading-5 text-gray-500">Comments</h4>
                </div>
                <div class="px-4 py-2 md:col-span-2">
                    <h4 class="text-sm font-medium leading-5 text-gray-500">File Upload</h4>
                </div>
            </div>

            <div class="min-w-[1200px] border-t border-gray-200 bg-white">
                <div
                    v-for="(professionalDevelopment, index) in formData.professional_developments"
                    :key="index"
                    class="grid grid-cols-1 items-start gap-4 border-b border-gray-200 p-4 xl:grid-cols-9"
                >
                    <div class="md:col-span-1">
                        <FormInput
                            v-model="professionalDevelopment.event_name"
                            placeholder="Event Name"
                            :required="true"
                            v-bind="
                                getValidationMessage(
                                    formData,
                                    `professional_developments.${index}.event_name`
                                )
                            "
                        />
                    </div>
                    <div class="md:col-span-1">
                        <FormInput
                            v-model="professionalDevelopment.organized_by"
                            placeholder="Organization By"
                            :required="true"
                            v-bind="
                                getValidationMessage(
                                    formData,
                                    `professional_developments.${index}.organized_by`
                                )
                            "
                        />
                    </div>
                    <div class="md:col-span-1">
                        <FormInput
                            v-model="professionalDevelopment.activity_name"
                            placeholder="Activity Name"
                            :required="true"
                            v-bind="
                                getValidationMessage(
                                    formData,
                                    `professional_developments.${index}.activity_name`
                                )
                            "
                        />
                    </div>
                    <div class="md:col-span-1">
                        <!--                    <FormInput-->
                        <!--                        v-model="professionalDevelopment.event_from"-->
                        <!--                        type="date"-->
                        <!--                    />-->
                        <FormDatePicker
                            v-model="professionalDevelopment.event_from"
                            type="date"
                            :required="true"
                            v-bind="
                                getValidationMessage(
                                    formData,
                                    `training_qualifications.${index}.end_date`
                                )
                            "
                            emit-format="yyyy-MM-dd"
                        />
                    </div>
                    <div class="md:col-span-1">
                        <!--                    <FormInput-->
                        <!--                        v-model="professionalDevelopment.event_to"-->
                        <!--                        type="date"-->
                        <!--                    />-->
                        <FormDatePicker
                            v-model="professionalDevelopment.event_to"
                            type="date"
                            :required="true"
                            v-bind="
                                getValidationMessage(
                                    formData,
                                    `training_qualifications.${index}.end_date`
                                )
                            "
                            emit-format="yyyy-MM-dd"
                        />
                    </div>
                    <div class="md:col-span-1">
                        <FormNumericInput
                            v-model="professionalDevelopment.cpd_points"
                            placeholder="CPD Points"
                            :required="true"
                            v-bind="
                                getValidationMessage(
                                    formData,
                                    `professional_developments.${index}.cpd_points`
                                )
                            "
                        />
                    </div>
                    <div class="md:col-span-1">
                        <FormInput
                            v-model="professionalDevelopment.comments"
                            placeholder="Comments"
                        />
                    </div>
                    <div class="flex items-end gap-2 md:col-span-2">
                        <FormUploader
                            v-model="professionalDevelopment.document"
                            accept=".pdf,.doc,.docx"
                            mode="simple"
                            class="flex-1"
                        />
                        <button
                            type="button"
                            @click="removeProfessionalDevelopment(index)"
                            class="flex-shrink-0 rounded-full p-2 text-red-500 transition-colors duration-150 hover:bg-red-50 hover:text-red-700"
                            :aria-label="`Remove employment history ${index + 1}`"
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="18"
                                height="18"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                class="pointer-events-none"
                            >
                                <circle cx="12" cy="12" r="10" />
                                <path d="m15 9-6 6" />
                                <path d="m9 9 6 6" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex justify-end pt-3">
            <Button
                @click="addProfessionalDevelopment"
                variant="primary"
                outline
                class="flex items-center"
            >
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    ></path>
                </svg>
                Add More
            </Button>
        </div>
    </div>
</template>

<style scoped></style>
