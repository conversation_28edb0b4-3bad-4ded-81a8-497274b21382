<?php

namespace App\Model;

use App\Exceptions\ApplicationException;
use App\Model\v2\Student;
use App\Model\v2\StudentCourses;
use App\Traits\AvetMissTrait;
use Domains\Xero\Facades\Xero;
use Helpers;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Support\Services\UploadService;

class StudentCourse extends Model
{
    protected $table = 'rto_student_courses';

    use AvetMissTrait;
    use LogsActivity;

    const STATUS_DID_NOT_COMMENCE = 'Did Not Commence';

    protected $fillable = [
        'student_id',
        'agent_id',
        'campus_id',
        'palacement_manager_id',
        'course_manager_id',
        'res_cal_method',
        'course_type_id',
        'intake_year',
        'intake_date',
        'issued_date',
        'course_id',
        'start_date',
        'finish_date',
        'total_weeks',
        'enroll_fee',
        'study_reason_id',
        'course_fee',
        'course_upfront_fee',
        'course_material_fee',
        'application_request',
        'advance_standing_credit',
        'credit_transfer_request',
        'special_instructions',
        'employer_id',
        'contract_schedule_id',
        'purchasing_contract_ode',
        'schedule_code',
        'auto_update_to_sru',
        'census_date1',
        'census_date2',
        'census_date3',
        'mode_of_delivery',
        'is_material_fee_inc_initail_payment',
        'internal',
        'external',
        'workplace_based_delivery',
        'status',
        'offer_status',
        'orientation_comment',
        'offer_id',
        'coe_applicable',
        'coe_name',
        'coe_image',
        'coe_material_id',
        'course_template',
        'default_unit_fee',
        'is_finish_dt',
        'is_orientation',
        'is_orientation_sms_send',
        'is_orientation_email_send',
        'is_claim',
        'is_qualification',
        'is_certificate',
        'group_id',
        'course_attempt',
        'date_approved',
        'survey_contact_status',
        'purchasing_contract_identifier',
        'purchasing_contract_schedule_identifier',
        'associated_course_identifier',
        'is_fulltimelearing',
        'created_by',
        'updated_by',
    ];

    protected $logAttributes = [
        'student_id',
        'agent_id',
        'campus_id',
        'palacement_manager_id',
        'course_manager_id',
        'res_cal_method',
        'course_type_id',
        'intake_year',
        'intake_date',
        'issued_date',
        'course_id',
        'start_date',
        'finish_date',
        'total_weeks',
        'enroll_fee',
        'study_reason_id',
        'course_fee',
        'course_upfront_fee',
        'course_material_fee',
        'application_request',
        'advance_standing_credit',
        'credit_transfer_request',
        'special_instructions',
        'employer_id',
        'contract_schedule_id',
        'purchasing_contract_ode',
        'schedule_code',
        'auto_update_to_sru',
        'census_date1',
        'census_date2',
        'census_date3',
        'mode_of_delivery',
        'is_material_fee_inc_initail_payment',
        'internal',
        'external',
        'workplace_based_delivery',
        'status',
        'offer_status',
        'orientation_comment',
        'offer_id',
        'coe_applicable',
        'coe_name',
        'coe_image',
        'coe_material_id',
        'course_template',
        'default_unit_fee',
        'is_finish_dt',
        'is_orientation',
        'is_orientation_sms_send',
        'is_orientation_email_send',
        'is_claim',
        'is_qualification',
        'is_certificate',
        'group_id',
        'course_attempt',
        'date_approved',
        'survey_contact_status',
        'purchasing_contract_identifier',
        'purchasing_contract_schedule_identifier',
        'associated_course_identifier',
        'is_fulltimelearing',
        'updated_by',
    ];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($studentCourse) {
            $studentCourse->student?->touch();
        });

        static::updating(function ($studentCourse) {
            $studentCourse->student?->touch();
        });
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => ":subject.galaxy_log_name Student Course has been {$eventName}");
    }

    public function tapActivity(Activity $activity, string $eventName)
    {
        // dd($activity);
        $attributes = $activity->properties['attributes'];
        if (isset($activity['group_id'])) {
            $attributes['group_id'] = $this->getGroupName($this->group_id);
        }

        // Modify the existing 'group_id' in the 'old' array
        $oldAttributes = $activity->properties['old'] ?? [];
        if (! empty($oldAttributes['group_id'])) {
            $oldAttributes['group_id'] = $this->getGroupName($this->getOriginal('group_id'));
        }

        $activity->subject_type = self::class;

        // Update the properties with the modified arrays
        $activity->properties = $activity->properties->put('attributes', $attributes);
        $activity->properties = $activity->properties->put('old', $oldAttributes);
        $activity->log_name = (new self)->getMorphClass().'_'.$this->student_id.'_'.$this->course_id;

    }

    public function getGalaxyLogNameAttribute(): string
    {
        return implode('-', [@$this->student->generated_stud_id, @$this->course->course_code]);
    }

    public function student()
    {
        return $this->hasOne(Student::class, 'id', 'student_id');
    }

    public function course()
    {
        return $this->hasOne(Courses::class, 'id', 'course_id');
    }

    public function getGroupName($group_id)
    {
        return GroupIntake::find($group_id)->group_id ?? 'Unknown Group';
    }

    public function updateCoeOfferManage($collegeId, $userId, $request, $studentId, $offerId, $id)
    {

        $rootFolder = Config::get('constants.arrCollegeRootFolder');

        $filePath = Config::get('constants.uploadFilePath.StudentCOE');
        $destinationPath = Helpers::changeRootPath($filePath, $studentId);
        $dataArr = [
            'college_id' => $collegeId,
            'folder_name' => $rootFolder['StudentCOE'],
            'sub_folder_name' => $studentId,
            'user_id' => $userId,
        ];
        $objCommonModel = new CommonModel;
        $clgMaterialParentId = $objCommonModel->getSubParentId($dataArr);

        $file_count = 0;
        $files = $request->file('images');
        if ($files != '') {
            $file_count = count($files);
        }

        $objStaff = StudentCourse::find($id);
        if (! config('features.scout') && ! config('features.scout_beta_search')) {
            $objStudent = Students::find($studentId);
        } else {
            $objStudent = Student::find($studentId);
        }

        if (! empty($objStudent->generated_stud_id)) {
            $objStudent->is_student = 1;
            $objStaff->status = 'Enrolled';
            $objStaff->offer_status = 'Enrolled';
            $objStaff->updated_by = Auth::user()->id;
            $getCurrentStatus = StudentCourse::where('id', $id)->select('offer_status', 'student_id')->get()->toArray();
            $offeredStatus = 'Enrolled';
            $objStudentCommunicationLog = new StudentCommunicationLog;
            $saveLog = $objStudentCommunicationLog->saveApproveStatusLog($request, $studentId, $id, $getCurrentStatus[0]['offer_status'], $offeredStatus);
        }

        $objStaff->coe_applicable = (! empty($request->is_applicable)) ? $request->is_applicable : '0';
        $objStaff->coe_name = ($request->is_applicable == 1) ? 'N/A' : $request->code_no;

        $studentListData = [
            'college_id' => $collegeId,
            'tenant_id' => tenant('id'),
        ];

        $this->updateStudentList($studentListData);

        if ($file_count > 0) {
            $uploadcount = 0;
            foreach ($files as $file) {
                $rules = ['file' => 'required'];
                $validator = Validator::make(['file' => $file], $rules);
                if ($validator->passes()) {
                    // $destinationPath = Config::get('constants.StudentCOEPath.DocumentsPath');
                    $time = time();
                    $originalName = $file->getClientOriginalName();
                    $filename = $time.$originalName;
                    $fileSize = $file->getSize();
                    // $upload_success = $file->move($destinationPath['default'], $filename);

                    $upload_success = UploadService::uploadAs($destinationPath['view'], $file, $filename);
                    info('file uploaded form COE Old', [$upload_success]);
                    $uploadcount++;
                    $studDataArr = [
                        'college_id' => $collegeId,
                        'original_name' => $originalName,
                        'file_name' => $filename,
                        'size' => $fileSize,
                        'type' => 'File',
                        'parent_id' => $clgMaterialParentId,
                        'file_path' => $destinationPath['view'],
                        'user_id' => $userId,
                    ];
                    $coe_material_id = $objCommonModel->addCollegeMaterialInfo($studDataArr);
                    $objStaff->coe_material_id = $coe_material_id;
                    $objStaff->coe_image = $filename;
                }
            }
        }
        $studentSave = $objStudent->save();
        if (galaxy_feature('galaxy_webhooks')) {
            $objStudentCourse = StudentCourses::find($id);
            \Webhooks\Facades\Webhook::dispatch(new \Webhooks\Events\Student\StudentEnrolledEvent($objStudentCourse));
        }
        $staffSave = $objStaff->save();
        if ($staffSave) {
            return true;
        } else {
            return false;
        }
    }

    public function updateStudentList($StudentListData)
    {
        // This method was previously used to dispatch StudentListEvent
        // The event has been removed as it's no longer needed
    }

    public function deleteCoeOfferManageImage($collegeId, $matarialId, $studentId, $oldFileName)
    {

        $rootFolder = Config::get('constants.arrCollegeRootFolder');

        $filePath = Config::get('constants.uploadFilePath.StudentCOE');
        $destinationPath = Helpers::changeRootPath($filePath, $studentId);

        if (file_exists($destinationPath['default'].$oldFileName)) {
            unlink($destinationPath['default'].$oldFileName);

            $deleteFromCollegeMeterail = CollegeMaterials::where('id', '=', $matarialId)->delete();

            if ($deleteFromCollegeMeterail) {
                StudentCourse::where('coe_material_id', '=', $matarialId)
                    ->where('student_id', '=', $studentId)
                    ->update([
                        'coe_image' => '',
                        'coe_material_id' => 0,
                    ]);
            }

            return true;
        }
    }

    public function updateStudentCourseId($studentId, $userId = '')
    {

        $userId2 = (! empty($userId)) ? $userId : ((Auth::user()) ? Auth::user()->id : '');

        StudentCourse::where('offer_status', '=', 'Offered')
            ->where('student_id', '=', $studentId)
            ->update([
                'offer_status' => 'Reconsider',
                'status' => 'Reconsider',
                'updated_by' => $userId2,
            ]);
    }

    public function studentCourseRejected($primaryId)
    {

        $objStudent = StudentCourse::find($primaryId);
        $objStudent->offer_status = 'Rejected';
        $objStudent->updated_by = Auth::user()->id;
        $objStudent->save();
    }

    public function offerStatusChanged($course_id)
    {
        $arrStaff = StudentCourse::select('id', 'offer_status')
            ->where('id', $course_id)
            //                ->where('offer_id', $offer_id)
            ->get()
            ->toArray();

        $offer_status = $arrStaff[0]['offer_status'];
        $offer_status_id = $arrStaff[0]['id'];
        // echo $offer_status.'-------'.$offer_status_id;exit;

        if ($offer_status == 'In Application') {
            $tid = 'Offered';
        } elseif ($offer_status == 'Reconsider') {
            $tid = 'Offered';
        } elseif ($offer_status == 'Offered') {
            $tid = 'Offered';
        }
        $objStaff = StudentCourse::find($offer_status_id);
        $objStaff->offer_status = $tid;
        $objStaff->status = $tid;
        $objStaff->updated_by = Auth::user()->id;
        $objStaff->save();
    }

    public function offerStatusPending($student_id)
    {
        $arrStaff = StudentCourse::select('id', 'offer_status')
            ->where('id', $student_id)
            ->get()
            ->toArray();

        $offer_status = $arrStaff[0]['offer_status'];
        $offer_status_id = $arrStaff[0]['id'];

        if ($offer_status == 'In Application') {
            $tid = 'Pending';
        } elseif ($offer_status == 'Reconsider') {
            $tid = 'Pending';
        } elseif ($offer_status == 'Offered') {
            $tid = 'Pending';
        }

        $objStaff = StudentCourse::find($offer_status_id);
        $objStaff->offer_status = $tid;
        $objStaff->status = $tid;
        $objStaff->updated_by = Auth::user()->id;
        $objStaff->save();
    }

    public function offerStatusReconsider($student_id)
    {
        $arrStaff = StudentCourse::select('id', 'offer_status')
            ->where('id', $student_id)
            ->get()
            ->toArray();

        $offer_status = $arrStaff[0]['offer_status'];
        $offer_status_id = $arrStaff[0]['id'];

        if ($offer_status == 'Pending') {
            $tid = 'Reconsider';
        } else {
            $tid = 'Reconsider';
        }

        $objStaff = StudentCourse::find($offer_status_id);
        $objStaff->offer_status = $tid;
        $objStaff->status = $tid;
        $objStaff->updated_by = Auth::user()->id;
        $objStaff->save();
    }

    public function saveStudentCourse($studentId, $studentOfferId, $request, $userId)
    {
        if ($request->input('agent_id') != '' && $request->input('campus_id') && $request->input('course_id') != '') {

            $arrCourse = Courses::select('results_calculation_methods')
                ->Where('id', $request->input('course_id'))
                ->get();

            $studentCourse = new StudentCourse;

            $studentCourse->student_id = $studentId;
            $studentCourse->agent_id = ($request->input('agent_id') != '') ? $request->input('agent_id') : null;
            $studentCourse->campus_id = ($request->input('campus_id') != '') ? $request->input('campus_id') : null;
            $studentCourse->palacement_manager_id = ($request->input('palacement_manager_id') != '') ? $request->input('palacement_manager_id') : null;
            $studentCourse->course_manager_id = ($request->input('course_manager_id') != '') ? $request->input('course_manager_id') : null;
            $studentCourse->course_type_id = ($request->input('course_type_id') != '') ? $request->input('course_type_id') : null;
            $studentCourse->intake_year = ($request->input('intake_year') != '') ? $request->input('intake_year') : null;
            $studentCourse->course_id = ($request->input('course_id') != '') ? $request->input('course_id') : null;
            $studentCourse->issued_date = ($request->input('issued_date') != '') ? date('Y-m-d', strtotime($request->input('issued_date'))) : null;
            $studentCourse->res_cal_method = $arrCourse[0]->results_calculation_methods;
            $studentCourse->is_qualification = ($request->input('is_qualification') != '') ? $request->input('is_qualification') : '0';
            if ($request->input('intake_date') != '') {
                $date1 = $request->input('intake_date');
                $studentCourse->intake_date = ($request->input('intake_date') != '') ? date('Y-m-d', strtotime($request->input('intake_date'))) : null;
                $studentCourse->start_date = ($request->input('start_date') != '') ? date('Y-m-d', strtotime($request->input('start_date'))) : null;
            } else {
                $date1 = $request->input('start_date');
                $studentCourse->intake_date = ($request->input('start_date') != '') ? date('Y-m-d', strtotime($request->input('start_date'))) : null;
                $studentCourse->start_date = ($request->input('start_date') != '') ? date('Y-m-d', strtotime($request->input('start_date'))) : null;
            }
            $total_weeks = ($request->input('total_weeks') != '') ? $request->input('total_weeks') : null;
            $date1 = strtotime($date1);
            $date2 = date('Y-m-d', strtotime('+'.$total_weeks.' week', $date1));
            $finish_date = date('Y-m-d', strtotime($date2.' -1 day'));

            $studentCourse->total_weeks = $total_weeks;
            $studentCourse->finish_date = $finish_date;

            $studentCourse->enroll_fee = ($request->input('enroll_fee') != '') ? $request->input('enroll_fee') : null;
            $studentCourse->study_reason_id = ($request->input('study_reason_id') != '') ? $request->input('study_reason_id') : null;
            $studentCourse->course_fee = ($request->input('course_fee') != '') ? $request->input('course_fee') : null;
            $studentCourse->course_upfront_fee = ($request->input('course_upfront_fee') != '') ? $request->input('course_upfront_fee') : null;
            $studentCourse->course_material_fee = ($request->input('course_material_fee') != '') ? $request->input('course_material_fee') : null;
            $studentCourse->advance_standing_credit = ($request->input('advance_standing_credit') != '') ? $request->input('advance_standing_credit') : null;
            $studentCourse->special_instructions = ($request->input('special_instructions') != '') ? $request->input('special_instructions') : null;

            //            $studentCourse->mode_of_delivery = ($request->input('mode_of_delivery') != '') ? $request->input('mode_of_delivery') : null;
            $studentCourse->internal = ($request->input('internal') != '') ? 'Y' : 'N';
            $studentCourse->external = ($request->input('external') != '') ? 'Y' : 'N';
            $studentCourse->workplace_based_delivery = ($request->input('workplace_based_delivery') != '') ? 'Y' : 'N';

            $studentCourse->census_date1 = ($request->input('census_date1') != '') ? date('Y-m-d', strtotime($request->input('census_date1'))) : null;
            $studentCourse->census_date2 = ($request->input('census_date2') != '') ? date('Y-m-d', strtotime($request->input('census_date2'))) : null;
            $studentCourse->census_date3 = ($request->input('census_date3') != '') ? date('Y-m-d', strtotime($request->input('census_date3'))) : null;

            $studentCourse->status = 'New Application Request';
            $studentCourse->offer_status = 'In Application';
            $studentCourse->offer_id = $studentOfferId;

            $studentCourse->created_by = $userId;
            $studentCourse->updated_by = $userId;

            $studentCourse->save();
        }
    }

    public function saveStudentCourseStep3FromAgent($studentId, $studentOfferId, $request, $userId)
    {
        if ($request->input('agent_id') != '' && $request->input('campus_id') && $request->input('course_id') != '') {

            $arrCourse = Courses::select('results_calculation_methods')
                ->Where('id', $request->input('course_id'))
                ->get();

            $objCourseTypeName = new Courses;
            $arrCourseTypeName = $objCourseTypeName->getCourseTypeId($request->input('course_id'));
            $studentCourse = new StudentCourse;

            $studentCourse->student_id = $studentId;
            $studentCourse->agent_id = ($request->input('agent_id') != '') ? $request->input('agent_id') : null;
            $studentCourse->campus_id = ($request->input('campus_id') != '') ? $request->input('campus_id') : null;
            $studentCourse->palacement_manager_id = '';
            $studentCourse->course_manager_id = '';
            $studentCourse->course_type_id = $arrCourseTypeName[0]['course_type_id'];
            $studentCourse->intake_year = ($request->input('intake_year') != '') ? $request->input('intake_year') : null;
            $studentCourse->course_id = ($request->input('course_id') != '') ? $request->input('course_id') : null;
            $studentCourse->res_cal_method = $arrCourse[0]->results_calculation_methods;

            if ($request->input('intake_date') != '') {
                $date1 = $request->input('intake_date');
                $studentCourse->intake_date = ($request->input('intake_date') != '') ? date('Y-m-d', strtotime($request->input('intake_date'))) : null;
                $studentCourse->start_date = ($request->input('start_date') != '') ? date('Y-m-d', strtotime($request->input('start_date'))) : null;
            } else {
                $date1 = $request->input('start_date');
                $studentCourse->intake_date = ($request->input('start_date') != '') ? date('Y-m-d', strtotime($request->input('start_date'))) : null;
                $studentCourse->start_date = ($request->input('start_date') != '') ? date('Y-m-d', strtotime($request->input('start_date'))) : null;
            }
            $total_weeks = ($request->input('total_weeks') != '') ? $request->input('total_weeks') : null;
            $date1 = strtotime($date1);
            $date2 = strtotime('+'.$total_weeks.' week', $date1);
            $finish_date = date('Y-m-d H:i:s', $date2);
            $studentCourse->total_weeks = $total_weeks;
            $studentCourse->finish_date = $finish_date;

            $studentCourse->enroll_fee = ($request->input('enroll_fee') != '') ? $request->input('enroll_fee') : null;
            $studentCourse->study_reason_id = ($request->input('study_reason_id') != '') ? $request->input('study_reason_id') : null;
            $studentCourse->course_fee = ($request->input('course_fee') != '') ? $request->input('course_fee') : null;
            $studentCourse->course_upfront_fee = ($request->input('course_upfront_fee') != '') ? $request->input('course_upfront_fee') : null;
            $studentCourse->course_material_fee = ($request->input('course_material_fee') != '') ? $request->input('course_material_fee') : null;
            $studentCourse->advance_standing_credit = ($request->input('advance_standing_credit') != '') ? $request->input('advance_standing_credit') : null;
            $studentCourse->special_instructions = ($request->input('special_instructions') != '') ? $request->input('special_instructions') : null;

            $studentCourse->census_date1 = ($request->input('census_date1') != '') ? date('Y-m-d', strtotime($request->input('census_date1'))) : null;
            $studentCourse->census_date2 = ($request->input('census_date2') != '') ? date('Y-m-d', strtotime($request->input('census_date2'))) : null;
            $studentCourse->census_date3 = ($request->input('census_date3') != '') ? date('Y-m-d', strtotime($request->input('census_date3'))) : null;

            $studentCourse->status = 'New Application Request';
            $studentCourse->offer_status = 'In Application';
            $studentCourse->offer_id = $studentOfferId;

            $studentCourse->created_by = $userId;
            $studentCourse->updated_by = $userId;

            $studentCourse->save();
        }
    }

    public function getStudentEmail($courseType, $selectStatus, $campus, $collegeId)
    {
        $getUsers = StudentCourse::leftjoin('rto_students', 'rto_students.id', 'rto_student_courses.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', 'rto_student_courses.course_id')
            ->where('rto_students.college_id', '=', $collegeId)
            ->where('rto_student_courses.course_id', '=', $courseType);
        $getUsers->groupBy('rto_students.id');
        //       $getUsers->groupBy('rto_student_courses.status');
        if ($selectStatus != 'all') {
            $getUsers->where('rto_student_courses.status', '=', $selectStatus);
        }
        $getUsers->where('rto_student_courses.campus_id', '=', $campus);
        $sql = $getUsers->get(['rto_students.*', 'rto_courses.course_code', 'rto_student_courses.course_attempt', 'rto_student_courses.status as courseStatus'])
            ->toarray();

        return $sql;
    }

    public function getStudentCoursesData($studentId)
    {

        //        return StudentCourse::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
        //                        ->join('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
        //                        ->leftjoin('rto_college_enrollment_fees as ref', 'ref.id', '=', 'rto_student_courses.enroll_fee')
        //                        ->select('rto_student_courses.*', 'rto_student_courses.offer_status as courseStatus', 'rto_agents.*', 'rto_campus.*', 'ref.fee_amount as feeAmount')
        //                        ->where('rto_student_courses.student_id', '=', $studentId)
        //                        ->orderBy('rto_student_courses.id', 'desc')
        //                        ->get();
        // pratik changes query desc to ASC
        return StudentCourse::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->select('rto_student_courses.*', 'rto_campus.name as campus_name', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_student_courses.offer_status as courseStatus')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->orderBy('rto_student_courses.id', 'ASC')
            ->get();
    }

    public function getStudentCoursesDataAgent($studentId)
    {

        // pratik changes query desc to ASC
        return StudentCourse::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->join('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->join('rto_users', 'rto_users.id', '=', 'rto_agents.user_id')
            ->select('rto_student_courses.*', 'rto_campus.name as campus_name', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_student_courses.offer_status as courseStatus')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->orderBy('rto_student_courses.id', 'ASC')
            ->get();
    }

    public function getAppliedStudentCourse($courseId, $studentId)
    {
        if (! empty($courseId)) {

            //            return StudentCourse::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            //                                ->where('rto_student_courses.student_id', '=', $studentId)
            //                                ->where('rto_student_courses.course_id', '=', $courseId)
            //                                ->get();

            return StudentCourse::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
                ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
                ->where('rto_student_courses.student_id', '=', $studentId)
                ->where('rto_student_courses.course_id', '=', $courseId)
                ->select('rto_campus.name as campus_name', 'rto_courses.cricos_code', 'rto_courses.course_code', 'rto_courses.work_placement_hour', 'rto_courses.work_placement', 'rto_courses.course_name', 'rto_student_courses.*', 'rto_agents.agency_name', 'rto_agents.office_address', 'rto_agents.office_city', 'rto_agents.office_state', 'rto_agents.office_postcode')
                ->get();
        } else {

            // return StudentCourse::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')->where('student_id', '=', $studentId)->get();
            return StudentCourse::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
                ->join('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
                ->leftjoin('rto_college_enrollment_fees as ref', 'ref.id', '=', 'rto_student_courses.enroll_fee')
                ->select('rto_student_courses.*', 'rto_agents.*', 'rto_campus.name as campus_name', 'rto_courses.cricos_code', 'rto_courses.work_placement_hour', 'rto_courses.work_placement', 'rto_campus.*', 'ref.fee_amount as feeAmount', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_student_courses.id as studentCourseId')
                ->where('rto_student_courses.student_id', '=', $studentId)
                ->orderBy('rto_student_courses.id', 'desc')
                ->get();
        }

        //        return StudentCourse::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
        //                        ->join('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
        //                        ->leftjoin('rto_college_enrollment_fees as ref', 'ref.id', '=', 'rto_student_courses.enroll_fee')
        //                        ->select('rto_student_courses.*', 'rto_agents.*', 'rto_campus.*', 'ref.fee_amount as feeAmount')
        //                        ->where('rto_student_courses.student_id', '=', $studentId)
        //                        ->orderBy('rto_student_courses.id', 'desc')
        //                        ->get();
    }

    public function getAppliedStudentCourseV2($courseId, $studentId, $student_course_id)
    {

        return StudentCourse::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.course_id', '=', $courseId)
            ->where('rto_student_courses.id', '=', $student_course_id)
            ->select('rto_campus.name as campus_name', 'rto_courses.cricos_code', 'rto_courses.course_code', 'rto_courses.work_placement_hour', 'rto_courses.work_placement', 'rto_courses.course_name', 'rto_student_courses.*', 'rto_agents.agency_name', 'rto_agents.office_address', 'rto_agents.office_city', 'rto_agents.office_state', 'rto_agents.office_postcode')
            ->get();
    }

    public function getStudentInfoFromStudentCourseId($courseId, $studentId, $studentCourseId)
    {
        if (! empty($courseId)) {
            return StudentCourse::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
                ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
                ->where('rto_student_courses.student_id', '=', $studentId)
                ->where('rto_student_courses.course_id', '=', $courseId)
                ->where('rto_student_courses.id', '=', $studentCourseId)
                ->select('rto_campus.name as campus_name', 'rto_courses.cricos_code', 'rto_courses.course_code', 'rto_courses.work_placement_component_type', 'rto_courses.work_placement_hour', 'rto_courses.work_placement', 'rto_courses.course_name', 'rto_courses.maximum_weekly_study', 'rto_courses.face_to_face_hours', 'rto_courses.online_hours', 'rto_student_courses.*', 'rto_agents.agency_name', 'rto_agents.office_address', 'rto_agents.office_city', 'rto_agents.office_state', 'rto_agents.office_postcode')
                ->orderBy('rto_student_courses.start_date', 'asc')
                ->get();
        } else {

            return StudentCourse::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
                ->join('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
                ->leftjoin('rto_college_enrollment_fees as ref', 'ref.id', '=', 'rto_student_courses.enroll_fee')
                ->select('rto_student_courses.*', 'rto_agents.*', 'rto_campus.name as campus_name', 'rto_courses.cricos_code', 'rto_courses.work_placement_component_type', 'rto_courses.work_placement_hour', 'rto_courses.face_to_face_hours', 'rto_courses.online_hours', 'rto_courses.work_placement', 'rto_campus.*', 'ref.fee_amount as feeAmount', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_student_courses.id as studentCourseId')
                ->where('rto_student_courses.student_id', '=', $studentId)
                ->where('rto_student_courses.id', '=', $studentCourseId)
                ->orderBy('rto_student_courses.start_date', 'asc')
                ->get();
        }
    }

    public function getStudentOfferLetterInfoFromStudentId($studentId, $isCombinedLetter = false)
    {
        $query = StudentCourse::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->where('rto_student_courses.student_id', '=', $studentId);

        // $query->where('rto_student_courses.course_id', '=', $courseId);
        // $query->where('rto_student_courses.id', '=', $studentCourseId);

        if ($isCombinedLetter) {
            $query->where('rto_student_courses.status', '!=', self::STATUS_DID_NOT_COMMENCE);
        }

        return $query->select('rto_campus.name as campus_name', 'rto_courses.cricos_code', 'rto_courses.course_code', 'rto_courses.work_placement_hour', 'rto_courses.vocational_duration', 'rto_courses.work_placement', 'rto_courses.work_placement_component_type', 'rto_courses.course_name', 'rto_courses.maximum_weekly_study', 'rto_courses.face_to_face_hours', 'rto_courses.online_hours', 'rto_student_courses.*', 'rto_agents.agency_name', 'rto_agents.office_address', 'rto_agents.office_city', 'rto_agents.office_state', 'rto_agents.office_postcode')
            ->orderBy('rto_student_courses.start_date', 'asc')
            ->get();
    }

    public function getOfferedStudebtList($courseId, $studentId)
    {
        if (! empty($courseId)) {
            return StudentCourse::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
                ->where('rto_student_courses.student_id', '=', $studentId)
                ->where('rto_student_courses.course_id', '=', $courseId)
                ->where('rto_student_courses.offer_status', '=', 'Offered')
                ->select('rto_campus.name as campus_name', 'rto_courses.cricos_code', 'rto_courses.course_code', 'rto_courses.work_placement_hour', 'rto_courses.work_placement', 'rto_courses.course_name', 'rto_student_courses.*')
                ->get();
        } else {
            // pratik changes desc to asc
            return StudentCourse::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
                ->join('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
                ->leftjoin('rto_college_enrollment_fees as ref', 'ref.id', '=', 'rto_student_courses.enroll_fee')
                ->select('rto_student_courses.*', 'rto_agents.*', 'rto_campus.name as campus_name', 'rto_courses.cricos_code', 'rto_courses.work_placement_hour', 'rto_courses.work_placement', 'rto_campus.*', 'ref.fee_amount as feeAmount', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_student_courses.id as studentCourseId')
                ->where('rto_student_courses.student_id', '=', $studentId)
                ->where('rto_student_courses.offer_status', '=', 'Offered')
                ->orderBy('rto_student_courses.start_date', 'ASC')
                //                            ->orderBy('rto_student_courses.id', 'ASC')
                ->get();
        }
    }

    public function getEnrolledStudebtList($courseId, $studentId, $studentCourseId)
    {
        if (! empty($courseId)) {
            return StudentCourse::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
                ->where('rto_student_courses.student_id', '=', $studentId)
                ->where('rto_student_courses.course_id', '=', $courseId)
                ->where('rto_student_courses.id', '=', $studentCourseId)
                ->where('rto_student_courses.offer_status', '=', 'Enrolled')
                ->select('rto_campus.name as campus_name', 'rto_courses.cricos_code', 'rto_courses.course_code', 'rto_courses.work_placement_hour', 'rto_courses.work_placement', 'rto_courses.course_name', 'rto_student_courses.*')
                ->get();
        } else {
            // pratik changes desc to asc
            return StudentCourse::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
                ->join('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
                ->leftjoin('rto_college_enrollment_fees as ref', 'ref.id', '=', 'rto_student_courses.enroll_fee')
                ->select('rto_student_courses.*', 'rto_agents.*', 'rto_campus.name as campus_name', 'rto_courses.cricos_code', 'rto_courses.work_placement_hour', 'rto_courses.work_placement', 'rto_campus.*', 'ref.fee_amount as feeAmount', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_student_courses.id as studentCourseId')
                ->where('rto_student_courses.student_id', '=', $studentId)
                ->where('rto_student_courses.id', '=', $studentCourseId)
                ->where('rto_student_courses.offer_status', '=', 'Enrolled')
                ->orderBy('rto_student_courses.id', 'ASC')
                ->get();
        }
    }

    public function getStudentCoursesInformation($studentId, $courseId)
    {
        return StudentCourse::from('rto_student_courses as rsc')
            ->join('rto_campus', 'rto_campus.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_course_template as rct', 'rct.id', '=', 'rsc.course_template')
            ->where('rsc.student_id', '=', $studentId)
            ->where('rsc.course_id', '=', $courseId)
            ->select('rc.results_calculation_methods', 'rto_campus.name as campus_name', 'rct.template_name as courseTemplate', 'rc.cricos_code', 'rc.course_code', 'rc.course_name', 'rsc.*')
            ->get();
    }

    public function getStudentCoursesEmailContent($studentId, $courseId)
    {
        $sql = StudentCourse::from('rto_student_courses as rsc')
            ->leftjoin('rto_campus as campus', 'campus.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rsc.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_course_template as rct', 'rct.id', '=', 'rsc.course_template')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_country as country1', 'country1.id', '=', 'rs.birth_country')
            ->leftjoin('rto_country as country2', 'country2.id', '=', 'rs.nationality')
            ->leftjoin('rto_country as country3', 'country3.id', '=', 'rs.current_country')
            ->leftjoin('rto_colleges as clg', 'rs.college_id', '=', 'clg.id')
            ->leftjoin('rto_college_details as rcd', 'rcd.college_id', '=', 'clg.id')
            ->leftjoin('rto_college_course_type as rcct', 'rcct.id', '=', 'rsc.course_type_id')
            ->leftjoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rsd.account_manager_id')
            ->leftjoin('rto_agents as agent', 'agent.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_setup_section as rss', 'rss.id', '=', 'rs.visa_status')
            ->where('rsc.student_id', '=', $studentId);
        if ($courseId != '') {
            $sql->where('rsc.course_id', '=', $courseId);
        }

        return $result = $sql->select(
            'rc.results_calculation_methods',
            'rc.cricos_code',
            'rc.course_code',
            'rc.course_name',
            'campus.name as campus_name',
            'rct.template_name as courseTemplate',
            DB::raw('concat(COALESCE(rs.name_title, ""), COALESCE(rs.first_name, ""), " ", COALESCE(rs.family_name, "")) as student_name'),
            DB::raw('concat(rst.name_title, rst.first_name, " ", rst.last_name) as account_manager'),
            'rs.first_name',
            'rs.middel_name',
            'rs.family_name',
            'rs.nickname',
            'rs.current_postcode',
            'rs.current_state',
            'rs.current_country',
            'rs.email as student_email',
            'rs.DOB as birth_date',
            'rs.visa_expiry_date',
            'rss.value as visa_type',
            'rs.generated_stud_id',
            'rs.current_mobile_phone',
            'rs.current_postcode',
            'rs.current_state',
            'rs.current_street_name',
            'rs.current_city',
            'rs.name_title',
            'rs.gender',
            'rs.passport_no',
            'rs.visa_status',
            'rs.visa_expiry_date',
            'rcd.fax',
            'country1.name as birth_country',
            'country2.nationality',
            'country3.name as country_name',
            'rsd.emergency_email',
            'clg.contact_email as college_email',
            'clg.college_name as entity_name',
            'clg.timezone as college_timezone',
            'rcct.title as course_type',
            'agent.id as agent_id',
            'agent.agency_name as agent_name',
            'rsc.*',
            'clg.RTO_code',
            'clg.CRICOS_code',
            'clg.legal_name',
            'clg.contact_person',
            'clg.contact_phone',
            'clg.college_url',
            'clg.college_logo',
            'clg.college_signature',
            'clg.dean_name',
            'clg.dean_signature',
            'clg.admission_manager_signature',
            'clg.student_support_signature',
            'clg.allow_public_images',
            'rcd.ABN as college_ABN',
            'rs.student_type',
            'rst.first_name as teacher_first_name',
            'rst.last_name as teacher_last_name',
            'rst.email as teacher_email',
            'rst.mobile as teacher_mobile',
            'agent.agency_name',
            'agent.contact_person as agent_name',
            'agent.primary_email as agent_email',
            'agent.telephone as agent_telephone',
            'rs.current_street_no',
            'rs.current_unit_detail',
            'rs.current_building_name'
        )
            ->get()->toarray();
    }

    public function searchStudentsWithCampus($campusID)
    {
        $res = StudentCourse::select('student_id');
        if ($campusID == 'all') {
            $res->where('campus_id', '!=', null);
        } elseif ($campusID == 'notAll') {
            $res->where('campus_id', '=', null);
        } else {
            $res->where('campus_id', '=', $campusID);
        }

        $res->distinct('student_id');
        $sql = $res->get()
            ->toArray();

        return $sql;
    }

    public function deleteStudentCourse($studentId)
    {
        return StudentCourse::where('student_id', '=', $studentId)->delete();
    }

    public function deleteStudentCourseById($primaryId)
    {
        // return StudentCourse::where('id', '=', $primaryId)->delete();

        // TODO::GNG-2218
        $whereArr = ['student_course_id' => $primaryId];
        DB::beginTransaction();
        try {
            // Delete agent commission data
            StudentAgentCommission::where($whereArr)->delete();

            // Delete miscellaneous data
            StudentMiscellaneousPayment::where($whereArr)->delete();

            // Delete scholarship data
            StudentScholarship::where($whereArr)->delete();

            // Delete payment transaction data
            StudentInitialPaymentTransaction::where($whereArr)->delete();

            // Delete payment detail data
            StudentInitialPaymentDetails::where($whereArr)->delete();

            // Delete initial payment data
            StudentInitialPayment::where($whereArr)->delete();

            // Delete student course mapping data
            $res = StudentCourse::where('id', $primaryId)->delete();

            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getStudentProfileCourses($studentId, $collegeId, $perPage = '')
    {
        $sql = StudentCourse::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_staff_and_teacher as rstp', 'rstp.id', '=', 'rto_student_courses.palacement_manager_id')
            ->leftjoin('rto_staff_and_teacher as rstc', 'rstc.id', '=', 'rto_student_courses.course_manager_id')
            ->leftjoin('rto_course_template as template', 'template.id', '=', 'rto_student_courses.course_template')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            // ->where('rto_student_courses.offer_status', '=', 'Enrolled') //19-10-2020 GD-820
            ->where('rto_courses.college_id', '=', $collegeId)
            ->orderBy('rto_student_courses.finish_date', 'asc')
            ->select('rto_student_courses.id as student_course_id', 'rto_student_courses.student_id as student_id', 'rto_student_courses.*', 'template.template_name', 'rto_courses.id as course_id', 'rstp.first_name as placement_manager_name', 'rstc.first_name as course_manager_name', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_courses.created_at', 'rto_courses.AVETMISS_Report', 'rto_campus.name as campus_name', 'rto_agents.agency_name');

        if (! empty($perPage)) {
            $StudentCourse = $sql->paginate($perPage);
        } else {
            $StudentCourse = $sql->get();
        }

        for ($i = 0; $i < count($StudentCourse); $i++) {
            $rslt = $this->getWeekDifferent($StudentCourse[$i]['start_date'], $StudentCourse[$i]['finish_date']);
            $StudentCourse[$i]['week_out_of_week'] = $rslt['weekDiff'];
        }

        return $StudentCourse;
    }

    public function getWeekDifferent($datefrom, $dateto)
    {

        $diff = strtotime($dateto, 0) - strtotime($datefrom, 0);
        $weekDiff = floor($diff / 604800);
        $ddate = date('Y-m-d');
        $currentDiff = date('W', strtotime($ddate));

        if ($dateto > $ddate) {
            $total_days = ((strtotime($ddate) - strtotime($datefrom)) / 86400);
            $total_week = $total_days / 7;
            $cure_week = ceil($total_week);
        } else {
            $total_days = ((strtotime($dateto) - strtotime($datefrom)) / 86400);
            $total_week = $total_days / 7;
            $cure_week = floor($total_week);
        }
        $arrDate = [
            'MonthCurrentWeek' => $currentDiff,
            'totalWeek' => $weekDiff,
            'currentweek' => $cure_week,
            'weekDiff' => $cure_week.' of '.$weekDiff,
        ];

        return $arrDate;
    }

    // temporary only need course apllied name
    // V2 Version is created For Remove First Option
    public function getOfferStudentCourseData($studentId)
    {
        $dataArray = [];
        $courseArray = [];
        $arrStudentCourse = StudentCourse::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.offer_status', '=', 'Enrolled')
            ->get([
                'rto_student_courses.id as studentCourseId',
                'rto_courses.course_name',
                'rto_courses.id as courseId',
                'rto_courses.course_code',
            ])
            ->toArray();

        for ($i = 0; $i < count($arrStudentCourse); $i++) {
            $courseArray[$arrStudentCourse[$i]['courseId']] = $arrStudentCourse[$i]['course_code'].':'.$arrStudentCourse[$i]['course_name'];
        }
        $dataArray[0] = $arrStudentCourse;
        $dataArray[1] = $courseArray;

        return $dataArray;
    }

    public function getStudentCourseWithId($studentId)
    {
        $arrStudentCourse = StudentCourse::join('rto_courses as rc', 'rc.id', '=', 'rto_student_courses.course_id')
            ->join('rto_college_course_type as rcct', 'rcct.id', '=', 'rc.course_type_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.offer_status', '=', 'Enrolled')
            ->get(['rto_student_courses.id as studentCourseId', 'rc.course_name', 'rc.id', 'rc.course_code', 'rcct.title as course_type'])->toArray();

        $arrStudentCourseCode = [];
        for ($i = 0; $i < count($arrStudentCourse); $i++) {
            $arrStudentCourseCode[$arrStudentCourse[$i]['id']] = [
                'text' => $arrStudentCourse[$i]['course_code'].' : '.$arrStudentCourse[$i]['course_name'],
                'id' => $arrStudentCourse[$i]['studentCourseId'],
                'course_type' => strtolower($arrStudentCourse[$i]['course_type']),
            ];
        }

        return $arrStudentCourseCode;
    }

    public function getVetFeeStudentCourseList($studentId)
    {
        $result = [];
        $resultArr = StudentCourse::from('rto_student_courses as rsc')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where('rsc.student_id', '=', $studentId)
            ->where('rsc.offer_status', '=', 'Enrolled')
            ->where('rc.fee_help', '1')
            ->get([
                'rsc.id as studentCourseId',
                'rc.id',
                'rc.course_code',
                'rc.course_name',
            ]);
        if ($resultArr->count() > 0) {
            foreach ($resultArr as $row) {
                $result[$row->id] = $row->course_code.' : '.$row->course_name;
            }
        } else {
            $result[''] = 'No course found';
        }

        return $result;
    }

    public function getCampusName($studentId, $courseId)
    {
        $campusName = StudentCourse::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.course_id', ($courseId > 0) ? '=' : '!=', $courseId)
            ->get(['rto_campus.name'])
            ->toArray();

        return (count($campusName) > 0) ? $campusName[0]['name'] : '--';
    }

    public function getCampusNameV2($studentcourseId, $studentId)
    {
        return $campusName = StudentCourse::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.id', ($studentcourseId > 0) ? '=' : '!=', $studentcourseId)
            ->get(['rto_campus.name'])
            ->toArray();
    }

    public function getStudentAppliedCourseName($studentId)
    {

        $arrStudentCourse = StudentCourse::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            // ->pluck('rto_courses.course_name', 'rto_courses.id')
            ->get(['rto_courses.course_name', 'rto_courses.id', 'rto_courses.course_code'])->toArray();

        // $arrStudentCourse = StudentCourse::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')->where('rto_student_courses.student_id', '=', $studentId)->get(['rto_courses.course_name','rto_courses.course_code', 'rto_courses.id'])->toArray();

        $nullRecord[''] = 'No Course Found';
        $arrStudentCourseCode = [];
        for ($i = 0; $i < count($arrStudentCourse); $i++) {
            $arrStudentCourseCode[$arrStudentCourse[$i]['id']] = $arrStudentCourse[$i]['course_code'].' : '.$arrStudentCourse[$i]['course_name'];
        }

        // $studentCourseList[''] = '-- Select Course List --';
        // return $returnArrStudentCourse = $studentCourseList + $arrStudentCourseCode;
        $result = (count($arrStudentCourseCode) > 0) ? $arrStudentCourseCode : $nullRecord;

        // print_r($result);exit;
        return $result;
    }

    public function getStudentAppliedOfferCourseName($studentId)
    {

        $arrStudentCourse = StudentCourse::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            // ->where('rto_student_courses.offer_status', '=', 'Enrolled')
            // ->pluck('rto_courses.course_name', 'rto_courses.id')
            ->get(['rto_courses.course_name', 'rto_courses.id', 'rto_courses.course_code'])->toArray();

        // $arrStudentCourse = StudentCourse::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')->where('rto_student_courses.student_id', '=', $studentId)->get(['rto_courses.course_name','rto_courses.course_code', 'rto_courses.id'])->toArray();

        $studentCourseList[''] = '-- Select Course List --';

        // print_r($arrStudentCourse);exit;
        $arrStudentCourseCode = [];
        for ($i = 0; $i < count($arrStudentCourse); $i++) {
            $arrStudentCourseCode[$arrStudentCourse[$i]['id']] = $arrStudentCourse[$i]['course_code'].' : '.$arrStudentCourse[$i]['course_name'];
        }

        return $returnArrStudentCourse = $studentCourseList + $arrStudentCourseCode;
    }

    // temporary only need course apllied name
    public function getStudentAppliedCourseNameV2($studentId)
    {

        $nullResult[''] = 'No Course Found';
        $arrStudentCourse = StudentCourse::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->whereIn('rto_student_courses.offer_status', ['Enrolled', 'Offered'])
            // ->pluck('rto_courses.course_name', 'rto_courses.id')
            ->get(['rto_courses.course_name', 'rto_courses.id', 'rto_courses.course_code'])
            ->toArray();

        // $arrStudentCourse = StudentCourse::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')->where('rto_student_courses.student_id', '=', $studentId)->get(['rto_courses.course_name','rto_courses.course_code', 'rto_courses.id'])->toArray();
        // print_r($arrStudentCourse);exit;
        $arrStudentCourseCode = [];
        for ($i = 0; $i < count($arrStudentCourse); $i++) {
            $arrStudentCourseCode[$arrStudentCourse[$i]['id']] = $arrStudentCourse[$i]['course_code'].' : '.$arrStudentCourse[$i]['course_name'];
        }

        $result = (count($arrStudentCourse) > 0) ? $arrStudentCourseCode : $nullResult;

        return $result;
    }

    public function getStudentAppliedCourseNameV3($studentId)
    {

        $nullResult[''] = 'No Course Found';
        $arrStudentCourse = StudentCourse::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->get(['rto_courses.course_name', 'rto_courses.id', 'rto_courses.course_code'])
            ->toArray();

        $arrStudentCourseCode = [];
        for ($i = 0; $i < count($arrStudentCourse); $i++) {
            $arrStudentCourseCode[$arrStudentCourse[$i]['id']] = $arrStudentCourse[$i]['course_code'].' : '.$arrStudentCourse[$i]['course_name'];
        }

        $result = (count($arrStudentCourse) > 0) ? $arrStudentCourseCode : $nullResult;

        return $result;
    }

    public function getStudentAppliedCourseNameV4($studentId)
    {

        $nullResult[''] = 'No Course Found';
        $arrStudentCourse = StudentCourse::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->get(['rto_courses.course_name', 'rto_student_courses.id', 'rto_student_courses.status', 'rto_courses.course_code'])
            ->toArray();

        $arrStudentCourseCode = [];
        for ($i = 0; $i < count($arrStudentCourse); $i++) {
            $arrStudentCourseCode[$arrStudentCourse[$i]['id']] = $arrStudentCourse[$i]['course_code'].' : '.$arrStudentCourse[$i]['course_name'].' ( '.$arrStudentCourse[$i]['status'].' ) ';
        }

        $result = (count($arrStudentCourse) > 0) ? $arrStudentCourseCode : $nullResult;

        return $result;
    }

    // temporary only need Agent name
    public function getStudentAgentName($studentId)
    {
        return StudentCourse::join('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->groupBy('rto_student_courses.agent_id')
            ->get([
                'rto_agents.id',
                'rto_agents.agency_name',
                'rto_agents.primary_email',
                'rto_agents.alertnet_email',
            ]);
    }

    // check with student_id and course_id
    public function getStudentAgentNameV2($studentId, $courseId)
    {
        $nullResult[] = '- - Select Agent Email - -';
        $result = StudentCourse::join('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.course_id', '=', $courseId)
            ->groupBy('rto_student_courses.agent_id')
            ->pluck('rto_agents.primary_email', 'rto_agents.id')->toArray();

        $arrayMerge = $nullResult + $result;

        return $arrayMerge;
    }

    public function getStudentAgentNameV3($studentId, $studentcourseId)
    {
        $nullResult[] = '- - Select Agent Email - -';
        $result = StudentCourse::join('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.id', '=', $studentcourseId)
            ->groupBy('rto_student_courses.agent_id')
            ->pluck('rto_agents.primary_email', 'rto_agents.id')->toArray();

        $arrayMerge = $nullResult + $result;

        return $arrayMerge;
    }

    public function getStudentData($courseID)
    {
        return StudentCourse::select('student_id')->where('id', '=', $courseID)->get()->toArray();
    }

    public function getStudentID($courseID)
    {
        $data = StudentCourse::where('id', '=', $courseID)->get(['student_id']);

        return $data[0]->student_id;
    }

    public function checkDuplicateRecord($studentId, $campusId, $courseId, $offerId = '')
    {
        // echo $studentId.'---------'.$campusId.'---------'.$courseId.'---------'.$offerId;
        $checkRec = StudentCourse::select('student_id')
            ->where('student_id', '=', $studentId)
            // ->where('campus_id', '=', $campusId)
            ->where('course_id', '=', $courseId);
        if (! empty($offerId)) {
            $checkRec->where('offer_id', $offerId);
        }
        $result = $checkRec->get();

        return $result;

        //        return StudentCourse::select('student_id')
        //                        ->where('student_id', '=', $studentId)
        //                        ->where('campus_id', '=', $campusId)
        //                        ->where('course_id', '=', $courseId)
        //                        ->get();
    }

    public function getStudentUpfrontFeeSchedule($studentId, $studentCourseId)
    {
        return StudentCourse::join('rto_courses as rc', 'rc.id', '=', 'rto_student_courses.course_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.course_id', '=', $studentCourseId)
            ->get(['rc.course_code', 'rc.course_name', 'rto_student_courses.*']);
    }

    public function getStudentUpfrontFeeScheduleV2($studentId, $studentCourseId)
    {
        return StudentCourse::join('rto_courses as rc', 'rc.id', '=', 'rto_student_courses.course_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.id', '=', $studentCourseId)
            ->get(['rc.course_code', 'rc.course_name', 'rto_student_courses.*']);
    }

    public function startDateCalculation($totalWeeks, $courseFee, $courseUpfrontFee, $startDate)
    {
        if ($totalWeeks == '') {
            $totalWeeks = 1;
        }
        $perDayFee = $courseFee / ($totalWeeks * 7);
        $upFrontFeeDayCount = $courseUpfrontFee / $perDayFee;
        $dayCount = round($upFrontFeeDayCount);
        $date = strtotime('+'.$dayCount.' days', strtotime($startDate));
        $installMentStartDate = date('d-m-Y', $date);

        return $installMentStartDate;
    }

    public function getStudentCOEDetail($courseID)
    {
        // return StudentCourse::where('id', $courseID)->get(['id', 'student_id', 'coe_applicable', 'coe_name', 'coe_image','coe_material_id'])->toArray();

        $res = StudentCourse::where('id', $courseID)
            ->get(['id', 'student_id', 'coe_applicable', 'coe_name', 'coe_image', 'coe_material_id'])
            ->first();

        $student = Student::findOrFail($res->student_id);
        $res->xeroContact = $student->fresh()->xeroContact;

        $outstandingBalance = 0;
        $unallocatedCredit = 0;
        if (Xero::isConnected()) {
            if ($student->isXeroContactCreatated()) {
                // dispatch_sync(new \Domains\Xero\Jobs\SyncContactFromXero($student->xeroContact));
                $outstandingBalance = $student->xeroContact->fresh()->outstanding_balance ? $student->xeroContact->fresh()->outstanding_balance : 0;
                $unallocatedCredit = $student->xeroContact->fresh()->unallocated_credit ?? 0;
            }
        }
        $res->outstanding_balance = abs($outstandingBalance);
        $res->unallocatedCredit = abs($unallocatedCredit);

        return $res->toArray();
    }

    public function getStudentCourseData($courseID)
    {
        return StudentCourse::select('*')
            ->where('id', '=', $courseID)
            ->get()->toArray();
    }

    public function getStudentUpfrontFeeScheduleNew($studentCourseID)
    {
        return StudentCourse::join('rto_courses as rc', 'rc.id', '=', 'rto_student_courses.course_id')
            ->where('rto_student_courses.id', '=', $studentCourseID)
            ->select('rc.course_code', 'rc.course_name', 'rto_student_courses.*')
            ->first();
    }

    public function getOfferIdListArr($studentId)
    {
        $offerId = StudentCourse::where('student_id', '=', $studentId)->pluck('offer_id', 'offer_id')->toArray();
        $offerIdRecord[''] = '- - Select Offer ID - -';

        return $returnOfferIdRecord = $offerIdRecord + $offerId;
    }

    public function updateStudentCourse($courseID, $request, $isScheduleExist = false)
    {

        // $isValidForUpdate = ($isScheduleExist) ? (!empty($request->campus_id)) : (!empty($request->agent_id) && !empty($request->campus_id));
        $isValidForUpdate = (! empty($request->agent_id) && ! empty($request->campus_id));

        if ($isValidForUpdate) {
            $studentCourse = StudentCourse::find($courseID);
            $oldStatus = $studentCourse->status;

            $studentCourse->offer_id = ($request->input('offer_id') != '') ? $request->input('offer_id') : null;
            // $studentCourse->course_id = ($request->input('course_id') != '') ? $request->input('course_id') : null;
            // $studentCourse->res_cal_method = ($request->input('res_cal_method') != '') ? $request->input('res_cal_method') : null;
            $studentCourse->palacement_manager_id = ($request->input('palacement_manager_id') != '') ? $request->input('palacement_manager_id') : null;
            $studentCourse->course_manager_id = ($request->input('course_manager_id') != '') ? $request->input('course_manager_id') : null;
            $studentCourse->campus_id = ($request->input('campus_id') != '') ? $request->input('campus_id') : null;

            $studentCourse->intake_year = ($request->input('intake_year') != '') ? $request->input('intake_year') : null;
            $studentCourse->intake_date = ($request->input('intake_date') != '') ? date('Y-m-d', strtotime($request->input('intake_date'))) : null;
            $studentCourse->issued_date = ($request->input('issued_date') != '') ? date('Y-m-d', strtotime($request->input('issued_date'))) : null;

            // $studentCourse->coe_name = ($request->input('coe_no') != '') ? $request->input('coe_no') : null;
            $studentCourse->course_template = ($request->input('course_template') != '') ? $request->input('course_template') : '';
            $studentCourse->is_finish_dt = ($request->input('is_finish_dt') != '') ? $request->input('is_finish_dt') : '0';

            // if(!$isScheduleExist){
            $studentCourse->course_fee = ($request->input('course_fee') != '') ? $request->input('course_fee') : null;
            $studentCourse->agent_id = ($request->input('agent_id') != '') ? $request->input('agent_id') : null;
            $studentCourse->course_upfront_fee = ($request->input('course_upfront_fee') != '') ? $request->input('course_upfront_fee') : null;
            $studentCourse->start_date = ($request->input('start_date') != '') ? date('Y-m-d', strtotime($request->input('start_date'))) : null;
            $studentCourse->finish_date = ($request->input('finish_date') != '') ? date('Y-m-d', strtotime($request->input('finish_date'))) : null;
            // }

            $studentCourse->enroll_fee = ($request->input('enroll_fee') != '') ? $request->input('enroll_fee') : null;
            $studentCourse->course_material_fee = ($request->input('course_material_fee') != '') ? $request->input('course_material_fee') : null;
            $studentCourse->status = ($request->input('status') != '') ? $request->input('status') : '';
            $studentCourse->study_reason_id = ($request->input('study_reason_id') != '') ? $request->input('study_reason_id') : null;

            $studentCourse->application_request = ($request->input('application_req') != '') ? $request->input('application_req') : null;
            // $studentCourse->advance_standing_credit = ($request->input('credit_transfer') != '') ? $request->input('credit_transfer') : null;
            $studentCourse->credit_transfer_request = ($request->input('credit_transfer') != '') ? $request->input('credit_transfer') : null;
            $studentCourse->special_instructions = ($request->input('special_instructions') != '') ? $request->input('special_instructions') : null;

            $studentCourse->employer_id = ($request->input('employer') != '') ? $request->input('employer') : null;
            $studentCourse->contract_schedule_id = ($request->input('contract_schedule') != '') ? $request->input('contract_schedule') : null;
            $studentCourse->purchasing_contract_ode = ($request->input('purchasing_contract_ode') != '') ? $request->input('purchasing_contract_ode') : null;
            $studentCourse->schedule_code = ($request->input('schedule_code') != '') ? $request->input('schedule_code') : null;
            $studentCourse->auto_update_to_sru = ($request->input('auto_update_to_sru') != '') ? $request->input('auto_update_to_sru') : null;

            $studentCourse->census_date1 = ($request->input('census_date1') != '') ? date('Y-m-d', strtotime($request->input('census_date1'))) : null;
            $studentCourse->census_date2 = ($request->input('census_date2') != '') ? date('Y-m-d', strtotime($request->input('census_date2'))) : null;
            $studentCourse->census_date3 = ($request->input('census_date3') != '') ? date('Y-m-d', strtotime($request->input('census_date3'))) : null;

            $studentCourse->is_orientation = ($request->input('is_orientation') != '') ? $request->input('is_orientation') : '0';
            $studentCourse->default_unit_fee = ($request->input('default_unit_fee') != '') ? $request->input('default_unit_fee') : null;
            //            $studentCourse->mode_of_delivery = ($request->input('mode_of_delivery') != '') ? $request->input('mode_of_delivery') : null;

            $studentCourse->internal = ($request->input('internal') != '') ? 'Y' : 'N';
            $studentCourse->external = ($request->input('external') != '') ? 'Y' : 'N';
            $studentCourse->workplace_based_delivery = ($request->input('workplace_based_delivery') != '') ? 'Y' : 'N';

            $studentCourse->is_claim = ($request->input('is_claim') != '') ? $request->input('is_claim') : '0';
            $studentCourse->is_qualification = ($request->input('is_qualification') != '') ? $request->input('is_qualification') : '0';
            $studentCourse->is_certificate = ($request->input('is_certificate') != '') ? $request->input('is_certificate') : '0';
            $studentCourse->is_material_fee_inc_initail_payment = ($request->input('is_material_fee_inc_initail_payment') != '') ? $request->input('is_material_fee_inc_initail_payment') : '0';
            $studentCourse->advance_standing_credit = ($request->input('advance_standing_credit') != '') ? $request->input('advance_standing_credit') : '0';
            if ($request->input('is_claim') == 1) {
                $studentCourse->survey_contact_status = $request->input('survey_contact_status');
                $studentCourse->purchasing_contract_identifier = $request->input('purchasing_contract_identifier');
                $studentCourse->purchasing_contract_schedule_identifier = $request->input('purchasing_contract_schedule_identifier');
                $studentCourse->associated_course_identifier = $request->input('associated_course_identifier');
                $studentCourse->is_fulltimelearing = $request->input('is_fulltimelearing');
            } else {
                $studentCourse->survey_contact_status = null;
                $studentCourse->purchasing_contract_identifier = null;
                $studentCourse->purchasing_contract_schedule_identifier = null;
                $studentCourse->associated_course_identifier = null;
                $studentCourse->is_fulltimelearing = 0;
            }

            if (! $isScheduleExist) {
                $total_weeks = ($request->input('total_weeks') != '') ? $request->input('total_weeks') : null;
                $date1 = strtotime($request->input('start_date'));
                $date2 = strtotime('+'.$total_weeks.' week', $date1);
                $finish_date = date('Y-m-d H:i:s', $date2);
                $studentCourse->total_weeks = $total_weeks;
                // $studentCourse->finish_date = $finish_date;
            }

            $studentCourse->updated_by = Auth::user()->id;

            $studentCourse->save();

            if ($request->input('status') != '' && $oldStatus != $request->input('status')) {

                /* Start Insert Status History Record on "rto_student_status_change_history" Table */
                $studentID = $studentCourse->student_id;
                $newStatus = $request->input('status');

                $objStatusHistory = new StudentCourseStatusHistory;
                $objStatusHistory->saveStatusHistory($studentID, $courseID, $newStatus, $oldStatus);
                /* End Insert Status History */
            }
        }
    }

    // Update Student Course Detail from Offer
    public function updateStudentCourseV2($courseID, $request)
    {

        if ($request->input('agent_id') != '' && $request->input('campus_id')) {

            // echo $request->input('is_orientation').'----------'. $request->input('is_finish_dt');exit;

            $studentCourse = StudentCourse::find($courseID);
            $oldStatus = $studentCourse->status;

            $studentCourse->course_id = ($request->input('course_id') != '') ? $request->input('course_id') : null;
            $studentCourse->palacement_manager_id = ($request->input('palacement_manager_id') != '') ? $request->input('palacement_manager_id') : null;
            $studentCourse->course_manager_id = ($request->input('course_manager_id') != '') ? $request->input('course_manager_id') : null;
            $studentCourse->campus_id = ($request->input('campus_id') != '') ? $request->input('campus_id') : null;
            $studentCourse->agent_id = ($request->input('agent_id') != '') ? $request->input('agent_id') : null;

            $studentCourse->intake_year = ($request->input('intake_year') != '') ? $request->input('intake_year') : null;
            $studentCourse->start_date = ($request->input('start_date') != '') ? date('Y-m-d', strtotime($request->input('start_date'))) : null;

            $studentCourse->is_finish_dt = ($request->input('is_finish_dt') != '') ? $request->input('is_finish_dt') : '0';

            $studentCourse->course_fee = ($request->input('course_fee') != '') ? $request->input('course_fee') : null;
            $studentCourse->enroll_fee = ($request->input('enroll_fee') != '') ? $request->input('enroll_fee') : null;
            $studentCourse->course_upfront_fee = ($request->input('course_upfront_fee') != '') ? $request->input('course_upfront_fee') : null;
            $studentCourse->course_material_fee = ($request->input('course_material_fee') != '') ? $request->input('course_material_fee') : null;
            $studentCourse->status = ($request->input('status') != '') ? $request->input('status') : '';
            $studentCourse->study_reason_id = ($request->input('study_reason_id') != '') ? $request->input('study_reason_id') : null;

            $studentCourse->special_instructions = ($request->input('special_instructions') != '') ? $request->input('special_instructions') : null;

            $studentCourse->census_date1 = ($request->input('census_date1') != '') ? date('Y-m-d', strtotime($request->input('census_date1'))) : null;
            $studentCourse->census_date2 = ($request->input('census_date2') != '') ? date('Y-m-d', strtotime($request->input('census_date2'))) : null;
            $studentCourse->census_date3 = ($request->input('census_date3') != '') ? date('Y-m-d', strtotime($request->input('census_date3'))) : null;

            //            $studentCourse->mode_of_delivery = ($request->input('mode_of_delivery') != '') ? $request->input('mode_of_delivery') : null;
            $studentCourse->internal = ($request->input('internal') != '') ? 'Y' : 'N';
            $studentCourse->external = ($request->input('external') != '') ? 'Y' : 'N';
            $studentCourse->workplace_based_delivery = ($request->input('workplace_based_delivery') != '') ? 'Y' : 'N';

            $total_weeks = ($request->input('total_weeks') != '') ? $request->input('total_weeks') : null;

            $date1 = strtotime($request->input('start_date'));
            $date2 = strtotime('+'.$total_weeks.' week', $date1);
            $finish_date = date('Y-m-d H:i:s', $date2);
            $studentCourse->total_weeks = $total_weeks;
            $studentCourse->finish_date = $finish_date;

            $studentCourse->updated_by = Auth::user()->id;

            $studentCourse->save();

            if ($request->input('status') != '' && $oldStatus != $request->input('status')) {

                /* Start Insert Status History Record on "rto_student_status_change_history" Table */
                $studentID = $studentCourse->student_id;
                $newStatus = $request->input('status');

                $objStatusHistory = new StudentCourseStatusHistory;
                $objStatusHistory->saveStatusHistory($studentID, $courseID, $newStatus, $oldStatus);
                /* End Insert Status History */
            }
        }
    }

    public function updateStudentCourseData_old($request)
    {
        //         print_r($request);
        //            exit;
        if ($request['editid'] != '') {

            $studentCourse = StudentCourse::find($request['editid']);
            $studentCourse->campus_id = ($request['campus_id'] != '') ? $request['campus_id'] : null;
            $studentCourse->course_type_id = ($request['course_type_id'] != '') ? $request['course_type_id'] : null;
            $studentCourse->course_id = ($request['course_id'] != '') ? $request['course_id'] : null;
            //            //$studentCourse->res_cal_method = ($request->input('res_cal_method') != '') ? $request->input('res_cal_method') : null;
            //            $studentCourse->palacement_manager_id = ($request->input('palacement_manager_id') != '') ? $request->input('palacement_manager_id') : null;
            $studentCourse->palacement_manager_id = ($request['palacement_manager_id'] != '') ? $request['palacement_manager_id'] : null;
            $studentCourse->course_manager_id = ($request['course_manager_id'] != '') ? $request['course_manager_id'] : null;
            $studentCourse->agent_id = ($request['agentid'] != '') ? $request['agentid'] : null;
            $studentCourse->intake_year = ($request['intake_year'] != '') ? $request['intake_year'] : null;
            $studentCourse->intake_date = ($request['preferr_start_date'] != '') ? date('Y-m-d', strtotime($request['preferr_start_date'])) : null;
            //            $studentCourse->issued_date = ($request->input('issued_date') != '') ? date("Y-m-d", strtotime($request->input('issued_date'))) : null;
            $studentCourse->start_date = ($request['preferr_start_date'] != '') ? date('Y-m-d', strtotime($request['preferr_start_date'])) : null;
            //            $studentCourse->finish_date = ($request->input('finish_date') != '') ? date('Y-m-d', strtotime($request->input('finish_date'))) : null;
            //
            //            $studentCourse->coe_name = ($request->input('coe_no') != '') ? $request->input('coe_no') : null;
            //            $studentCourse->course_template = ($request->input('course_template') != '') ? $request->input('course_template') : '';
            //            $studentCourse->is_finish_dt = ($request->input('is_finish_dt') != '') ? $request->input('is_finish_dt') : '0';
            //
            $studentCourse->course_fee = ($request['course_fee'] != '') ? $request['course_fee'] : null;
            $studentCourse->enroll_fee = ($request['enroll_fee'] != '') ? $request['enroll_fee'] : null;
            $studentCourse->course_upfront_fee = ($request['course_upfront_fee'] != '') ? $request['course_upfront_fee'] : null;
            $studentCourse->course_material_fee = ($request['course_material_fee'] != '') ? $request['course_material_fee'] : null;
            //            $studentCourse->status = ($request->input('status') != '') ? $request->input('status') : '';
            $studentCourse->study_reason_id = ($request['study_reason_id'] != '') ? $request['study_reason_id'] : null;
            //
            //            $studentCourse->application_request = ($request->input('application_req') != '') ? $request->input('application_req') : null;
            //            //$studentCourse->advance_standing_credit = ($request->input('credit_transfer') != '') ? $request->input('credit_transfer') : null;
            //            $studentCourse->credit_transfer_request = ($request->input('credit_transfer') != '') ? $request->input('credit_transfer') : null;
            $studentCourse->special_instructions = ($request['special_instructions'] != '') ? $request['special_instructions'] : null;
            //
            //            $studentCourse->employer_id = ($request->input('employer') != '') ? $request->input('employer') : null;
            //            $studentCourse->contract_schedule_id = ($request->input('contract_schedule') != '') ? $request->input('contract_schedule') : null;
            //            $studentCourse->purchasing_contract_ode = ($request->input('purchasing_contract_ode') != '') ? $request->input('purchasing_contract_ode') : null;
            //            $studentCourse->schedule_code = ($request->input('schedule_code') != '') ? $request->input('schedule_code') : null;
            //            $studentCourse->auto_update_to_sru = ($request->input('auto_update_to_sru') != '') ? $request->input('auto_update_to_sru') : null;
            //
            //            $studentCourse->census_date1 = ($request->input('census_date1') != '') ? date("Y-m-d", strtotime($request->input('census_date1'))) : null;
            //            $studentCourse->census_date2 = ($request->input('census_date2') != '') ? date("Y-m-d", strtotime($request->input('census_date2'))) : null;
            //            $studentCourse->census_date3 = ($request->input('census_date3') != '') ? date("Y-m-d", strtotime($request->input('census_date3'))) : null;
            //
            //            $studentCourse->is_orientation = ($request->input('is_orientation') != '') ? $request->input('is_orientation') : '0';
            //            $studentCourse->default_unit_fee = ($request->input('default_unit_fee') != '') ? $request->input('default_unit_fee') : null;
            // //            $studentCourse->mode_of_delivery = ($request->input('mode_of_delivery') != '') ? $request->input('mode_of_delivery') : null;
            //
            $studentCourse->internal = (isset($request['internal'])) ? 'Y' : 'N';
            $studentCourse->external = (isset($request['external'])) ? 'Y' : 'N';
            $studentCourse->workplace_based_delivery = (isset($request['workplace_based_delivery'])) ? 'Y' : 'N';
            //
            //            $studentCourse->is_claim = ($request->input('is_claim') != '') ? $request->input('is_claim') : '0';
            $studentCourse->is_qualification = isset($request['is_qualification']) ? $request['is_qualification'] : '0';
            //            $studentCourse->is_certificate = ($request->input('is_certificate') != '') ? $request->input('is_certificate') : '0';
            //            if ($request->input('is_claim') == 1) {
            //                $studentCourse->survey_contact_status = $request->input('survey_contact_status');
            //            } else {
            //                $studentCourse->survey_contact_status = null;
            //            }
            $total_weeks = ($request['noOfWeektext'] != '') ? $request['noOfWeektext'] : null;
            //
            $date1 = strtotime($request['preferr_start_date']);
            $date2 = date('Y-m-d', strtotime('+'.$total_weeks.' week', $date1));
            $finish_date = date('Y-m-d', strtotime($date2.' -1 day'));
            $studentCourse->total_weeks = $total_weeks;
            $studentCourse->finish_date = $finish_date;
            //
            $studentCourse->updated_by = Auth::user()->id;
            //
            $studentCourse->save();
            //
            //            if ($request->input('status') != "" && $oldStatus != $request->input('status')) {
            //
            //                /* Start Insert Status History Record on "rto_student_status_change_history" Table */
            //                $studentID = $studentCourse->student_id;
            //                $newStatus = $request->input('status');
            //
            //                $objStatusHistory = new StudentCourseStatusHistory();
            //                $objStatusHistory->saveStatusHistory($studentID, $courseID, $newStatus, $oldStatus);
            //                /* End Insert Status History */
            //            }
        }
    }

    public function updateStudentCourseData($request)
    {
        //         print_r($request);
        //            exit;
        if ($request['editid'] != '') {

            $studentCourseDetails = StudentCourse::find($request['editid'])->toArray();
            //                   print_r($studentCourseDetails);exit;

            $studentCourse = StudentCourse::find($request['editid']);
            $studentCourse->campus_id = ($request['campus_id'] != '') ? $request['campus_id'] : null;
            $studentCourse->course_type_id = ($request['course_type_id'] != '') ? $request['course_type_id'] : null;
            $studentCourse->course_id = ($request['course_id'] != '') ? $request['course_id'] : null;
            //            //$studentCourse->res_cal_method = ($request->input('res_cal_method') != '') ? $request->input('res_cal_method') : null;
            //            $studentCourse->palacement_manager_id = ($request->input('palacement_manager_id') != '') ? $request->input('palacement_manager_id') : null;
            $studentCourse->palacement_manager_id = (isset($request['palacement_manager_id']) && $request['palacement_manager_id'] != '') ? $request['palacement_manager_id'] : null;
            $studentCourse->course_manager_id = (isset($request['course_manager_id']) && $request['course_manager_id'] != '') ? $request['course_manager_id'] : null;
            $studentCourse->agent_id = ($request['agentid'] != '') ? $request['agentid'] : null;
            $studentCourse->intake_year = ($request['intake_year'] != '') ? $request['intake_year'] : null;
            $studentCourse->intake_date = ($request['preferr_start_date'] != '') ? date('Y-m-d', strtotime($request['preferr_start_date'])) : null;
            if (isset($request['issued_date'])) {
                $studentCourse->issued_date = ($request['issued_date'] != '') ? date('Y-m-d', strtotime($request['issued_date'])) : null;
            }
            $studentCourse->start_date = ($request['preferr_start_date'] != '') ? date('Y-m-d', strtotime($request['preferr_start_date'])) : null;
            //            $studentCourse->finish_date = ($request->input('finish_date') != '') ? date('Y-m-d', strtotime($request->input('finish_date'))) : null;
            //
            //            $studentCourse->coe_name = ($request->input('coe_no') != '') ? $request->input('coe_no') : null;
            //            $studentCourse->course_template = ($request->input('course_template') != '') ? $request->input('course_template') : '';
            //            $studentCourse->is_finish_dt = ($request->input('is_finish_dt') != '') ? $request->input('is_finish_dt') : '0';
            //
            $studentCourse->course_fee = ($request['course_fee'] != '') ? $request['course_fee'] : null;
            $studentCourse->enroll_fee = ($request['enroll_fee'] != '') ? $request['enroll_fee'] : null;
            $studentCourse->course_upfront_fee = ($request['course_upfront_fee'] != '') ? $request['course_upfront_fee'] : null;
            $studentCourse->course_material_fee = ($request['course_material_fee'] != '') ? $request['course_material_fee'] : null;
            //            $studentCourse->status = ($request->input('status') != '') ? $request->input('status') : '';
            $studentCourse->study_reason_id = ($request['study_reason_id'] != '') ? $request['study_reason_id'] : null;
            //
            //            $studentCourse->application_request = ($request->input('application_req') != '') ? $request->input('application_req') : null;
            //            //$studentCourse->advance_standing_credit = ($request->input('credit_transfer') != '') ? $request->input('credit_transfer') : null;
            //            $studentCourse->credit_transfer_request = ($request->input('credit_transfer') != '') ? $request->input('credit_transfer') : null;
            $studentCourse->special_instructions = ($request['special_instructions'] != '') ? $request['special_instructions'] : null;
            //
            //            $studentCourse->employer_id = ($request->input('employer') != '') ? $request->input('employer') : null;
            //            $studentCourse->contract_schedule_id = ($request->input('contract_schedule') != '') ? $request->input('contract_schedule') : null;
            //            $studentCourse->purchasing_contract_ode = ($request->input('purchasing_contract_ode') != '') ? $request->input('purchasing_contract_ode') : null;
            //            $studentCourse->schedule_code = ($request->input('schedule_code') != '') ? $request->input('schedule_code') : null;
            //            $studentCourse->auto_update_to_sru = ($request->input('auto_update_to_sru') != '') ? $request->input('auto_update_to_sru') : null;
            //
            //            $studentCourse->census_date1 = ($request->input('census_date1') != '') ? date("Y-m-d", strtotime($request->input('census_date1'))) : null;
            //            $studentCourse->census_date2 = ($request->input('census_date2') != '') ? date("Y-m-d", strtotime($request->input('census_date2'))) : null;
            //            $studentCourse->census_date3 = ($request->input('census_date3') != '') ? date("Y-m-d", strtotime($request->input('census_date3'))) : null;
            //
            //            $studentCourse->is_orientation = ($request->input('is_orientation') != '') ? $request->input('is_orientation') : '0';
            //            $studentCourse->default_unit_fee = ($request->input('default_unit_fee') != '') ? $request->input('default_unit_fee') : null;
            // //            $studentCourse->mode_of_delivery = ($request->input('mode_of_delivery') != '') ? $request->input('mode_of_delivery') : null;
            //
            $studentCourse->internal = (isset($request['internal'])) ? 'Y' : 'N';
            $studentCourse->external = (isset($request['external'])) ? 'Y' : 'N';
            $studentCourse->workplace_based_delivery = (isset($request['workplace_based_delivery'])) ? 'Y' : 'N';
            //
            //            $studentCourse->is_claim = ($request->input('is_claim') != '') ? $request->input('is_claim') : '0';
            $studentCourse->is_qualification = isset($request['is_qualification']) ? $request['is_qualification'] : '0';
            //            $studentCourse->is_certificate = ($request->input('is_certificate') != '') ? $request->input('is_certificate') : '0';
            //            if ($request->input('is_claim') == 1) {
            //                $studentCourse->survey_contact_status = $request->input('survey_contact_status');
            //            } else {
            //                $studentCourse->survey_contact_status = null;
            //            }
            $total_weeks = ($request['noOfWeektext'] != '') ? $request['noOfWeektext'] : null;
            //
            $date1 = strtotime($request['preferr_start_date']);
            $date2 = date('Y-m-d', strtotime('+'.$total_weeks.' week', $date1));
            $finish_date = date('Y-m-d', strtotime($date2.' -1 day'));
            $studentCourse->total_weeks = $total_weeks;
            $studentCourse->finish_date = $finish_date;
            //
            $studentCourse->updated_by = $request['userId'];
            if ($studentCourse->save()) {
                // get upfront fee schedule for student
                $objRtoStudentCourses = new StudentCourse;
                $objStudentUpfrontFeeSchedule = $objRtoStudentCourses->getStudentUpfrontFeeSchedule($studentCourseDetails['student_id'], $studentCourseDetails['course_id']);

                $objUpfrontFeeSchedule = new OfferUpfrontFeeSchedule;
                $upFruntFeeScheduleDetails = $objUpfrontFeeSchedule->getUpfrontFeeSchedule($studentCourseDetails['student_id'], $studentCourseDetails['course_id']);
                //                    print_r($studentCourseDetails);exit;

                if ($studentCourseDetails['course_id'] == $request['course_id']) {
                    if (count($upFruntFeeScheduleDetails) != 0) {
                        $remainFee = $objStudentUpfrontFeeSchedule[0]->course_fee - $objStudentUpfrontFeeSchedule[0]->course_upfront_fee;

                        // dividation
                        $noOfInstallment = $upFruntFeeScheduleDetails[0]['no_of_installment'];
                        $installmentFee = $remainFee / $noOfInstallment;

                        $arrScheduleData = [
                            'remain_fee' => $remainFee,
                            'installment_fee' => $installmentFee,
                            'userId' => $request['userId'],
                        ];

                        // insert
                        $objOfferUpfrontFeeSchedule = $objUpfrontFeeSchedule->updateUpfrontFeeSchedule($arrScheduleData, $upFruntFeeScheduleDetails);
                    }
                } else {

                    $arrScheduleData = [
                        'student_id' => $studentCourseDetails['student_id'],
                        'course_id' => $studentCourseDetails['course_id'],
                    ];
                    // delete same records before insert
                    //                       print_r($arrScheduleData);exit;
                    $deleteSameUpfrontFeeSchedule = $objUpfrontFeeSchedule->deleteSameUpfrontFeeSchedule($arrScheduleData);
                }
            }
            //
            //            if ($request->input('status') != "" && $oldStatus != $request->input('status')) {
            //
            //                /* Start Insert Status History Record on "rto_student_status_change_history" Table */
            //                $studentID = $studentCourse->student_id;
            //                $newStatus = $request->input('status');
            //
            //                $objStatusHistory = new StudentCourseStatusHistory();
            //                $objStatusHistory->saveStatusHistory($studentID, $courseID, $newStatus, $oldStatus);
            //                /* End Insert Status History */
            //            }
        }
    }

    public function insertStudentCourse($studentID, $request)
    {

        if ($request->input('agent_id') != '' && $request->input('campus_id') != '' && $request->input('course_id') != '') {

            $studentCourse = new StudentCourse;
            $studentCourse->student_id = $studentID;
            $studentOfferID = $studentCourse->addStudentOffer($studentID, $request);

            $studentCourse->offer_id = ($studentOfferID != '') ? $studentOfferID : null;
            $studentCourse->course_id = ($request->input('course_id') != '') ? $request->input('course_id') : null;
            $studentCourse->res_cal_method = ($request->input('res_cal_method') != '') ? $request->input('res_cal_method') : null;
            $studentCourse->course_type_id = ($request->input('course_type_id') != '') ? $request->input('course_type_id') : null;
            $studentCourse->palacement_manager_id = ($request->input('palacement_manager_id') != '') ? $request->input('palacement_manager_id') : null;
            $studentCourse->course_manager_id = ($request->input('course_manager_id') != '') ? $request->input('course_manager_id') : null;
            $studentCourse->campus_id = ($request->input('campus_id') != '') ? $request->input('campus_id') : null;
            $studentCourse->agent_id = ($request->input('agent_id') != '') ? $request->input('agent_id') : null;

            $studentCourse->intake_year = ($request->input('intake_year') != '') ? $request->input('intake_year') : null;
            $studentCourse->intake_date = ($request->input('intake_date') != '') ? date('Y-m-d', strtotime($request->input('intake_date'))) : null;
            $studentCourse->issued_date = ($request->input('issued_date') != '') ? date('Y-m-d', strtotime($request->input('issued_date'))) : null;
            $studentCourse->start_date = ($request->input('start_date') != '') ? date('Y-m-d', strtotime($request->input('start_date'))) : null;
            $studentCourse->finish_date = ($request->input('finish_date') != '') ? date('Y-m-d', strtotime($request->input('finish_date'))) : null;
            $studentCourse->is_material_fee_inc_initail_payment = ($request->input('is_material_fee_inc_initail_payment') != '') ? $request->input('is_material_fee_inc_initail_payment') : '0';
            $studentCourse->advance_standing_credit = ($request->input('advance_standing_credit') != '') ? $request->input('advance_standing_credit') : '0';

            $start = strtotime($request->input('start_date'));
            $end = strtotime($request->input('finish_date'));
            $total_weeks = round(abs($end - $start) / 604800);

            $studentCourse->total_weeks = ($total_weeks > 0) ? $total_weeks : '0';
            $studentCourse->coe_name = ($request->input('coe_no') != '') ? $request->input('coe_no') : '';
            $studentCourse->course_template = ($request->input('course_template') != '') ? $request->input('course_template') : '';
            $studentCourse->is_finish_dt = ($request->input('is_finish_dt') != '') ? $request->input('is_finish_dt') : '0';

            $studentCourse->course_fee = ($request->input('course_fee') != '') ? $request->input('course_fee') : null;
            $studentCourse->enroll_fee = ($request->input('enroll_fee') != '') ? $request->input('enroll_fee') : null;
            $studentCourse->course_upfront_fee = ($request->input('course_upfront_fee') != '') ? $request->input('course_upfront_fee') : null;
            $studentCourse->course_material_fee = ($request->input('course_material_fee') != '') ? $request->input('course_material_fee') : null;
            $studentCourse->status = ($request->input('status') != '') ? $request->input('status') : '';
            $studentCourse->offer_status = 'Enrolled';
            $studentCourse->study_reason_id = ($request->input('study_reason_id') != '') ? $request->input('study_reason_id') : null;

            $studentCourse->application_request = ($request->input('application_req') != '') ? $request->input('application_req') : '';
            // $studentCourse->advance_standing_credit = ($request->input('credit_transfer') != '') ? $request->input('credit_transfer') : null;
            $studentCourse->credit_transfer_request = ($request->input('credit_transfer') != '') ? $request->input('credit_transfer') : null;
            $studentCourse->special_instructions = ($request->input('special_instructions') != '') ? $request->input('special_instructions') : '';

            $studentCourse->census_date1 = ($request->input('census_date1') != '') ? date('Y-m-d', strtotime($request->input('census_date1'))) : null;
            $studentCourse->census_date2 = ($request->input('census_date2') != '') ? date('Y-m-d', strtotime($request->input('census_date2'))) : null;
            $studentCourse->census_date3 = ($request->input('census_date3') != '') ? date('Y-m-d', strtotime($request->input('census_date3'))) : null;

            $studentCourse->is_orientation = ($request->input('is_orientation') != '') ? $request->input('is_orientation') : '0';
            $studentCourse->default_unit_fee = ($request->input('default_unit_fee') != '') ? $request->input('default_unit_fee') : null;
            //            $studentCourse->mode_of_delivery = ($request->input('mode_of_delivery') != '') ? $request->input('mode_of_delivery') : null;

            $studentCourse->internal = ($request->input('internal') != '') ? 'Y' : 'N';
            $studentCourse->external = ($request->input('external') != '') ? 'Y' : 'N';
            $studentCourse->workplace_based_delivery = ($request->input('workplace_based_delivery') != '') ? 'Y' : 'N';

            $studentCourse->is_claim = ($request->input('is_claim') != '') ? $request->input('is_claim') : '0';
            $studentCourse->is_qualification = ($request->input('is_qualification') != '') ? $request->input('is_qualification') : '0';
            $studentCourse->is_certificate = ($request->input('is_certificate') != '') ? $request->input('is_certificate') : '0';
            if ($request->input('is_claim') == 1) {
                $studentCourse->survey_contact_status = $request->input('survey_contact_status');
            } else {
                $studentCourse->survey_contact_status = null;
            }
            $studentCourse->created_by = Auth::user()->id;
            $studentCourse->updated_by = Auth::user()->id;
            $studentCourse->save();

            if ($request->input('status') != '') {

                /* Start Insert Status History Record on "rto_student_status_change_history" Table */
                $courseID = $studentCourse->id;
                $newStatus = $request->input('status');

                $objStatusHistory = new StudentCourseStatusHistory;
                $objStatusHistory->saveStatusHistory($studentID, $courseID, $newStatus);
                /* End Insert Status History */
            }
        }
    }

    public function deleteStudentCourseRecord($courseID, $studentId)
    {

        $count = StudentCourse::where('student_id', $studentId)->get()->count();
        if ($count > 0) {
            return StudentCourse::where('course_id', $courseID)->where('student_id', $studentId)->delete();
        }

        return false;
    }

    public function deleteStudentCourseRecordWithId($studentCourseId, $studentId, $courseId)
    {

        $count = StudentCourse::where('student_id', $studentId)->get()->count();
        if ($count > 1) {
            return StudentCourse::where('id', $studentCourseId)->where('course_id', $courseId)->where('student_id', $studentId)->delete();
        }

        return false;
    }

    public function _getAccountPayment($course_id, $student_id)
    {

        return StudentCourse::join('rto_agents as rc', 'rc.id', '=', 'rto_student_courses.agent_id')
            ->where('rto_student_courses.course_id', '=', $course_id)
            ->where('rto_student_courses.student_id', '=', $student_id)
            //   ->get(['rc.agency_name','rc.id as agent_id','rto_student_courses.status','rto_student_courses.start_date','rto_student_courses.finish_date','rto_student_courses.course_fee','rto_student_courses.total_weeks']);
            ->get(['rc.agency_name', 'rc.id as agent_id', 'rto_student_courses.*']);
    }

    public function _getAccountPaymentV2($student_course_id, $student_id)
    {

        return StudentCourse::join('rto_agents as rc', 'rc.id', '=', 'rto_student_courses.agent_id')
            ->where('rto_student_courses.id', '=', $student_course_id)
            // ->where('rto_student_courses.student_id', '=', $student_id)
            //   ->get(['rc.agency_name','rc.id as agent_id','rto_student_courses.status','rto_student_courses.start_date','rto_student_courses.finish_date','rto_student_courses.course_fee','rto_student_courses.total_weeks']);
            ->get(['rc.agency_name', 'rc.id as agent_id', 'rto_student_courses.*']);
    }

    // get start date from student_subject_enrolled
    public function getStartDate($courseId, $studentId)
    {
        return StudentCourse::where('student_id', '=', $studentId)->where('course_id', '=', $courseId)->get(['start_date', 'campus_id']);
    }

    public function getCourseDateRange($courseId, $studentId)
    {
        return StudentCourse::where('course_id', '=', $courseId)
            // ->where('student_id', '=', $studentId)
            ->select('start_date', 'finish_date')
            ->get();
    }

    public function getStudentCoeHistory($courseId, $studentId)
    {

        $dataArr = StudentCourse::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->join('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->join('rto_users', 'rto_users.id', '=', 'rto_students.updated_by')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.id', '=', $courseId)
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->select('rto_courses.course_code', 'rto_courses.course_name', 'rto_student_courses.coe_name', 'rto_student_courses.start_date', 'rto_student_courses.finish_date', 'rto_student_courses.updated_at', 'rto_users.name as updated_by')
            ->get();

        return $dataArr;
        // print_r($dataArr);exit;
    }

    public function getStudentPdfCourseData($courseId, $studentId)
    {
        $dataArr = StudentCourse::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'rto_students.updated_by')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.course_id', '=', $courseId)
            ->select('rto_courses.course_code', 'rto_courses.course_name', 'rto_student_courses.coe_name', 'rto_student_courses.start_date', 'rto_student_courses.finish_date', 'rto_student_courses.updated_at', 'rto_users.name as updated_by')
            ->get();

        return $dataArr;
        // print_r($dataArr);exit;
    }

    public function getStudentPdfCourseDataV2($courseId, $studentId, $student_course_id)
    {
        $dataArr = StudentCourse::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'rto_students.updated_by')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.course_id', '=', $courseId)
            ->where('rto_student_courses.id', '=', $student_course_id)
            ->select('rto_courses.course_code', 'rto_courses.course_name', 'rto_student_courses.coe_name', 'rto_student_courses.start_date', 'rto_student_courses.finish_date', 'rto_student_courses.updated_at', 'rto_users.name as updated_by')
            ->get();

        return $dataArr;
        // print_r($dataArr);exit;
    }

    public function getStatementPdfCourseData($courseId, $studentId, $student_course_id)
    {
        $dataArr = StudentCourse::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'rto_students.updated_by')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.course_id', '=', $courseId)
            ->where('rto_student_courses.id', '=', $student_course_id)
            ->select(
                'rto_courses.course_code',
                'rto_courses.course_name',
                'rto_student_courses.coe_name',
                'rto_student_courses.start_date',
                'rto_student_courses.finish_date',
                'rto_student_courses.updated_at',
                'rto_users.name as updated_by',
                'rto_students.first_name as student_first_name',
                'rto_students.middel_name as student_middle_name',
                'rto_students.generated_stud_id as generated_stud_id',
                'rto_campus.name as campus_name'
            )
            ->get();

        return $dataArr;
        // print_r($dataArr);exit;
    }

    // get course info from student-certificate-register
    public function _getCourseInfo($courseId, $studentId)
    {
        return StudentCourse::where('student_id', '=', $studentId)
            ->where('course_id', '=', $courseId)
            ->get(['status', 'start_date', 'finish_date', 'course_type_id']);
    }

    public function _getGenerateInvoiceDataStudent($courseId, $student_status, $pagenumb)
    {
        // echo
        $RecordsperPage = 2;

        return StudentCourse::select('student_id', 'status', 'id')
            ->where('offer_status', ($student_status != '') ? '=' : '!=', $student_status)
            ->where('course_id', ($courseId != '') ? '=' : '!=', $courseId)
            ->offset(($pagenumb - 1) * $RecordsperPage)
            ->limit($RecordsperPage)
            ->get()
            ->toArray();
    }

    public function addStudentOffer($studentID, $request)
    {

        if ($request->input('offer_id') == 'new_offer') {
            $objStudentOffer = new StudentOffers;
            $studentOfferID = $objStudentOffer->saveStudentOffers($studentID, $request);

            $objStudentService = new StudentServiceInformation;
            $objStudentService->defaultStudentService($studentID, $studentOfferID);
        } else {
            $studentOfferID = $request->input('offer_id');
        }
        $studentDetails = new StudentDetails;
        $studentDetails->saveStudentDetailsStep3($studentID, $studentOfferID);

        return $studentOfferID;
    }

    public function getStudentCourseIntakeYear($courseId)
    {
        return StudentCourse::where('course_id', '=', $courseId)
            ->groupBy('intake_year')
            ->get(['intake_year'])
            ->toArray();
    }

    public function _getStatus($selectedDate, $searchBy)
    {
        $selectedDate = date('Y-m-d', strtotime($selectedDate));
        if ($searchBy == 'startDate') {
            $sql = StudentCourse::where('start_date', '=', $selectedDate);
        }
        if ($searchBy == 'intakDate') {
            $sql = StudentCourse::where('intake_date', '=', $selectedDate);
        }

        return $result = $sql->groupBy('status')
            ->get(['status'])
            ->toArray();
    }

    public function getCourseDate($yearData, $courseId, $searchBy)
    {

        $sql = StudentCourse::where('course_id', '=', $courseId)
            ->where('intake_year', '=', $yearData);
        if ($searchBy == 'startDate') {
            $result = $sql->groupBy('start_date')
                ->get([DB::raw("DATE_FORMAT(start_date, '%d-%m-%Y') AS start_date")])
                //                    ->get(['start_date'])
                ->toArray();
        }
        if ($searchBy == 'intakDate') {
            $result = $sql->groupBy('intake_date')
                ->get([DB::raw("DATE_FORMAT(intake_date, '%d-%m-%Y') AS intake_date")])
                //                    ->get(['intake_date'])
                ->toArray();
        }

        return $result;
    }

    public function getStudentOreintationData($campusId, $courseType, $courseId, $intakeYear, $selectDate, $status, $searchBy)
    {

        $sql = StudentCourse::from('rto_student_courses as rsc')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rsc.student_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rsc.course_id')
            ->where('rsc.campus_id', '=', $campusId)
            ->where('rsc.course_type_id', '=', $courseType)
            ->where('rsc.course_id', '=', $courseId)
            ->where('rsc.intake_year', '=', $intakeYear)
            ->where('rsc.offer_status', '=', 'Enrolled');

        if ($status != '') {
            $sql->where('rsc.status', '=', $status);
        }

        if ($searchBy == 'startDate') {
            $date = date('Y-m-d', strtotime($selectDate));
            $sql->where('rsc.start_date', '=', $date);
        }

        if ($searchBy == 'intakDate') {
            $date = date('Y-m-d', strtotime($selectDate));
            $sql->where('rsc.intake_date', '=', $date);
        }

        $result = $sql->get([
            'rsc.*',
            'rto_students.first_name',
            'rto_students.name_title',
            'rto_students.middel_name',
            'rto_students.family_name',
            'rto_students.email',
            'rto_students.generated_stud_id',
            'rto_students.current_mobile_phone',
            'rto_courses.course_code',
            'rto_courses.course_name',
            'rsc.created_at as courseCreatedDate',
            'rto_agents.agency_name',
            'rto_agents.primary_email as agent_email',
        ]);

        for ($i = 0; $i < count($result); $i++) {
            $result[$i]['courseCreatedDate'] = date('d-m-Y', strtotime($result[$i]['courseCreatedDate']));
            $result[$i]['start_date'] = date('d/m/Y', strtotime($result[$i]['start_date']));
            $result[$i]['finish_date'] = date('d/m/Y', strtotime($result[$i]['finish_date']));
        }

        // print_r($result);exit;
        return $result;
    }

    public function getOrentationCourseName($courseTypeId, $campus)
    {
        return StudentCourse::join('rto_courses as rc', 'rc.id', '=', 'rto_student_courses.course_id')
            ->where('rto_student_courses.course_type_id', '=', $courseTypeId)
            ->where('rto_student_courses.campus_id', '=', $campus)
            ->groupBy('rc.id')
            ->get(['rc.course_code', 'rc.course_name', 'rc.id']);
    }

    public function _checkCourseExist($courseId)
    {
        return StudentCourse::where('course_id', '=', $courseId)->get();
    }

    public function _getstudentGroups($collegeId, $studentId)
    {

        $dataArr = StudentCourse::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->where('rto_students.generated_stud_id', '=', $studentId)
            ->where('rto_students.college_id', '=', $collegeId)
            ->select(
                'rto_student_courses.id as studentcourseid',
                'rto_student_courses.group_id',
                'rto_student_courses.course_attempt',
                'rto_student_courses.course_id',
                'rto_courses.course_code',
                'rto_courses.course_name',
                'rto_student_courses.updated_at',
                'rto_students.generated_stud_id as generated_stud_id'
            )
            ->get();

        foreach ($dataArr as $k => $dt) {

            $groupdata = GroupIntake::where('course_id', '=', $dt->course_id)->get();
            $dataArr[$k]->group = $groupdata;
        }

        return $dataArr;
    }

    public function assignstudentgroup($groupval, $studentcourseid)
    {

        $studentCourse = StudentCourse::find($studentcourseid);
        $studentCourse->group_id = $groupval;
        $studentCourse->updated_by = Auth::user()->id;
        $studentCourse->save();

        if ($groupval != '0') {
            return ['status' => 'true', 'action' => 'success', 'msg' => 'Group has been assigned successffully.'];
        } else {
            return ['status' => 'false', 'action' => 'error', 'msg' => 'No group assign.'];
        }

    }

    public function getassignstudentlist($collegeId, $group_id, $year, $course, $startdate, $todate, $status, $assigned, $campusId, $sortBy)
    {

        $results = DB::table('rto_student_courses')
            ->join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->join('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id');
        $results->where('rto_courses.college_id', '=', $collegeId);
        $results->where('rto_student_courses.offer_status', '=', 'Enrolled');
        if ($status != '') {
            $results->where('rto_student_courses.status', '=', $status);
        }
        if ($campusId != '') {
            $results->where('rto_campus.id', '=', $campusId);
        }
        if ($course != '') {
            $results->where('rto_student_courses.course_id', '=', $course);
        }
        //        echo $sortBy;exit;

        if ($group_id != '' && $assigned == 1) {
            $results->where('rto_student_courses.group_id', '=', $group_id);
        }
        if ($assigned == 0) {
            $results->where('rto_student_courses.group_id', '=', 0);
        }
        if ($startdate != '' && $todate == '') {
            $results->where('rto_student_courses.start_date', '>=', date('Y-m-d', strtotime($startdate)));
        }
        if ($startdate != '' && $todate != '') {
            $results->where('rto_student_courses.start_date', '>=', date('Y-m-d', strtotime($startdate)));
            $results->where('rto_student_courses.start_date', '<=', date('Y-m-d', strtotime($todate)));
        }
        if ($sortBy != '' && $sortBy == 'FName') {
            $results->orderBy('rto_students.first_name', 'DESC');
            $results->orderBy('rto_students.middel_name', 'DESC');
        }
        if ($sortBy != '' && $sortBy == 'StudentId') {
            $results->orderBy('rto_students.generated_stud_id', 'DESC');
        }
        // ->whereBetween('rto_student_courses.start_date', array("STR_TO_DATE(".$startdate.",'%Y-%m-%d')", "STR_TO_DATE(".$todate.",'%Y-%m-%d')"))
        $results->select(
            'rto_student_courses.status as studentstatus',
            'rto_student_courses.id as studentcourseid',
            'rto_student_courses.mode_of_delivery',
            'rto_student_courses.student_id',
            'rto_student_courses.course_id',
            'rto_courses.course_code',
            'rto_courses.course_name',
            'rto_student_courses.coe_name',
            'rto_student_courses.start_date',
            'rto_student_courses.finish_date',
            'rto_student_courses.updated_at',
            'rto_students.first_name as student_first_name',
            'rto_students.family_name as student_family_name',
            'rto_students.generated_stud_id as generated_stud_id',
            'rto_campus.name as campus_name'
        );
        $sqlResult = $results->get();

        return $sqlResult;
    }

    public function exportstudentData($collegeId, $request)
    {

        return $dataArr = StudentCourse::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'rto_students.updated_by')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->where('rto_student_courses.status', '=', $request->input('status'))
            ->where('rto_student_courses.course_id', '=', $request->input('course_type'))
            ->where('rto_student_courses.group_id', '=', $request->input('group_id'))
            ->select('rto_student_courses.id as studentcourseid', 'rto_students.first_name as student_first_name', 'rto_campus.name as campus_name', 'rto_student_courses.mode_of_delivery', 'rto_courses.course_code', 'rto_student_courses.start_date', 'rto_student_courses.finish_date')
            ->get()->toArray();
    }

    public function assignStudentToGroup($collegeId, $checkstudent, $group_id, $course)
    {

        for ($i = 0; $i < count($checkstudent); $i++) {

            $studentCourse = StudentCourse::find($checkstudent[$i]);
            $studentCourse->group_id = $group_id;
            $studentCourse->updated_by = Auth::user()->id;
            $studentCourse->save();
            // unset($studentCourse);
        }

        return ['status' => 'true', 'action' => 'success', 'msg' => 'Group has been assigned successffully.'];

    }

    public function removeAssignGroup($collegeId, $checkstudent, $group_id, $course)
    {
        for ($i = 0; $i < count($checkstudent); $i++) {

            $studentCourse = StudentCourse::find($checkstudent[$i]);
            $studentCourse->group_id = 0;
            $studentCourse->updated_by = Auth::user()->id;
            $studentCourse->save();

        }

        return ['status' => 'true', 'action' => 'success', 'msg' => 'Group has been unassigned successffully.'];

    }

    public function coeExpiredStudentList($collegeId, $courseTypeId, $courseId)
    {

        $sql = StudentCourse::from('rto_student_courses as rsc')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_course_template as rct', 'rct.id', '=', 'rsc.course_template')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rsc.status', '=', 'Current Student')
            ->where('rsc.offer_status', '=', 'Enrolled')
            ->where('rsc.finish_date', '<', date('Y-m-d'));

        if (! empty($courseTypeId) && $courseTypeId > 0) {
            $sql->where('rsc.course_type_id', '=', $courseTypeId);
        }
        if (! empty($courseId) && $courseId > 0) {
            $sql->where('rsc.course_id', '=', $courseId);
        }
        $result = $sql->get([
            'rs.generated_stud_id as generated_id',
            'rs.first_name',
            'rs.family_name',
            'rc.course_code',
            'rc.course_name',
            'rsc.id as course_id',
            'rsc.course_attempt',
            'rsc.start_date',
            'rsc.finish_date',
            'rsc.coe_name',
            'rct.template_name',
            'rsc.status',
        ]);

        foreach ($result as $row) {
            $row->start_date = date('d/m/Y', strtotime($row->start_date));
            $row->finish_date = date('d/m/Y', strtotime($row->finish_date));
            $row->coe_name = (empty($row->coe_name) || $row->coe_name == 'N/A') ? '-' : $row->coe_name;
            $row->template_name = empty($row->template_name) ? '-' : $row->template_name;
        }

        return $result;
    }

    public function getStudentCourseList($collegeId, $data)
    {

        $form_date = date('Y-m-d', strtotime($data['from_date']));
        $to_date = date('Y-m-d', strtotime($data['to_date']));
        $course_type_id = $data['courseType'];
        $course_id = $data['course'];
        $status = $data['status'];

        $sql = StudentCourse::from('rto_student_courses as rsc')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_course_template as rct', 'rct.id', '=', 'rsc.course_template')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rsc.course_type_id', '=', $course_type_id)
            ->where('rsc.course_id', '=', $course_id)
            ->where('rsc.offer_status', '=', 'Enrolled');

        if (! empty($status) && $status != '0') {
            $sql->where('rsc.status', '=', $status);
        }

        if (! empty($data['from_date']) && ! empty($data['to_date'])) {
            $sql->whereBetween('rsc.finish_date', [$form_date, $to_date]);
        } elseif (! empty($data['from_date'])) {
            $sql->where('rsc.finish_date', '>=', $form_date);
        } elseif (! empty($data['to_date'])) {
            $sql->where('rsc.finish_date', '<=', $to_date);
        }

        $result = $sql->get([
            'rs.generated_stud_id as generated_id',
            'rs.first_name',
            'rs.family_name',
            'rs.USI',
            'rc.course_code',
            'rc.course_name',
            'rsc.id as stud_course_id',
            'rsc.course_attempt',
            'rsc.start_date',
            'rsc.finish_date',
            'rsc.coe_name',
            'rsc.status',
            'rct.template_name',
            'rsc.course_type_id',
        ]);

        foreach ($result as $row) {
            $row->start_date = date('d/m/Y', strtotime($row->start_date));
            $row->finish_date_temp = $row->finish_date;
            $row->finish_date = date('d/m/Y', strtotime($row->finish_date));
            $row->coe_name = (empty($row->coe_name) || $row->coe_name == 'N/A') ? '-' : $row->coe_name;
        }

        return $result;
    }

    public function getStudentCourseCertificateList($collegeId, $data)
    {

        $course_type_id = $data['courseType'];
        $course_id = $data['course'];
        $status = $data['status'];

        $sql = StudentCourse::from('rto_student_courses as rsc')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_course_template as rct', 'rct.id', '=', 'rsc.course_template')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rsc.course_type_id', '=', $course_type_id)
            ->where('rsc.course_id', '=', $course_id)
            ->where('rsc.offer_status', '=', 'Enrolled');

        if (! empty($status) && $status != '0') {
            $sql->where('rsc.status', '=', $status);
        }

        $result = $sql->get([
            'rs.generated_stud_id as generated_id',
            'rs.first_name',
            'rs.family_name',
            'rs.USI',
            'rc.course_code',
            'rc.course_name',
            'rsc.id as stud_course_id',
            'rsc.course_attempt',
            'rsc.start_date',
            'rsc.finish_date',
            'rsc.coe_name',
            'rsc.status',
            'rct.template_name',
            'rsc.course_type_id',
        ]);

        foreach ($result as $row) {
            $row->start_date = date('d/m/Y', strtotime($row->start_date));
            $row->finish_date_temp = $row->finish_date;
            $row->finish_date = date('d/m/Y', strtotime($row->finish_date));
            $row->coe_name = (empty($row->coe_name) || $row->coe_name == 'N/A') ? '-' : $row->coe_name;
        }

        return $result;
    }

    public function updateStudentCourseInfo($studCourseIdArr, $fieldName, $fieldValue, $statusText)
    {

        if (count($studCourseIdArr) > 0) {
            foreach ($studCourseIdArr as $rowId) {
                StudentCourse::where('id', '=', $rowId)->update([$fieldName => $fieldValue]);
            }

            return ['status' => 'true', 'action' => 'alert-success', 'msg' => 'Update the selected Student Course "'.$statusText.'" Successfully. Please Click "View Student List" to View the new Student List.'];
        } else {
            return ['status' => 'null', 'action' => 'alert-danger', 'msg' => 'Please Select at least one Student Course Record.'];
        }

        return ['status' => 'false', 'action' => 'alert-danger', 'msg' => 'Error occured while Update '.$fieldName.'. Please try again.'];
    }

    public function getStudentCourseInfo($collegeId, $studCourseId)
    {
        return StudentCourse::from('rto_student_courses as rsc')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_colleges', 'rto_colleges.id', '=', 'rs.college_id')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rsc.id', '=', $studCourseId)
            ->get([
                'rs.generated_stud_id as generated_id',
                'rs.name_title',
                'rs.first_name',
                'rs.middel_name',
                'rs.family_name',
                'rs.DOB',
                'rc.course_code',
                'rc.course_name',
                'rc.cricos_code',
                'rc.national_code',
                'rsc.start_date',
                'rsc.finish_date',
                'rsc.status',
                'rto_colleges.college_name',
                'rs.id as studentId',
                'rc.id as courseId',
            ]);
    }

    public function _getCourseTypeDetails($courseId, $studentId)
    {
        return StudentCourse::where('student_id', '=', $studentId)
            ->where('course_id', '=', $courseId)
            ->get(['course_type_id'])
            ->toarray();
    }

    public function getCourseCampusAndDateRange($studentId, $courseId)
    {
        $result = [];
        $result = StudentCourse::from('rto_student_courses as rsc')
            ->leftjoin('rto_campus as rc', 'rc.id', '=', 'rsc.campus_id')
            ->where('rsc.course_id', '=', $courseId)
            ->where('rsc.student_id', '=', $studentId)
            ->select('rsc.start_date', 'rsc.finish_date', 'rc.name')
            ->get();
        if (count($result) > 0) {
            $result[0]->start_date = date('d-m-Y', strtotime($result[0]->start_date));
            $result[0]->finish_date = date('d-m-Y', strtotime($result[0]->finish_date));
            $result[0]->duration = date('d/m/Y', strtotime($result[0]->start_date)).' - '.date('d/m/Y', strtotime($result[0]->finish_date));

            return $result[0]->toArray();
        }

        return $result;
    }

    public function completeStudentCourseInfo($studCourseId)
    {
        $objStudentCourse = StudentCourse::find($studCourseId);
        $objStudentCourse->status = 'Completed';
        $objStudentCourse->updated_by = Auth::user()->id;
        $objStudentCourse->save();

        return true;
    }

    public function getStudentCourseListWithStatus($studentId, $current = false)
    {

        $arrStudentCourse = StudentCourse::from('rto_student_courses as rsc')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where('rsc.student_id', '=', $studentId);
        if ($current) {
            $arrStudentCourse->where('rsc.offer_status', 'Enrolled')->where('rsc.status', 'Current Student');
        }

        $arrStudentCourse = $arrStudentCourse->get([
            'rc.id',
            'rc.course_name',
            'rc.course_code',
            'rsc.offer_status',
            'rsc.id as enroll_id',
            'rsc.start_date',
            'rsc.finish_date',
        ]);

        $arrStudentCourseList = [];
        if ($arrStudentCourse->count() > 0) {
            foreach ($arrStudentCourse as $row) {
                $arrStudentCourseList[$row->enroll_id] = $row->course_code.' : '.$row->course_name.' - ('.date('d/m/Y', strtotime($row->start_date)).' - '.date('d/m/Y', strtotime($row->finish_date)).') ('.$row->offer_status.')';
            }
        } else {
            $arrStudentCourseList[''] = 'No Course Found';
        }

        return $arrStudentCourseList;
    }

    public function getStudyPlanList($collegeId, $studentId)
    {
        return StudentCourse::from('rto_student_courses as rsc')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where('rc.college_id', '=', $collegeId)
            ->where('rsc.student_id', '=', $studentId)
            ->get([
                'rsc.id',
                'rc.id as course_id',
                'rc.course_code',
                'rc.course_name',
                'rsc.course_attempt',
                'rsc.mode_of_delivery',
                'rsc.palacement_manager_id',
                'rsc.course_manager_id',
                'rsc.start_date',
                'rsc.finish_date',
                'rsc.status',
                'rsc.coe_name',
                'rsc.course_fee',
            ]);
    }

    public function getBulkEnrolledUsers($collegeId, $courseTypeId = '', $courseId = '', $status = '', $expired = '1', $userStatus = '1')
    {
        $sql = StudentCourse::from('rto_student_courses as rsc')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->join('rto_users as ru', 'ru.username', '=', 'rs.generated_stud_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where('rsc.offer_status', '=', 'Enrolled')
            ->where('rc.college_id', '=', $collegeId)
            ->where('ru.status', '=', $userStatus);

        if (! empty($courseTypeId)) {
            $sql->where('rsc.course_type_id', '=', $courseTypeId);
        }
        if (! empty($courseId)) {
            $sql->where('rsc.course_id', '=', $courseId);
        }
        if (! empty($status)) {
            $sql->where('rsc.status', '=', $status);
        }
        if ($expired == '1') {
            $sql->where('rsc.finish_date', '<', date('Y-m-d'));
        } else {
            $sql->where('rsc.finish_date', '>=', date('Y-m-d'));
        }

        $result = $sql->get([
            'rs.generated_stud_id as generated_id',
            'rs.first_name',
            'rs.family_name',
            'rs.USI',
            'rc.course_code',
            'rc.course_name',
            'rsc.id as stud_course_id',
            'rsc.course_attempt',
            'rsc.start_date',
            'rsc.finish_date',
            'rsc.coe_name',
            'rsc.status as student_course_status',
            'rs.generated_stud_id',
            'rsc.course_type_id',
            'ru.id',
            'ru.username',
            'ru.status',
        ])->toArray();

        return $result;
    }

    public function getEnrolledUsers($collegeId, $courseTypeId = '', $courseId = '', $status = '', $expired = '1', $userStatus = '1')
    {
        $sql = StudentCourse::from('rto_student_courses as rsc')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->join('rto_users as ru', 'ru.username', '=', 'rs.generated_stud_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where('rc.college_id', '=', $collegeId)
            ->where('ru.status', '=', $userStatus);

        if (! empty($courseTypeId)) {
            $sql->where('rsc.course_type_id', '=', $courseTypeId);
        }
        if (! empty($courseId)) {
            $sql->where('rsc.course_id', '=', $courseId);
        }
        if (! empty($status)) {
            $sql->where('rsc.status', '=', $status);
        }
        if ($expired == '1') {
            $sql->where('rsc.finish_date', '<', date('Y-m-d'));
        } else {
            $sql->where('rsc.finish_date', '>=', date('Y-m-d'));
        }

        $result = $sql->get([
            'rs.generated_stud_id as generated_id',
            'rs.first_name',
            'rs.family_name',
            'rs.USI',
            'rc.course_code',
            'rc.course_name',
            'rsc.id as stud_course_id',
            'rsc.course_attempt',
            'rsc.start_date',
            'rsc.finish_date',
            'rsc.coe_name',
            'rsc.status as student_course_status',
            'rs.generated_stud_id',
            'rsc.course_type_id',
            'ru.id',
            'ru.username',
            'ru.status',
        ])->toArray();

        return $result;
    }

    public function getBulkEnrolledStudents($collegeId, $courseTypeId = '', $courseId = '', $status = '')
    {
        $sql = StudentCourse::from('rto_student_courses as rsc')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_users as ru', 'ru.username', '=', 'rs.generated_stud_id')
            ->leftjoin('rto_colleges as clg', 'clg.id', '=', 'rs.college_id')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rsc.offer_status', '=', 'Enrolled')
            ->whereNull('ru.username')
            ->whereNotNull('rs.generated_stud_id');

        if (! empty($courseTypeId)) {
            $sql->where('rsc.course_type_id', '=', $courseTypeId);
        }
        if (! empty($courseId)) {
            $sql->where('rsc.course_id', '=', $courseId);
        }
        if (! empty($status)) {
            $sql->where('rsc.status', '=', $status);
        }

        $result = $sql->get([
            'rs.first_name',
            'rs.family_name',
            'rs.DOB',
            'rs.email',
            'rc.course_code',
            'rc.course_name',
            'rsc.id as stud_course_id',
            'rsc.course_attempt',
            'rsc.start_date',
            'rsc.finish_date',
            'rsc.coe_name',
            'rsc.status as student_course_status',
            'rs.generated_stud_id',
            'rsc.course_type_id',
            'rs.id',
            'ru.username',
            'clg.contact_email as clg_email',
        ])->toArray();

        return $result;
    }

    public function getEnrolledStudents($collegeId, $courseTypeId = '', $courseId = '', $status = '')
    {
        $sql = StudentCourse::from('rto_student_courses as rsc')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_users as ru', 'ru.username', '=', 'rs.generated_stud_id')
            ->leftjoin('rto_colleges as clg', 'clg.id', '=', 'rs.college_id')
            ->where('rs.college_id', '=', $collegeId)
            ->whereNull('ru.username')
            ->whereNotNull('rs.generated_stud_id');

        if (! empty($courseTypeId)) {
            $sql->where('rsc.course_type_id', '=', $courseTypeId);
        }
        if (! empty($courseId)) {
            $sql->where('rsc.course_id', '=', $courseId);
        }
        if (! empty($status)) {
            $sql->where('rsc.status', '=', $status);
        }

        $result = $sql->get([
            'rs.first_name',
            'rs.family_name',
            'rs.DOB',
            'rs.email',
            'rc.course_code',
            'rc.course_name',
            'rsc.id as stud_course_id',
            'rsc.course_attempt',
            'rsc.start_date',
            'rsc.finish_date',
            'rsc.coe_name',
            'rsc.status as student_course_status',
            'rs.generated_stud_id',
            'rsc.course_type_id',
            'rs.id',
            'ru.username',
            'clg.contact_email as clg_email',
        ])->toArray();

        return $result;
    }

    public function getStudentDetailsForXlsx130($arrFilter)
    {

        $form_date = date('Y-m-d', strtotime($arrFilter['from_date']));
        $to_date = date('Y-m-d', strtotime($arrFilter['to_date']));

        $sql = StudentCourse::join('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_colleges', 'rto_colleges.id', '=', 'rto_courses.college_id')
            //                ->leftjoin('rto_student_subject_enrolment', 'rto_student_subject_enrolment.student_id', '=', 'rto_students.id')
            ->leftJoin('rto_student_subject_enrolment', function ($join) {
                $join->on('rto_student_subject_enrolment.student_id', '=', 'rto_student_courses.student_id');
                $join->on('rto_student_subject_enrolment.course_id', '=', 'rto_student_courses.course_id');
            })
            ->leftjoin('rto_venue', 'rto_venue.id', '=', 'rto_student_subject_enrolment.vanue_location')
            ->leftJoin('rto_student_certificate_register as rscr', function ($join) {
                $join->on('rscr.student_id', '=', 'rto_student_courses.student_id');
                $join->on('rscr.course_id', '=', 'rto_student_courses.course_id');
            })
            ->where('rto_courses.college_id', '=', Auth::user()->college_id)
            ->where('rto_courses.module_delivery', '=', 0)
            ->where('rto_student_courses.status', '=', 'Completed')
            ->orderBy('rto_student_subject_enrolment.last_assessment_approved_date', 'desc');
        $sql->groupBy('rto_students.id');
        $sql->groupBy('rto_student_courses.id');

        $sql = $this->applyGlobalFilterFor130File($sql, $arrFilter);
        // if (!empty($arrFilter['year'])) {
        //     if($arrFilter['reportType']==1 || $arrFilter['reportType']==2){
        //         $sql->whereYear('rscr.date_generated', '=', $arrFilter['year']);
        //     }
        // }
        // if (!empty($arrFilter['month']) && $arrFilter['reportType'] == 2) {
        //     $sql->whereMonth('rscr.date_generated', '=', $arrFilter['month']);
        // }
        // if (!empty($arrFilter['stateName']) && $arrFilter['export_type'] == 1) {
        //    $sql->where('rto_students.current_state', '=', $arrFilter['stateName']);
        // }
        // if (!empty($arrFilter['from_date']) && !empty($arrFilter['to_date']) && $arrFilter['reportType'] == 3) {
        //     $sql->whereBetween('rscr.date_generated', array($form_date, $to_date));
        // }
        // if (!empty($arrFilter['claim_only']) && $arrFilter['claim_only'] == 1) {
        //     $sql->where('rto_student_courses.is_claim', '=', ($arrFilter['claim_only']));
        // }

        $sql = $this->applySmartAndSkillFilterWithStudent($sql, $arrFilter);

        $result = $sql->get([
            'rto_student_courses.*',
            'rto_courses.course_code',
            'rto_courses.status',
            'rto_colleges.RTO_code',
            'rto_courses.course_name',
            'rto_students.generated_stud_id',
            'rto_student_courses.offer_status as courseStatus',
            'rto_venue.state',
            'rto_student_courses.finish_date',
            'rto_student_subject_enrolment.activity_start_date',
            'rto_courses.national_code',
            'rscr.certificate_no',
            'rscr.date_generated as issueDate',
            'rto_student_subject_enrolment.last_assessment_approved_date',
        ]);

        return $result;
    }

    public function cqrReportData($collegeId, $yearValue)
    {
        // echo $yearValue;exit;
        return StudentCourse::from('rto_student_courses as rsc')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_course_training_plan as rctp', 'rctp.student_id', '=', 'rs.id')
            ->where('rsc.status', '=', 'Completed')
            ->where('rctp.state', '=', 8)  // 8 for WA
            ->get([
                'rs.generated_stud_id',
                'rs.family_name',
                'rs.DOB',
                'rs.gender',
                'rc.national_code',
                'rctp.contract_schedule_id as parchment_no',
                'rsc.student_id',
                'rsc.course_id',
                'rc.national_code',
            ])
            ->toarray();
    }

    public function generateEnrollmentReport($perPage, $fromDate, $toDate, $collegeId)
    {
        $sql = StudentCourse::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->leftjoin('rto_users', 'rto_student_courses.created_by', '=', 'rto_users.id')
            ->select('rto_campus.name as campus_name', 'rto_students.id', 'rto_students.first_name', 'rto_students.family_name', 'rto_students.generated_stud_id', 'rto_courses.course_name', 'rto_courses.course_code', 'rto_student_courses.course_attempt', 'rto_student_courses.coe_name', 'rto_student_courses.status', 'rto_student_courses.intake_date', 'rto_student_courses.start_date', 'rto_student_courses.finish_date', 'rto_student_courses.created_at', 'rto_student_courses.updated_at');

        if ($fromDate != '' && $fromDate != 0) {
            $checkFromDate = date('Y-m-d', strtotime($fromDate));
            $sql->where('rto_student_courses.created_at', '>=', $checkFromDate);
        }
        if ($toDate != '') {
            $checkToDate = date('Y-m-d', strtotime($toDate));
            $sql->where('rto_student_courses.created_at', '<=', $checkToDate);
        }
        // $sql->groupBy('rto_student_subject_enrolment.student_id','rto_student_subject_enrolment.course_id');

        if (! empty($perPage)) {
            return $sql->where('rto_students.college_id', $collegeId)->paginate($perPage);
        } else {
            return $sql->where('rto_students.college_id', $collegeId)->get();
        }
    }

    public function getAgentConversionReport($perPage, $courseTypeId, $agentId, $fromDate, $toDate, $collegeId)
    {
        $sql = StudentCourse::leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_staff_and_teacher', 'rto_staff_and_teacher.id', '=', 'rto_agents.account_manager_id')
            ->leftjoin('rto_country', 'rto_country.id', '=', 'rto_agents.target_primary_country')
            ->where('rto_student_courses.course_type_id', '=', $courseTypeId)
            ->select('rto_staff_and_teacher.first_name', 'rto_staff_and_teacher.last_name', 'rto_agents.id', 'rto_agents.agency_name', 'rto_country.name as country_name', DB::raw('COUNT( rto_student_courses.`student_id`) AS total_students'), DB::raw('SUM(rto_student_courses.status = "Offered") AS total_students_offered'), DB::raw('SUM(rto_student_courses.status="Enrolled") AS total_students_enrolled'), DB::raw('SUM(IF(rto_student_courses.status = "Offered", total_weeks, 0)) AS total_weeks_offered'));
        if ($agentId != '' && $agentId != 0) {
            $sql->where('rto_student_courses.agent_id', '=', $agentId);
        }
        if ($fromDate != '' && $fromDate != 0) {
            $checkFromDate = date('Y-m-d', strtotime($fromDate));
            $sql->where('rto_student_courses.start_date', '>=', $checkFromDate);
        }
        if ($toDate != '') {
            $checkToDate = date('Y-m-d', strtotime($toDate));
            $sql->where('rto_student_courses.start_date', '<=', $checkToDate);
        }
        $sql->groupBy('rto_student_courses.agent_id');
        $sql->where('rto_agents.college_id', $collegeId);
        if (! empty($perPage)) {
            return $sql->paginate($perPage);
        } else {
            return $sql->get();
        }
    }

    public function getStudentCountByStatus($perPage, $courseTypeId, $status, $agentId, $fromDate, $toDate, $collegeId)
    {
        $sql = StudentCourse::leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_college_course_type', 'rto_college_course_type.id', '=', 'rto_student_courses.course_type_id')
            ->where('rto_courses.college_id', '=', $collegeId)
            ->select('rto_agents.id', 'rto_agents.agency_name', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_college_course_type.title as course_type', 'rto_student_courses.status', DB::raw('COUNT( rto_student_courses.`student_id`) AS total_students'));

        if ($courseTypeId != '' && $courseTypeId != 0) {
            $sql->where('rto_student_courses.course_type_id', '=', $courseTypeId);
        }
        if ($agentId != '' && $agentId != 0) {
            $sql->where('rto_student_courses.agent_id', '=', $agentId);
        }
        if ($status != '') {
            $sql->where('rto_student_courses.status', '=', $status);
        }
        if ($fromDate != '' && $fromDate != 0) {
            $checkFromDate = date('Y-m-d', strtotime($fromDate));
            $sql->where('rto_student_courses.start_date', '>=', $checkFromDate);
        }
        if ($toDate != '') {
            $checkToDate = date('Y-m-d', strtotime($toDate));
            $sql->where('rto_student_courses.start_date', '<=', $checkToDate);
        }
        $sql->groupBy(['rto_student_courses.agent_id', 'rto_student_courses.status']);
        if (! empty($perPage)) {
            return $sql->paginate($perPage);
        } else {
            return $sql->get();
        }
    }

    public function getStudentCountByCourse($perPage, $courseTypeId, $courseId, $collegeId)
    {
        $sql = StudentCourse::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_college_course_type', 'rto_college_course_type.id', '=', 'rto_student_courses.course_type_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->where('rto_courses.college_id', '=', $collegeId)
            ->where('rto_student_courses.status', '=', 'Enrolled')
            ->select('rto_campus.name as campus_name', 'rto_courses.course_code', 'rto_courses.course_name', DB::raw('COUNT( rto_student_courses.`student_id`) AS total_students'));

        if ($courseTypeId != '' && $courseTypeId != 0) {
            $sql->where('rto_student_courses.course_type_id', '=', $courseTypeId);
        }
        if ($courseId != '' && $courseId != 0) {
            $sql->where('rto_student_courses.course_id', '=', $courseId);
        }
        $sql->groupBy(['rto_student_courses.course_id']);
        if (! empty($perPage)) {
            return $sql->paginate($perPage);
        } else {
            return $sql->get();
        }
    }

    public function getStudentOfferList($perPage, $fromDate, $toDate, $collegeId)
    {
        $sql = StudentCourse::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_staff_and_teacher as staff1', 'staff1.id', '=', 'rto_agents.account_manager_id')
            ->leftjoin('rto_staff_and_teacher as staff2', 'staff2.id', '=', 'rto_student_courses.course_manager_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->leftjoin('rto_country', 'rto_students.nationality', '=', 'rto_country.id')
            ->where('rto_courses.college_id', '=', $collegeId)
            ->where('rto_student_courses.offer_status', '!=', 'In Applicant')
            ->select('rto_students.generated_stud_id', 'rto_students.DOB', 'rto_students.email', 'rto_students.student_type', 'rto_students.student_type', 'rto_students.current_home_phone', 'rto_students.current_mobile_phone', 'rto_students.first_name', 'rto_students.family_name', 'rto_campus.name as campus_name', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_courses.tuition_fee', 'rto_courses.domestic_fee', 'rto_agents.agency_name', 'staff1.first_name as agent_manager_first_name', 'staff1.last_name as agent_manager_last_name', 'staff2.first_name as course_manager_first_name', 'staff2.last_name as course_manager_last_name', 'rto_country.nationality', 'rto_student_courses.*');
        if ($fromDate != '' && $fromDate != 0) {
            $checkFromDate = date('Y-m-d', strtotime($fromDate));
            $sql->where('rto_student_courses.created_at', '>=', $checkFromDate);
        }
        if ($toDate != '') {
            $checkToDate = date('Y-m-d', strtotime($toDate));
            $sql->where('rto_student_courses.created_at', '<=', $checkToDate);
        }
        if (! empty($perPage)) {
            return $sql->paginate($perPage);
        } else {
            return $sql->get();
        }
    }

    public function getStudentOfferListNotEnrolled($perPage, $fromDate, $toDate, $collegeId, $collegeId1)
    {
        $sql = StudentCourse::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_courses.college_id', '=', $collegeId)
            ->where('rto_student_courses.offer_status', '!=', 'Enrolled')
            ->select('rto_students.generated_stud_id', 'rto_students.DOB', 'rto_students.first_name', 'rto_students.family_name', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_courses.tuition_fee', 'rto_courses.domestic_fee', 'rto_agents.agency_name', 'rto_student_courses.*');
        if ($fromDate != '' && $fromDate != 0) {
            $checkFromDate = date('Y-m-d', strtotime($fromDate));
            $sql->where('rto_student_courses.start_date', '>=', $checkFromDate);
        }
        if ($toDate != '') {
            $checkToDate = date('Y-m-d', strtotime($toDate));
            $sql->where('rto_student_courses.start_date', '<=', $checkToDate);
        }
        if (! empty($perPage)) {
            return $sql->paginate($perPage);
        } else {
            return $sql->get();
        }
    }

    public function getMissingDocuments($fromDate, $toDate, $collegeId)
    {
        $sql = StudentCourse::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_courses.college_id', '=', $collegeId)
            ->where('rto_student_courses.offer_status', '!=', 'Enrolled')
            ->select('rto_students.generated_stud_id', 'rto_students.DOB', 'rto_students.first_name', 'rto_students.family_name', 'rto_students.student_type', 'rto_students.DOB', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_agents.agency_name', 'rto_student_courses.*');
        if ($fromDate != '' && $fromDate != 0) {
            $checkFromDate = date('Y-m-d', strtotime($fromDate));
            $sql->where('rto_student_courses.start_date', '>=', $checkFromDate);
        }
        if ($toDate != '') {
            $checkToDate = date('Y-m-d', strtotime($toDate));
            $sql->where('rto_student_courses.start_date', '<=', $checkToDate);
        }
        $arrResult = $sql->get()->toArray();
        $arrReturn = [];
        $arrDocuments = [];
        foreach ($arrResult as $result) {
            $objOfferDocumentChecklist = new OfferDocumentChecklist;
            $objStudentOfferDocuments = new StudentOfferDocuments;
            $arrRequiredDocuments = $objOfferDocumentChecklist->getOfferDocumentName(Auth::user()->college_id, $result['student_type']);
            foreach ($arrRequiredDocuments as $document) {
                $findStudentOfferDocumentId = $objStudentOfferDocuments->checkDocument($document->id, $result['student_id']);
                if (empty($findStudentOfferDocumentId[0])) {
                    $arrReturn['offer_id'] = $result['offer_id'];
                    $arrReturn['generated_stud_id'] = $result['generated_stud_id'];
                    $arrReturn['name'] = $result['first_name'].' '.$result['family_name'];
                    $arrReturn['DOB'] = $result['DOB'];
                    $arrReturn['origin'] = $result['student_type'];
                    $arrReturn['course_code'] = $result['course_code'];
                    $arrReturn['course_name'] = $result['course_name'];
                    $arrReturn['status'] = $result['status'];
                    $arrReturn['stage'] = $result['status'] == 'Enrolled' ? 'Student' : 'Offer';
                    $arrReturn['start_date'] = $result['start_date'];
                    $arrReturn['intake_date'] = $result['intake_date'];
                    $arrReturn['agency_name'] = $result['agency_name'];
                    $arrReturn['student_id'] = $result['student_id'];
                    $arrReturn['document_name'] = $document->document_name;
                    $arrReturn['is_mandatory'] = $document->is_compulsory;
                    $arrDocuments[] = $arrReturn;
                }
            }
        }

        return $arrDocuments;
    }

    public function getIntakeYear($courseTypeId, $courseId = '')
    {
        $sql = StudentCourse::where('course_type_id', '=', $courseTypeId);
        if ($courseId != '') {
            $sql->where('course_id', '=', $courseId);
        }
        $sql->groupBy(['intake_year']);

        $returnArray = $sql->pluck('intake_year')->toArray();
        $arrYear = [];
        foreach ($returnArray as $year) {
            $arrYear[$year] = $year;
        }

        return $arrYear;
    }

    public function getIntakeDateList($intakeYear, $courseTypeId, $courseId = '')
    {
        $sql = StudentCourse::where('course_type_id', '=', $courseTypeId)->where('intake_year', '=', $intakeYear);
        if ($courseId != '') {
            $sql->where('course_id', '=', $courseId);
        }
        $sql->groupBy(['intake_date']);
        $arrResult = $sql->pluck('intake_date')->toArray();
        $arrDate = [];
        foreach ($arrResult as $result) {
            if ($result != '0000-00-00' && $result != '') {
                $arrDate[$result] = date('d-m-Y', strtotime($result));
            }
        }

        return $arrDate;
    }

    public function getStudentListByIntakeDate($perPage, $courseTypeId, $courseId, $intakeDate, $collegeId)
    {
        $sql = StudentCourse::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_courses.college_id', '=', $collegeId)
            // ->where('rto_student_courses.offer_status', '!=', 'Enrolled')
            ->select('rto_students.generated_stud_id', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_courses.tuition_fee', 'rto_courses.domestic_fee', 'rto_agents.agency_name', 'rto_student_courses.*');
        if ($courseTypeId != '' && $courseTypeId != 0) {
            $sql->where('rto_student_courses.course_type_id', '=', $courseTypeId);
        }
        if ($courseId != '' && $courseId != 0) {
            $sql->where('rto_student_courses.course_id', '=', $courseId);
        }
        if ($intakeDate != '' && $intakeDate != 0) {
            $checkFromDate = date('Y-m-d', strtotime($intakeDate));
            $sql->where('rto_student_courses.intake_date', '=', $checkFromDate);
        }
        if (! empty($perPage)) {
            return $sql->paginate($perPage);
        } else {
            return $sql->get();
        }
    }

    public function getStudentListCompletedCourse($perPage, $courseTypeId, $status, $fromDate, $toDate, $collegeId)
    {
        // echo "hi";exit;
        $sql = StudentCourse::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->where('rto_courses.college_id', '=', $collegeId)
            // ->where('rto_student_courses.offer_status', '!=', 'Enrolled')
            ->select('rto_campus.name as campus_name', 'rto_students.generated_stud_id', 'rto_students.DOB', 'rto_students.first_name', 'rto_students.family_name', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_agents.agency_name', 'rto_student_courses.*');
        if ($fromDate != '' && $fromDate != 0) {
            $checkFromDate = date('Y-m-d', strtotime($fromDate));
            $sql->where('rto_student_courses.finish_date', '>=', $checkFromDate);
        }
        if ($toDate != '') {
            $checkToDate = date('Y-m-d', strtotime($toDate));
            $sql->where('rto_student_courses.finish_date', '<=', $checkToDate);
        }
        if ($status != '') {
            $sql->where('rto_student_courses.status', '=', $status);
        }
        if ($courseTypeId != '') {
            $sql->where('rto_student_courses.course_type_id', '=', $courseTypeId);
        }
        if (! empty($perPage)) {
            return $sql->paginate($perPage);
        } else {
            return $sql->get();
        }
    }

    public function listStudentCourseAgent($collegeId, $userId)
    {

        return $sql = StudentCourse::leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_agents.user_id', $userId)
            ->where('rto_student_courses.offer_status', '!=', 'Pending')
            ->where('rto_student_courses.offer_status', '!=', 'Enrolled')
            // ->groupBy('rto_student_courses.student_id')
            ->get([
                'rto_students.first_name',
                'rto_students.middel_name',
                'rto_students.family_name',
                'rto_students.created_at',
                'rto_students.student_type',
                'rto_student_courses.offer_id',
                'rto_student_courses.start_date',
                'rto_student_courses.finish_date',
                'rto_student_courses.intake_date',
                'rto_student_courses.status',
                'rto_student_courses.course_attempt',
                'rto_student_courses.student_id',
                'rto_student_courses.course_id',
                'rto_student_courses.id as student_course_id',
                'rto_student_courses.created_at',
                'rto_courses.course_code',
                'rto_courses.course_name',
            ])->toarray();
    }

    public function getOfferData($request)
    {

        $userId = Auth::user()->id;

        $requestData = $_REQUEST;

        $columns = [
            // datatable column index  => database column name
            0 => 'rto_student_courses.offer_id',
            1 => 'rto_student_courses.created_at',
            2 => 'rto_students.student_type',
            3 => 'rto_students.first_name',
            4 => 'rto_courses.course_code',
            5 => 'rto_student_courses.course_attempt',
            6 => 'rto_student_courses.start_date',
            7 => 'rto_student_courses.status',

        ];

        $query = StudentCourse::leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_agents.user_id', $userId)
            ->where('rto_student_courses.offer_status', '!=', 'Pending')
            ->where('rto_student_courses.offer_status', '!=', 'Enrolled');

        // print_r($requestData); exit();
        if (! empty($requestData['search']['value'])) {   // if there is a search parameter, $requestData['search']['value'] contains search parameter
            $searchVal = $requestData['search']['value'];

            $query->where(function ($query) use ($columns, $searchVal, $requestData) {
                $flag = 0;

                foreach ($columns as $key => $value) {
                    $searchVal = $requestData['search']['value'];

                    if ($requestData['columns'][$key]['searchable'] == 'true' && $searchVal != '') {
                        if ($flag == 0) {
                            $query->where($value, 'like', '%'.$searchVal.'%');
                            $flag = $flag + 1;
                        } else {
                            $query->orWhere($value, 'like', '%'.$searchVal.'%');
                        }
                    }
                }
            });
        }

        $temp = $query->orderBy($columns[$requestData['order'][0]['column']], $requestData['order'][0]['dir']);

        $totalData = count($temp->get());
        $totalFiltered = count($temp->get());

        $resultArr = $query->skip($requestData['start'])
            ->take($requestData['length'])
            ->select(
                'rto_student_courses.offer_id',
                'rto_student_courses.created_at',
                'rto_students.student_type',
                DB::raw("CONCAT(rto_students.first_name,' ',rto_students.family_name) AS fullname"),
                'rto_courses.course_code',
                'rto_courses.course_name',
                'rto_student_courses.course_attempt',
                'rto_student_courses.start_date',
                'rto_student_courses.finish_date',
                'rto_student_courses.status',
                'rto_student_courses.student_id',
                'rto_student_courses.course_id',
                'rto_student_courses.id as student_course_id',
                'rto_student_courses.created_at'
            )
            ->get()->toArray();

        $data = [];

        foreach ($resultArr as $row) {
            $row['offer_id'] = ! empty($row['offer_id']) ? $row['offer_id'] : 0;
            $actionHtml = '';
            $actionHtml .= "<li><a class='link-black text-sm' data-original-title='View the Selected student profile' data-toggle='tooltip' href='".route('agent_student_profile', ['studentId' => $row['student_id']])."'><i class='fa fa-user'></i></a></li>";

            if ($row['status'] !== 'New Application Request') {

                if ($row['student_type'] == 'Domestic') {
                    $actionHtml .= "<li><a class='link-black text-sm' data-original-title='Download domestic offer letter' data-toggle='tooltip' href='".route('agent_domestic_offer_letter_pdf', ['courseID' => $row['course_id'], 'id' => $row['student_id'], 'studentCourseID' => $row['student_course_id']])."'><i class='fa fa-download'></i></a></li>";
                } else {
                    $actionHtml .= "<li><a class='link-black text-sm' data-original-title='Download the offer letter' data-toggle='tooltip' href='".route('agent_offer_letter_pdf_new', ['courseID' => $row['course_id'], 'id' => $row['student_id'], 'studentCourseID' => $row['student_course_id']])."'><i class='fa fa-download'></i></a></li>";
                }
            }

            $actionHtml .= "<li><a class='link-black text-sm' data-original-title='Add/View Offer Communication' data-toggle='tooltip' href='".route('add-view-agent-offer-communication', ['id' => $row['student_id'], 'offer_id' => $row['offer_id']])."'><i class='fa fa-list-alt'></i></a></li>";
            $actionHtml .= "<li><a class='link-black text-sm' data-original-title='Start Student Gte Process' data-toggle='tooltip' href='".route('agent-gte-dashboard', ['id' => $row['student_id']])."'><i class='fa fa-user'></i></a></li>";

            $action = '<div class="action-overlay">
                            <ul class="icon-actions-set">
                                '.$actionHtml.'
                            </ul>
                        </div>';

            $ostatus = ($row['status'] == 'In Application') ? 'New Application Request' : $row['status'];
            $status_color = '';

            if ($ostatus == 'New Application Request') {
                $status_color = 'gray';
            } elseif ($ostatus == 'Reconsider') {
                $status_color = '#f0d723';
            } elseif ($ostatus == 'Rejected') {
                $status_color = '#ea6747';
            } elseif ($ostatus == 'Offered') {
                $status_color = '#70b23f';
            } elseif ($ostatus == 'Pending') {
                $status_color = '#3ab6cd';
            } else {
                $status_color = '#598d83';
            }

            $str = "<span style='font-size: 11px; border-radius: 5px; color: #fff; padding: 2px; background-color: $status_color; ' >  $ostatus </span>";
            $applied_course = $row['course_code'].' : '.$row['course_name'];

            if (strlen($applied_course) > 25) {
                $apCourse = substr($applied_course, 0, 25).'...';
            } else {
                $apCourse = $applied_course;
            }
            $appCor = '<span title='.$applied_course.'>'.$apCourse.'</span>';

            $nestedData = [];
            $nestedData[] = empty($row['offer_id']) ? '-' : $row['offer_id'].$action;
            $nestedData[] = date('d M Y', strtotime($row['created_at']));
            $nestedData[] = $row['student_type'];
            $nestedData[] = $row['fullname'];
            $nestedData[] = $appCor;
            $nestedData[] = $row['course_attempt'];
            $nestedData[] = date('d M Y', strtotime($row['start_date'])).' - '.date('d MY', strtotime($row['finish_date']));
            $nestedData[] = $str;

            $data[] = $nestedData;
        }
        // print_r($data); exit();
        $json_data = [
            'draw' => intval($requestData['draw']), // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
            'recordsTotal' => intval($totalData), // total number of records
            'recordsFiltered' => intval($totalFiltered), // total number of records after searching, if there is no searching then totalFiltered = totalData
            'data' => $data,   // total data array
        ];

        return $json_data;
    }

    public function getStaffApplication($request)
    {

        $userId = Auth::guard()->user()->id;
        $getAgentStaff = AgentStaff::leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_agents_staff.agent_id')
            ->where('rto_agents.user_id', $userId)
            ->pluck('rto_agents_staff.user_id');
        $requestData = $_REQUEST;

        $columns = [
            // datatable column index  => database column name
            0 => 'rto_student_courses.offer_id',
            1 => 'rto_students.created_at',
            2 => 'rto_students.student_type',
            3 => 'rto_students.first_name',
            4 => 'rto_courses.course_code',
            // 5 => 'rto_student_courses.course_attempt',
            5 => 'rto_users.name',
            6 => 'rto_student_courses.start_date',
            7 => 'rto_student_courses.status',

        ];

        $query = Students::leftjoin('rto_student_courses', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'rto_students.created_by')
            ->whereIn('rto_students.created_by', $getAgentStaff);

        if (! empty($requestData['search']['value'])) {   // if there is a search parameter, $requestData['search']['value'] contains search parameter
            $searchVal = $requestData['search']['value'];

            $query->where(function ($query) use ($columns, $searchVal, $requestData) {
                $flag = 0;

                foreach ($columns as $key => $value) {
                    $searchVal = $requestData['search']['value'];

                    if ($requestData['columns'][$key]['searchable'] == 'true' && $searchVal != '') {
                        if ($flag == 0) {
                            $query->where($value, 'like', '%'.$searchVal.'%');
                            $flag = $flag + 1;
                        } else {
                            $query->orWhere($value, 'like', '%'.$searchVal.'%');
                        }
                    }
                }
            });
        }

        $temp = $query->orderBy($columns[$requestData['order'][0]['column']], $requestData['order'][0]['dir']);
        $totalData = count($temp->get());
        $totalFiltered = count($temp->get());

        $resultArr = $query->skip($requestData['start'])
            ->take($requestData['length'])
            ->select(
                'rto_student_courses.offer_id',
                'rto_student_courses.created_at',
                'rto_students.student_type',
                DB::raw("CONCAT(rto_students.first_name,' ',rto_students.family_name) AS fullname"),
                'rto_courses.course_code',
                'rto_courses.course_name',
                'rto_student_courses.course_attempt',
                'rto_users.name',
                'rto_student_courses.start_date',
                'rto_student_courses.finish_date',
                'rto_student_courses.status',
                'rto_students.id as student_id',
                'rto_student_courses.course_id',
                'rto_student_courses.id as student_course_id',
                'rto_students.created_at'
            )
            ->get()->toArray();
        $data = [];

        foreach ($resultArr as $row) {
            $actionHtml = '';
            $ostatus = (($row['status'] == 'In Application') ? 'New Applications Request' : (! empty($row['status']))) ? $row['status'] : 'Incomplete';

            if ($ostatus == 'Incomplete') {
                $actionHtml .= "<li><a class='link-black text-sm' data-original-title='View Edit this Application Detail' data-toggle='tooltip' href='".route('agent-apply-online', $row['student_id'])."'><i class='fa fa-edit'></i></a></li>";
            } else {
                $actionHtml .= "<li><a class='link-black text-sm' data-original-title='View the Selected student profile' data-toggle='tooltip' href='".route('agent_student_profile', ['studentId' => $row['student_id']])."'><i class='fa fa-user'></i></a></li>";
                if ($row['status'] !== 'New Application Request') {
                    if ($row['student_type'] == 'Domestic') {
                        if ($row['course_id'] != '') {
                            $actionHtml .= "<li><a class='link-black text-sm' data-original-title='Download domestic offer letter' data-toggle='tooltip' href='".route('agent_domestic_offer_letter_pdf', ['courseID' => $row['course_id'], 'id' => $row['student_id'], 'studentCourseID' => $row['student_course_id']])."'><i class='fa fa-download'></i></a></li>";
                        } else {
                            $actionHtml .= "<li><a class='link-black text-sm' data-original-title='Download domestic offer letter' data-toggle='tooltip' href='#'><i class='fa fa-download'></i></a></li>";
                        }
                    } else {
                        if ($row['course_id'] != '') {
                            $actionHtml .= "<li><a class='link-black text-sm' data-original-title='Download the offer letter' data-toggle='tooltip' href='".route('agent_offer_letter_pdf_new', ['courseID' => $row['course_id'], 'id' => $row['student_id'], 'studentCourseID' => $row['student_course_id']])."'><i class='fa fa-download'></i></a></li>";
                        } else {
                            $actionHtml .= "<li><a class='link-black text-sm' data-original-title='Download domestic offer letter' data-toggle='tooltip' href='#'><i class='fa fa-download'></i></a></li>";
                        }
                    }
                }
                if ($row['course_id'] != '') {
                    $actionHtml .= "<li><a class='link-black text-sm' data-original-title='Add/View Offer Communication' data-toggle='tooltip' href='".route('add-view-agent-offer-communication', ['id' => $row['student_id'], 'offer_id' => $row['offer_id']])."'><i class='fa fa-list-alt'></i></a></li>";
                } else {
                    $actionHtml .= "<li><a class='link-black text-sm' data-original-title='Download domestic offer letter' data-toggle='tooltip' href='#'><i class='fa fa-download'></i></a></li>";
                }
            }

            $action = '<div class="action-overlay">
                            <ul class="icon-actions-set">
                                '.$actionHtml.'
                            </ul>
                        </div>';

            $status_color = '';
            $status_color = Config::get('constants.arrStatusbgColor');
            $status_chkcolor = isset($status_color[$ostatus]) ? ($status_color[$ostatus]) : 'blue';

            $str = "<span style='font-size: 11px; border-radius: 5px; color: #fff; padding: 2px; background-color: $status_chkcolor; ' >  $ostatus </span>";
            $applied_course = $row['course_code'].' : '.$row['course_name'];

            if (strlen($applied_course) > 25) {
                $apCourse = substr($applied_course, 0, 25).'...';
            } else {
                $apCourse = $applied_course;
            }
            $appCor = '<span title='.$applied_course.'>'.$apCourse.'</span>';

            $nestedData = [];
            $nestedData[] = (empty($row['offer_id']) ? '-' : $row['offer_id']).$action;
            $nestedData[] = date('d M Y', strtotime($row['created_at']));
            $nestedData[] = $row['student_type'];
            $nestedData[] = $row['fullname'];
            $nestedData[] = $appCor;
            // $nestedData[] = empty($row["course_attempt"]) ? '-' : $row["course_attempt"];
            $nestedData[] = $row['name'];
            $nestedData[] = date('d M Y', strtotime($row['start_date'])).' - '.date('d MY', strtotime($row['finish_date']));
            $nestedData[] = $str;

            $data[] = $nestedData;
        }
        $json_data = [
            'draw' => intval($requestData['draw']), // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
            'recordsTotal' => intval($totalData), // total number of records
            'recordsFiltered' => intval($totalFiltered), // total number of records after searching, if there is no searching then totalFiltered = totalData
            'data' => $data,   // total data array
        ];

        return $json_data;
    }

    public function getYourStudent($request)
    {
        $userId = Auth::guard()->user()->id;

        $requestData = $_REQUEST;

        $columns = [
            // datatable column index  => database column name
            0 => 'rto_student_courses.offer_id',
            1 => 'rto_students.first_name',
            2 => 'rto_students.family_name',
            3 => 'rto_students.generated_stud_id',
            4 => 'rto_courses.course_code',
            5 => 'rto_students.student_type',
            6 => 'rto_users.name',
            7 => 'rto_agents_staff.id',
            // 6 => 'rto_student_courses.course_attempt',
            8 => 'rto_student_courses.start_date',
            9 => 'rto_student_courses.finish_date',
            10 => 'rto_student_courses.status',

        ];

        $query = StudentCourse::leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'rto_student_courses.created_by')
            ->leftjoin('rto_agents_staff', 'rto_agents_staff.user_id', '=', 'rto_student_courses.created_by')
            ->where('rto_agents.user_id', $userId)
            ->where('rto_student_courses.offer_status', 'Enrolled');

        // print_r($requestData); exit();
        if (! empty($requestData['search']['value'])) {   // if there is a search parameter, $requestData['search']['value'] contains search parameter
            $searchVal = $requestData['search']['value'];

            $query->where(function ($query) use ($columns, $searchVal, $requestData) {
                $flag = 0;

                foreach ($columns as $key => $value) {
                    $searchVal = $requestData['search']['value'];

                    if ($requestData['columns'][$key]['searchable'] == 'true' && $searchVal != '') {
                        if ($flag == 0) {

                            $query->where($value, 'like', '%'.$searchVal.'%');
                            $flag = $flag + 1;
                        } else {

                            $query->orWhere($value, 'like', '%'.$searchVal.'%');
                        }
                    }
                }
            });
        }

        $temp = $query->orderBy($columns[$requestData['order'][0]['column']], $requestData['order'][0]['dir']);

        $totalData = count($temp->get());
        $totalFiltered = count($temp->get());

        $resultArr = $query->skip($requestData['start'])
            ->take($requestData['length'])
            ->select(
                'rto_student_courses.offer_id',
                'rto_students.first_name',
                'rto_students.family_name',
                'rto_students.generated_stud_id',
                'rto_courses.course_code',
                'rto_students.student_type',
                'rto_students.created_at',
                'rto_student_courses.course_attempt',
                'rto_student_courses.start_date',
                'rto_student_courses.finish_date',
                'rto_student_courses.status',
                'rto_courses.course_name',
                'rto_student_courses.intake_date',
                'rto_student_courses.student_id',
                'rto_student_courses.course_id',
                'rto_student_courses.id as student_course_id',
                'rto_student_courses.created_at',
                'rto_users.name',
                DB::raw('(CASE WHEN rto_agents_staff.location IS NULL THEN rto_agents.office_address ELSE rto_agents_staff.location END) as location')
            )
            ->get()->toArray();

        $data = [];

        // print_r($resultArr); exit();
        foreach ($resultArr as $row) {
            $actionHtml = '';
            $actionHtml .= "<li><a class='link-black text-sm' data-original-title='View the Selected student profile' data-toggle='tooltip' href='".route('agent_student_profile', ['studentId' => $row['student_id']])."'><i class='fa fa-user'></i></a></li>";

            if ($row['status'] !== 'New Application Request') {

                if ($row['student_type'] == 'Domestic') {
                    $actionHtml .= "<li><a class='link-black text-sm' data-original-title='Download domestic offer letter' data-toggle='tooltip' href='".route('agent_domestic_offer_letter_pdf', ['courseID' => $row['course_id'], 'id' => $row['student_id'], 'studentCourseID' => $row['student_course_id']])."'><i class='fa fa-download'></i></a></li>";
                } else {
                    $actionHtml .= "<li><a class='link-black text-sm' data-original-title='Download the offer letter' data-toggle='tooltip' href='".route('agent_offer_letter_pdf_new', ['courseID' => $row['course_id'], 'id' => $row['student_id'], 'studentCourseID' => $row['student_course_id']])."'><i class='fa fa-download'></i></a></li>";
                }
            }

            $actionHtml .= "<li><a class='link-black text-sm' data-original-title='Add/View Offer Communication' data-toggle='tooltip' href='".route('add-view-agent-offer-communication', ['id' => $row['student_id'], 'offer_id' => $row['offer_id']])."'><i class='fa fa-list-alt'></i></a></li>";

            $action = '<div class="action-overlay">
                                    <ul class="icon-actions-set">
                                        '.$actionHtml.'
                                    </ul>
                                </div>';

            $ostatus = ($row['status'] == 'In Application') ? 'New Application Request' : trim($row['status']);
            $status_color = '';

            if ($ostatus == 'New Application Request') {
                $status_color = 'gray';
            } elseif ($ostatus == 'Reconsider') {
                $status_color = '#f0d723';
            } elseif ($ostatus == 'Rejected') {
                $status_color = '#ea6747';
            } elseif ($ostatus == 'Offered') {
                $status_color = '#70b23f';
            } elseif ($ostatus == 'Pending') {
                $status_color = '#3ab6cd';
            } else {
                $status_color = '#598d83';
            }

            $str = "<span style='font-size: 11px; border-radius: 5px; color: #fff; padding: 2px; background-color: $status_color; ' >  $ostatus </span>";
            $applied_course = $row['course_code'].' : '.$row['course_name'];

            if (strlen($applied_course) > 25) {
                $apCourse = substr($applied_course, 0, 25).'...';
            } else {
                $apCourse = $applied_course;
            }
            $appCor = '<span title='.$applied_course.'>'.$apCourse.'</span>';

            $nestedData = [];
            $nestedData[] = $row['generated_stud_id'].$action;
            $nestedData[] = empty($row['offer_id']) ? '-' : $row['offer_id'];
            $nestedData[] = $row['first_name'];
            $nestedData[] = $row['family_name'];
            $nestedData[] = $appCor;
            $nestedData[] = $row['student_type'];
            $nestedData[] = $row['name'];
            $nestedData[] = $row['location'];
            // $nestedData[] = $row["course_attempt"];
            $nestedData[] = date('d M Y', strtotime($row['start_date']));
            $nestedData[] = date('d M Y', strtotime($row['finish_date']));
            $nestedData[] = $str;

            $data[] = $nestedData;
        }
        // print_r($data); exit();
        $json_data = [
            'draw' => intval($requestData['draw']), // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
            'recordsTotal' => intval($totalData), // total number of records
            'recordsFiltered' => intval($totalFiltered), // total number of records after searching, if there is no searching then totalFiltered = totalData
            'data' => $data,   // total data array
        ];

        return $json_data;
    }

    public function listStudentCourseAgentV2($collegeId, $userId)
    {

        return $sql = StudentCourse::leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_agents.user_id', $userId)
            ->where('rto_student_courses.offer_status', 'Enrolled')
            // ->groupBy('rto_student_courses.student_id')
            ->get([
                'rto_students.first_name',
                'rto_students.family_name',
                'rto_students.created_at',
                'rto_students.student_type',
                'rto_students.generated_stud_id',
                'rto_student_courses.offer_id',
                'rto_student_courses.start_date',
                'rto_student_courses.finish_date',
                'rto_student_courses.intake_date',
                'rto_student_courses.status',
                'rto_student_courses.course_attempt',
                'rto_student_courses.student_id',
                'rto_student_courses.course_id',
                'rto_student_courses.id as student_course_id',
                'rto_student_courses.created_at',
                'rto_courses.course_code',
                'rto_courses.course_name',
            ])->toarray();
    }

    public function listStudentCourseAgentV3($collegeId, $userId, $searchBy, $searchString, $searchDropDown)
    {
        $sql = StudentCourse::leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_agents.user_id', $userId)
            ->where('rto_student_courses.status', '!=', 'Pending');

        if ($searchBy == 'Offer ID' && ! empty($searchString)) {
            $sql->where('rto_student_courses.offer_id', $searchString);
        }
        if ($searchBy == 'Date Applied') {
        }
        if ($searchBy == 'First Name' && ! empty($searchString)) {
            $sql->where('rto_students.first_name', $searchString);
        }
        if ($searchBy == 'Last Name' && ! empty($searchString)) {
            $sql->where('rto_students.family_name', $searchString);
        }
        if ($searchBy == 'Course') {
            $sql->where('rto_courses.id', $searchDropDown);
        }
        if ($searchBy == 'Status') {
            $sql->where('rto_student_courses.status', $searchDropDown);
        }

        if ($searchBy == 'Application' && ! empty($searchString)) {
            $sql->where(function ($query) use ($searchString) {
                $query->where('rto_students.first_name', 'like', '%'.$searchString.'%');
                $query->orWhere('rto_students.family_name', 'like', '%'.$searchString.'%');
                $query->orWhere('rto_students.passport_no', 'like', '%'.$searchString.'%');
            });
        }

        // $sql->groupBy('rto_student_courses.student_id');
        $result = $sql->get([
            'rto_students.first_name',
            'rto_students.middel_name',
            'rto_students.family_name',
            'rto_students.created_at',
            'rto_students.student_type',
            'rto_students.generated_stud_id',
            'rto_students.passport_no',
            'rto_student_courses.offer_id',
            'rto_student_courses.start_date',
            'rto_student_courses.finish_date',
            'rto_student_courses.intake_date',
            'rto_student_courses.status',
            'rto_student_courses.course_attempt',
            'rto_student_courses.student_id',
            'rto_student_courses.course_id',
            'rto_student_courses.id as student_course_id',
            'rto_student_courses.created_at',
            'rto_courses.course_code',
            'rto_courses.course_name',
        ])->toarray();

        return $result;
    }

    public function listStudentCourseAgentPending($collegeId, $userId)
    {
        return $sql = StudentCourse::leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_agents.user_id', $userId)
            ->where('rto_student_courses.offer_status', 'Pending')
            // ->groupBy('rto_student_courses.student_id')
            ->get([
                'rto_students.first_name',
                'rto_students.middel_name',
                'rto_students.family_name',
                'rto_students.created_at',
                'rto_students.student_type',
                'rto_student_courses.offer_id',
                'rto_student_courses.start_date',
                'rto_student_courses.finish_date',
                'rto_student_courses.intake_date',
                'rto_student_courses.status',
                'rto_student_courses.course_attempt',
                'rto_student_courses.student_id',
                'rto_student_courses.course_id',
                'rto_student_courses.id as student_course_id',
                'rto_student_courses.created_at',
                'rto_courses.course_code',
                'rto_courses.course_name',
            ])->toarray();
    }

    public function getStudentExcelDataStudent($studentId, $courseId)
    {
        return $sql = StudentCourse::leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->where('rto_student_courses.student_id', $studentId)
            ->where('rto_student_courses.course_id', $courseId)
            ->get([
                'rto_campus.name',
            ])->toarray();
    }

    public function getStudentConversationReport($collegeId, $userId, $course_type, $fromDate, $toDate)
    {

        $agentId = Agent::where('college_id', $collegeId)->where('user_id', $userId)->value('id');

        $fromDate = (! empty($fromDate)) ? date('Y-m-d H:i:s', strtotime($fromDate)) : null;
        $toDate = (! empty($toDate)) ? date('Y-m-d H:i:s', strtotime($toDate)) : null;

        $sql = StudentCourse::where('course_type_id', $course_type)
            ->where('agent_id', $agentId);
        if (! empty($fromDate) && ! empty($toDate)) {
            $sql->whereBetween('created_at', [$fromDate, $toDate]);
        }

        $res1 = $sql->count();
        $res2 = $sql->where('offer_status', 'Enrolled')->count();
        $res3 = $sql->where('offer_status', 'Enrolled')->where('status', 'Current Student')->count();

        return [$res1, $res2, $res3];

        //        $sql1 = StudentCourse::where('course_type_id', $course_type)
        //                ->where('agent_id', $agentId)
        //                ->where('offer_status', "Enrolled")
        //                ->whereBetween('created_at', array($fromDate, $toDate))
        //                ->count();
        //
        //        $sql2 = StudentCourse::where('course_type_id', $course_type)
        //                ->where('agent_id', $agentId)
        //                ->whereBetween('created_at', array($fromDate, $toDate))
        //                ->count();
        //
        //        $sql3 = StudentCourse::where('course_type_id', $course_type)
        //                ->where('offer_status', "Enrolled")
        //                ->where('status', "Current Student")
        //                ->where('agent_id', $agentId)
        //                ->whereBetween('created_at', array($fromDate, $toDate))
        //                ->count();
        // return [$sql2, $sql1, $sql3];
    }

    public function getCurrentStudentList($collegeId, $userId, $course_type, $fromDate, $toDate, $typeOfReport)
    {

        $agentId = Agent::where('college_id', $collegeId)->where('user_id', $userId)->value('id');

        $fromDate = (! empty($fromDate)) ? date('Y-m-d', strtotime($fromDate)) : null;
        $toDate = (! empty($toDate)) ? date('Y-m-d', strtotime($toDate)) : null;

        $sql = StudentCourse::from('rto_student_courses as rsc')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where('rsc.agent_id', $agentId);

        if ($typeOfReport == 4) {
            $sql->where('rsc.course_type_id', $course_type);
            $sql->where('rsc.status', 'Current Student');
            $sql->whereBetween('rsc.created_at', [$fromDate, $toDate]);
        }
        if ($typeOfReport == 5) {
            $sql->where('rsc.status', 'Inactive');
        }

        $result = $sql->get([
            'rs.first_name',
            'rs.family_name',
            'rs.generated_stud_id',
            'rsc.status',
            'rc.course_code',
            'rc.course_name',
            'rc.course_duration',
            'rsc.course_fee',
            'rc.tuition_fee',
        ])->toarray();

        return $result;
    }

    public function getCourseListForCourseMaterial($collegeId, $studentId)
    {
        $result = [];
        $resultArr = StudentCourse::from('rto_student_courses as rsc')
            ->leftjoin('rto_courses as rc', 'rc.id', 'rsc.course_id')
            ->where('rsc.student_id', '=', $studentId)
            ->groupBy('rsc.course_id')
            ->get(['rc.id', 'rc.course_code', 'rc.course_name']);
        foreach ($resultArr as $row) {
            $result[] = $row->course_code;
        }

        return $result;
    }

    public function getCourseListByCoeNumber($coe_name)
    {
        $resultArr = StudentCourse::from('rto_student_courses as rsc')
            ->leftjoin('rto_courses as rc', 'rc.id', 'rsc.course_id')
            ->leftjoin('rto_students as rs', 'rs.id', 'rsc.student_id')
            ->leftjoin('rto_country as coun', 'coun.id', 'rs.birth_country')
            ->leftjoin('rto_country as currentCountry', 'currentCountry.id', 'rs.current_country')
            ->leftjoin('rto_college_course_type as coutseType', 'coutseType.id', 'rc.course_type_id')
            ->leftjoin('rto_level_of_educations as levelEducation', 'levelEducation.avaitmiss_id', 'rc.level_of_education_id')
            ->leftjoin('rto_course_recognitions as courseRecognition', 'courseRecognition.avaitmiss_id', 'rc.course_recognition_id')
            ->leftjoin('rto_anzsco_codes as anzscoCodes', 'anzscoCodes.avaitmiss_id', 'rc.ANZSCO_code')
            ->leftjoin('rto_country as nation', 'nation.id', 'rs.nationality')
            ->where('rsc.coe_name', '=', $coe_name)
            ->get([
                'rc.id',
                'rc.course_code',
                'rc.cricos_code',
                'rc.work_placement',
                'rc.work_placement_hour',
                'rc.tuition_fee',
                'rc.course_name',
                'rsc.*',
                'rs.*',
                'coun.name as birthCountryName',
                'currentCountry.name as currentCountryName',
                'nation.nationality as studentNationality',
                'coutseType.title as courseTypeName',
                'levelEducation.name as levalOfEducation',
                'courseRecognition.name as courseRecognitionsName',
                'anzscoCodes.name as anzscoCodesName',
            ])->toarray();

        return $resultArr;
    }

    public function getStudentDetailsForNSW130($arrFilter)
    {

        $sql = StudentCourse::join('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_colleges', 'rto_colleges.id', '=', 'rto_courses.college_id')
            ->leftjoin('rto_student_subject_enrolment', 'rto_student_subject_enrolment.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_venue', 'rto_venue.id', '=', 'rto_student_subject_enrolment.vanue_location')
            ->where('rto_courses.college_id', '=', Auth::user()->college_id)
            ->where('rto_student_courses.status', '=', 'Completed')
            ->orderBy('rto_student_courses.id', 'desc');
        $sql->groupBy('rto_students.id');
        if (! empty($arrFilter['claim_stage'])) {
            $sql->leftjoin('rto_student_claim_tracking', 'rto_student_claim_tracking.student_id', '=', 'rto_students.id');
            $sql->where('rto_student_claim_tracking.lodgement_type', $arrFilter['claim_stage']);
        }
        $sql->where('rto_venue.state', '=', 'NSW');

        $result = $sql->get([
            'rto_student_courses.*',
            'rto_courses.course_code',
            'rto_colleges.RTO_code',
            'rto_courses.course_name',
            'rto_students.generated_stud_id',
            'rto_student_courses.offer_status as courseStatus',
            'rto_venue.state',
            'rto_student_subject_enrolment.activity_start_date',
            'rto_student_subject_enrolment.activity_finish_date',
        ]);

        return $result;
    }

    public function checkCampusExistInStudentCourse($collegeId, $campusId)
    {
        return StudentCourse::where('campus_id', $campusId)->get()->count();
    }

    public function getAssignstudentlistForExcel($collegeId, $arrCourse, $arrStudent, $arrStudentCourse)
    {
        $resultArr = [];
        $arrStudents = explode(',', $arrStudent[0]);
        $arrCourses = explode(',', $arrCourse[0]);
        $studentCourse = explode(',', $arrStudentCourse[0]);
        $count = 0;
        for ($j = 0; $j < count($studentCourse); $j++) {
            //            echo $studentCourse[$j];exit;
            $count++;
            $results = DB::table('rto_student_courses')
                ->join('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
                ->join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
                ->join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id');
            $results->where('rto_courses.college_id', '=', $collegeId);
            //            $results->where('rto_student_courses.student_id', '=', $arrStudents[$j]);
            //            $results->where('rto_student_courses.course_id', '=', $arrCourses[$j]);
            $results->where('rto_student_courses.id', '=', $studentCourse[$j]);
            $results->where('rto_student_courses.offer_status', '=', 'Enrolled');
            $results->select(
                'rto_student_courses.status as studentstatus',
                'rto_student_courses.id as studentcourseid',
                'rto_student_courses.mode_of_delivery',
                'rto_student_courses.student_id',
                'rto_student_courses.course_id',
                'rto_courses.course_code',
                'rto_courses.course_name',
                'rto_student_courses.coe_name',
                'rto_student_courses.start_date',
                'rto_student_courses.finish_date',
                'rto_student_courses.updated_at',
                'rto_students.first_name as student_first_name',
                'rto_students.middel_name as student_middle_name',
                'rto_students.generated_stud_id as generated_stud_id',
                'rto_campus.name as campus_name'
            );
            $sqlResult = $results->get()->toArray();
            $resultArr[$count] = $sqlResult[0];
        }

        return $resultArr;
    }

    public function getVetFeeStudentList($collegeId, $dataArr)
    {

        $courseIdArr = json_decode($dataArr['courseIdArr']);
        $statusIdArr = json_decode($dataArr['statusIdArr']);
        $fromDate = date('Y-m-d', strtotime($dataArr['fromDate']));
        $toDate = date('Y-m-d', strtotime($dataArr['toDate']));
        // echo "From: $fromDate and To : $toDate";exit;

        $resultArr = StudentCourse::from('rto_student_courses as rsc')
            ->join('rto_students as rs', 'rs.id', 'rsc.student_id')
            ->join('rto_courses as rc', 'rc.id', 'rsc.course_id')
            ->join('rto_student_wet_fee_help as rsvfh', 'rsvfh.student_id', 'rsc.student_id')
            ->whereIn('rsc.course_id', $courseIdArr)
            ->whereIn('rsc.offer_status', $statusIdArr)
            ->where('rs.student_type', 'Domestic')
            ->whereBetween('rsc.start_date', [$fromDate, $toDate])
            // ->groupBy('rsc.course_id')
            ->select('rsc.student_id', 'rsc.course_id', 'rs.generated_stud_id', DB::raw('concat(COALESCE(rs.name_title, ""), COALESCE(rs.first_name, ""), " ", COALESCE(rs.family_name, "")) as student_name'), 'rsc.course_attempt as attempt', 'rsc.offer_status as status', DB::raw('concat(rc.course_code, " : ", rc.course_name) as course'))
            ->get();
        // return $resultArr;
        foreach ($resultArr as $row) {
            $studentId = $row->student_id;
            $courseId = $row->course_id;
            $objStudSubEnroll = new StudentSubjectEnrolment;
            $row['unit'] = $objStudSubEnroll->getVetFeeStudentUnitInfo($collegeId, $studentId, $courseId);
            $row['uniqueId'] = $studentId.'_'.$courseId;
        }

        // echo "<pre/>";print_r($resultArr);exit;
        return $resultArr;
    }

    public function checkAssignStudentCount($group_id)
    {
        return StudentCourse::where('group_id', $group_id)->count();
    }

    public function getStudentCourseStudyReasonIdData($studentID, $courseId)
    {
        return StudentCourse::select('study_reason_id')->where('student_id', '=', $studentID)
            ->where('course_id', '=', $courseId)->get()->toArray();
    }

    public function getStudentCourseDetails($courseId, $studentID)
    {
        return StudentCourse::where('student_id', '=', $studentID)
            ->where('course_id', '=', $courseId)->first();
    }

    public function getStudentCourseName($studentId, $courseId = '')
    {
        $dataArr = StudentCourse::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'rto_students.updated_by')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->where('rto_student_courses.student_id', '=', $studentId);
        if ($courseId != '') {
            $dataArr->where('rto_student_courses.course_id', '=', $courseId);
        }
        $resultArr = $dataArr->select('rto_students.first_name', 'rto_students.family_name', 'rto_courses.course_code')
            ->get();

        return $resultArr;
        // print_r($dataArr);exit;
    }

    public function getArrayStudentEnrolledCourseName($studentId)
    {

        $arrStudentCourse = StudentCourse::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.offer_status', '=', 'Enrolled')
            ->get(['rto_courses.course_name', 'rto_courses.id', 'rto_courses.course_code', 'rto_student_courses.start_date', 'rto_student_courses.finish_date'])->toArray();

        return $arrStudentCourse;
    }

    public function getArrayStudentOfferedCourseName($studentId)
    {

        $arrStudentCourse = StudentCourse::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.offer_status', '!=', 'Enrolled')
            ->get(['rto_courses.course_name', 'rto_courses.id', 'rto_courses.course_code', 'rto_student_courses.start_date', 'rto_student_courses.finish_date'])->toArray();

        return $arrStudentCourse;
    }

    public function getTcsiStudentCourseList($collegeId, $studentId)
    {
        $nullResult[''] = 'No Course Found';
        $whereArr = [
            'rc.college_id' => $collegeId,
            'rsc.student_id' => $studentId,
        ];
        $selectArr = [
            'rsc.id as studentCourseId',
            'rc.id',
            DB::raw('concat(rc.course_code, " : ", rc.course_name) as course'),
        ];
        $resArr = StudentCourse::from('rto_student_courses as rsc')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where($whereArr)
            ->get($selectArr)
            ->toArray();
        $courseList = [];
        foreach ($resArr as $res) {
            $courseList[$res['id']] = $res['course'];
        }
        $finalRes = (count($courseList) > 0) ? $courseList : $nullResult;

        return $finalRes;
    }
}
