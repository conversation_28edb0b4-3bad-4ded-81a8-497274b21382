<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="true"
        :has-export="false"
        :has-filters="false"
        :create-btn-label="'Add Intervention Strategy'"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :actions="['edit', 'delete']"
    >
        <template #body-cell-strategy="{ props }">
            <div class="group space-y-1 py-2 text-gray-800">
                <p class="text-sm">{{ props.dataItem?.strategy }}</p>
                <Button
                    variant="tertiary"
                    class="invisible border border-dashed border-primary-blue-400 transition-all duration-300 group-hover:visible"
                    size="xs"
                    @click="store.createInterventionType(props.dataItem)"
                >
                    <icon name="plus" fill="currentColor" width="14" height="14" />
                    Add another strategy for this type
                </Button>
            </div>
        </template>
        <template #body-cell-intervention_type="{ props }">
            <div class="font-medium text-gray-800" v-if="props.dataItem?.intervention_type">
                {{ props.dataItem?.intervention_type }}
            </div>
        </template>
    </AsyncGrid>
    <InterventionStrategyForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useInterventionStrategyStore } from '@spa/stores/modules/interventionstrategy/useInterventionStrategyStore.js';
import InterventionStrategyForm from '@spa/modules/interventionstrategy/InterventionStrategyForm.vue';
import Button from '@spa/components/Buttons/Button.vue';
import dayjs from 'dayjs';

const store = useInterventionStrategyStore();

const columns = [
    {
        field: 'intervention_type_id',
        name: 'intervention_type',
        title: 'Intervention Type',
        width: '300px',
        sortable: true,
        replace: true,
    },
    {
        field: 'strategy',
        name: 'strategy',
        title: 'Strategy',
        width: '300px',
        replace: true,
        sortable: true,
    },
    {
        field: 'created_at',
        title: 'Created At',
        width: '300px',
        formatCellData: (val) => {
            return (
                dayjs(val, ['DD-MM-YYYY hh:mm A', 'YYYY-MM-DDTHH:mm:ssZ']).format('DD-MM-YYYY') ??
                'N/A'
            );
        },
        sortable: true,
    },
    // Add more columns as needed
];

const initFilters = () => {
    store.filters = {};
};

onMounted(() => {
    initFilters();
});
</script>
