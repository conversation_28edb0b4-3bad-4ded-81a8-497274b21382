<template>
    <fieldwrapper :class="rootClass">
        <klabel
            :class="labelClass"
            :editor-id="id"
            :editor-valid="internalValid"
            :disabled="disabled"
            :optional="optional"
        >
            {{ label }}
            <div v-if="hint && hintOnLabel" class="font-light italic text-gray-400">
                ({{ hint }})
            </div>
            <span v-if="required || indicaterequired" class="ml-1 text-red-500">*</span>
        </klabel>

        <div :class="wrapClass">
            <div class="tw-phone-mask-input flex w-full">
                <!-- Country Dropdown -->
                <DropDownList
                    :data-items="countries"
                    v-model="selectedCountry"
                    text-field="name"
                    value-field="name"
                    :popup-settings="{
                        position: 'bottom',
                        style: 'width: 100px !important',
                        className: 'tw-width-auto',
                        animate: false,
                    }"
                    @change="handleCountryChange"
                    :style="{ width: '80px !important', flex: '1 0 auto' }"
                    :item-render="'itemRender'"
                    :value-render="'valueRender'"
                    :filterable="true"
                    @filterchange="handleFilterChange"
                >
                    <template #itemRender="{ props }">
                        <li v-bind="props">
                            <div class="flex items-center gap-2">
                                <CountryImage :shortName="props.dataItem?.short_name" />
                                <span
                                    >{{ props.dataItem.name }} ({{
                                        props.dataItem?.short_name
                                    }})</span
                                >
                            </div>
                        </li>
                    </template>
                    <template #valueRender="{ props }">
                        <div v-bind="props">
                            <div class="flex items-center gap-2">
                                <CountryImage :shortName="props.value?.short_name" />
                                <span>{{ props.value?.short_name }}</span>
                            </div>
                        </div>
                    </template>
                </DropDownList>

                <!-- Masked Input -->
                <MaskedTextBox
                    :style="{ width: '100%' }"
                    :mask="mask"
                    :name="name"
                    :id="id"
                    :disabled="disabled"
                    v-model="phoneNumber"
                    :placeholder="mask"
                    :input-class="inputClass"
                    :input-prefix="'prefix'"
                    @change="handlePhoneChange"
                    @blur="handleBlur"
                    @focus="handleFocus"
                >
                    <template #prefix>
                        {{ prefix }}
                    </template>
                </MaskedTextBox>
            </div>

            <error v-if="showValidationMessage">
                {{ validationMessage }}
            </error>
            <hint v-else-if="!showValidationMessage && !hintOnLabel">
                {{ hint }}
            </hint>
        </div>
    </fieldwrapper>
</template>

<script>
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { MaskedTextBox } from '@progress/kendo-vue-inputs';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import CountryImage from '@spa/components/Country/CountryImage.vue';
import { twMerge } from 'tailwind-merge';

const countries = window['kendo_countries_codes'];

export default {
    props: {
        modelValue: [String, Number],
        optional: Boolean,
        disabled: Boolean,
        placeholder: String,
        touched: Boolean,
        label: String,
        validationMessage: String,
        hint: String,
        hintOnLabel: { type: Boolean, default: false },
        id: String,
        valid: { type: Boolean, default: true },
        class: String,
        indicaterequired: { type: Boolean, default: false },
        orientation: { type: String, default: 'vertical' },
        value: [String, Number],
        defaultValue: String,
        name: String,
        required: { type: Boolean, default: false },
        country_code: {
            type: String,
            default: '',
        },
        pt: {
            type: Object,
            default: () => ({}),
        },
    },
    emits: ['update:modelValue', 'update:country_code', 'change', 'blur', 'focus'],
    components: {
        fieldwrapper: FieldWrapper,
        klabel: Label,
        hint: Hint,
        error: Error,
        DropDownList,
        MaskedTextBox,
        CountryImage,
    },
    data() {
        return {
            countries: countries.slice(),
            selectedCountry: null,
            mask: '',
            prefix: '',
            internalValid: this.valid,
            phoneNumber: '',
            isInternalUpdate: false,
        };
    },
    computed: {
        showValidationMessage() {
            return this.touched && this.validationMessage;
        },
        rootClass() {
            const base = 'tw-form__fieldwrapper';
            const orientation = this.orientation === 'horizontal' ? 'field-horizontal' : '';
            return twMerge(`${base} ${orientation}`, this.pt.root);
        },
        wrapClass() {
            return twMerge('k-form-field-wrap', this.pt.wrap);
        },
        labelClass() {
            return twMerge(
                'tw-form__label mb-1 font-medium leading-5 text-gray-700',
                this.pt.label
            );
        },
        inputClass() {
            return twMerge('tw-form__input', this.pt.input);
        },
    },
    watch: {
        modelValue: {
            immediate: true,
            handler(newVal) {
                if (!this.isInternalUpdate && newVal) {
                    this.parsePhoneNumber(newVal);
                }
                // reset internal valid on user input
                this.internalValid = true;
            },
        },
        valid(newVal) {
            this.internalValid = newVal;
        },
        country_code: {
            immediate: true,
            handler(newVal) {
                if (!this.isInternalUpdate && newVal) {
                    this.setCountryByCode(newVal);
                }
            },
        },
    },
    methods: {
        parsePhoneNumber(phoneValue) {
            if (!phoneValue) {
                this.phoneNumber = '';
                return;
            }

            const phoneStr = phoneValue.toString();

            // Handle integer phone numbers (full numbers with country code as digits)
            if (!phoneStr.includes('+') && !phoneStr.includes('-')) {
                // Try to match against country prefixes (without the + sign)
                const matchingCountry = this.countries.find((country) => {
                    const prefixDigits = country.prefix.replace('+', '');
                    return phoneStr.startsWith(prefixDigits);
                });

                if (matchingCountry && matchingCountry !== this.selectedCountry) {
                    this.selectedCountry = matchingCountry;
                    this.mask = matchingCountry.mask || '';
                    this.prefix = matchingCountry.prefix || '';
                    // Extract phone number without country prefix digits
                    const prefixDigits = matchingCountry.prefix.replace('+', '');
                    this.phoneNumber = phoneStr.substring(prefixDigits.length);
                } else if (this.selectedCountry) {
                    // If we already have a country selected, extract number part
                    const prefixDigits = this.selectedCountry.prefix.replace('+', '');
                    if (phoneStr.startsWith(prefixDigits)) {
                        this.phoneNumber = phoneStr.substring(prefixDigits.length);
                    } else {
                        // Treat as local number
                        this.phoneNumber = phoneStr;
                    }
                } else {
                    // No country detected, use full number
                    this.phoneNumber = phoneStr;
                }
                return;
            }

            // Handle string formats with + or -
            if (phoneStr.startsWith('+')) {
                const matchingCountry = this.countries.find((country) => {
                    return phoneStr.startsWith(country.prefix);
                });

                if (matchingCountry && matchingCountry !== this.selectedCountry) {
                    this.selectedCountry = matchingCountry;
                    this.mask = matchingCountry.mask || '';
                    this.prefix = matchingCountry.prefix || '';
                    this.phoneNumber = phoneStr.substring(matchingCountry.prefix.length);
                } else if (this.selectedCountry) {
                    this.phoneNumber = phoneStr.substring(this.selectedCountry.prefix.length);
                } else {
                    this.phoneNumber = phoneStr;
                }
            } else if (phoneStr.includes('-') && phoneStr.startsWith('+')) {
                const [countryPrefix, number] = phoneStr.split('-', 2);
                const matchingCountry = this.countries.find(
                    (country) => country.prefix === countryPrefix
                );

                if (matchingCountry && matchingCountry !== this.selectedCountry) {
                    this.selectedCountry = matchingCountry;
                    this.mask = matchingCountry.mask || '';
                    this.prefix = matchingCountry.prefix || '';
                    this.phoneNumber = number || '';
                } else if (this.selectedCountry && this.selectedCountry.prefix === countryPrefix) {
                    this.phoneNumber = number || '';
                } else {
                    this.phoneNumber = phoneStr;
                }
            } else {
                this.phoneNumber = phoneStr;
            }
        },

        setCountryByCode(countryCode) {
            const prefix = countryCode.startsWith('+') ? countryCode : '+' + countryCode;
            const country = this.countries.find((c) => c.prefix === prefix);

            if (country && country !== this.selectedCountry) {
                this.selectedCountry = country;
                this.mask = country.mask || '';
                this.prefix = country.prefix || '';
            }
        },

        handleCountryChange(event) {
            // selectedCountry is already updated via v-model, so we don't need to set it again
            this.mask = this.selectedCountry?.mask || '';
            this.prefix = this.selectedCountry?.prefix || '';

            // Clear the phone number when country changes to avoid confusion
            this.phoneNumber = '';

            // Emit country code update
            const countryCode = this.selectedCountry?.prefix?.replace('+', '') || '';
            this.isInternalUpdate = true;
            this.$emit('update:country_code', countryCode);

            // Update the full phone number
            this.updateFullPhoneNumber();
            this.isInternalUpdate = false;
        },

        handleFilterChange(event) {
            this.countries = countries.filter((c) =>
                c.name.toLowerCase().includes(event.filter?.value?.toLowerCase() || '')
            );
        },

        handlePhoneChange(event) {
            this.phoneNumber = event.target.value;
            this.updateFullPhoneNumber();
        },

        updateFullPhoneNumber() {
            const cleanedNumber = this.phoneNumber?.replace(/[^0-9]/g, '') || '';
            const fullNumber =
                this.selectedCountry && cleanedNumber
                    ? `${this.selectedCountry.prefix}${cleanedNumber}`
                    : cleanedNumber;

            this.isInternalUpdate = true;
            this.$emit('update:modelValue', fullNumber);

            // Also emit country code if we have a selected country
            if (this.selectedCountry) {
                const countryCode = this.selectedCountry.prefix.replace('+', '');
                this.$emit('update:country_code', countryCode);
            }

            this.$emit('change', { target: { value: fullNumber } }, fullNumber);
            this.isInternalUpdate = false;
        },

        handleBlur(e) {
            this.updateFullPhoneNumber();
            this.$emit('blur', e);
        },

        handleFocus(e) {
            this.$emit('focus', e);
        },
    },
    mounted() {
        // Initialize with default country if country_code is provided
        if (this.country_code) {
            this.setCountryByCode(this.country_code);
        } else if (!this.selectedCountry && this.countries.length > 0) {
            // Set first country as default if no country is selected
            this.selectedCountry = this.countries[0];
            this.mask = this.selectedCountry?.mask || '';
            this.prefix = this.selectedCountry?.prefix || '';
        }

        // Parse initial modelValue if provided
        if (this.modelValue) {
            this.parsePhoneNumber(this.modelValue);
        }
    },
};
</script>
