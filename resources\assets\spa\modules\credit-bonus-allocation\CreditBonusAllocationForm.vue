<template>
    <AsyncForm
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        position="center"
        :dialogTitle="'Add Credit Bonus Allocation'"
        :store="store"
    >
        <div class="grid grid-cols-2 gap-4 p-4">
            <!--            <LabelValuePair label="Agent Name" :value="formData.agent?.name" />-->
            <FormValidationWrapper
                :validation-message="store.errors?.type"
                :valid="!store.errors?.type"
                :indicate-required="true"
                label="Agent Status:"
            >
                <template #field>
                    <EnumSelect
                        className="tw-w-full"
                        enum-class="GalaxyAPI\Enums\CreditAllocationTypeEnum"
                        v-model="formData.type"
                        :default-value="1"
                        :placeholder="'Select Status'"
                    />
                </template>
            </FormValidationWrapper>
            <Field
                :id="'amount'"
                :name="'amount'"
                :label="'Credit Amount'"
                :component="FormInput"
                v-model="formData.amount"
                :required="true"
                :layout="'horizontal'"
                :indicaterequired="true"
                :validation-message="store.errors?.amount"
                :valid="!store.errors?.amount"
            />
            <PaymentModeSelect
                v-if="formData.type !== 2"
                v-model="formData.payment_mode"
                name="payment_mode"
                label="Payment Mode"
                :validation-message="store.errors?.payment_mode"
                :valid="!store.errors?.payment_mode"
                :touched="true"
                :indicaterequired="true"
            />
            <field
                v-if="formData.type !== 2"
                :id="'payment_date'"
                :name="'payment_date'"
                v-model="formData.payment_date"
                :label="'Payment Date'"
                :component="FormDatePicker"
                :validation-message="store.errors?.payment_date"
                :valid="!store.errors?.payment_date"
                :format="'dd-MM-yyyy'"
                :emit-format="'yyyy-MM-dd'"
                :required="true"
                :indicaterequired="true"
            />
            <div class="col-span-2">
                <Field
                    :id="'comment'"
                    :name="'comment'"
                    :label="'Comment'"
                    :component="FormTextArea"
                    v-model="formData.comment"
                    :required="true"
                    :layout="'horizontal'"
                    :indicaterequired="true"
                    :rows="5"
                    :validation-message="store.errors?.comment"
                    :valid="!store.errors?.comment"
                />
            </div>
        </div>
    </AsyncForm>
</template>
<script setup>
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import { useCreditBonusAllocationStore } from '@spa/stores/modules/credit-bonus-allocation/useCreditBonusAllocationStore.js';
import { storeToRefs } from 'pinia';
import EnumSelect from '@spa/components/AsyncComponents/Select/EnumSelect.vue';
import FormValidationWrapper from '@spa/components/KendoInputs/FormValidationWrapper.vue';
import FormRadioGroup from '@spa/components/KendoInputs/FormRadioGroup.vue';
import { Field } from '@progress/kendo-vue-form';
import PaymentModeSelect from '@spa/modules/payment-mode/PaymentModeSelect.vue';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
import LabelValuePair from '@spa/components/LabelValuePair/LabelValuePair.vue';
import { watch } from 'vue';

const props = defineProps({
    agentId: [String, Number],
});

const store = useCreditBonusAllocationStore();
const { formData } = storeToRefs(store);

watch(
    () => store.formDialog,
    (val) => {
        if (val) {
            formData.value = {
                ...formData.value,
                agent_id: props.agentId,
            };
        }
    }
);

watch(
    () => formData.value.type,
    (newType) => {
        if (newType === 2) {
            //formData.value.payment_mode = null;
            //formData.value.payment_date = null;
        }
    }
);
</script>
