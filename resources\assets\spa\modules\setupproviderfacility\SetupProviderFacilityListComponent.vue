<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="false"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :actions="['delete']"
    >
        <template #bulk-actions>
            <!--            <Button-->
            <!--                type="link"-->
            <!--                class="tw-btn-primary !h-9 shadow-none"-->
            <!--                size="xs"-->
            <!--                @click="-->
            <!--                    () => {-->
            <!--                        store.formDialog = true;-->
            <!--                    }-->
            <!--                "-->
            <!--            >-->
            <!--                <icon name="check" :fill="'currentColor'" />-->
            <!--                Add Facility-->
            <!--            </Button>-->
            <a :href="route('provider-facility')" class="tw-btn-primary !h-9 shadow-none">
                <icon name="check" :fill="'currentColor'" />
                Add Facility
            </a>
        </template>
        <template #body-cell-service_name="{ props }">
            {{ props.dataItem?.service_name?.service_name }}
        </template>
        <template #body-cell-category_name="{ props }">
            {{ props.dataItem?.category?.category_name }}
        </template>
        <template #body-cell-facility_name="{ props }">
            {{ props.dataItem?.facility?.facility_name }}
        </template>
        <template #body-cell-is_active="{ props }">
            <badge :variant="props.dataItem?.is_active === 1 ? 'success' : 'error'">
                {{ props.dataItem?.is_active === 1 ? 'Active' : 'Inactive' }}
            </badge>
        </template>
        <template #body-cell-provider_price="{ props }">
            <!--            <EditableColumn v-model:rowForm="props.rowForm" :data-item="props.dataItem" :store="store" column="provider_price" />-->
            <PriceColumn :value="props.dataItem?.provider_price" />
        </template>
        <template #body-cell-std_price="{ props }">
            <!--            <EditableColumn v-model:rowForm="props.rowForm" :data-item="props.dataItem" :store="store" column="std_price" />-->
            <PriceColumn :value="props.dataItem?.std_price" />
        </template>
        <template #body-cell-gst_inc="{ props }">
            <Badge :variant="props.dataItem?.gst_inc === 1 ? 'success' : 'error'">
                {{ props.dataItem?.gst_inc === 1 ? 'Yes' : 'No' }}
            </Badge>
        </template>
        <template #body-cell-comment="{ props }">
            <!--            <EditableColumn v-model:rowForm="props.rowForm" :data-item="props.dataItem" :store="store" column="comment" inputType="textarea" />-->
            <p v-html="props.dataItem?.comment"></p>
        </template>
        <template #filters>
            <FilterBlockWrapper label="Is Active">
                <EnumSelect
                    enum-class="GalaxyAPI\Enums\StatusEnum"
                    v-model="store.filters.isActive"
                />
            </FilterBlockWrapper>
        </template>
    </AsyncGrid>
    <SetupProviderFacilityForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useSetupProviderFacilityStore } from '@spa/stores/modules/setup-provider-facility/useSetupProviderFacilityStore.js';
import SetupProviderFacilityForm from '@spa/modules/setupproviderfacility/SetupProviderFacilityForm.vue';
import Badge from '@spa/components/badges/Badge.vue';
import EnumSelect from '@spa/components/AsyncComponents/Select/EnumSelect.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import PriceColumn from '@spa/components/AsyncComponents/Grid/Partials/ColumnTemplates/PriceColumn.vue';
import EditableColumn from '@spa/components/AsyncComponents/Grid/Partials/EditableColumn.vue';
import { router } from '@inertiajs/vue3';

const store = useSetupProviderFacilityStore();

const props = defineProps({
    id: Number,
});

const columns = [
    {
        field: 'service_name',
        name: 'service_name',
        title: 'Service Name',
        width: '200px',
        replace: true,
    },
    {
        field: 'category_name',
        name: 'category_name',
        title: 'Category Name',
        width: '200px',
        replace: true,
    },
    {
        field: 'facility_name',
        name: 'facility_name',
        title: 'Facility Name',
        width: '200px',
        replace: true,
    },
    {
        field: 'is_active',
        name: 'is_active',
        title: 'Active',
        width: '100px',
        replace: true,
    },
    {
        field: 'provider_price',
        name: 'provider_price',
        title: 'Provider Price',
        width: '200px',
        replace: true,
    },
    {
        field: 'std_price',
        name: 'std_price',
        title: 'Std Price',
        width: '200px',
        replace: true,
    },
    {
        field: 'gst_inc',
        name: 'gst_inc',
        title: 'GST Inc.',
        width: '100px',
        replace: true,
    },
    {
        field: 'comment',
        name: 'comment',
        title: 'Comment',
        width: '200px',
        replace: true,
    },
];

const initFilters = () => {
    store.filters = {
        provider_id: props.id,
    };
};

onMounted(() => {
    initFilters();
});
</script>
