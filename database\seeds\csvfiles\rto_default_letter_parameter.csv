id,parameter_name,parameter_value,title,created_at,updated_at,created_by,updated_by
1,A<PERSON><PERSON><PERSON><PERSON><PERSON>,{Account<PERSON><PERSON>ger},"{""type"": ""text"",""value"": ""Account Manager Name""}",28-08-2017 07:29,28-08-2017 01:59,78,78
2,agentid,{agentid},"{""type"": ""text"", ""value"": ""Id of agent""}",28-08-2017 07:29,28-08-2017 01:59,78,78
3,<PERSON><PERSON><PERSON>,{AgentName},"{""type"": ""text"", ""value"": ""Name of agent""}",28-08-2017 07:29,28-08-2017 01:59,78,78
4,Campus<PERSON><PERSON>,{CampusName},"{""type"": ""text"", ""value"": ""Name of campus""}",28-08-2017 07:29,28-08-2017 01:59,78,78
5,<PERSON><PERSON><PERSON>,{<PERSON><PERSON><PERSON>},"{""type"": ""text"", ""value"": ""Coe number""}",28-08-2017 07:29,28-08-2017 01:59,78,78
6,<PERSON><PERSON><PERSON>,{CollegeEmail},"{""type"": ""text"", ""value"": ""Email of  college""}",28-08-2017 07:29,28-08-2017 01:59,78,78
7,Country,{Country},"{""type"": ""text"", ""value"": ""Name of Country""}",28-08-2017 07:29,28-08-2017 01:59,78,78
8,Course Attd,{Course Attd},"{""type"": ""text"", ""value"": ""Student Course attendant""}",28-08-2017 07:29,28-08-2017 01:59,78,78
9,CourseAttempt,{CourseAttempt},"{""type"": ""text"", ""value"": ""Student course attempt number""}",28-08-2017 07:29,28-08-2017 01:59,78,78
10,CourseCode,{CourseCode},"{""type"": ""text"", ""value"": ""Course Code""}",28-08-2017 07:29,28-08-2017 01:59,78,78
11,CourseID,{CourseID},"{""type"": ""text"", ""value"": ""Id of course ""}",28-08-2017 07:29,28-08-2017 01:59,78,78
12,CourseName,{CourseName},"{""type"": ""text"", ""value"": ""Name of course""}",28-08-2017 07:29,28-08-2017 01:59,78,78
13,CourseType,{CourseType},"{""type"": ""text"", ""value"": ""Course Type""}",28-08-2017 07:29,28-08-2017 01:59,78,78
14,CricosCode,{CricosCode},"{""type"": ""text"", ""value"": ""Cricos Code""}",28-08-2017 07:29,28-08-2017 01:59,78,78
15,Current Date,{Current Date},"{""type"": ""text"", ""value"": ""Current system date""}",28-08-2017 07:29,28-08-2017 01:59,78,78
16,Dob,{Dob},"{""type"": ""text"", ""value"": ""Student Date of birth""}",28-08-2017 07:29,28-08-2017 01:59,78,78
17,Duration,{Duration},"{""type"": ""text"", ""value"": ""Duration of course""}",28-08-2017 07:29,29-08-2017 13:02,78,78
18,DurationType,{DurationType},"{""type"": ""text"", ""value"": ""Course Duration Type""}",28-08-2017 07:29,28-08-2017 01:59,78,78
19,Email,{Email},"{""type"": ""text"", ""value"": ""Student Email""}",28-08-2017 07:29,28-08-2017 01:59,78,78
20,EntityName,{EntityName},"{""type"": ""text"", ""value"": ""Name of Entity""}",28-08-2017 07:29,28-08-2017 01:59,78,78
21,Fax,{Fax},"{""type"": ""text"", ""value"": ""Fax number""}",28-08-2017 07:29,28-08-2017 01:59,78,78
22,Finishdate,{Finishdate},"{""type"": ""text"", ""value"": ""course finish date""}",28-08-2017 07:29,28-08-2017 01:59,78,78
23,FirstName,{FirstName},"{""type"": ""text"", ""value"": ""student first name""}",28-08-2017 07:29,28-08-2017 01:59,78,78
24,Gender,{Gender},"{""type"": ""text"", ""value"": ""student gender""}",28-08-2017 07:29,28-08-2017 01:59,78,78
25,LastLetterName,{LastLetterName},"{""type"": ""text"", ""value"": ""Last Letter Name""}",28-08-2017 07:29,28-08-2017 01:59,78,78
26,LastLetterSentDate,{LastLetterSentDate},"{""type"": ""text"", ""value"": ""Date of last Letter sent""}",28-08-2017 07:29,28-08-2017 01:59,78,78
27,Lastname,{Lastname},"{""type"": ""text"", ""value"": ""Student Last name""}",28-08-2017 07:29,28-08-2017 01:59,78,78
28,Mobile,{Mobile},"{""type"": ""text"", ""value"": ""Student Mobile""}",28-08-2017 07:29,28-08-2017 01:59,78,78
29,Name,{Name},"{""type"": ""text"", ""value"": ""Name""}",28-08-2017 07:29,28-08-2017 01:59,78,78
30,Nationality,{Nationality},"{""type"": ""text"", ""value"": ""Student Nationality""}",28-08-2017 07:29,28-08-2017 01:59,78,78
31,OfferId,{OfferId},"{""type"": ""text"", ""value"": ""Id of Offer""}",28-08-2017 07:29,28-08-2017 01:59,78,78
32,OfferNo,{OfferNo},"{""type"": ""text"", ""value"": ""No of Offer""}",28-08-2017 07:29,28-08-2017 01:59,78,78
33,Page Break,{Page Break},"{""type"": ""text"", ""value"": ""Break""}",28-08-2017 07:29,28-08-2017 01:59,78,78
34,Passportno,{Passportno},"{""type"": ""text"", ""value"": ""Passport number""}",28-08-2017 07:29,28-08-2017 01:59,78,78
35,Phone,{Phone},"{""type"": ""text"", ""value"": ""Student Phone""}",28-08-2017 07:29,28-08-2017 01:59,78,78
36,PostCode,{PostCode},"{""type"": ""text"", ""value"": ""PostCode""}",28-08-2017 07:29,28-08-2017 01:59,78,78
37,Proj Attd,{Proj Attd},"{""type"": ""text"", ""value"": ""Attd""}",28-08-2017 07:29,28-08-2017 01:59,78,78
38,Startdate,{Startdate},"{""type"": ""text"", ""value"": ""Course Start date""}",28-08-2017 07:29,28-08-2017 01:59,78,78
39,State,{State},"{""type"": ""text"", ""value"": ""State""}",28-08-2017 07:29,28-08-2017 01:59,78,78
40,Status,{Status},"{""type"": ""text"", ""value"": ""Status""}",28-08-2017 07:29,28-08-2017 01:59,78,78
41,StreetAddress,{StreetAddress},"{""type"": ""text"", ""value"": ""Street Address""}",28-08-2017 07:29,28-08-2017 01:59,78,78
42,StudentId,{StudentId},"{""type"": ""text"", ""value"": ""Student Id""}",28-08-2017 07:29,28-08-2017 01:59,78,78
43,Suburb,{Suburb},"{""type"": ""text"", ""value"": ""Suburb""}",28-08-2017 07:29,28-08-2017 01:59,78,78
44,Title,{Title},"{""type"": ""text"", ""value"": ""Title""}",28-08-2017 07:29,28-08-2017 01:59,78,78
45,StreetNumber,{StreetNumber},"{""type"": ""text"", ""value"": ""Street Number""}",26-10-2020 00:00,18-12-2020 21:39,78,78
46,UnitDetail,{UnitDetail},"{""type"": ""text"", ""value"": ""Unit Detail""}",26-10-2020 00:00,18-12-2020 21:39,78,78
47,BuildingName,{BuildingName},"{""type"": ""text"", ""value"": ""Building Name""}",26-10-2020 00:00,18-12-2020 21:39,78,78
48,StudentType,{StudentType},"{""type"": ""text"", ""value"": ""Student Type""}",26-10-2020 15:34,18-12-2020 21:39,1,1
49,CollegeRtoCode,{CollegeRtoCode},"{""type"": ""text"", ""value"": ""College Rto Code""}",26-10-2020 15:34,18-12-2020 21:39,1,1
50,CollegeCircosCode,{CollegeCircosCode},"{""type"": ""text"", ""value"": ""College Circos Code""}",26-10-2020 15:34,18-12-2020 21:39,1,1
51,CollegeLegalName,{CollegeLegalName},"{""type"": ""text"", ""value"": ""College Legal Name""}",26-10-2020 15:34,18-12-2020 21:39,1,1
52,CollegeName,{CollegeName},"{""type"": ""text"", ""value"": ""College Name""}",26-10-2020 15:34,18-12-2020 21:39,1,1
53,CollegeContactPerson,{CollegeContactPerson},"{""type"": ""text"", ""value"": ""College Contact Person""}",26-10-2020 15:34,18-12-2020 21:39,1,1
54,CollegeContactPhone,{CollegeContactPhone},"{""type"": ""text"", ""value"": ""College Contact Phone""}",26-10-2020 15:34,18-12-2020 21:39,1,1
55,CollegeURL,{CollegeURL},"{""type"": ""text"", ""value"": ""College URL""}",26-10-2020 15:34,18-12-2020 21:39,1,1
56,CollegeABN,{CollegeABN},"{""type"": ""text"", ""value"": ""College ABN""}",26-10-2020 15:34,18-12-2020 21:39,1,1
57,CollegeFax,{CollegeFax},"{""type"": ""text"", ""value"": ""College Fax""}",26-10-2020 15:34,18-12-2020 21:39,1,1
58,TeacherFirstName,{TeacherFirstName},"{""type"": ""text"", ""value"": ""Teacher First Name""}",26-10-2020 15:34,18-12-2020 21:39,1,1
59,TeacherLastName,{TeacherLastName},"{""type"": ""text"", ""value"": ""Teacher Last Name""}",26-10-2020 15:34,18-12-2020 21:39,1,1
60,TeacherEmail,{TeacherEmail},"{""type"": ""text"", ""value"": ""Teacher Email""}",26-10-2020 15:34,18-12-2020 21:39,1,1
61,TeacherMobile,{TeacherMobile},"{""type"": ""text"", ""value"": ""Teacher Mobile""}",26-10-2020 15:34,18-12-2020 21:39,1,1
62,AgencyName,{AgencyName},"{""type"": ""text"", ""value"": ""Agency Name""}",26-10-2020 15:34,18-12-2020 21:39,1,1
63,AgentEmail,{AgentEmail},"{""type"": ""text"", ""value"": ""Agent Email""}",26-10-2020 15:34,18-12-2020 21:39,1,1
64,AgentTelephone,{AgentTelephone},"{""type"": ""text"", ""value"": ""Agent Telephone""}",26-10-2020 15:34,18-12-2020 21:39,1,1
65,CollegeLogo,{CollegeLogo},"{""type"": ""text"", ""value"": ""College Logo""}",26-10-2020 15:34,18-12-2020 21:39,1,1
66,EnrolledCourseList,{EnrolledCourseList},"{""type"": ""table"", ""value"": ""<ul><li>ICT50118 : Diploma Of Information Technology (17-08-2020 - 14-02-2022)<li>ICT50120 : Diploma Of Information Technology (12-10-2020 - 11-04-2022)<li>BSB51918 : Diploma Of Leadership and Management (11-01-2021 - 10-01-2022)<li>BSB50420 : Diploma of Leadership and Management (11-01-2021 - 27-12-2021)<li>CHC50113 : Diploma of Early Childhood Education and Care (07-10-2024 - 07-01-2026)</ul>""}",26-10-2020 15:34,18-12-2020 21:39,1,1
67,OfferedCourseList,{OfferedCourseList},"{""type"": ""table"", ""value"": ""<ul><li>ICT50118 : Diploma Of Information Technology (17-08-2020 - 14-02-2022)<li>ICT50120 : Diploma Of Information Technology (12-10-2020 - 11-04-2022)<li>BSB51918 : Diploma Of Leadership and Management (11-01-2021 - 10-01-2022)<li>BSB50420 : Diploma of Leadership and Management (11-01-2021 - 27-12-2021)<li>CHC50113 : Diploma of Early Childhood Education and Care (07-10-2024 - 07-01-2026)</ul>""}",26-10-2020 15:34,18-12-2020 21:39,1,1
68,CourseStartDate,{CourseStartDate},"{""type"": ""text"", ""value"": ""Course Start Date""}",29-03-2024 00:00,17-12-2020 20:53,1,1
69,CourseEndDate,{CourseEndDate},"{""type"": ""text"", ""value"": ""Course End Date""}",29-03-2024 00:00,17-12-2020 20:53,1,1
70,CourseDuration,{CourseDuration},"{""type"": ""text"", ""value"": ""Course Duration""}",29-03-2024 00:00,17-12-2020 20:53,1,1
71,EnrolledCourseListDesign2,{EnrolledCourseListDesign2},"{""type"":""table"",""value"":""<table><thead><tr><th style=border-style:solid;border-width:1px>Course Code<th style=border-style:solid;border-width:1px>Course Title<th style=border-style:solid;border-width:1px>Course Duration<th style=border-style:solid;border-width:1px>Course Status<tbody><tr><td style=border-style:solid;border-width:1px>ICT50118<td style=border-style:solid;border-width:1px>Diploma Of Information Technology<td style=border-style:solid;border-width:1px>78 Weeks<td style=border-style:solid;border-width:1px>Transitioned<tr><td style=border-style:solid;border-width:1px>ICT50120<td style=border-style:solid;border-width:1px>Diploma Of Information Technology<td style=border-style:solid;border-width:1px>78 Weeks<td style=border-style:solid;border-width:1px>Completed<tr><td style=border-style:solid;border-width:1px>BSB51918<td style=border-style:solid;border-width:1px>Diploma Of Leadership and Management<td style=border-style:solid;border-width:1px>52 Weeks<td style=border-style:solid;border-width:1px>Offered<tr><td style=border-style:solid;border-width:1px>BSB50420<td style=border-style:solid;border-width:1px>Diploma of Leadership and Management<td style=border-style:solid;border-width:1px>50 Weeks<td style=border-style:solid;border-width:1px>Completed<tr><td style=border-style:solid;border-width:1px>CHC50113<td style=border-style:solid;border-width:1px>Diploma of Early Childhood Education and Care<td style=border-style:solid;border-width:1px>65 Weeks<td style=border-style:solid;border-width:1px>Suspended</table>""}",26-10-2020 15:34,18-12-2020 21:39,1,1
72,EnrolledUnitList,{EnrolledUnitList},"{""type"":""table"",""value"":""<table><thead><tr><th style=border-style:solid;border-width:1px>Unit<th style=border-style:solid;border-width:1px>Result<tbody><tr><td style=border-style:solid;border-width:1px>BSBPMG513 : Manage project quality<td style=border-style:solid;border-width:1px>CE<tr><td style=border-style:solid;border-width:1px>ICTICT532 : Apply IP, ethics and privacy in ICT environments<td style=border-style:solid;border-width:1px>CE</table>""}",26-10-2020 15:34,18-12-2020 21:39,1,1
73,StudentContactEmail,{StudentContactEmail},"{""type"": ""text"", ""value"": ""Student Contact Email""}",29-03-2024 00:00,17-12-2020 20:53,1,1
74,StudentAlternateEmail,{StudentAlternateEmail},"{""type"": ""text"", ""value"": ""Student Alternate Email""}",29-03-2024 00:00,17-12-2020 20:53,1,1
75,StudentEmergencyEmail,{StudentEmergencyEmail},"{""type"": ""text"", ""value"": ""Student Emergency Email""}",29-03-2024 00:00,17-12-2020 20:53,1,1
76,DeanName,{DeanName},"{""type"": ""text"", ""value"": ""Dean Name""}",02-07-2025 00:00,02-07-2025 00:00,1,1
77,DeanSignature,{DeanSignature},"{""type"": ""text"", ""value"": ""Dean Signature""}",02-07-2025 00:00,02-07-2025 00:00,1,1
78,CollegeSignature,{CollegeSignature},"{""type"": ""text"", ""value"": ""College Signature""}",02-07-2025 00:00,02-07-2025 00:00,1,1
79,AdmissionManagerSignature,{AdmissionManagerSignature},"{""type"": ""text"", ""value"": ""Admission Manager Signature""}",02-07-2025 00:00,02-07-2025 00:00,1,1
80,StudentSupportSignature,{StudentSupportSignature},"{""type"": ""text"", ""value"": ""Student Support Signature""}",02-07-2025 00:00,02-07-2025 00:00,1,1
