<?php

namespace App\Http\Controllers\Login;

use App\Http\Controllers\Controller;
use App\Http\Responses\AfterLoginTrait;
use App\Model\AgentStudentGroupPermission;
use App\Model\CollegeDocument;
use App\Model\CollegeMaterials;
use App\Model\Colleges;
use App\Model\GroupPermission;
use App\Model\PermissionGroups;
use App\Model\SecurityQuestion;
use App\Model\SendMail;
use App\Model\Students;
use App\Model\SurvayNewValue;
use App\Model\SurveyManagerQustionAnswer;
use App\Model\SurveyManagerQustionManagement;
use App\Model\SurveyQuestionHeading;
use App\Model\Teacher;
use App\Model\UserActivityLog;
use App\Model\UserGroupPermission;
use App\Model\UserRoleType;
use App\Repositories\Repository;
use App\Roles;
use App\Users;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Session;
use Spatie\Permission\Exceptions\UnauthorizedException;
use SSO\Facades\SSO;
use Support\Auth\UserGroup;
use Support\DTO\MetaKey;
use Support\Services\UploadService;

class UsersController extends Controller
{
    use AfterLoginTrait;

    public $userModel;

    public function __construct(Users $user)
    {
        $this->userModel = new Repository($user);
    }

    public function login(Request $request)
    {

        if (! empty(Auth()->guard('student')->user())) {

            return redirect(route('student_dashboard'));
        } elseif (! empty(Auth()->guard('teacher')->user())) {

            return redirect(route('teacher_dashboard'));
        } elseif (! empty(Auth()->guard('agent')->user())) {
            $collegeId = Auth::guard('agent')->user()->college_id;
            $objGroupPermission = new AgentStudentGroupPermission;
            $arrAgentPermissionList = $objGroupPermission->getAgentStudentGroupPermissionV2($collegeId, 'agent');
            $request->session()->put('arrAgentPermissionList', $arrAgentPermissionList);

            return redirect(route('student_apply'));
        } elseif (! empty(Auth::user())) {

            return redirect(route('user_dashboard'));
        }

        //        if (Auth::user()) {
        //            return redirect(route('user_dashboard'));
        //        }

        $data['js'] = ['login.js'];
        $data['funinit'] = ['Login.initloginjs()'];

        if ($request->isMethod('post')) {
            $validator = Validator::make($request->all(), [
                // 'email' => 'required|email|max:255',
                'email' => 'required',
                'password' => 'required',
            ]);

            if ($validator->fails()) {
                return redirect(route('user_login'))->withErrors($validator)->withInput();
            }

            $email = $request['email'];
            $password = $request['password'];
            $activity = 'login';

            $objUsers = new Users;
            $arrDataActivity = [];
            $arrDataActivity['ip_address'] = $request->ip();
            $arrDataActivity['category'] = '4';
            $arrDataActivity['event_name'] = 'User Authentication';
            $arrDataActivity['event_detail'] = 'Login Success';

            $objUserActivity = new UserActivityLog;

            // echo "<pre/>";print_r($email);exit;

            if (Auth::attempt(['username' => $email, 'password' => $password, 'role_id' => 12, 'status' => 1])) {
                $request->session()->put('teacher.first_time_reset_pwd', Auth::user()->first_time_reset_pwd);
                $objRtoUsers = $objUsers->editLastActivityDate(auth()->user(), $activity);
                $arrDataActivity['user_id'] = auth()->user()->id;
                $arrDataActivity['role_id'] = auth()->user()->role_id;
                // $request->session()->put('currentRole', auth()->user()->role_id);
                $objUserActivity->logActivity($arrDataActivity);

                $this->checkAuthForUser($activity, $arrDataActivity, $request);
                $this->updateToken($request);

                return redirect(route('teacher_dashboard'));
            } elseif (Auth::guard('student')->attempt(['email' => $email, 'password' => $password, 'role_id' => 13, 'status' => 1])) {

                $request->session()->put('student.first_time_reset_pwd', Auth::guard('student')->user()->first_time_reset_pwd);
                $objRtoUsers = $objUsers->editLastActivityDate(auth()->guard('student')->user(), $activity);
                $arrDataActivity['user_id'] = auth()->guard('student')->user()->id;
                $arrDataActivity['role_id'] = auth()->guard('student')->user()->role_id;
                $request->session()->put('currentRole', auth()->guard('student')->user()->role_id);
                $objUserActivity->logActivity($arrDataActivity);

                return redirect(route('student_dashboard'));
            } elseif (Auth::guard('agent')->attempt(['email' => $email, 'password' => $password, 'role_id' => 14, 'status' => 1])) {

                $request->session()->put('agent.first_time_reset_pwd', Auth::guard('agent')->user()->first_time_reset_pwd);
                $objRtoUsers = $objUsers->editLastActivityDate(auth()->guard('agent')->user(), $activity);
                $arrDataActivity['user_id'] = auth()->guard('agent')->user()->id;
                $arrDataActivity['role_id'] = auth()->guard('agent')->user()->role_id;
                $request->session()->put('currentRole', auth()->guard('agent')->user()->role_id);
                $objUserActivity->logActivity($arrDataActivity);

                return redirect(route('student_apply'));
            } elseif (Auth::guard('agent')->attempt(['username' => $email, 'password' => $password, 'role_id' => 14, 'status' => 1])) {

                $request->session()->put('agent.first_time_reset_pwd', Auth::guard('agent')->user()->first_time_reset_pwd);
                $objRtoUsers = $objUsers->editLastActivityDate(auth()->guard('agent')->user(), $activity);
                $arrDataActivity['user_id'] = auth()->guard('agent')->user()->id;
                $arrDataActivity['role_id'] = auth()->guard('agent')->user()->role_id;
                $request->session()->put('currentRole', auth()->guard('agent')->user()->role_id);
                $objUserActivity->logActivity($arrDataActivity);

                return redirect(route('student_apply'));
            } elseif (Auth::guard('agentstaff')->attempt(['username' => $email, 'password' => $password, 'role_id' => 15, 'status' => 1])) {

                $request->session()->put('agent.first_time_reset_pwd', Auth::guard('agentstaff')->user()->first_time_reset_pwd);
                $objRtoUsers = $objUsers->editLastActivityDate(auth()->guard('agentstaff')->user(), $activity);
                $arrDataActivity['user_id'] = auth()->guard('agentstaff')->user()->id;
                $arrDataActivity['role_id'] = auth()->guard('agentstaff')->user()->role_id;
                $request->session()->put('currentRole', auth()->guard('agentstaff')->user()->role_id);
                $objUserActivity->logActivity($arrDataActivity);

                return redirect(route('agentstaff-student-online-application'));
            } elseif (Auth::guard('agentstaff')->attempt(['email' => $email, 'password' => $password, 'role_id' => 15, 'status' => 1])) {

                $request->session()->put('agent.first_time_reset_pwd', Auth::guard('agentstaff')->user()->first_time_reset_pwd);
                $objRtoUsers = $objUsers->editLastActivityDate(auth()->guard('agentstaff')->user(), $activity);
                $arrDataActivity['user_id'] = auth()->guard('agentstaff')->user()->id;
                $arrDataActivity['role_id'] = auth()->guard('agentstaff')->user()->role_id;
                $request->session()->put('currentRole', auth()->guard('agentstaff')->user()->role_id);
                $objUserActivity->logActivity($arrDataActivity);

                return redirect(route('agentstaff-student-online-application'));
            } elseif (Auth::attempt(['email' => $email, 'password' => $password, 'status' => 1])) {

                $this->checkAuthForUser($activity, $arrDataActivity, $request);
                $this->updateToken($request);
                $role = $request->session()->get('currentRole');
                if ($role == 13) {
                    Auth::logout();
                    Auth::guard('student')->logout();
                    $request->session()->flash('session_error', 'Student login with valid Username & Password.');
                } else {
                    return redirect(route('user_dashboard'));
                }
            } elseif (Auth::attempt(['username' => $email, 'password' => $password, 'role_id' => 9, 'status' => 1])) {

                $this->checkAuthForUser($activity, $arrDataActivity, $request);
                $this->updateToken($request);

                return redirect(route('user_dashboard'));
            } else {
                $objUsers = new Users;
                $wrongCount = $objUsers->checkEmailOrWrongCount($email);
                // print_r($objRtoUsers);exit;
                if ($wrongCount == 0) {
                    $error_message = 'Incorrect Email or Password.';
                } elseif ($wrongCount < 5) {
                    $wrongCount = 5 - $wrongCount;
                    $error_message = 'Invalid Email or password. You are left with '.$wrongCount.' more attempts.';
                } else {
                    $error_message = 'Please admin contact to unlock your account.';
                }

                $request->session()->flash('session_error', $error_message);
            }
        }

        return view('login.login', $data);
    }

    public function updateToken(Request $request)
    {

        $id = $request->user()->id;
        $objUsers = Users::find($id);
        $objUsers->api_token = $request->user()->createToken('auth_token')->plainTextToken;
        $objUsers->save();
    }

    public function forgotPassword(Request $request)
    {

        $data['js'] = ['login.js'];
        $data['funinit'] = ['Login.initForgotPassword()'];

        if ($request->isMethod('post')) {

            $validations = [
                'email' => 'required|email|max:255',
            ];
            $validator = Validator::make($request->all(), $validations);

            if ($validator->fails()) {
                return redirect(route('forgot_password'))->withErrors($validator)->withInput();
            }

            if (! empty($request->input('email'))) {

                $email = $request->input('email');

                $objUsers = new Users;
                $objRtoUsers = $objUsers->checkUsersEmail($email);

                if (empty($objRtoUsers[0])) {
                    $request->session()->flash('session_success', 'User not found.');

                    return redirect(route('user_login'));
                }

                $result = $this->sendMailResetPassword($objRtoUsers);

                if ($result) {
                    $request->session()->flash('session_success', 'Check your inbox for reset your password.');

                    return redirect(route('user_login'));
                } else {
                    $request->session()->flash('session_error', 'Something will be wrong.Please try again.');

                    return redirect(route('forgot_password'));
                }
            }
        }

        return view('login.forgot_password', $data);
    }

    public function resetPassword($param, Request $request)
    {
        // $result = Users::find($userId);
        $paramData = json_decode(base64_decode($param));

        $checkUserData = Users::where('email', $paramData->email)->where('token', $paramData->token)->first();

        $data['userDetails'] = $checkUserData;
        $data['js'] = ['login.js'];
        $data['funinit'] = ['Login.initResetPassword()'];

        if ($request->isMethod('post')) {

            $validator = Validator::make($request->all(), [
                'password' => 'required|min:8|max:16',
                'confirm_password' => 'required|same:password',
            ]);
            if ($validator->fails()) {
                return redirect(route('reset-password', $param))->withErrors($validator)->withInput();
            }

            $password = $request['password'];
            $confirm_password = $request['confirm_password'];

            if ($password == $confirm_password) {
                $objUsers = new Users;
                $objRtoUsers = $objUsers->changeResetPassword($request);
                $request->session()->flash('session_success', 'Password successfully changed');

                return redirect(route('user_login'));
            } else {
                $request->session()->flash('session_error', 'New password or confirm new password does not match');
                // return redirect(route('reset-password', $param));
            }
        }

        if ($checkUserData) {
            return view('login.reset_password', $data);
        } else {
            $request->session()->flash('session_error', 'You are already used this link.');

            return redirect(route('forgot_password'));
        }
    }

    public function logout(Request $request)
    {
        $ssoRedirect = null;
        if (galaxy_feature('sso') && SSO::getAccessTokenFromSession()) {
            $ssoRedirect = SSO::sessionLogoutUrl();
        }
        $user = $request->user();

        Auth::logout();
        /* IF ADMIN WAS IMPERSONATING, LOG OUT AND RETURN TO ADMIN DASHBOARD */
        if ($id = session()->get(MetaKey::ADMIN_IMPERSONATING)) {
            info('admin impersonating', [$id]);
            // $user = auth()->user();

            $adminUser = Users::find(decryptIt($id));
            if ($adminUser) {
                activity('auth')
                    ->causedBy($adminUser)
                    ->performedOn($user)
                    ->log('User Impersonation Ended '.$user->name);
                session()->forget(MetaKey::ADMIN_IMPERSONATING);
                auth()->loginUsingId($adminUser->id);
                session()->flash('success', 'You have stopped impersonating '.$user->name.'.');

                return $this->logAndRedirect($request);
            }
        }
        // dd($ssoRedirect);
        if ($request->hasSession()) {
            $request->session()->invalidate();
            $request->session()->regenerateToken();
        }
        // Auth::guard('student')->logout();
        // Auth::guard('agent')->logout();
        // Auth::guard('agentstaff')->logout();
        if (! empty($this->loginUser)) {
            $activity = 'logout';
            $objUsers = new Users;
            $objUsers->editLastActivityDate($this->loginUser, $activity);
            $request->session()->flush();

            Auth::logout();
            Auth::guard('student')->logout();
        } /* else {
            $request->session()->flash('session_error', 'You are already logged out. Please sign in.');
        } */
        $request->session()->flash('session_success', 'You have successfully logged out.');
        if ($ssoRedirect) {
            // dd($ssoRedirect);
            try {
                return redirect()->away($ssoRedirect);
            } catch (\Exception $e) {
                return redirect()->route('user_login');
            }
        }

        return redirect()->route('user_login');
        // dd("HERE");
    }

    public function checkAuthForUser($activity, $arrDataActivity, $request)
    {

        $userId = Auth::user()->id;
        $roleId = Auth::user()->role_id;
        $request->session()->put('currentRole', $roleId);

        $objUsers = new Users;
        $objUsers->editLastActivityDate(Auth::user(), $activity);

        $arrDataActivity['user_id'] = $userId;
        $arrDataActivity['role_id'] = $roleId;

        $objUserActivity = new UserActivityLog;
        $objUserActivity->logActivity($arrDataActivity);

        $this->getUserRoleList($userId, $request);

        return true;
    }

    public function getUserRoleList($userId, $request)
    {

        $objRoleType = new UserRoleType;
        $userRoleArr = $objRoleType->getUserRoleTypeList($userId);
        $request->session()->put('userRole', $userRoleArr);

        return true;
    }

    public function ajaxAction(Request $request)
    {
        $action = $request->input('action');
        switch ($action) {
            case 'getUserQuestion':
                $username = $request->input('username');
                $objUsers = new Users;
                $result = $objUsers->getUserQuestion($username);

                echo json_encode($result);
                break;
        }
        exit;
    }

    public function dashboard(Request $request)
    {

        $collegeId = Auth::user()->college_id;
        $userId = Auth::user()->id;
        $userRole = Session::get('userRole');
        $currentRole = Session::get('currentRole');

        $objPermission = new PermissionGroups;
        $arrPermissionList = $objPermission->getPermissionNameV2();

        if (Session::get('currentRole') != 9) {
            $objUserGroupPermission = new UserGroupPermission;
            $arrGroupPermission = $objUserGroupPermission->getUserGroupPermissionV2($collegeId, $userId);
        } else {
            $objGroupPermission = new GroupPermission;
            $arrGroupPermission = $objGroupPermission->getGroupPermission();
        }

        // dd($arrGroupPermission); exit();

        for ($i = 0; $i < count($arrGroupPermission); $i++) {
            $arrPermissionList[$arrGroupPermission[$i]['permission_id']] = 'yes';
        }

        $request->session()->put('arrPermissionList', $arrPermissionList);
        $request->session()->get('arrPermissionList', 'default');

        $data['pagetitle'] = 'Dashboard';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = [];
        $data['js'] = [];
        $data['funinit'] = [];
        $data['header'] = [
            'title' => 'Dashboard Version 2.0',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Dashboard' => '',
            ],
        ];
        $data['activateValue'] = 'Dashboard';
        $data['mainmenu'] = 'dashboard';
        if ($currentRole == '1') {
            return view('frontend.users.dashboard', $data);
        } elseif ($currentRole == '2') {
            return view('frontend.users.dashboard', $data);
        } elseif ($currentRole == '3' || $currentRole == '4' || $currentRole == '5' || $currentRole == '6') {
            return view('frontend.users.dashboard', $data);
        } elseif ($currentRole == '7') {
            return view('frontend.users.dashboard', $data);
        } elseif ($currentRole == '8') {
            return view('frontend.users.dashboard', $data);
        } elseif ($currentRole == '9') {
            return redirect(route('admin-dashboard'));

            return view('frontend.users.dashboard', $data);
        } elseif ($currentRole == '10') {
            return redirect(route('view-staff-task'));

            return view('frontend.dashboard.staff_dashboard', $data);
        } elseif ($currentRole == '11') {
            return view('frontend.users.dashboard', $data);
        } elseif ($currentRole == '12') {
            return redirect(route('teacher_dashboard'));
        }
        // return view('frontend.users.dashboard', $data);
    }

    public function userPage()
    {
        $user = Auth::user()->email;

        return view('users.userpage', compact('user'));
    }

    public function profile(Request $request)
    {
        if (! Auth::user()) {
            return redirect()->route('user_login');
        }
        $userId = Auth::user()->id;
        $collegeId = Auth::user()->college_id;

        if ($request->isMethod('post')) {
            $validator = Validator::make($request->all(), [
                'name' => 'required',
                'username' => 'required|unique:rto_users,"username",'.$userId,
                'email' => 'required|unique:rto_users,"email",'.$userId,
                'phone' => 'required',
                'mobile' => 'required',
            ]);

            if ($validator->fails()) {
                return redirect(route('user_profile'))->withErrors($validator)->withInput();
            }

            $objUsers = new Users;
            $objRtoUsers = $objUsers->editUsers($collegeId, $request, $userId);
            $request->session()->flash('session_success', 'Your profile is changed');

            return redirect(route('user_profile'));
        }

        $objSecurityQuestion = new SecurityQuestion;
        $objArrSecurityQuestion = $objSecurityQuestion->getSecurityQuestion();

        $userFinalData = Users::where('id', $userId)->where('college_id', $collegeId)->get();
        $roles = Roles::pluck('role_name', 'id');
        $collegeName = Colleges::pluck('college_name', 'id');

        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['profile.js'];
        $data['funinit'] = ['Profile.initEditProfile()', 'Profile.initEditPassword()', 'Profile.initResetQuestion()'];
        $data['header'] = [
            'title' => 'Edit Profile',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Edit profile' => '',
            ],
        ];

        $data['pagetitle'] = 'Profile';
        $data['activateValue'] = 'Home';
        $data['roles'] = $roles;
        $data['arrSecurityQuestion'] = $objArrSecurityQuestion;
        $data['userData'] = $userFinalData;
        $data['collegeName'] = $collegeName;
        $data['id'] = $userFinalData[0]->id;
        $data['mainmenu'] = 'administration';

        return view('frontend.users.profile', $data);
    }

    public function passwordChange(Request $request)
    {
        $userId = Auth::user()->id;
        $collegeId = Auth::user()->college_id;
        if ($request->isMethod('post')) {

            $password_data = Auth::user()->password;
            $password = $request['password'];

            $validator = Validator::make($request->all(), [
                'password' => 'required',
                'new_password' => 'required|min:8|max:16',
                'confirm_password' => 'required|same:new_password',
            ]);
            if ($validator->fails()) {
                return redirect(route('user_profile'))->withErrors($validator)->withInput();
            }
            if (Hash::check($password, $password_data)) {
                $objUsers = new Users;
                $objRtoUsers = $objUsers->editPassword($collegeId, $request, $userId);
                $request->session()->flash('session_success', 'Password successfully change');

                return redirect(route('user_profile'));
            } else {
                $request->session()->flash('session_error', 'Old password does not match');

                return redirect(route('user_profile'));
            }
        }
    }

    public function resetQuestion(Request $request)
    {

        $userId = Auth::user()->id;
        $collegeId = Auth::user()->college_id;

        if ($request->isMethod('post')) {
            $password_data = Auth::user()->password;
            $password = $request['password'];

            $validator = Validator::make($request->all(), [
                'password' => 'required',
                'question' => 'required',
                'answer' => 'required',
            ]);
            if ($validator->fails()) {
                return redirect(route('user_profile'))->withErrors($validator)->withInput();
            }
            if (Hash::check($password, $password_data)) {
                $objUsers = new Users;
                $objRtoUsers = $objUsers->editSecurityQue($collegeId, $request, $userId);
                $request->session()->flash('session_success', 'Security Question-Answer SuccessFully Changed');

                return redirect(route('user_profile'));
            } else {
                $request->session()->flash('session_error', 'Current Password does not match');

                return redirect(route('user_profile'));
            }
        }
    }

    /* Start for Student Login */

    public function studentDashboard(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $objGroupPermission = new AgentStudentGroupPermission;
        $arrStudentPermissionList = $objGroupPermission->getAgentStudentGroupPermissionV3($collegeId, 'student');

        $request->session()->put('arrStudentPermissionList', $arrStudentPermissionList);
        $request->session()->get('arrStudentPermissionList', 'default');
        $data['pwd_status'] = $request->session()->get('student.first_time_reset_pwd');
        $request->session()->put('student.first_time_reset_pwd', 1);
        $data['pagetitle'] = 'Dashboard';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = [];
        $data['js'] = ['profile.js'];
        $data['funinit'] = ['Profile.initDashboard()'];
        $data['activateValue'] = 'Dashboard';
        $data['header'] = [
            'title' => 'Dashboard Version 2.0',
            'breadcrumb' => [
                'Home' => route('student_dashboard'),
                'Dashboard' => '',
            ],
        ];
        $data['name'] = Auth::user()->name;

        $data['mainmenu'] = 'dashboard';

        return view('student.profile.dashboard', $data);
    }

    public function studentProfile(Request $request)
    {

        $userId = Auth::user()->id;
        $collegeId = Auth::user()->college_id;

        if ($request->isMethod('post')) {
            $validator = Validator::make($request->all(), [
                'name' => 'required',
                'username' => 'required|unique:rto_users,"username",'.$userId,
                'email' => 'required|unique:rto_users,"email",'.$userId,
                'phone' => 'required',
                'mobile' => 'required',
            ]);

            if ($validator->fails()) {
                return redirect(route('student_profile'))->withErrors($validator)->withInput();
            }

            $objUsers = new Users;
            $result = $objUsers->editUsers($collegeId, $request, $userId);
            $objStudents = new Students;
            $result = $objStudents->editStudentsFromStudentProfile($collegeId, $request);
            $request->session()->flash('session_success', 'Your profile is updated successfully.');

            return redirect(route('student_profile'));
        }

        $userFinalData = Users::where('id', $userId)->where('college_id', $collegeId)->get();
        $roles = Roles::pluck('role_name', 'id');
        $collegeName = Colleges::pluck('college_name', 'id');
        $selected_roles = null;
        $objSecurityQuestion = new SecurityQuestion;
        $objArrSecurityQuestion = $objSecurityQuestion->getSecurityQuestion();

        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['profile.js'];
        $data['funinit'] = ['Profile.initEditProfile()', 'Profile.initEditPassword()', 'Profile.initResetQuestion()'];
        $data['pagetitle'] = 'Profile';
        $data['activateValue'] = 'Home';
        $data['header'] = [
            'title' => 'Edit Profile',
            'breadcrumb' => [
                'Home' => route('student_dashboard'),
                'Edit Profile' => '',
            ],
        ];
        $data['roles'] = $roles;
        $data['userData'] = $userFinalData;
        $data['arrSecurityQuestion'] = $objArrSecurityQuestion;
        $data['collegeName'] = $collegeName;
        $data['id'] = $userFinalData[0]->id;
        $data['mainmenu'] = 'profile';

        return view('student.profile.edit-profile', $data);
    }

    public function studentPasswordChange(Request $request)
    {
        $userId = Auth::user()->id;
        $collegeId = Auth::user()->college_id;
        if ($request->isMethod('post')) {

            $password_data = Auth::user()->password;
            $password = $request['password'];

            $validator = Validator::make($request->all(), [
                'password' => 'required',
                'new_password' => 'required|min:8|max:16',
                'confirm_password' => 'required|same:new_password',
            ]);
            if ($validator->fails()) {
                return redirect(route('student_profile'))->withErrors($validator)->withInput();
            }
            if (Hash::check($password, $password_data)) {
                $objUsers = new Users;
                $objUsers->editPassword($collegeId, $request, $userId);
                $request->session()->flash('session_success', 'Password changed successfully');

                return redirect(route('student_profile'));
            } else {
                $request->session()->flash('session_error', 'Old Password does not match');

                return redirect(route('student_profile'));
            }
        }
    }

    public function studentResetQuestion(Request $request)
    {

        $userId = Auth::guard('student')->user()->id;
        $collegeId = Auth::guard('student')->user()->college_id;

        if ($request->isMethod('post')) {
            $password_data = Auth::guard('student')->user()->password;
            $password = $request['password'];

            $validator = Validator::make($request->all(), [
                'password' => 'required',
                'question' => 'required',
                'answer' => 'required',
            ]);
            if ($validator->fails()) {
                return redirect(route('student_profile'))->withErrors($validator)->withInput();
            }
            if (Hash::check($password, $password_data)) {
                $objUsers = new Users;
                $objUsers->editSecurityQue($collegeId, $request, $userId);
                $request->session()->flash('session_success', 'Security Question-Answer SuccessFully Changed');

                return redirect(route('student_profile'));
            } else {
                $request->session()->flash('session_error', 'Current Password does not match');

                return redirect(route('student_profile'));
            }
        }
    }

    /* ENd for Student Login */

    /* Start for Teacher Login */

    public function teacherDashboard(Request $request)
    {

        $data['pagetitle'] = 'Dashboard';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = [];
        $data['js'] = ['profile.js'];
        $data['funinit'] = ['Profile.initDashboard()'];
        $data['activateValue'] = 'Dashboard';
        $data['header'] = [
            'title' => 'Dashboard Version 2.0',
            'breadcrumb' => [
                'Home' => route('teacher_dashboard'),
                'Dashboard' => '',
            ],
        ];
        $data['name'] = Auth::user()->name;
        $data['pwd_status'] = Session::get('teacher.first_time_reset_pwd');
        $request->session()->put('teacher.first_time_reset_pwd', 1);

        $data['mainmenu'] = 'dashboard';

        return view('teacher.profile.dashboard', $data);
    }

    public function teacherProfile(Request $request)
    {

        $userId = Auth::user()->id;
        $collegeId = Auth::user()->college_id;

        $objSecurityQuestion = new SecurityQuestion;
        $objArrSecurityQuestion = $objSecurityQuestion->getSecurityQuestion();

        if ($request->isMethod('post')) {
            $validator = Validator::make($request->all(), [
                'name' => 'required',
                'username' => 'required|unique:rto_users,"username",'.$userId,
                'email' => 'required|unique:rto_users,"email",'.$userId,
                'phone' => 'required',
                'mobile' => 'required',
            ]);

            if ($validator->fails()) {
                return redirect(route('teacher_profile'))->withErrors($validator)->withInput();
            }

            $objUsers = new Users;
            $result = $objUsers->editUsers($collegeId, $request, $userId);
            $objRtoTeacher = new Teacher;
            $result = $objRtoTeacher->editTeacherFromTeacherProfile($collegeId, $request, $userId);
            if ($result) {
                $request->session()->flash('session_success', 'Your profile is updated successfully');
            }

            return redirect(route('teacher_profile'));
        }

        $userFinalData = Users::where('id', $userId)->where('college_id', $collegeId)->get();
        $roles = Roles::pluck('role_name', 'id');
        $collegeName = Colleges::pluck('college_name', 'id');
        $selected_roles = null;

        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['profile.js'];
        $data['funinit'] = ['Profile.initEditProfile()', 'Profile.initEditPassword()', 'Profile.initResetQuestion()'];
        $data['header'] = [
            'title' => 'Edit Profile',
            'breadcrumb' => [
                'Home' => route('teacher_dashboard'),
                'Edit Profile' => '',
            ],
        ];
        $data['pagetitle'] = 'Profile';
        $data['activateValue'] = 'Home';
        $data['arrSecurityQuestion'] = $objArrSecurityQuestion;
        $data['roles'] = $roles;
        $data['userData'] = $userFinalData;
        $data['collegeName'] = $collegeName;
        $data['id'] = $userFinalData[0]->id;
        $data['mainmenu'] = 'profile';

        return view('teacher.profile.edit-profile', $data);
    }

    public function agentProfile(Request $request)
    {

        $userId = Auth::guard()->user()->id;
        $collegeId = Auth::guard()->user()->college_id;
        $objSecurityQuestion = new SecurityQuestion;
        $objArrSecurityQuestion = $objSecurityQuestion->getSecurityQuestion();

        if ($request->isMethod('post')) {
            $validator = Validator::make($request->all(), [
                'name' => 'required',
                'username' => 'required|unique:rto_users,"username",'.$userId,
                'phone' => 'required',
                'mobile' => 'required',
            ]);

            if ($validator->fails()) {
                return redirect(route('agent_profile'))->withErrors($validator)->withInput();
            }

            $objUsers = new Users;
            $result = $objUsers->editUsers($collegeId, $request, $userId);
            if ($result) {
                $request->session()->flash('session_success', 'Your Profile is Changed');
            }

            return redirect(route('agent_profile'));
        }

        $userFinalData = Users::where('id', $userId)->where('college_id', $collegeId)->get();
        $roles = Roles::pluck('role_name', 'id');
        $collegeName = Colleges::pluck('college_name', 'id');
        $selected_roles = null;

        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['profile.js'];
        $data['funinit'] = ['Profile.initEditProfile()', 'Profile.initEditPassword()', 'Profile.initResetQuestion()'];
        $data['header'] = [
            'title' => 'Edit Profile',
            'breadcrumb' => [
                'Home' => route('agent-dashboard'),
                'Profile' => '',
                'Edit Profile' => '',
            ],
        ];
        $data['pagetitle'] = 'Profile';
        $data['activateValue'] = 'Home';
        $data['roles'] = $roles;
        $data['userData'] = $userFinalData;
        $data['collegeName'] = $collegeName;
        $data['id'] = $userFinalData[0]->id;
        $data['mainmenu'] = 'profile';
        $data['arrSecurityQuestion'] = $objArrSecurityQuestion;

        return view('agent.profile.edit-profile', $data);
    }

    public function teacherPasswordChange(Request $request)
    {
        $userId = Auth::user()->id;
        $collegeId = Auth::user()->college_id;
        if ($request->isMethod('post')) {

            $routeName = $request->input('current_route');
            $password_data = Auth::user()->password;
            $password = $request['password'];

            $validator = Validator::make($request->all(), [
                'password' => 'required',
                'new_password' => 'required|min:8|max:16',
                'confirm_password' => 'required|same:new_password',
            ]);
            if ($validator->fails()) {
                return redirect(route($routeName))->withErrors($validator)->withInput();
            }
            if (Hash::check($password, $password_data)) {
                $objUsers = new Users;
                $objUsers->editPassword($collegeId, $request, $userId);
                $request->session()->flash('session_success', 'Password successfully change');

                return redirect(route($routeName));
            } else {
                $request->session()->flash('session_error', 'Old password does not match');

                return redirect(route($routeName));
            }
        }
    }

    public function agentPasswordChange(Request $request)
    {
        $userId = auth()->user()->id;
        $collegeId = auth()->user()->college_id;
        if ($request->isMethod('post')) {

            $password_data = auth()->user()->password;
            $password = $request['password'];

            $validator = Validator::make($request->all(), [
                'password' => 'required',
                'new_password' => 'required|min:8|max:16',
                'confirm_password' => 'required|same:new_password',
            ]);
            if ($validator->fails()) {
                return redirect(route('agent_profile'))->withErrors($validator)->withInput();
            }
            if (Hash::check($password, $password_data)) {
                $objUsers = new Users;
                $objUsers->editPassword($collegeId, $request, $userId);
                $request->session()->flash('session_success', 'Password successFully Change');

                return redirect(route('agent_profile'));
            } else {
                $request->session()->flash('session_error', 'Old password does not match');

                return redirect(route('agent_profile'));
            }
        }
    }

    public function teacherResetQuestion(Request $request)
    {

        $userId = Auth::user()->id;
        $collegeId = Auth::user()->college_id;

        if ($request->isMethod('post')) {
            $password_data = Auth::user()->password;
            $password = $request['password'];

            $validator = Validator::make($request->all(), [
                'password' => 'required',
                'question' => 'required',
                'answer' => 'required',
            ]);
            if ($validator->fails()) {
                return redirect(route('teacher-reset-question'))->withErrors($validator)->withInput();
            }
            if (Hash::check($password, $password_data)) {
                $objUsers = new Users;
                $objUsers->editSecurityQue($collegeId, $request, $userId);
                $request->session()->flash('session_success', 'Security Question-Answer SuccessFully Changed');

                return redirect(route('teacher-reset-question'));
            } else {
                $request->session()->flash('session_error', 'Current Password does not match');

                return redirect(route('teacher-reset-question'));
            }
        }
    }

    public function agentResetQuestion(Request $request)
    {

        $userId = auth()->user()->id;
        $collegeId = auth()->user()->college_id;

        if ($request->isMethod('post')) {
            $password_data = auth()->user()->password;
            $password = $request['password'];

            $validator = Validator::make($request->all(), [
                'password' => 'required',
                'question' => 'required',
                'answer' => 'required',
            ]);
            if ($validator->fails()) {
                return redirect(route('agent_profile'))->withErrors($validator)->withInput();
            }
            if (Hash::check($password, $password_data)) {
                $objUsers = new Users;
                $objUsers->editSecurityQue($collegeId, $request, $userId);
                $request->session()->flash('session_success', 'Security Question-Answer SuccessFully Changed');

                return redirect(route('agent_profile'));
            } else {
                $request->session()->flash('session_error', 'Current Password does not match');

                return redirect(route('agent_profile'));
            }
        }
    }

    /* ENd for Teacher Login */

    public function agentDashboard(Request $request)
    {
        // echo "fafa"; exit();
        // return redirect(route('student_apply'));

        $collegeId = Auth::user()->college_id;

        $objGroupPermission = new AgentStudentGroupPermission;
        $arrAgentPermissionList = $objGroupPermission->getAgentStudentGroupPermissionV2($collegeId, 'agent');
        $request->session()->put('arrAgentPermissionList', $arrAgentPermissionList);
        // $value = $request->session()->get('arrAgentPermissionList', 'default');
        // echo "<pre>";print_r($value);exit;

        $data['pagetitle'] = 'Dashboard';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = [];
        $data['js'] = ['profile.js'];
        $data['funinit'] = ['Profile.initDashboard()'];
        $data['activateValue'] = 'Dashboard';
        $data['header'] = [
            'title' => 'Dashboard Version 2.0',
            'breadcrumb' => [
                'Home' => route('agent-dashboard'),
                'Dashboard' => '',
            ],
        ];
        $data['name'] = Auth::user()->name;
        $data['pwd_status'] = Session::get('agent.first_time_reset_pwd');
        $data['mainmenu'] = 'dashboard';

        return view('agent.profile.dashboard', $data);
    }

    public function sessionUpdate($roleId, Request $request)
    {
        if ($roleId > 0) {
            $request->session()->put('currentRole', $roleId);
            if ($roleId == 12) {
                // $this->redirectForTeacherLogin($request);
            }
        }

        return redirect(route('user_dashboard'));
    }

    public function submitSurvey($encodeId, Request $request)
    {

        $decodeId = explode('/', base64_decode($encodeId));

        $collegeId = $decodeId[0];
        $formId = $decodeId[1];
        $entryType = $decodeId[2];
        $entryId = $decodeId[3];

        $objQuestioHeading = new SurveyQuestionHeading;
        $arrQuestionHeadingList = $objQuestioHeading->getQuestionHeadinglist($collegeId);

        $objQuestioManager = new SurveyManagerQustionManagement;
        $arrQuestionAnswerList = $objQuestioManager->getSubmitSurvayList($arrQuestionHeadingList, $collegeId, $formId);

        $objNewValue = new SurvayNewValue;
        $arrFormData = $objNewValue->getFormNameByCategory($formId);

        if ($request->isMethod('post')) {

            $dataArr = [
                'collegeId' => $collegeId,
                'formId' => $formId,
                'entryType' => $entryType,
                'entryId' => $entryId,
                'formId' => $formId,
                'formRole' => $arrFormData->form_access_roll,
                'userId' => $entryId,
            ];

            $objQuestionAnswer = new SurveyManagerQustionAnswer;
            $result = $objQuestionAnswer->saveQuestionAndAnswer($request, $dataArr);
            if ($result == 'success') {
                $request->session()->flash('session_success', 'Survey successfully submiited. Thank you for your online evaluation submission.');
            } elseif ($result == 'submitted') {
                $request->session()->flash('session_error', 'This survey information already submiited.');
            } else {
                $request->session()->flash('session_error', 'Something will be Wrong. Please Try again.');
            }

            return redirect(route('submit-survey', ['id' => $encodeId]));
        }

        $data['arrFormData'] = $arrFormData;
        $data['arrQuestionAnswerList'] = $arrQuestionAnswerList;
        $data['pagetitle'] = 'Survey Manager - Question Management';
        $data['plugincss'] = [];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['survey.js'];
        $data['funinit'] = ['survey.initSubmitQuestion()'];

        return view('frontend.survey.submit-question', $data);
    }

    public function downloadFile(Request $request)
    {

        $primaryId = base64_decode($request->id);
        if (empty($primaryId)) {
            $request->session()->flash('session_error', 'File does not exist.');

            return redirect()->back();
        }
        $objCollegeMaterials = CollegeMaterials::find($primaryId);

        $folder_path = $objCollegeMaterials->file_path;
        $file_name = $objCollegeMaterials->folder_or_file;
        $original_name = $objCollegeMaterials->original_name;

        $path = public_path().$folder_path.$file_name;

        return redirect()->away(UploadService::download($folder_path.$file_name, $original_name));
    }

    public function downloadClgDoc(Request $request)
    {

        $primaryId = base64_decode($request->id);

        $objCollegeDocument = CollegeDocument::find($primaryId);

        $folder_path = $objCollegeDocument->file_path;
        $file_name = $objCollegeDocument->folder_or_file;
        $original_name = $objCollegeDocument->original_name;
        $path = public_path().$folder_path.$file_name;

        return response()->download($path, $original_name);
    }

    public function sendMail($postData)
    {

        $objUsers = new Users;
        $resultArr = $objUsers->duplicateUserNameCheck($postData['username']);
        $str = substr(str_shuffle(str_repeat('0123456789abcdefghijklmnopqrstuvwxyz', 5)), 0, 5);
        $newPassword = time().$str;
        // echo $newPassword;exit;

        $dataArray = [
            'name' => $resultArr[0]['name'],
            'username' => $resultArr[0]['username'],
            'email' => $resultArr[0]['email'],
            'new_pwd' => $newPassword,
        ];

        $mailData = [
            'from' => env('MAIL_USERNAME'),
            'fromName' => env('MAIL_NAME'),
            'to' => $resultArr[0]['email'],
            'cc' => '',
            'bcc' => '',
            'page' => 'mail.forgot-password',
            'subject' => 'New password',
            'attachFile' => [],
            'data' => ['content' => $dataArray],
        ];
        $sendMail = new SendMail;
        $mailStatus = $sendMail->sendSmtpMail($mailData);

        if ($mailStatus == 'success') {
            $updatePass = $objUsers->updateNewPassword($postData['username'], $newPassword);
            $updateStatus = ($updatePass) ? true : false;
        } else {
            $updateStatus = false;
        }

        return $updateStatus;
    }

    public function sendMailResetPassword($UserData)
    {

        $token = Str::random(40);

        $objUser = Users::find($UserData['0']->id);
        $objUser->token = $token;
        $objUser->save();

        $param = base64_encode(json_encode(['email' => $UserData[0]->email, 'token' => $token]));

        $dataArray = [
            'name' => $UserData[0]['name'],
            'link' => env('APP_URL').'/reset-password/'.$param,
        ];

        $mailData = [
            'from' => env('MAIL_USERNAME'),
            'fromName' => env('MAIL_NAME'),
            'to' => $UserData[0]->email,
            'cc' => '',
            'page' => 'mail.reset-password',
            'subject' => 'Reset Password',
            'attachFile' => [],
            'data' => ['content' => $dataArray],
        ];
        //  print_r($mailData);exit;
        $sendMail = new SendMail;
        $mailStatus = $sendMail->sendSmtpMailForAdmin($mailData);

        return $mailStatus;
    }

    public function switchRole(Request $request, $roleId)
    {
        // $this->validate($request, [
        //     'role_id' => 'required'
        // ]);

        try {

            $roleId = decryptIt($roleId);

            $user = auth()->user();

            if ($user->isSadmin()) {
                throw new UnauthorizedException('Admin user cannot switch role. They can directly impersonate other user.');
            }

            $role = $user->roles()->where('role_id', $roleId)->first();
            if (! $role) {
                throw new UnauthorizedException('Role not found for this user.');
            }

            $request->session()->put(\Support\Auth\UserGroup::SESSION_KEY, $roleId);

            /* IF user is teacher and switching to staff role change the role */
            if ($user->isTeacher()) {
                if ($role->type == UserGroup::TYPE_STAFFS) {
                    $request->session()->put('currentRole', Roles::TYPE_STAFF);
                } else {
                    $request->session()->put('currentRole', $user->role_id);
                }
            }

            $request->session()->put('role_switched', 1);

            return redirect('/dashboard')->with(['success' => 'Role Changed']);
        } catch (\Exception $e) {
            info('role switch failed', [$e->getMessage(), $e->getTraceAsString()]);
            abort(404, $e->getMessage());
        }
    }
}
