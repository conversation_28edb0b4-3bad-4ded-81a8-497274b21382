<template>
    <div class="flex-1 space-y-6 overflow-y-auto px-6 py-4">
        <Card :pt="{ root: 'relative p-4 rounded-lg' }">
            <template #content> </template>
        </Card>
    </div>
</template>
<script setup>
import { inject, computed } from 'vue';
import { Field } from '@progress/kendo-vue-form';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import { useRegisterImprovementStore } from '@spa/stores/modules/continuous-improvement/registerImprovementStore.js';
import { useStaffPositionStore } from '@spa/stores/modules/staff-position/useStaffPositionStore.js';
import { useStaffStore } from '@spa/stores/modules/staff/useStaffStore.js';
import Card from '@spa/components/Card/Card.vue';
import StudentSelect from '@spa/modules/register-improvements/partials/StudentSelect.vue';

const kendoForm = inject('kendoForm', {});

const store = useRegisterImprovementStore();
const staffStore = useStaffStore();
const staffPositionStore = useStaffPositionStore();

const staffNameOptions = computed(() => {
    return staffStore.all.map((item) => ({
        text: item.full_name,
        value: item.id,
    }));
});

const staffRoles = computed(() => {
    return staffPositionStore.all.map((item) => ({
        text: item.position,
        value: item.id,
    }));
});

const categories = computed(() => {
    return [{ text: '+ Add New Category', value: 'new' }, ...store.categories];
});

const handleStaffRoleChange = (e) => {
    const value = e.value;
    kendoForm.onChange('staff_role', {
        value: value,
    });
    staffStore.filters.position = value;
};
</script>
<style lang=""></style>
