<template>
    <div class="relative">
        <Card :variant="'compact'" :pt="{ root: 'bg-gray-100' }">
            <template #header>
                <div class="flex items-center gap-2">
                    <span class="text-gray-700">
                        <icon name="shield" :fill="'currentColor'" :width="'20'" :height="'20'" />
                    </span>
                    <h2 class="text-lg font-medium">Login & Access</h2>
                </div>
            </template>
            <template #content>
                <div class="grid grid-cols-2 gap-4">
                    <div class="col-span-1 space-y-6">
                        <LabelValuePair
                            :label="'User Email:'"
                            :value="data?.email"
                            :orientation="'vertical'"
                        />
                        <LabelValuePair
                            :label="'Username:'"
                            :value="data?.username"
                            :orientation="'vertical'"
                        />
                        <!--                    <LabelValuePair-->
                        <!--                        :label="'Assigned School:'"-->
                        <!--                        :value="'ABC College'"-->
                        <!--                        :orientation="'vertical'"-->
                        <!--                    />-->
                    </div>
                    <div class="col-span-1 space-y-6">
                        <LabelValuePair
                            :label="'Account Status'"
                            :orientation="'vertical'"
                            :pt="{ root: 'w-[180px]' }"
                        >
                            <EnumSelect
                                enum-class="GalaxyAPI\Enums\UserStatusEnum"
                                v-model="data.user_status_id"
                                :default-value="data?.user_status_id"
                                :placeholder="'Select Status'"
                                @change="handleStatusChange"
                                :loading="userStore.ctxLoading['update-status']"
                            />
                        </LabelValuePair>
                        <LabelValuePair
                            :label="'Last Password Reset:'"
                            :value="data?.last_password_updated_date"
                            :orientation="'vertical'"
                        />
                        <LabelValuePair :label="'2FA Enabled:'" :orientation="'vertical'">
                            <div class="flex items-center gap-1 text-gray-600">
                                <icon
                                    name="shield"
                                    :fill="'currentColor'"
                                    :width="'18'"
                                    :height="'18'"
                                />
                                <span
                                    class="text-sm font-medium"
                                    :class="{
                                        'text-green-500': data?.two_factor_enabled,
                                        'text-red-500': !data?.two_factor_enabled,
                                    }"
                                    >{{ data?.two_factor_enabled ? 'Yes' : 'No' }}</span
                                >
                            </div>
                        </LabelValuePair>
                    </div>
                </div>
            </template>
            <template #footer>
                <div class="flex items-center gap-4">
                    <Button
                        variant="warning"
                        @click="handleActionClick(ACTIONS.RESET_PASSWORD)"
                        :disabled="!data.user_id"
                    >
                        <icon name="password" :fill="'currentColor'" :width="'18'" :height="'18'" />
                        Reset Password
                    </Button>
                    <Button
                        variant="error"
                        @click="handleActionClick(ACTIONS.FORCE_LOGOUT)"
                        :disabled="!data.user_id"
                    >
                        <icon name="password" :fill="'currentColor'" :width="'18'" :height="'18'" />
                        Force Logout
                    </Button>
                    <Button
                        variant="secondary"
                        @click="handleActionClick(ACTIONS.DISABLE_ACCOUNT)"
                        :disabled="!data.user_id"
                    >
                        <icon name="password" :fill="'currentColor'" :width="'18'" :height="'18'" />
                        Disable Account
                    </Button>
                </div>
            </template>
        </Card>
        <div
            class="absolute inset-0 z-10 flex h-full w-full items-center justify-center backdrop-blur-sm"
            v-if="!data?.user_id"
        >
            <div class="relative flex flex-col items-center gap-4">
                <span class="text-yellow-500">
                    <icon name="warning" :fill="'currentColor'" :width="'48'" :height="'48'" />
                </span>
                <div class="max-w-lg space-y-3">
                    <p class="text-center text-base font-medium text-gray-600">
                        This user has not been invited yet. Please send an invite to access the
                        system.
                    </p>
                </div>
                <Button variant="primary" @click="handleActionClick(ACTIONS.SEND_INVITE)">
                    {{ data?.user_id ? 'Re-Send Invite' : 'Send Invite' }}
                </Button>
            </div>
        </div>
    </div>
    <ActionsPopup :selected-action="selectedAction" :store="store" />
</template>
<script setup>
import { watch, onMounted } from 'vue';
import Button from '@spa/components/Buttons/Button.vue';
import Card from '@spa/components/Card/Card.vue';
import EnumSelect from '@spa/components/AsyncComponents/Select/EnumSelect.vue';
import LabelValuePair from '@spa/components/LabelValuePair/LabelValuePair.vue';
import { computed, ref } from 'vue';
import ActionsPopup from '@spa/modules/users/ActionsPopup.vue';
import { useUsersStore } from '@spa/stores/modules/users/useUsersStore.js';
import useConfirm from '@spa/services/useConfirm.js';

const confirm = useConfirm();

const ACTIONS = {
    RESET_PASSWORD: 'reset_password',
    FORCE_LOGOUT: 'force_logout',
    DISABLE_ACCOUNT: 'disable_user',
    SEND_INVITE: 'send_invite',
};

const props = defineProps({
    store: Object,
});

const userStore = useUsersStore();

const selectedAction = ref(null);

const userStatus = computed({
    get() {
        return {
            value: props.store.formData.user_status_id,
            label: props.store.formData.user_status,
        };
    },
    set(val) {
        props.store.formData.user_status_id = val.value;
        props.store.formData.user_status = val.label;
    },
});

const data = computed(() => {
    return props.store.formData;
});

const handleActionClick = (action) => {
    if (action === 'force_logout') {
        const data = {
            id: props.store.formData?.user_id,
        };
        userStore.forceLogout(data);
        return;
    }
    selectedAction.value = action;
    userStore.actionDialog = true;
};

onMounted(() => {
    userStore.selected = [props.store.formData];
});

const handleStatusChange = async (val) => {
    const data = {
        ids: [props.store.formData.id],
        user_type: props.store.userType,
        status: val,
    };
    confirm.require({
        message: 'Are you sure you want to change the status of this user?',
        header: 'Change User Status',
        icon: 'pi pi-exclamation-triangle',
        accept: async () => {
            const response = await userStore.updateUserStatus(data);
            if (response?.success) {
                await props.store.fetchDataById(props.store.formData.id);
            }
        },
    });
};

// watch(userStatus, (newVal) => {
//     handleStatusChange();
// });

watch(
    props.store,
    (newVal) => {
        if (newVal) {
            userStore.selected = [newVal.formData];
        }
    },
    { deep: true }
);
</script>
