<?php

namespace Support\Auth;

use App\Model\v2\SystemRoles;
use App\Users;
use Carbon\Carbon;
use Exception;
use GalaxyAPI\Enums\OldRolesEnums;
use GalaxyAPI\Traits\AsyncSelectFilterTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Permission\Models\Role;

class UserGroup extends Role
{
    use AsyncSelectFilterTrait;
    use HasFactory;
    use LogsActivity;

    const SESSION_KEY = 'current_group_id';

    const TYPE_STAFFS = 'team-members';

    const TYPE_AGENTS = 'agents';

    const TYPE_TEACHERS = 'teachers';

    const TYPE_EMPLOYERS = 'employers';

    const TYPE_SERVICE_PROVIDERS = 'service-providers';

    const TYPE_PLACEMENT_PROVIDERS = 'placement-providers';

    const TYPE_STUDENT = 'student';

    protected $table = 'galaxy_app_roles';

    protected $fillable = ['name', 'guard_name', 'system_role_type', 'has_all_access', 'duplicated_from', 'is_default'];

    protected $appends = ['secure_id'];

    protected static $logAttributes = [
        'name',
        'guard_name',
        'system_role_type',
        'has_all_access',
    ];

    public function getSecureIdAttribute()
    {
        return encryptIt($this->id);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => "User Role has been {$eventName}");
    }

    public function roleType()
    {
        return $this->belongsTo(SystemRoles::class, 'system_role_type', 'id');
    }

    public function parentrole()
    {
        return $this->belongsTo(UserGroup::class, 'duplicated_from', 'id');
    }

    public function inheritedrole()
    {
        return $this->hasMany(UserGroup::class, 'id', 'duplicated_from');
    }

    public function scopeExcludeChilds($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('duplicated_from')
                ->orWhere('duplicated_from', '=', 0);
        });
    }

    public function scopeDefault($q)
    {
        return $q->where('is_default', 1);
    }

    public function duplicateRole(?Users $user = null)
    {
        $permissions = $this->permissions()->get();
        $newGroup = $this->replicate();
        $time = Carbon::now();
        $newGroup->name = $newGroup->name." -copy- {$user->id}_{$time}";
        $newGroup->duplicated_from = $this->id;
        $newGroup->push();
        $newGroup->givePermissionTo($permissions);
        if ($user) {
            $user->assignRole($newGroup);
            $user->removeRole($this);
        }

        return $newGroup;
    }

    public function scopeFilterUser($query, $value = null)
    {
        /* get the user id */
        try {
            $userId = decryptIt($value);
        } catch (Exception $e) {
            $userId = null;
        }
        $user = Users::find($userId);
        if ($user == null) {
            return $query->where('id', '=', null);
        }
        // get the new role from admin
        $newRole = OldRolesEnums::getNewRoleFromOld((int) $user->role_id);

        $roleArray = getRolesMatched($newRole);

        $roles = SystemRoles::whereIn('role_alias', $roleArray)->pluck('id');
        $query->whereIn('system_role_type', $roles);
    }

    public function scopeFilterIds($query, $value)
    {
        // this method is in AsyncSelectFilterTrait as well,
        // this overwrites the function to pick the selected roles even if the records goes out of paginated scope
        if (empty($value)) {
            return;
        }
        if (! is_array($value)) {
            $value = explode(',', $value);
        }
        $ids = array_filter($value, 'is_numeric');
        // ids can be child role ids (ie overwritten ids so we need to get the parent role ids)
        $parentRoles = self::whereIn('id', $ids)->whereNotNull('duplicated_from')->pluck('duplicated_from', 'id');
        $ids = collect($ids)
            ->map(fn ($id) => $parentRoles[$id] ?? $id)
            ->unique()
            ->values()
            ->all();

        $query->orderByRaw('
                        CASE
                            WHEN id IN ('.implode(',', $ids).') THEN 0
                            ELSE 1
                        END
                    ');
    }

    public function scopeFilterType($query, $value)
    {
        $roleArray = getRolesMatched($value);
        $roles = SystemRoles::whereIn('role_alias', $roleArray)->pluck('id');
        $query->whereIn('system_role_type', $roles);
    }
}
