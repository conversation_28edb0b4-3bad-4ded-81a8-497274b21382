<template>
    <template v-if="store.selected.length > 1">
        <h3 class="text-base font-medium text-gray-900">How do you want to send login invites?</h3>
        <ul class="list-none text-gray-600">
            <li>
                <Checkbox
                    v-model:checked="sendInviteToAll"
                    label="Send Invites to ever user regardless of their status."
                />
            </li>
            <li>
                <Checkbox
                    v-model:checked="skipExistingUsers"
                    label="Skip users who already have access."
                />
            </li>
            <li>
                <Checkbox
                    v-model:checked="userStore.payload.re_enable_users"
                    label="Re-enable disabled users and send invite."
                />
            </li>
        </ul>
        <!--    <HighlightBox variant="info" :pt="{ root: 'max-w-3xl', icon: 'text-primary-blue-500' }">-->
        <!--        <template #icon>-->
        <!--            <icon name="info-help" />-->
        <!--        </template>-->
        <!--        <template #content>-->
        <!--            <div class="space-y-2">-->
        <!--                <p class="text-sm text-gray-600">-->
        <!--                    <strong>Note:</strong>-->
        <!--                </p>-->
        <!--                <ul class="list-disc pl-5 text-gray-600">-->
        <!--                    <li>“8” users have never logged in.</li>-->
        <!--                    <li>“2” users have never login access.</li>-->
        <!--                    <li>“1” user was previously disabled.</li>-->
        <!--                </ul>-->
        <!--                <div class="flex items-center gap-2 text-yellow-400">-->
        <!--                    <icon name="warning" :width="'16'" :height="'16'" :fill="'currentColor'" />-->
        <!--                    <p class="text-sm text-yellow-600">-->
        <!--                        Some users may have already received invitations.-->
        <!--                    </p>-->
        <!--                </div>-->
        <!--            </div>-->
        <!--        </template>-->
        <!--    </HighlightBox>-->
    </template>
    <template v-else>
        <h3 class="text-base font-medium text-gray-900">
            You are about to send a login invite to {{ userStore.selected[0]?.name }}.
        </h3>
    </template>
</template>

<script setup>
import { computed } from 'vue';
import HighlightBox from '@spa/components/HighlightBox/HighlightBox.vue';
import { useUsersStore } from '@spa/stores/modules/users/useUsersStore.js';
import Checkbox from '@spa/components/Checkbox.vue';
import { storeToRefs } from 'pinia';
const props = defineProps({
    store: Object,
});

const userStore = useUsersStore();

const { payload } = storeToRefs(userStore);

const sendInviteToAll = computed({
    get: () => userStore.payload.send_invite_to_all,
    set: (val) => {
        userStore.payload.send_invite_to_all = val;
        if (val) userStore.payload.skip_users_with_access = false;
    },
});

const skipExistingUsers = computed({
    get: () => userStore.payload.skip_users_with_access,
    set: (val) => {
        userStore.payload.skip_users_with_access = val;
        if (val) userStore.payload.send_invite_to_all = false;
    },
});
</script>
