<?php

namespace App\Services;

use App\Model\v2\ResultGrade;
use DOMDocument;
use DOMXPath;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;
use Support\Services\UploadService;

class CertificateContentReplacer
{
    protected array $data;

    const ALLOWED_TAGS = [
        '[unit.list]',
        '[unit.list.with.result]',
        '[unit.interim.table]',
    ];

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function replace(string $template): string
    {
        // First replace image URLs
        $template = $this->replaceImageUrl($template);

        // First replace QR code if present
        $template = $this->replaceQrCode($template);

        // Replace image attributes (college_logo, profile_picture)
        $template = $this->replaceImageAttributes($template);

        // Match all placeholders like [student.fullname], [unit.list]
        return preg_replace_callback('/\[(.*?)\]/', function ($matches) {
            $key = $matches[1];

            // Special: grade legend
            if ($key === 'grade_legend') {
                return $this->renderGradeLegend();
            }

            // Support table tags like unit.list, unit.list.with.result, etc.
            if (Str::startsWith($key, 'unit.')) {
                return $this->renderUnitTable($key);
            }

            // Get nested value from data using dot notation
            return data_get($this->data, $key, '');
        }, $template);
    }

    protected function replaceQrCode(string $template): string
    {
        if (! isset($this->data['qr_code_src'])) {
            return $template;
        }

        // Find the div with id="qrImage" and replace the img src inside it
        return preg_replace(
            '/(<div[^>]*id="qrImage"[^>]*>.*?<img[^>]*src=")[^"]*("[^>]*>.*?<\/div>)/s',
            '$1'.$this->data['qr_code_src'].'$2',
            $template
        );
    }

    protected function replaceImageAttributes(string $template): string
    {
        // Replace standalone [college_logo] placeholders with complete img tags
        if (isset($this->data['college_logo']) && ! empty($this->data['college_logo'])) {
            $template = str_replace(
                '[college_logo]',
                '<img src="'.$this->data['college_logo'].'" alt="College Logo" style="width: auto; height: 28px; display: block; margin: 0 auto;" />',
                $template
            );
        }

        // Replace standalone [profile_picture] placeholders with complete img tags
        if (isset($this->data['profile_picture']) && ! empty($this->data['profile_picture'])) {
            $imgSize = Config::get('constants.defaultProfileSize') / 2 ?? 85;
            $template = str_replace(
                '[profile_picture]',
                '<img src="'.$this->data['profile_picture'].'" alt="Profile Picture" style="width: 100%; height: auto; object-fit: cover;" />',
                $template
            );
        }

        // Replace standalone [college.admission_manager_signature] placeholders with complete img tags
        if (isset($this->data['college']['admission_manager_signature']) && ! empty($this->data['college']['admission_manager_signature'])) {
            $template = str_replace(
                '[college.admission_manager_signature]',
                $this->data['college']['admission_manager_signature'],
                $template
            );
        }

        // Replace standalone [college.student_support_signature] placeholders with complete img tags
        if (isset($this->data['college']['student_support_signature']) && ! empty($this->data['college']['student_support_signature'])) {
            $template = str_replace(
                '[college.student_support_signature]',
                $this->data['college']['student_support_signature'],
                $template
            );
        }

        return $template;
    }

    protected function renderUnitTable(string $key): string
    {
        $units = $this->data['unit']['list'] ?? [];
        $unitsWithResult = $this->data['unit']['listwithresult'] ?? [];
        $unitsTrascript = $this->data['unit']['trascript'] ?? [];
        $gradeData = $this->data['unit']['otherData']['gradeData'] ?? [];
        $gradePointData = $this->data['unit']['otherData']['gradePointData'] ?? [];

        if ($key === 'unit.list.with.result') {
            return $this->renderTable($unitsWithResult, ['Code', 'Name', 'Result', 'Year'], function ($unit) {
                return [
                    $unit['code'] ?? '',
                    $unit['name'] ?? '',
                    $unit['result'] ?? '',
                    $unit['year'] ?? '',
                ];
            });
        }

        if ($key === 'unit.interim.table') {
            return $this->renderSemesterWiseTranscript($unitsTrascript, $gradeData, $gradePointData);
        }

        // Default unit list
        return $this->renderTable($units, ['Unit Code', 'Unit Name'], function ($unit) {
            return [
                $unit['code'] ?? '',
                $unit['name'] ?? '',
            ];
        });
    }

    protected function renderSemesterWiseTranscript(array $semWiseChunkDatas, array $gradeData, array $gradePointData): string
    {
        $html = '';

        foreach ($semWiseChunkDatas as $index => $semWiseDatas) {
            foreach ($semWiseDatas['data'] as $semWiseData) {
                $html .= $this->renderSemesterTable($semWiseData, $gradeData, $gradePointData);
            }
            // Overflow note after table for transcript chunks (if enabled)
            $html .= $this->renderOverflowNote($index, count($semWiseChunkDatas));
            // Add page-break if not the last chunk
            if ($index < count($semWiseChunkDatas) - 1) {
                $html .= '</div><div class="page-break"></div><div class="page-break-table">';
            }
        }

        return $html;
    }

    protected function renderSemesterTable(array $semWiseData, array $gradeData, array $gradePointData): string
    {
        $data = $semWiseData['data'];
        $semesterName = $semWiseData['semester_name'];

        $html = '<table width="100%">';
        $html .= $this->renderTableHeader();
        $html .= '<tbody>';

        if (empty($data)) {
            $html .= $this->renderNoRecordsRow();
        } else {
            $html .= $this->renderSemesterRows($data, $semesterName, $gradeData, $gradePointData);
        }

        $html .= '</tbody></table>';

        return $html;
    }

    protected function renderTableHeader(): string
    {
        return '<thead>
            <tr style="background-color: #f2f2f2;">
                <th>Semester</th>
                <th>Unit Code</th>
                <th>Unit Name</th>
                <th>Credit Point</th>
                <th>Marks</th>
                <th>Grade</th>
                <th>Grade Point</th>
            </tr>
        </thead>';
    }

    protected function renderNoRecordsRow(): string
    {
        return '<tr><td colspan="7">No Records Found</td></tr>';
    }

    protected function renderSemesterRows($data, string $semesterName, array $gradeData, array $gradePointData): string
    {
        $html = '';
        $averageGPA = 0;
        $sumOfCreditPoint = 0;

        // Convert Collection to array if needed
        $dataArray = is_array($data) ? $data : $data->toArray();

        foreach ($dataArray as $index => $enrollData) {
            if (! $this->isValidEnrollmentData($enrollData)) {
                $html .= $this->renderNoRecordsRow();

                continue;
            }

            $html .= $this->renderEnrollmentRow($enrollData, $index, $semesterName, $gradeData, $gradePointData, count($dataArray));

            $gradePoint = $gradePointData[$enrollData['mark_outcome']] ?? 0;
            $creditPoint = $enrollData['subject']['credit_point'] ?? 0;
            $averageGPA += ($gradePoint * $creditPoint);
            $sumOfCreditPoint += $creditPoint;
        }

        $gpa = $sumOfCreditPoint ? number_format($averageGPA / $sumOfCreditPoint, 2) : 0;
        $html .= $this->renderGPARow($gpa);

        return $html;
    }

    protected function isValidEnrollmentData(array $enrollData): bool
    {
        return isset($enrollData['unit']) && isset($enrollData['unit']['unit_name']);
    }

    protected function renderEnrollmentRow(array $enrollData, int $index, string $semesterName, array $gradeData, array $gradePointData, int $totalRows): string
    {
        $html = '<tr>';

        if ($index === 0) {
            $html .= '<td style="min-height:40px;height:auto;line-height:1.4em;vertical-align:top;" rowspan="'.$totalRows.'">'.$semesterName.'</td>';
        }

        $grade = $gradeData[$enrollData['mark_outcome']] ?? '';
        $gradePoint = $gradePointData[$enrollData['mark_outcome']] ?? 0;

        $html .= '<td>'.($enrollData['unit']['unit_code'] ?? '').'</td>';
        $html .= '<td>'.($enrollData['unit']['unit_name'] ?? '').'</td>';
        $html .= '<td>'.($enrollData['subject']['credit_point'] ?? '').'</td>';
        $html .= '<td>'.($enrollData['marks'] ?? '').'</td>';
        $html .= '<td>'.$grade.'</td>';
        $html .= '<td>'.$gradePoint.'</td>';
        $html .= '</tr>';

        return $html;
    }

    protected function renderGPARow(float $gpa): string
    {
        return '<tr>
            <td colspan="4"></td>
            <td colspan="3">Grade Point Average (GPA): '.$gpa.'</td>
        </tr>';
    }

    protected function renderTable(array $chunkItems, array $headers, \Closure $rowCallback): string
    {
        $html = '';

        if (empty($chunkItems)) {
            $html .= '<table width="100%"><thead><tr>';
            foreach ($headers as $header) {
                $html .= "<th>{$header}</th>";
            }
            $html .= '</tr></thead><tbody>';
            $html .= '<tr><td colspan="'.count($headers).'">No Records Found</td></tr>';
            $html .= '</tbody></table>';

            return $html;
        }

        $totalChunks = count($chunkItems);

        foreach ($chunkItems as $index => $items) {
            $html .= '<table width="100%"><thead><tr>';
            foreach ($headers as $header) {
                $html .= "<th>{$header}</th>";
            }
            $html .= '</tr></thead><tbody>';

            foreach ($items as $item) {
                $row = $rowCallback($item);
                $html .= '<tr>';
                foreach ($row as $col) {
                    $html .= "<td>{$col}</td>";
                }
                $html .= '</tr>';
            }

            $html .= '</tbody></table>';

            // Overflow note after unit tables (if enabled)
            $html .= $this->renderOverflowNote($index, $totalChunks);

            // Add page-break if not the last chunk
            if ($index < $totalChunks - 1) {
                $html .= '</div><div class="page-break"></div><div class="page-break-table">';
            }
        }

        return $html;
    }

    protected function renderOverflowNote(int $index, int $totalChunks): string
    {
        $settings = $this->data['settings']['overflow'] ?? null;
        if (! is_array($settings)) {
            return '';
        }
        $enabled = (bool) ($settings['enabled'] ?? false);
        $text = (string) ($settings['text'] ?? 'Units Achieved (continued)');
        if (! $enabled) {
            return '';
        }
        // Only show for overflow pages (not on last chunk page)
        if ($index < $totalChunks - 1) {
            return '<div style="margin-top:6px; font-size: 8px;">'.e($text).'</div>';
        }

        return '';
    }

    public function extractStyles(string $html): array
    {
        $dom = new DOMDocument;
        libxml_use_internal_errors(true); // Suppress invalid HTML warnings
        $dom->loadHTML($html);
        libxml_clear_errors();

        $xpath = new DOMXPath($dom);
        $tagStyles = [];

        foreach (self::ALLOWED_TAGS as $tag) {
            // Search for elements containing the tag text
            $elements = $xpath->query("//*[contains(text(), '".$tag."')]");

            foreach ($elements as $el) {
                $updatedStyle = '';

                if ($el->hasAttribute('style')) {
                    $style = $el->getAttribute('style');
                    // Remove 'top:...;' using regex
                    $updatedStyle = preg_replace('/top\s*:\s*[^;]+;?/i', '', $style);
                }

                // Save the style for the tag
                $tagStyles[$tag] = $updatedStyle;

                // Break if you only want the first match for each tag
                break;
            }
        }

        return $tagStyles;
    }

    protected function renderGradeLegend(): string
    {
        try {
            $collegeId = Auth::user()?->college_id;
        } catch (\Throwable $e) {
            $collegeId = null;
        }

        // Try from passed data first (if provided by caller), else DB by college
        $prebuilt = data_get($this->data, 'grade_legend');
        if (is_string($prebuilt) && $prebuilt !== '') {
            return $prebuilt;
        }

        if (! $collegeId) {
            return '';
        }

        $grades = ResultGrade::where('college_id', $collegeId)
            ->orderBy('marks', 'desc')
            ->get(['grade', 'marks', 'maximum_marks', 'grade_title', 'description']);

        if ($grades->isEmpty()) {
            return '';
        }

        $rows = '';
        foreach ($grades as $g) {
            $label = trim((string) ($g->grade_title ?? $g->grade ?? ''));
            $range = '';
            if (! is_null($g->marks)) {
                if (! is_null($g->maximum_marks) && (int) $g->maximum_marks > 0) {
                    $range = $g->marks.' - '.$g->maximum_marks.'%';
                } else {
                    $range = $g->marks.'%';
                }
            }
            $definition = trim((string) ($g->description ?? ''));
            $rows .= '<tr>'
                .'<td style="white-space:nowrap; vertical-align:top;"><strong>'.e($label).'</strong></td>'
                .'<td style="white-space:nowrap; vertical-align:top;">'.e($range).'</td>'
                .'<td style="vertical-align:top;">'.nl2br(e($definition)).'</td>'
                .'</tr>';
        }

        $html = '</div><div class="page-break"></div><div class="page-break-table">
        <table class="legend-table" width="100%" style="border-collapse:collapse; page-break-inside: auto;">'
            .'<thead>'
            .'<tr style="background-color:#f2f2f2;">'
            .'<th style="text-align:left;">Legend</th>'
            .'<th style="text-align:left;">Range</th>'
            .'<th style="text-align:left;">Definition</th>'
            .'</tr>'
            .'</thead>'
            .'<tbody>'.$rows.'</tbody>'
            .'</table>';

        return $html;
    }

    public function makeFontLink(string $metadata): string
    {
        $decodeJson = json_decode($metadata, true);

        $fonts = $decodeJson['fonts'] ?? [];
        // Step 1: Replace space with `+` in each font
        $fontsFormatted = array_map(function ($font) {
            return str_replace(' ', '+', $font);
        }, $fonts);

        // Step 2: Implode into a single string
        $fontQuery = implode('&family=', $fontsFormatted);

        return 'https://fonts.googleapis.com/css2?family='.$fontQuery.'&display=swap';
    }

    protected function replaceImageUrl(string $template): string
    {
        $result = preg_replace_callback(
            '/(<img[^>]*\bsrc=")([^"]*)("[^>]*>)/i',
            function ($matches) {
                $prefix = $matches[1];
                $src = $matches[2];
                $suffix = $matches[3];

                // Skip base64 images
                if (stripos($src, 'data:image') === 0) {
                    return $matches[0];
                }

                // Only process if $src is a full URL (http or https)
                if (filter_var($src, FILTER_VALIDATE_URL) !== false) {
                    // Convert absolute URL to relative path
                    $path = parse_url($src, PHP_URL_PATH) ?? '';
                    $path = ltrim((string) $path, '/');

                    // Generate fresh URL with UploadService; if it fails, keep original src
                    $newUrl = UploadService::url($path) ?: $src;

                    return $prefix.$newUrl.$suffix;
                }

                // For relative URLs or others, leave as is
                return $matches[0];
            },
            $template
        );

        // preg_replace_callback returns null on error; fall back to original template
        return is_string($result) ? $result : $template;
    }
}
