<?php

use App\Exceptions\ApplicationException;
use Illuminate\Http\Response;
use Spatie\Permission\Exceptions\UnauthorizedException;
use Support\Auth\Permission;
use Support\Auth\PermissionResolver;

if (! function_exists('authCanDo')) {
    function authCanDo($permission, $throw = false)
    {
        // if(auth('api')->user()){}
        if (! auth()->user()) {
            return false;
        }

        if (auth()->user()->isAdmin()) {
            return true;
        }

        $role = auth()->user()->currentActiveRole();
        if (! $role) {
            return false;
        }

        if (! $role->hasPermissionTo($permission, Permission::DEFAULT_GUARD)) {
            if ($throw) {
                throw new UnauthorizedException(Response::HTTP_FORBIDDEN, 'You do not have required Authorization');
            }

            return false;
        }

        return true;
    }
}

if (! function_exists('authCanDoAny')) {
    function authCanDoAny($permissions = [], $throw = false)
    {
        // if(auth('api')->user()){}
        if (! auth()->user()) {
            return false;
        }

        if (auth()->user()->isAdmin()) {
            return true;
        }

        $role = auth()->user()->currentActiveRole();
        if (! $role) {
            return false;
        }

        $permissions = is_array($permissions) ? $permissions : [$permissions];

        if (! $role->hasAnyPermission($permissions, Permission::DEFAULT_GUARD)) {
            if ($throw) {
                throw new UnauthorizedException(Response::HTTP_FORBIDDEN, 'You do not have required Authorization');
            }

            return false;
        }

        return true;
    }
}
if (! function_exists('authCanDoAll')) {
    function authCanDoAll($permissions = [], $throw = false)
    {
        // if(auth('api')->user()){}
        if (! auth()->user()) {
            return false;
        }

        if (auth()->user()->isAdmin()) {
            return true;
        }

        $role = auth()->user()->currentActiveRole();
        if (! $role) {
            return false;
        }

        $permissions = is_array($permissions) ? $permissions : [$permissions];

        if (! $role->hasAllPermissions($permissions, Permission::DEFAULT_GUARD)) {
            if ($throw) {
                throw new UnauthorizedException(Response::HTTP_FORBIDDEN, 'You do not have required Authorization');
            }

            return false;
        }

        return true;
    }
}
if (! function_exists('authPermissionChecker')) {
    function authPermissionChecker($methodName = null)
    {
        // if(auth('api')->user()){}
        if (! auth()->user()) {
            return false;
        }

        if (auth()->user()->isAdmin()) {
            return true;
        }

        $role = auth()->user()->currentActiveRole();
        if (! $role) {
            return false;
        }

        $resolver = new PermissionResolver(auth()->user(), $role);

        if ($methodName) {
            if (! method_exists($resolver, $methodName)) {
                throw new ApplicationException("{$methodName} permission checker not defined.");
            }

            return $resolver->{$methodName}();
        }

        return $resolver;
    }
}
if (! function_exists('getRolesMatched')) {
    function getRolesMatched($role = '')
    {
        $common_roles = [
            'team-members' => ['team-members', 'teachers'],
            'teachers' => ['team-members', 'teachers'],
            'agents' => ['agents', 'agent-staffs'],
        ];

        return isset($common_roles[$role]) ? $common_roles[$role] : [$role];
    }
}
