@extends('frontend.layouts.frontend')
@section('title', $pagetitle )
@section('content')
@php
$routeGet = explode("/", Route::getCurrentRoute()->uri());
$activeVar = $routeGet[0];
@endphp
<!-- Main content -->
<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="custom-header">
                <div class="row">
                    <div class="col-md-offset-11 col-md-1">
                        <span class="pull-right add-btn-block">
                            <a href="{{ route('view-agent-payment-list') }}" data-toggle="tooltip" data-original-title="Go back" >
                                <div class="btn-add"><i class="fa fa-reply"></i></div>
                            </a>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="nav-tabs-custom">
                <ul class="nav nav-tabs">
                    <li class="{{ ($activeVar=='process-commission-agent' ) ? 'icon-active' : '' }}"> 
                        <a href="{{ route('process-commission-agent',array('id'=>$agentId)) }}">Process Commission</a></li>
                    <li class="active {{ ($activeVar=='payment-history') ? 'icon-active' : '' }}">
                        <a href="{{ route('spa.manage-users.agents.profile', array('id'=>encryptIt($agentId)))}}#payment">Payment History</a></li>
                    <li class="{{ ($activeVar=='credit-bonus-allocation') ? 'icon-active'  : '' }}">
                        <a href="{{ route('credit-bonus-allocation',array('id'=>$agentId))}}">Credit/Bonus Allocation</a></li>
                </ul>
                <div class="tab-content">
                    <div class="active tab-pane padding-30">
                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        {{ Form::hidden('agent_id', $agentId ,  array('class' => 'form-horizontal vertical-add-form', 'id' => 'agent_id')) }}
                        <div class="row form-horizontal">
                            <div class="col-md-12">
                                <div class="box box-info">
                                    <div class="custom-header">
                                        <h3 class="box-title">Payment History for Agent : {{ $arrAgentDetail->agency_name }}</h3>
                                        <div style="clear:both"></div>
                                    </div>   
                                    <div class="box-body">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label for="full_name" class="col-sm-4 control-label">Year: </label>
                                                    <div class="col-sm-5">
                                                        {{ Form::select('pay_year', $arrPaymentHistoryYear , null, array('class' => 'form-control filterData', 'id' => 'pay_year')) }}
                                                    </div>   
                                                </div>
                                                <div class="form-group mt-30">
                                                    <label for="full_name" class="col-sm-4 control-label">View Type: </label>
                                                    <div class="col-sm-5">
                                                        {{ Form::select('view_type', $arrPaymentHistoryViewType , null, array('class' => 'form-control filterData', 'id' => 'view_type')) }}
                                                    </div>   
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" style="display: none" id="invoiceAgentCommission">
                            <div class="col-md-12">
                                <div class="custom-header">
                                    <h3 class="box-title">Invoiced Agent Commission</h3>
                                </div>
                                <div class="box-body table-responsive no-padding">
                                    <table class="table table-hover table-custom" id="subjectData">
                                        <thead>
                                            <tr>
                                                <th scope="col">Invoice No</th>
                                                <th scope="col">Paid Date</th>
                                                <th scope="col">Invoice Amount</th>
                                                <!--<th scope="col">View</th>-->
                                            </tr>
                                        </thead>
                                        <tbody id="dataAppendInvoice">
                                            <tr>
                                                <td colspan="3" style="text-align: center">
                                                    <p style="color:red;">No Record Found</p>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <!--            <div class="text-center type2" id="noRecords">
                                                <table class="table table-hover table-custom">
                                                    <tbody>
                                                        <tr><td colspan="12" style="text-align: center">
                                                                <p style="color:red;">No Record Found</p>
                                                            </td></tr>
                                                    </tbody>
                                                </table>
                                            </div>-->
                            </div>
                        </div>    

                        <div class="row" id="paymentSchesuleForPayment">
                            <div class="col-md-12">
                                <div class="custom-header">
                                    <h3 class="box-title">
                                        <span id='auto_deducted' style='display: none;'>Auto Deducted Agent Commission</span>
                                        <span id='other_type' style='display: none;'>Recorded Agent Commission - Not auto deducted</span>
                                        <span id='invoiced_commission' style='display: none;'>Invoiced Commission Detail</span>
                                    </h3>
                                </div>
                                <div class="box-body table-responsive no-padding">
                                    <table class="table table-hover table-custom" id="subjectData">
                                        <thead>
                                            <tr>
                                                <th scope="col">Agent<br/>Name</th>
                                            <!--<th scope="col">Transaction<br/>No</th>-->
                                                <th scope="col">Invoice<br/>No</th>
                                                <th scope="col">Student<br/>Id</th>
                                                <th scope="col">Student<br/>Name</th>
                                                <th scope="col">Course<br/>Id</th>
                                                <th scope="col">Course<br/>Attemp</th>
                                                <th scope="col">Invoice<br/>Amount</th>
                                                <th scope="col">Transaction<br/>Amount</th>
                                                <th scope="col">Paid<br/>Date</th>
                                                <th scope="col">Commission</th>
                                                <th scope="col">GST</th>
                                                <th scope="col">Commission <br/>Payable</th>
                                                <th scope="col">Comm.<br/>Paid</th>
                                                <th scope="col">Comm.<br/>Paid Date</th>
                                                <th scope="col">Remarks</th>
                                            </tr>
                                        </thead>
                                        <tbody id="dataAppend">
                                            <tr>
                                                <td colspan="15" style="text-align: center">
                                                    <p style="color:red;">No Record Found</p>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div> 
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Main content -->
<!--<section class="content">
    <input type="hidden" name="_token" value="{{ csrf_token() }}">
    {{ Form::hidden('agent_id', $agentId ,  array('class' => 'form-horizontal vertical-add-form', 'id' => 'agent_id')) }}
    <div class="row form-horizontal">
        <div class="col-md-9">
            <div class="box box-info">
                <div class="custom-header">
                    <h3 class="box-title">Payment History for Agent : {{ $arrAgentDetail->agency_name }}</h3>
                    <div style="clear:both"></div>
                </div>   
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="full_name" class="col-sm-4 control-label">Year: </label>
                                <div class="col-sm-5">
                                    {{ Form::select('pay_year', $arrPaymentHistoryYear , null, array('class' => 'form-control filterData', 'id' => 'pay_year')) }}
                                </div>   
                            </div>
                            <div class="form-group mt-30">
                                <label for="full_name" class="col-sm-4 control-label">View Type: </label>
                                <div class="col-sm-5">
                                    {{ Form::select('view_type', $arrPaymentHistoryViewType , null, array('class' => 'form-control filterData', 'id' => 'view_type')) }}
                                </div>   
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="box box-info">
                <div class="custom-header">
                    <h3 class="box-title">Agent Payment</h3>
                    <a class="link-black text-sm pull-right" data-toggle="tooltip" data-original-title="Edit" style="font-size: 24px;" href="http://www.rtomanager.dev/apply-online/31"><i class="fa fa-edit"></i></a>
                    <div style="clear:both"></div>
                </div> 
                <div class="box-body">
                    <div id="midsidebar">
                        @include('frontend.agent_payment.agent-payment-module-list')
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row" style="display: none" id="invoiceAgentCommission">
        <div class="col-md-12">
            <div class="custom-header">
                <h3 class="box-title">Invoiced Agent Commission</h3>
            </div>
            <div class="box-body table-responsive no-padding">
                <table class="table table-hover table-custom" id="subjectData">
                    <thead>
                        <tr>
                            <th scope="col">Invoice No</th>
                            <th scope="col">Paid Date</th>
                            <th scope="col">Invoice Amount</th>
                            <th scope="col">View</th>
                        </tr>
                    </thead>
                    <tbody id="dataAppendInvoice">
                        <tr>
                            <td colspan="4" style="text-align: center">
                                <p style="color:red;">No Record Found</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
                        <div class="text-center type2" id="noRecords">
                            <table class="table table-hover table-custom">
                                <tbody>
                                    <tr><td colspan="12" style="text-align: center">
                                            <p style="color:red;">No Record Found</p>
                                        </td></tr>
                                </tbody>
                            </table>
                        </div>
        </div>
    </div>    

    <div class="row" id="paymentSchesuleForPayment">
        <div class="col-md-12">
            <div class="custom-header">
                <h3 class="box-title">
                    <span id='auto_deducted' style='display: none;'>Auto Deducted Agent Commission</span>
                    <span id='other_type' style='display: none;'>Recorded Agent Commission - Not auto deducted</span>
                </h3>
            </div>
            <div class="box-body table-responsive no-padding">
                <table class="table table-hover table-custom" id="subjectData">
                    <thead>
                        <tr>
                            <th scope="col">Agent<br/>Name</th>
                            <th scope="col">Transaction<br/>No</th>
                            <th scope="col">Student<br/>Id</th>
                            <th scope="col">Student<br/>Name</th>
                            <th scope="col">Course<br/>Id</th>
                            <th scope="col">Course<br/>Attemp</th>
                            <th scope="col">Due<br/>Amount</th>
                            <th scope="col">Paid<br/>Amont</th>
                            <th scope="col">Paid<br/>Date</th>
                            <th scope="col">Commission <br/>Payable</th>
                            <th scope="col">GST</th>
                            <th scope="col">Comm.<br/>Paid</th>
                            <th scope="col">Comm.<br/>Paid Date</th>
                            <th scope="col">Remarks</th>
                        </tr>
                    </thead>
                    <tbody id="dataAppend">
                        <tr>
                            <td colspan="14" style="text-align: center">
                                <p style="color:red;">No Record Found</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
             <div class="text-center type1" style="display: none;" id="noRecords">
                <table class="table table-hover table-custom">
                    <tbody>
                        <tr><td colspan="12" style="text-align: center">
                            <p style="color:red;">No Record Found</p>
                        </td></tr>
                    </tbody>
                </table>
            </div> 
        </div>
    </div>    

    <style>
       
    </style>
</section>-->
<!--<style>
    .error {
        border: 1px solid red !important;
    }
    #midsidebar ul{
        list-style: none;
        padding: 0 !important;
    }
    #midsidebar ul li a{
        color: #15a3f5;
        font-size: 14px;
        line-height: 22px;
    }
    #midsidebar ul li.active{
        background: #e7e7e7;
    }
    #midsidebar ul li.active a{
        padding: 0 0 0 10px !important;
    }
</style> -->
@endsection