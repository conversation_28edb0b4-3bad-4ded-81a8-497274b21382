<?php

namespace GalaxyAPI\Services;

use App\Helpers\Helpers;
use App\Model\v2\SystemRoles;
use App\Users;
use GalaxyAPI\Enums\OldRolesEnums;
use GalaxyAPI\Enums\UserStatusEnum;
use GalaxyAPI\Enums\UserTypeEnum;
use Illuminate\Http\UploadedFile;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Support\Auth\UserGroup;
use Support\DTO\MailAddress;
use Support\Mails\EmailService;
use Support\Services\UploadService;

class UserService
{
    protected UserStatusService $userStatusService;

    public function __construct(UserStatusService $userStatusService)
    {
        $this->userStatusService = $userStatusService;
    }

    //    public function getUsers(string $type, array $params = []): array
    //    {
    //        return match ($type) {
    //            'staff' => $this->getStaffs($params),
    //            'agent' => $this->getAgents($params),
    //            'teacher' => $this->getTeachers($params),
    //            default => throw new \InvalidArgumentException("Invalid user type: $type"),
    //        };
    //    }
    //
    //    public function getStaffs(array $params = []): array
    //    {
    //        $query = Staff::query();
    //
    //        return $query->paginate(10)->map(fn($staff) => UserInfo::fromStaff($staff))->toArray();
    //    }

    public function getAuthenticatedUserData($user): array
    {
        return [
            'username' => $user->username,
            'email' => $user->email,
            'role_id' => $user->role_id,
            'college_id' => $user->college_id,
            'profile_picture' => $user->profile_picture,
            'uuid' => $user->uuid,
        ];
    }

    public function disableUsers(array $ids): array
    {
        $successCount = 0;
        $errors = [];
        Users::whereIn('id', $ids)->get()->each(function ($user) use (&$successCount, &$errors) {
            try {
                $this->userStatusService->disableUser($user);
                $successCount++;
            } catch (\Exception $e) {
                $errors[] = "Failed to disable user {$user->email}: ".$e->getMessage();
            }
        });

        if (! empty($errors)) {
            throw new \Exception('Some users failed to disable: '.implode(', ', $errors));
        }

        return [
            'success' => true,
            'message' => "Successfully disabled {$successCount} users",
            'success_count' => $successCount,
            'error_count' => count($errors),
            'errors' => $errors,
        ];
    }

    public function enableUsers(array $ids): void
    {
        Users::whereIn('id', $ids)->get()->each(function ($user) {
            $this->userStatusService->enableUser($user);
        });
    }

    public function resetPasswords(array $ids): array
    {
        $successCount = 0;
        $errors = [];

        Users::whereIn('id', $ids)->get()->each(function ($user) use (&$successCount, &$errors) {
            try {
                $success = $this->resetPassword($user);
                if ($success) {
                    $successCount++;
                }
            } catch (\Exception $e) {
                $errors[] = "Failed to reset password for user {$user->email}: ".$e->getMessage();
            }
        });

        if (! empty($errors)) {
            throw new \Exception('Some password resets failed: '.implode(', ', $errors));
        }

        return [
            'success' => true,
            'message' => "Password reset links sent successfully to {$successCount} users",
            'success_count' => $successCount,
            'error_count' => count($errors),
            'errors' => $errors,
        ];
    }

    public function sendInvite(array $ids, string $userType, array $options = []): array
    {
        $sendToAll = $options['send_invite_to_all'] ?? true;
        $skipUsersWithAccess = $options['skip_users_with_access'] ?? false;
        $reEnableUsers = $options['re_enable_users'] ?? false;

        $modelClass = $this->getModelClass($userType);
        if (empty($ids)) {
            throw new \Exception("No {$userType}s provided");
        }

        // Get all models with the provided IDs
        $models = $modelClass::whereIn('id', $ids)->get();

        if ($models->count() !== count($ids)) {
            $foundIds = $models->pluck('id')->toArray();
            $missingIds = array_diff($ids, $foundIds);
            throw new \Exception("{$userType}(s) not found: ".implode(', ', $missingIds));
        }

        // Filter models based on flags
        $filteredModels = $models->filter(function ($model) use ($skipUsersWithAccess, $sendToAll, $reEnableUsers) {
            if (! $model->user_id) {
                return true;
            }

            $user = Users::find($model->user_id);

            if (! $user) {
                // User not found, include for error handling
                return true;
            }

            $isActive = $user->status === UserStatusEnum::ACTIVE->value;

            // If skip_users_with_access is true, exclude ACTIVE users
            if ($skipUsersWithAccess && $isActive) {
                return false;
            }

            // If re_enable_users is true, include DISABLED users
            if ($reEnableUsers && ! $isActive) {
                return true;
            }

            // If send_invite_to_all is false, exclude users with existing access
            if (! $sendToAll && $model->user_id) {
                return false;
            }

            return true;
        });

        $skippedCount = $models->count() - $filteredModels->count();

        $createdUsers = [];
        $errors = [];

        foreach ($filteredModels as $model) {
            try {
                $user = null;

                // If model already linked to a user
                if ($model->user_id) {
                    $user = Users::find($model->user_id);

                    if (! $user) {
                        $errors[] = "User ID {$model->user_id} not found for {$userType} ID: {$model->id}";

                        continue;
                    }

                    // Re-enable user if flag is set
                    if ($reEnableUsers) {
                        $user->status = UserStatusEnum::ACTIVE->value;
                        $user->save();
                    }

                    $this->sendInviteEmail($user, $model, $userType, true);
                }
                // If model is not linked yet, create a new user
                else {
                    $user = $this->createUser($modelClass, $model->id);
                    $model->user_id = $user->id;
                    $model->save();

                    $this->sendInviteEmail($user, $model, $userType);
                }

                // Add to successful invite list
                $invitedUsers[] = [
                    'model_id' => $model->id,
                    'user_data' => $user,
                ];

            } catch (\Throwable $e) {
                $errorMsg = "Failed to process {$userType} ID {$model->id}: ".$e->getMessage();
                $errors[] = $errorMsg;

                \Log::error('Invite processing failed', [
                    'user_type' => $userType,
                    'model_id' => $model->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }
        if (! empty($errors) && empty($invitedUsers)) {
            throw new \Exception('Failed to create users: '.implode(', ', $errors));
        }

        $successCount = count($invitedUsers);
        $message = "Invites sent successfully to {$successCount} {$userType}(s)";

        if ($skippedCount > 0) {
            $message .= ". {$skippedCount} {$userType}(s) skipped based on filters";
        }

        if (! empty($errors)) {
            $message .= '. Some failed: '.implode(', ', $errors);
        }

        return [
            'success' => true,
            'message' => $message,
            'data' => $createdUsers,
            'success_count' => $successCount,
            'skipped_count' => $skippedCount,
            'error_count' => count($errors),
            'errors' => $errors,
        ];
    }

    public function assignRoles(array $ids, array $roles = []): void
    {
        try {
            $users = Users::with('oldroles')->whereIn('id', $ids)->get();
            $users->each(function ($user) use ($roles) {
                // get the new role from admin
                $newRole = OldRolesEnums::getNewRoleFromOld((int) $user->role_id);

                $roleArray = getRolesMatched($newRole);

                $roleTypes = SystemRoles::whereIn('role_alias', $roleArray)->pluck('id');

                $rolesToSync = UserGroup::whereIn('system_role_type', $roleTypes)->whereIn('id', $roles)->get();

                $synced = $user->userRoles()->sync($rolesToSync);
            });
        } catch (\Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function createUser($modelClass, $modelId)
    {
        $model = $modelClass::findOrFail($modelId);
        $userData = $model->toUserData();
        $userData['status'] = UserStatusEnum::PENDING->value;

        return Users::create($userData);
    }

    public function getModelClass(string $userType): string
    {
        $modelClass = UserTypeEnum::modelMap()[$userType] ?? null;

        if (! $modelClass || ! class_exists($modelClass)) {
            throw new \Exception('Invalid user type');
        }

        return $modelClass;
    }

    public function getUserStatusCount(string $userType): array
    {
        $roles = UserTypeEnum::getRoles();

        if (! $roles || ! isset($roles[$userType])) {
            return [];
        }

        $roleIds = $roles[$userType];

        if (empty($roleIds)) {
            return [];
        }

        $counts = Users::select('status', DB::raw('count(*) as total'))
            ->whereIn('role_id', $roleIds)
            ->groupBy('status')
            ->pluck('total', 'status');

        return $counts->toArray();
    }

    protected function resetPassword($user): bool
    {
        GalaxyUtilityService::initTenantSMTP();
        $status = Password::sendResetLink([
            'email' => $user->email,
        ]);
        if ($status !== Password::RESET_LINK_SENT) {
            throw new \Exception(__($status));
        }

        return true;
    }

    public function uploadProfile(
        $userId,
        $file,
    ) {
        $filename = $this->uploadUserFile($file);
        Users::find($userId)->update(['profile_picture' => $filename]);

        return $filename;
    }

    public function uploadUserFile($file)
    {
        if (! ($file instanceof UploadedFile)) {
            throw new \InvalidArgumentException('Expected instance of UploadedFile, got '.gettype($file));
        }

        $collegeId = Auth::user()->college_id;
        $filePath = config('constants.uploadFilePath.Users');
        $destinationPath = Helpers::changeRootPath(filePath: $filePath, collegeId: $collegeId);
        $filename = Str::uuid().'.'.$file->getClientOriginalExtension();
        UploadService::uploadAs($destinationPath['view'], $file, $filename);

        // Return the full relative path instead of just filename
        return $destinationPath['view'].$filename;
    }

    public function updateUserStatus($userId, $status)
    {
        Users::whereIn('id', $userId)->get()->each(function ($user) use ($status) {
            $user->status = $status;
            $user->save();
        });
    }

    public function sendInviteEmail(Users $user, mixed $model, ?string $userType = null, bool $isReInvite = false)
    {
        $data['name'] = $user->name;
        $data['email'] = $user->email;
        $data['link'] = route('login');
        $userTypeLabel = $userType ? Str::ucfirst($userType).' ' : '';
        $data['subject'] = 'Welcome! Access Your Galaxy360 '.$userTypeLabel.'Portal.';
        $data['message'] = 'You have been invited to GALAXY360. Please click on the link below to login.';
        $data['button_text'] = 'Login';
        $token = Password::getRepository()->create($user);
        $data['button_link'] = route('password.reset', $token).'?email='.urlencode($user->email);

        \Log::info('Attempting to send invite email', [
            'user_id' => $user->id,
            'email' => $user->email,
            'user_type' => $userType,
            'is_reinvite' => $isReInvite,
        ]);

        try {
            //            GalaxyUtilityService::initTenantSMTP();
            (EmailService::makeForSmtp(false))
                ->subject($data['subject'])
                ->to(new MailAddress($data['email']))
                ->sendUsingMailMessage(function (MailMessage $message) use ($data) {
                    $message->greeting($data['message']);
                    $message->action($data['button_text'], $data['button_link']);
                    $message->salutation(config('app.name'));
                });

            \Log::info('Invite email sent successfully', [
                'user_id' => $user->id,
                'email' => $user->email,
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to send invite email', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new \Exception('Failed to send invite email: '.$e->getMessage());
        }

    }
}
