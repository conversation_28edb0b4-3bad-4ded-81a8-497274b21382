<?php

namespace GalaxyAPI\Requests;

use GalaxyAPI\Enums\ATSICodeEnum;
use GalaxyAPI\Enums\FunctionCodeEnum;
use GalaxyAPI\Enums\HighestQualificationCodeEnum;
use GalaxyAPI\Enums\HighestQualificationPlaceCodeEnum;
use GalaxyAPI\Enums\OrganisationalUnitCodeEnum;
use GalaxyAPI\Enums\StaffWorkLevelCodeEnum;
use GalaxyAPI\Enums\UserTypeEnum;
use GalaxyAPI\Enums\WorkContractCodeEnum;
use GalaxyAPI\Enums\WorkSectorCodeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;

class UserStepRequest extends FormRequest
{
    public function rules(): array
    {
        $step = $this->request->get('step');
        $subStep = $this->request->get('sub_step');

        return match ($step) {
            'user_info' => $this->userInfo($subStep),
            'address_postal' => $this->addressPostalStep($subStep),
            'employment_info' => $this->employmentInfoStep($subStep),
            'qualifications' => $this->qualificationsStep($subStep),
            'personal_development' => $this->personalDevelopmentStep(),
            'bank_details' => $this->bankingInfoStep($subStep),
            'tcsi_report' => $this->tscInfoStep(),
            'upload_docs' => $this->uploadDocsStep(),
            default => [],
        };
    }

    private function userInfo($subStep = null): array
    {

        $rules = [
            // Staff User Info
            'user_type' => ['required', UserTypeEnum::getValidationString()],

            'name_title' => 'required|string|max:20',
            'first_name' => 'required|string|max:255',
            'middle_name' => 'nullable|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'email2' => 'required|email|max:255',
            'mobile' => 'required|string|max:20',
            'phone' => 'nullable|string|max:20',
            'gender' => 'required|string|max:50',
            'birth_date' => 'required|date|before:today',
            'country' => 'required|max:100',

            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_relationship' => 'required|string|max:255',
            'emergency_phone_1' => 'required|string|max:20',
            'emergency_phone_2' => 'required|string|max:20',
            'emergency_email' => 'required|email|max:255',
            'emergency_address' => 'required|string|max:255',
        ];

        if ($subStep === 'personal_details') {
            return Arr::only($rules, [
                'user_type', 'name_title', 'first_name', 'middle_name', 'last_name', 'email', 'email2', 'mobile', 'phone', 'gender', 'birth_date', 'country',
            ]);
        }

        if ($subStep === 'emergency_contact') {
            return Arr::only($rules, [
                'emergency_contact_name', 'emergency_contact_relationship', 'emergency_phone_1', 'emergency_phone_2',
                'emergency_email', 'emergency_address',
            ]);
        }

        return $rules;
    }

    private function addressPostalStep($subStep = null): array
    {
        $rules = [
            'residential_address' => 'required|string|max:255',
            'residential_country' => 'required|max:255',
            'residential_state' => 'required|string|max:255',
            'residential_city' => 'required|string|max:255',
            'residential_postcode' => 'nullable|string|max:255',
            'residential_abn' => 'nullable|string|max:255',

            'is_post_residential_same' => 'nullable|in:0,1',

            'postal_address' => 'required|string|max:255',
            'postal_country' => 'required|max:255',
            'postal_state' => 'required|string|max:255',
            'postal_city' => 'required|string|max:255',
            'postal_postcode' => 'nullable|string|max:255',
            'postal_abn' => 'nullable|string|max:255',
        ];

        if ($subStep === 'residential_address') {
            return Arr::only($rules, [
                'residential_address', 'residential_country', 'residential_state',
                'residential_city', 'residential_postcode', 'residential_abn',
                'is_post_residential_same',
            ]);
        }

        if ($subStep === 'postal_address') {
            return Arr::only($rules, [
                'postal_address', 'postal_country',
                'postal_state', 'postal_city', 'postal_postcode', 'postal_abn',
            ]);
        }

        return $rules;
    }

    private function employmentInfoStep($subStep = null): array
    {
        $staffNumberRule = [
            'required',
            'string',
            'max:255',
        ];

        if (! $this->get('id')) {
            $staffNumberRule[] = Rule::unique('rto_staff_and_teacher', 'staff_number');
        }

        $rules = [
            'staff_number' => $staffNumberRule,
            'employment_type' => 'required|string|max:255',
            'joined_on' => 'required|date|before:today',
            'campus_location' => 'required|string|max:255',
            'job_position' => 'required|string|max:255',
            'line_manager' => 'required|string|max:255',
            'employment_histories' => [
                'nullable',
                'array',
            ],
            'employment_histories.*.title' => 'required|string|max:255',
            'employment_histories.*.organization' => 'required|string|max:255',
            'employment_histories.*.start_date' => 'required|date',
            'employment_histories.*.end_date' => 'required|date|after_or_equal:start_date',
        ];

        if ($subStep === 'employment_info') {
            return Arr::only($rules, [
                'staff_number', 'employment_type', 'joined_on', 'campus_location', 'job_position', 'line_manager',
            ]);
        }

        if ($subStep === 'employment_history') {
            return Arr::only($rules, [
                'employment_histories',
            ]);
        }

        return $rules;
    }

    private function qualificationsStep($subStep = null): array
    {
        $rules = [
            'education_qualifications' => [
                'nullable',
                'array',
            ],
            'education_qualifications.*.code' => 'nullable|string|max:255',
            'education_qualifications.*.provider' => 'nullable|string|max:255',
            'education_qualifications.*.name' => 'nullable|string|max:255',
            'education_qualifications.*.start_date' => 'nullable|date',
            'education_qualifications.*.end_date' => 'nullable|date|after_or_equal:education_qualifications.*.start_date',
            'training_qualifications' => [
                'nullable',
                'array',
            ],
            'training_qualifications.*.code' => 'nullable|string|max:255',
            'training_qualifications.*.provider' => 'required|string|max:255',
            'training_qualifications.*.name' => 'required|string|max:255',
            'training_qualifications.*.start_date' => 'nullable|date',
            'training_qualifications.*.end_date' => 'nullable|date|after_or_equal:training_qualifications.*.start_date',
        ];

        if ($subStep === 'education_qualifications') {
            return Arr::only($rules, [
                'education_qualifications',
            ]);
        }

        if ($subStep === 'training_qualifications') {
            return Arr::only($rules, [
                'training_qualifications',
            ]);
        }

        return $rules;
    }

    private function personalDevelopmentStep($subStep = null): array
    {
        return [
            'professional_developments' => 'nullable|array',
            'professional_developments.*.event_name' => 'required|string|max:255',
            'professional_developments.*.organized_by' => 'required|string|max:255',
            'professional_developments.*.activity_name' => 'required|string|max:255',
            'professional_developments.*.event_from' => 'required|date',
            'professional_developments.*.event_to' => 'required|date|after_or_equal:professional_developments.*.event_from',
            'professional_developments.*.cpd_points' => 'required|numeric|min:0',
            'professional_developments.*.comments' => 'nullable|string|max:1000',
        ];
    }

    private function bankingInfoStep($subStep = null): array
    {
        $rules = [
            'bank_name' => 'required|string|max:255',
            'account_name' => 'required|string|max:255',
            'bsb' => 'required|string|max:20',
            'account_number' => 'required|string|max:50',

            'swift_code' => 'nullable|string|max:50',
            'tax_file_number' => 'required|string|max:50',
            'super_fund_name' => 'nullable|string|max:255',
            'super_member_number' => 'nullable|string|max:50',
        ];

        if ($subStep === 'bank_details') {
            return Arr::only($rules, [
                'bank_name', 'account_name', 'bsb', 'account_number', 'swift_code',
            ]);
        }

        if ($subStep === 'payroll_information') {
            return Arr::only($rules, [
                'tax_file_number', 'super_fund_name', 'super_member_number',
            ]);
        }

        return $rules;
    }

    private function tscInfoStep(): array
    {
        return [
            'birth_date' => 'required_if:not_applicable,false|date',
            'gender' => 'required_if:not_applicable,false|string|max:50',
            'joining_date' => 'nullable|date',
            'atsi_code.code' => ['nullable', ATSICodeEnum::getValidationString()],
            'highest_qualification_code.code' => ['nullable', HighestQualificationCodeEnum::getValidationString()],
            'highest_qualification_place_code.code' => ['nullable', HighestQualificationPlaceCodeEnum::getValidationString()],
            'work_contract_code.code' => ['nullable', WorkContractCodeEnum::getValidationString()],
            'staff_work_level_code.code' => ['nullable', StaffWorkLevelCodeEnum::getValidationString()],
            'organisational_unit_code.code' => ['nullable', OrganisationalUnitCodeEnum::getValidationString()],
            'work_sector_code.code' => ['nullable', WorkSectorCodeEnum::getValidationString()],
            'function_code.code' => ['nullable', FunctionCodeEnum::getValidationString()],
        ];
    }

    public function messages(): array
    {
        return [
            // Basic Info
            'user_type.required' => 'Please select a user type.',
            'name_title.required_if' => 'Title is required.',
            'first_name.required_if' => 'First name is required.',
            'last_name.required_if' => 'Last name is required.',
            'name.required_if' => 'Business name is required.',
            'is_agency.required_if' => 'Please specify if this is an agency.',
            'super_agent_id.required_if' => 'Super agent is required.',
            'total_employes.required_if' => 'Number of employees is required.',
            'industry_id.required_if' => 'Industry is required.',
            'account_manager_id.required_if' => 'Account manager is required.',
            'notes.required_if' => 'Notes are required.',
            'website.required_if' => 'Website is required.',
            'gender.required' => 'Gender is required.',
            'email.unique' => 'This email is already registered.',
            'dob.before' => 'Date of birth must be in the past.',
            'gender.in' => 'Please select a valid gender.',
            'gender.required_if' => 'Gender is required.',

            // Emergency Contact
            'emergency_contact_name.required' => 'Emergency contact name is required.',
            'emergency_contact_relationship.required_if' => 'Relationship is required.',
            'emergency_phone_1.required' => 'Primary emergency phone is required.',
            'emergency_phone_2.required' => 'Secondary emergency phone is required.',
            'emergency_email.required' => 'Emergency email is required.',
            'emergency_address.required' => 'Emergency address is required.',
            'country.required' => 'Country is required.',
            'country.exists' => 'Please select a valid country.',

            // Address & Postal
            'residential_address.required' => 'Residential address is required.',
            'residential_country.required' => 'Residential country is required.',
            'residential_state.required' => 'Residential state is required.',
            'residential_city.required' => 'Residential city is required.',
            'residential_postcode.required' => 'Postcode is required.',
            'postal_address.required_if' => 'Postal address is required.',
            'postal_country.required_if' => 'Postal country is required.',
            'postal_state.required_if' => 'Postal state is required.',
            'postal_city.required_if' => 'Postal city is required.',
            'postal_postcode.required_if' => 'Postal postcode is required.',
            'postal_abn.required_if' => 'ABN is required.',
            'is_post_residential_same.required' => 'Please specify if postal address matches residential.',
            'is_post_residential_same.in' => 'Please select a valid option.',

            // Employment Info
            'employment_type.required' => 'Employment type is required.',
            'joined_on.required' => 'Join date is required.',
            'campus_location.required' => 'Campus location is required.',
            'job_position.required' => 'Job position is required.',
            'line_manager.required' => 'Line manager is required.',

            // Employment History
            'employment_histories.*.title.required' => 'Job title is required.',
            'employment_histories.*.organization.required' => 'Organization name is required.',
            'employment_histories.*.start_date.required' => 'Start date is required.',
            'employment_histories.*.end_date.required' => 'End date is required.',

            // Banking Info
            'bank_name.required' => 'Bank name is required.',
            'account_name.required' => 'Account name is required.',
            'bsb.required' => 'BSB is required.',
            'account_number.required' => 'Account number is required.',
            'tax_file_number.required' => 'TFN is required.',
            'joining_date.required' => 'Joining date is required.',
            'atsi_code.code.required' => 'ATSI code is required.',
            'highest_qualification_code.code.required' => 'Highest qualification is required.',
            'highest_qualification_place_code.code.required' => 'Qualification place is required.',
            'work_contract_code.code.required' => 'Work contract type is required.',
            'staff_work_level_code.code.required' => 'Work level is required.',
            'organisational_unit_code.code.required' => 'Organizational unit is required.',
            'work_sector_code.code.required' => 'Work sector is required.',
            'function_code.code.required' => 'Function code is required.',
            'birth_date.before' => 'Birth date must be in the past.',
            'end_date.after_or_equal' => 'End date cannot be before start date.',

            // Qualifications
            'qualifications.*.code.required' => 'Qualification code is required.',
            'qualifications.*.provider.required' => 'Provider is required.',
            'qualifications.*.name.required' => 'Qualification name is required.',
            'qualifications.*.start_date.required' => 'Start date is required.',
            'qualifications.*.end_date.after_or_equal' => 'End date cannot be before start date.',
            'qualifications.*.document.required' => 'Document is required.',
            'qualifications.*.document.mimes' => 'Please upload a valid file type (JPG, PNG, PDF, DOCX).',
            'qualifications.*.document.max' => 'File size must be under 50MB.',
            'qualifications.*.document.file' => 'Please upload a valid file.',

            // Education Qualifications
            'education_qualifications.*.code.required' => 'Education code is required.',
            'education_qualifications.*.provider.required' => 'Education provider is required.',
            'education_qualifications.*.name.required' => 'Education name is required.',
            'education_qualifications.*.start_date.required' => 'Start date is required.',
            'education_qualifications.*.end_date.required' => 'End date is required.',
            'education_qualifications.*.end_date.after_or_equal' => 'End date cannot be before start date.',

            // Training Qualifications
            'training_qualifications.*.code.required' => 'Training code is required.',
            'training_qualifications.*.provider.required' => 'Training provider is required.',
            'training_qualifications.*.name.required' => 'Training name is required.',
            'training_qualifications.*.start_date.required' => 'Start date is required.',
            'training_qualifications.*.end_date.after_or_equal' => 'End date cannot be before start date.',

            // Professional Development
            'professional_developments.*.event_name.required' => 'Event name is required.',
            'professional_developments.*.organized_by.required' => 'Organizer is required.',
            'professional_developments.*.activity_name.required' => 'Activity name is required.',
            'professional_developments.*.event_from.required' => 'Start date is required.',
            'professional_developments.*.event_to.required' => 'End date is required.',
            'professional_developments.*.event_to.after_or_equal' => 'End date cannot be before start date.',
            'professional_developments.*.cpd_points.required' => 'CPD points are required.',
        ];
    }

    private function uploadDocsStep()
    {
        return [
            'documents' => 'nullable|array',
            'documents.*' => 'file|mimes:pdf,doc,docx,jpg,jpeg,png|max:10240', // 10MB max
        ];
    }
}
