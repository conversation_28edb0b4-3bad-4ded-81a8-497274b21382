<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Support\Auth\Permission;

class ModulePermissionSeeder extends Seeder
{
    private $permissionsTypesFolderMap = [
        'staff' => [
            'type' => 'Staffs',
            'type_alias' => 'team-members',
            'prefix' => '',
        ],
        'agent' => [
            'type' => 'Agents',
            'type_alias' => 'agents',
            'prefix' => 'ap_',
        ],
        'teacher' => [
            'type' => 'Teachers',
            'type_alias' => 'teachers',
            'prefix' => 'tp_',
        ],
        'employers' => [
            'type' => 'Employers',
            'type_alias' => 'employers',
            'prefix' => 'ep_',
        ],
        'serviceproviders' => [
            'type' => 'Service Providers',
            'type_alias' => 'service-providers',
            'prefix' => 'spp_',
        ],
        'placementproviders' => [
            'type' => 'Placement Providers',
            'type_alias' => 'placement-providers',
            'prefix' => 'plpp_',
        ],
        'student' => [
            'type' => 'Students',
            'type_alias' => 'students',
            'prefix' => 'sp_',
        ],
    ];

    private $permissionsSeeded = [];

    private $basePath = 'storage/app/permissions/';

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        /*
        Get all the folders inside the permissions directory.
        */
        foreach ($this->permissionsTypesFolderMap as $folder => $type) {
            if (! File::exists($this->basePath.$folder)) {
                info('File does not exist: '.$this->basePath.$folder);

                continue;
            }
            $files = File::files($this->basePath.$folder);
            $allPermissions = [];
            foreach ($files as $file) {
                $fileName = $file->getFilename();
                $ext = $file->getExtension();
                if ($ext === 'json' && ! str_contains($fileName, '_pending')) {
                    $fileContent = File::get($file->getPathname());
                    $permissionsData = json_decode($fileContent, true);
                    // $permissionsData = file_get_contents(base_path($file->getPathname()));
                    if ($permissionsData) {
                        $allPermissions = [...$allPermissions, ...$permissionsData];
                    }
                }
            }
            if (count($allPermissions) > 0) {
                $this->command->info("\n****\tProcessing permissions for: {$type['type']}\t****\n");
                $this->createPermissions($allPermissions, $type['type_alias'], $type['prefix']);
            }
        }
        Permission::whereNotIn('name', $this->permissionsSeeded)->delete();

        app()->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();
    }

    private function createPermissions($permissions, $type, $prefix = '', $parentId = null)
    {
        foreach ($permissions as $permission) {
            $children = $permission['children'] ?? null;
            $insertData = [
                'name' => $prefix.$permission['name'],
                'guard_name' => Permission::DEFAULT_GUARD,
                'label' => $permission['label'] ?? null,
                'module' => $permission['module'] ?? null,
                'motive' => $permission['motive'] ?? 'read',
                'parent_id' => $parentId,
                'type' => $type,
            ];
            $permissionRecord = Permission::where([
                'name' => $insertData['name'],
                'guard_name' => Permission::DEFAULT_GUARD,
            ])->first();
            if (! $permissionRecord) {
                // If permission does not exist, create it
                $permissionRecord = Permission::create($insertData);
                $this->command->info("Created : {$insertData['name']}");
            } else {
                $permissionRecord->update($insertData);
                $this->command->info("Updated : {$insertData['name']}");
            }
            $this->permissionsSeeded[] = $insertData['name'];
            if ($permissionRecord && $children && is_array($children) && count($children) > 0) {
                $this->createPermissions($children, $type, $prefix, $permissionRecord->id);
            }
        }
    }
}
