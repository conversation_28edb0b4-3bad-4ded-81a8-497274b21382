<?php

namespace GalaxyAPI\Requests;

use GalaxyAPI\Enums\UserTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;

class ServiceProviderStepRequest extends FormRequest
{
    public function rules(): array
    {
        $step = $this->request->get('step');
        $subStep = $this->request->get('sub_step');

        return match ($step) {
            'user_info' => $this->userInfo($subStep),
            'address_postal' => $this->addressPostalStep(),
            default => [],
        };
    }

    private function userInfo($subStep = null)
    {
        $rules = [
            'user_type' => ['required', UserTypeEnum::getValidationString()],
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'mobile' => 'required|string|max:20',
            'phone' => 'nullable|string|max:20',
            'website' => 'required|string|max:255',
            'fax' => 'required|string|max:20',
            'notes' => 'nullable|string',
            'emergency_contact_name' => 'required|string|max:255',
            //            'emergency_phone_1' => 'required|string|max:20',
            //            'emergency_phone_2' => 'required|string|max:20',
            //            'emergency_email' => 'nullable|email|max:255',
            //            'emergency_address' => 'required|string|max:255',
        ];

        if ($subStep === 'personal_details') {
            return Arr::only($rules, [
                'name', 'email', 'mobile', 'phone', 'website', 'fax', 'notes',
            ]);
        }

        if ($subStep === 'emergency_contact') {
            return Arr::only($rules, [
                'emergency_contact_name',
            ]);
        }

        return $rules;
    }

    private function addressPostalStep()
    {
        return [
            'residential_address' => 'required|string|max:255',
            'residential_country' => 'required|max:255',
            'residential_state' => 'required|string|max:255',
            'residential_city' => 'required|string|max:255',
            'residential_postcode' => 'required|string|max:255',
            'residential_abn' => 'nullable|string|max:255',
        ];
    }
}
