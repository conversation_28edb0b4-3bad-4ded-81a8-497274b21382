<template>
    <div class="w-full max-w-5xl overflow-hidden rounded-lg bg-white">
        <FormElement :class="'flex h-full flex-col space-y-4'">
            <div class="flex items-center justify-between border-b-0 border-gray-200 p-4">
                <h2 class="text-lg font-semibold">{{ title }}</h2>
                <div>
                    <div class="mr-2">
                        <Button
                            :variant="'primary'"
                            :type="'submit'"
                            :size="'sm'"
                            :class="{ 'cursor-not-allowed opacity-50': !kendoForm.allowSubmit }"
                            @click="checkFormErrors"
                            :icon="'loading'"
                            :disabled="isSaving"
                        >
                            <icon :name="'loading'" :width="16" :height="16" v-if="isSaving" />
                            <span>{{ isSaving ? 'Saving...' : 'Save' }} Role</span>
                        </Button>
                    </div>
                </div>
            </div>
            <div class="flex-1 overflow-y-auto">
                <div class="space-y-4 rounded-md bg-white p-4">
                    <div class="card-form space-y-4" :class="{ hidden: !viewRoleInformation }">
                        <div class="">
                            <fieldset class="k-form-fieldset">
                                <Field
                                    :id="'role_name'"
                                    :name="'role_name'"
                                    :component="'roleName'"
                                    :label="'Role Name'"
                                    :placeholder="'Enter a name for the role'"
                                    :validator="requiredtrue"
                                    :indicaterequired="true"
                                >
                                    <template #roleName="{ props }">
                                        <FormInput
                                            v-bind="props"
                                            @change="props.onChange"
                                            @blur="props.onBlur"
                                            @focus="props.onFocus"
                                        />
                                    </template>
                                </Field>
                            </fieldset>
                        </div>
                        <div v-if="hasChilds && multilevel">
                            <field
                                :id="'role_category'"
                                :name="'role_category'"
                                :label="getCategoryLabel"
                                :data-items="getChildRoles"
                                :layout="'horizontal'"
                                :component="'childRoleTypeTemplate'"
                                :validator="requiredtrue"
                                :pt="{
                                    field: 'grid !grid-cols-1 md:!grid-cols-2',
                                }"
                            >
                                <template v-slot:childRoleTypeTemplate="{ props }">
                                    <FormRadioGroup
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                        </div>
                        <div>
                            <fieldset class="k-form-fieldset">
                                <Field
                                    :id="'role_description'"
                                    :name="'role_description'"
                                    :component="'roleDescription'"
                                    label="Description"
                                    :validator="requiredtrue"
                                    :indicaterequired="true"
                                    :placeholder="'Provide a short description of the role'"
                                    :type="'email'"
                                >
                                    <template #roleDescription="{ props }">
                                        <FormTextArea
                                            v-bind="props"
                                            @change="props.onChange"
                                            @blur="props.onBlur"
                                            @focus="props.onFocus"
                                        />
                                    </template>
                                </Field>
                            </fieldset>
                        </div>
                    </div>
                </div>
                <div class="space-y-4 rounded-md p-4">
                    <div class="space-x-8 rounded-md bg-gray-100 p-4">
                        <div class="card-form space-y-8" :class="{ hidden: !viewUserRoles }">
                            <div class="w-full">
                                <div class="font-medium text-gray-700">
                                    Select Permissions by Module
                                </div>
                                <div class="text-gray-700">
                                    Enable or disable permissions grouped under each functional
                                    module.
                                </div>
                            </div>
                            <NewRoleGrid
                                :mode="'edit'"
                                :newrolename="getRoleName"
                                :save="'track'"
                                :openstate="true"
                                @permissionchanged="updatePermissions"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </FormElement>
    </div>
</template>
<script>
import { mapState } from 'pinia';
import { usersStore } from '@spa/stores/modules/userManagement';
import { DialogActionsBar } from '@progress/kendo-vue-dialogs';
import { Field, FormElement } from '@progress/kendo-vue-form';
import FormNumericTextBox from '@spa/components/KendoInputs/FormNumericTextBox.vue';
import Button from '@spa/components/Buttons/Button';
import PrimaryButton from '@spa/components/Buttons/PrimaryButton.vue';
import SecondaryButton from '@spa/components/Buttons/SecondaryButton.vue';
import GlobalContextLoader from '@spa/components/Loader/GlobalContextLoader.vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormRadioGroup from '@spa/components/KendoInputs/FormRadioGroup.vue';
import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
import PermissionGrid from '@spa/modules/usermanagement/PermissionGrid.vue';
import NewRoleGrid from '@spa/modules/usermanagement/forms/NewRoleGrid.vue';
import { useLoaderStore } from '@spa/stores/modules/global-loader';

import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';

export default {
    setup() {
        const store = useLoaderStore();
        return {
            store,
        };
    },
    props: {
        multilevel: { type: Boolean, default: false },
        title: { type: String, default: 'Add New User' },
    },
    components: {
        Field,
        FormElement,
        DialogActionsBar,
        SecondaryButton,
        Button,
        GlobalContextLoader,
        FormInput,
        FormDropDown,
        FormRadioGroup,
        FormTextArea,
        PermissionGrid,
        NewRoleGrid,
    },
    inject: {
        kendoForm: { default: {} },
    },
    computed: {
        ...mapState(usersStore, ['getCurrentSystemRole', 'getCurrentSystemChildRole']),
        systemRole() {
            return this.getCurrentSystemRole || {};
        },
        hasChilds() {
            return this.getChildRoles?.length > 0;
        },
        getChildRoles() {
            const roles = this.systemRole?.child_roles || [];
            const transformedRoles =
                roles?.length > 0
                    ? roles.map(({ role_name, role_alias }) => ({
                          label: role_name,
                          value: role_alias,
                      }))
                    : {};
            return transformedRoles;
        },
        getCategoryLabel() {
            const systemRoleName = this.systemRole?.role_name || '';
            return `${systemRoleName} Type`;
        },
        getRoleName() {
            return this.kendoForm.valueGetter('role_name');
        },
        isSaving() {
            return this.store.isContextLoading(this.contextName);
        },
    },
    data() {
        return {
            viewRoleInformation: true,
            viewUserRoles: true,
            contextName: 'savingRole',
        };
    },
    methods: {
        requiredtrue,
        cancelProcess() {
            this.store.stopContextLoading(this.contextName);
            this.$emit('cancel');
        },
        updatePermissions(permissions) {
            this.kendoForm.onChange('permissions', {
                value: permissions,
            });
        },
        checkFormErrors() {
            if (!this.kendoForm.valid) {
                setTimeout(() => {
                    this.scrollToFirstError();
                }, 1);
            }
        },
        scrollToFirstError() {
            const firstElement = document.querySelector('.k-text-error');
            if (firstElement) {
                firstElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start', // aligns top of element with top of viewport
                    inline: 'nearest',
                });
            }
        },
    },
};
</script>
