<template>
    <Dialog
        v-if="visible"
        :title="'Template Settings'"
        :width="500"
        :height="700"
        @close="handleClose"
        dialog-class="tw-content-p-0 tw-dialog"
    >
        <div class="modal-content">
            <Form
                ref="formRef"
                @submit="handleSubmit"
                :initial-values="initialValues"
                id="settingsForm"
                :ignoreModified="true"
            >
                <FormElement class="pt-4">
                    <fieldset class="mx-6 rounded-md border border-gray-200 bg-white p-4">
                        <legend class="px-1 text-sm font-semibold text-gray-900">
                            Page Settings
                        </legend>
                        <div class="space-y-5">
                            <Field
                                :id="'pagination_enabled'"
                                :name="'pagination_enabled'"
                                :label="'Show Page Numbers'"
                                :hint="'Display page numbers at the bottom of each page.'"
                                :data-items="enableOptions"
                                :layout="'horizontal'"
                                :component="'PaginationEnable'"
                                :pt="{
                                    root: 'flex items-center gap-5',
                                    field: '!grid !gap-6 grid-cols-3 max-w-xl',
                                    label: 'mb-2 text-base',
                                }"
                            >
                                <template #PaginationEnable="{ props }">
                                    <FormRadioGroup
                                        v-bind="props"
                                        :value="paginationEnabled"
                                        :default-value="paginationEnabled"
                                        @change="
                                            (e) => {
                                                paginationEnabled = e.value;
                                            }
                                        "
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </Field>

                            <div v-if="Number(paginationEnabled) === 1" class="space-y-4">
                                <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div class="col-span-1 md:col-span-2">
                                        <Field
                                            :id="'pagination_position'"
                                            :name="'pagination_position'"
                                            :label="'Position on Page'"
                                            :hint="'Choose where to show the page number (e.g., Bottom Right).'"
                                            :data-items="sortedPositions"
                                            :default-item="{ label: 'Select Position' }"
                                            :text-field="'label'"
                                            :data-item-key="'value'"
                                            :value-field="'value'"
                                            :valuePrimitive="true"
                                            :component="'PositionSelect'"
                                            :pt="{
                                                root: 'flex items-center gap-5',
                                                input: 'w-full',
                                            }"
                                        >
                                            <template #PositionSelect="{ props }">
                                                <FormDropDown
                                                    v-bind="props"
                                                    @change="props.onChange"
                                                    @blur="props.onBlur"
                                                />
                                            </template>
                                        </Field>
                                    </div>

                                    <Field
                                        :id="'pagination_offset_x'"
                                        :name="'pagination_offset_x'"
                                        :label="'Move Sideways (X)'"
                                        :hint="'Adjust the page number’s position left or right. Default: 0.'"
                                        :component="'OffsetX'"
                                        :pt="{ root: 'flex items-center gap-5', input: 'w-full' }"
                                    >
                                        <template #OffsetX="{ props }">
                                            <FormInput
                                                v-bind="props"
                                                :type="'number'"
                                                @change="props.onChange"
                                                @blur="props.onBlur"
                                                @focus="props.onFocus"
                                            />
                                        </template>
                                    </Field>

                                    <Field
                                        :id="'pagination_offset_y'"
                                        :name="'pagination_offset_y'"
                                        :label="'Move Up/Down (Y)'"
                                        :hint="'Adjust the page number’s position higher or lower. Default: 0.'"
                                        :component="'OffsetY'"
                                        :pt="{ root: 'flex items-center gap-5', input: 'w-full' }"
                                    >
                                        <template #OffsetY="{ props }">
                                            <FormInput
                                                v-bind="props"
                                                :type="'number'"
                                                @change="props.onChange"
                                                @blur="props.onBlur"
                                                @focus="props.onFocus"
                                            />
                                        </template>
                                    </Field>

                                    <Field
                                        :id="'pagination_font_size'"
                                        :name="'pagination_font_size'"
                                        :label="'Text Size'"
                                        :hint="'Set the font size for page numbers. Default: 9.'"
                                        :component="'FontSize'"
                                        :pt="{ root: 'flex items-center gap-5', input: 'w-full' }"
                                    >
                                        <template #FontSize="{ props }">
                                            <FormInput
                                                v-bind="props"
                                                :type="'number'"
                                                @change="props.onChange"
                                                @blur="props.onBlur"
                                                @focus="props.onFocus"
                                            />
                                        </template>
                                    </Field>
                                </div>
                                <p class="text-xs text-gray-500">
                                    💡 Tip: X controls sideways movement; Y controls up/down
                                    movement.
                                </p>
                            </div>
                        </div>
                    </fieldset>

                    <fieldset class="mx-6 mt-4 rounded-md border border-gray-200 bg-white p-4">
                        <legend class="px-1 text-sm font-semibold text-gray-900">
                            Table Settings
                        </legend>
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-1">
                            <Field
                                :id="'page_break_top'"
                                :name="'page_break_top'"
                                :label="'Top Margin for Next Table (px)'"
                                :component="'PageBreakTop'"
                                :pt="{ root: 'flex items-center gap-5', input: 'w-full' }"
                                :hint="'Set how far from the top the next table should begin. Default: 130px.'"
                            >
                                <template #PageBreakTop="{ props }">
                                    <FormInput
                                        v-bind="props"
                                        :type="'number'"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </Field>
                            <Field
                                :id="'unit_chunk'"
                                :name="'unit_chunk'"
                                :label="'Units Per Table'"
                                :component="'UnitChunk'"
                                :pt="{ root: 'flex items-center gap-5', input: 'w-full' }"
                                :hint="'Choose how many units appear in each table. Default: 10. (used by [unit.list] / [unit.list.with.result])'"
                            >
                                <template #UnitChunk="{ props }">
                                    <FormInput
                                        v-bind="props"
                                        :type="'number'"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </Field>
                            <Field
                                :id="'trascript_chunk'"
                                :name="'trascript_chunk'"
                                :label="'Transcripts Per Table'"
                                :component="'TranscriptChunk'"
                                :pt="{ root: 'flex items-center gap-5', input: 'w-full' }"
                                :hint="'Choose how many transcripts appear in each table. Default: 2. (used by [unit.interim.table] )'"
                            >
                                <template #TranscriptChunk="{ props }">
                                    <FormInput
                                        v-bind="props"
                                        :type="'number'"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </Field>
                        </div>
                    </fieldset>

                    <fieldset class="mx-6 mt-4 rounded-md border border-gray-200 bg-white p-4">
                        <legend class="px-1 text-sm font-semibold text-gray-900">
                            Overflow Handling
                        </legend>
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-1">
                            <Field
                                :id="'overflow_enabled'"
                                :name="'overflow_enabled'"
                                :label="'Show Note on Overflow Pages'"
                                :hint="'Enable a message when a table continues onto the next page.'"
                                :component="'OverflowEnable'"
                                :pt="{ root: 'flex items-center gap-5' }"
                            >
                                <template #OverflowEnable="{ props }">
                                    <FormRadioGroup
                                        v-bind="props"
                                        :value="overflowEnabled"
                                        :default-value="overflowEnabled"
                                        :data-items="enableOptions"
                                        @change="
                                            (e) => {
                                                overflowEnabled = e.value;
                                            }
                                        "
                                        @blur="props.onBlur"
                                    />
                                </template>
                            </Field>

                            <Field
                                v-if="Number(overflowEnabled) === 1"
                                :id="'overflow_text'"
                                :name="'overflow_text'"
                                :label="'Note Text'"
                                :component="'OverflowText'"
                                :pt="{ root: 'flex items-center gap-5', input: 'w-full' }"
                                :hint="'Customize the note shown on overflow pages. Example: “Units Achieved (continued)”.'"
                            >
                                <template #OverflowText="{ props }">
                                    <FormInput
                                        v-bind="props"
                                        :type="'text'"
                                        :placeholder="'Units Achieved (continued)'"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                    />
                                </template>
                            </Field>
                        </div>
                    </fieldset>

                    <div
                        class="sticky bottom-0 mt-4 flex w-full items-center justify-end gap-3 bg-white px-6 py-4"
                    >
                        <Button variant="secondary" size="sm" @click="handleClose">Cancel</Button>
                        <Button
                            type="submit"
                            size="sm"
                            variant="primary"
                            class="min-w-[100px]"
                            :loading="loaderStore.contextLoaders['button']"
                            loadingText="Saving..."
                            >Save</Button
                        >
                    </div>
                </FormElement>
            </Form>
        </div>
    </Dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Dialog } from '@progress/kendo-vue-dialogs';
import Button from '@spa/components/Buttons/Button.vue';
import { Form, FormElement, Field } from '@progress/kendo-vue-form';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import FormRadioGroup from '@spa/components/KendoInputs/FormRadioGroup.vue';

const props = defineProps({
    visible: { type: Boolean, required: true },
    template: { type: Object, required: true },
});

const emit = defineEmits(['close', 'submit']);
const loaderStore = useLoaderStore();

const paginationEnabled = ref(0);
const position = ref('bottom_right');
const offsetX = ref(0);
const offsetY = ref(0);
const fontSize = ref(9);
const pageBreakTop = ref(130);
const unitChunk = ref(10);
const trascriptChunk = ref(2);

const positions = [
    { value: 'bottom_center', label: 'Bottom Center' },
    { value: 'bottom_left', label: 'Bottom Left' },
    { value: 'bottom_right', label: 'Bottom Right' },
    { value: 'top_center', label: 'Top Center' },
    { value: 'top_left', label: 'Top Left' },
    { value: 'top_right', label: 'Top Right' },
];

// Overflow handling state
const overflowEnabled = ref(0);
const overflowText = ref('Units Achieved (continued)');

const enableOptions = [
    { label: 'Enable', value: 1 },
    { label: 'Disable', value: 0 },
];

const sortedPositions = computed(() =>
    [...positions].sort((a, b) => a.label.localeCompare(b.label))
);

const initialValues = computed(() => ({
    pagination_enabled: Number(paginationEnabled.value),
    pagination_position: position.value,
    pagination_offset_x: Number(offsetX.value),
    pagination_offset_y: Number(offsetY.value),
    pagination_font_size: Number(fontSize.value),
    page_break_top: Number(pageBreakTop.value),
    unit_chunk: Number(unitChunk.value),
    trascript_chunk: Number(trascriptChunk.value),
    overflow_enabled: Number(overflowEnabled.value),
    overflow_text: String(overflowText.value),
}));

watch(
    () => props.template,
    (tpl) => {
        try {
            let meta = tpl?.metadata;
            if (typeof meta === 'string') meta = JSON.parse(meta);
            const p = meta?.settings?.pagination || {};
            paginationEnabled.value = p?.enabled ? 1 : 0;
            position.value = p?.position || 'bottom_right';
            offsetX.value = Number(p?.offset_x ?? 0);
            offsetY.value = Number(p?.offset_y ?? 0);
            fontSize.value = Number(p?.font_size ?? 9);
            const nextTable = meta?.settings?.page_break_table || {};
            pageBreakTop.value = Number(nextTable?.top ?? 130);
            const chunk = meta?.settings?.chunk || {};
            unitChunk.value = Number(chunk?.unit ?? 10);
            trascriptChunk.value = Number(chunk?.trascript ?? 2);
            const overflow = meta?.settings?.overflow || {};
            overflowEnabled.value = overflow?.enabled ? 1 : 0;
            overflowText.value = String(overflow?.text ?? 'Units Achieved (continued)');
        } catch (e) {
            paginationEnabled.value = 0;
            position.value = 'bottom_right';
            offsetX.value = 0;
            offsetY.value = 0;
            fontSize.value = 9;
            pageBreakTop.value = 130;
            unitChunk.value = 10;
            trascriptChunk.value = 2;
            overflowEnabled.value = 0;
            overflowText.value = 'Units Achieved (continued)';
        }
    },
    { immediate: true }
);

const handleClose = () => emit('close');

const handleSubmit = (formData) => {
    // Prefer Kendo Form values for dropdown/inputs; radio is still native
    const v = formData || {};
    emit('submit', {
        pagination_enabled: Number(paginationEnabled.value) === 1,
        pagination_position: v.pagination_position ?? position.value,
        pagination_offset_x: Number(v.pagination_offset_x ?? offsetX.value) || 0,
        pagination_offset_y: Number(v.pagination_offset_y ?? offsetY.value) || 0,
        pagination_font_size: Number(v.pagination_font_size ?? fontSize.value) || 9,
        page_break_top: Number(v.page_break_top ?? pageBreakTop.value) || 130,
        unit_chunk: Number(v.unit_chunk ?? unitChunk.value) || 10,
        trascript_chunk: Number(v.trascript_chunk ?? trascriptChunk.value) || 2,
        overflow_enabled: Number(v.overflow_enabled ?? overflowEnabled.value) === 1,
        overflow_text: String(v.overflow_text ?? overflowText.value),
    });
};
</script>
