<template>
    <AsyncForm
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        position="center"
        :max-width="'400px'"
        :dialogTitle="'Add Intervention Type'"
        :store="store"
    >
        <div class="p-4">
            <div class="p-2">
                <FormInput
                    name="intervention_type"
                    label="Intervention Type"
                    v-model="formData.intervention_type"
                    :validation-message="store.errors?.intervention_type"
                    :valid="!store.errors?.intervention_type"
                    :touched="true"
                    :indicaterequired="true"
                />
            </div>
        </div>
    </AsyncForm>
</template>
<script setup>
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import { useInterventionTypeStore } from '@spa/stores/modules/interventiontype/useInterventionTypeStore.js';
import { storeToRefs } from 'pinia';
// Uncomment these if needed:
// import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
// import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';

const store = useInterventionTypeStore();
const { formData } = storeToRefs(store);
</script>
